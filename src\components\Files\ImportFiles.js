import React, { useState } from "react";
import { useDropzone } from "react-dropzone";
import { DeleteIcon, DeleteIconn, ImportFileIcon } from "../../icons";
import ConfirmNNextButton from "../Buttons/Button";

function ImportFiles({ setFile, isDetailList, isScAtLCR, file }) {
  const [selectedFileName, setSelectedFileName] = useState("");
  const [isLessThan5MB, setIsLessThan5MB] = useState(true);
  const [isCSVOrExcelFormat, setIsCSVOrExcelFormat] = useState(true);

  const onDrop = (acceptedFiles) => {
    if (acceptedFiles.length > 0) {
      const selectedFile = acceptedFiles[0];
      setFile(selectedFile);
      setSelectedFileName(selectedFile.name);
      setIsLessThan5MB(selectedFile.size < 5 * 1024 * 1024);
      setIsCSVOrExcelFormat(
        selectedFile.type === "text/csv" ||
          selectedFile.type === "application/vnd.ms-excel" ||
          selectedFile.type ===
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
    }
  };

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: ".csv,.xls,.xlsx",
    multiple: false,
    maxSize: 5 * 1024 * 1024,
  });

  const clearFile = (e) => {
    e.stopPropagation();
    setFile(null);
    setSelectedFileName("");
  };

  return (
    <>
      {!isDetailList ? (
        <>
          <div className="border border-tableBorder bg-bgField rounded-lg mb-5">
            <div className="grid grid-cols-3 items-center mt-1 mx-5 p-0.5">
              <div
                {...getRootProps()}
                className={`flex items-center justify-center font-bold text-sm text-black cursor-pointer h-[40px] rounded-[10px] shadow-none ${
                  file ? "bg-bgSecondary text-white" : "bg-bgSelectOption"
                }`}
              >
                {!file ? (
                  <>
                    <ImportFileIcon className="mr-2" />
                    <div>{`Import ${isScAtLCR ? "SC AT" : ""} LCR File`}</div>
                    <input {...getInputProps()} className="hidden" />
                  </>
                ) : (
                  <>
                    <div className="flex items-center text-sm">
                      <div className="mx-3">
                        {selectedFileName.length > 20 ? (
                          <div
                            className="truncate max-w-[20ch]"
                            title={selectedFileName}
                          >
                            {selectedFileName}
                          </div>
                        ) : (
                          <div>{selectedFileName}</div>
                        )}
                      </div>

                      <DeleteIconn
                        onClick={(e) => {
                          clearFile(e);
                        }}
                        className="cursor-pointer w-6 h-6 mr-3"
                      />
                    </div>
                  </>
                )}
              </div>

              <div className="text-xs font-bold flex items-center justify-center">
                {"Or"}
              </div>
              <div>
                <ConfirmNNextButton
                  buttonClassName={`rounded-lg w-full h-[40px] ${
                    file
                      ? "bg-bgSelectOption text-sm text-black shadow-none font-bold"
                      : ""
                  }`}
                  label={`+ Add ${isScAtLCR ? "SC AT" : ""} LCR manually`}
                />
              </div>
            </div>

            <div className="text-red-500 text-xs mt-1">
              {!isCSVOrExcelFormat &&
                "File should be either CSV or Excel format"}
              {!isLessThan5MB &&
                "Max file size should be less than or equal to 5 MB"}
            </div>
          </div>
        </>
      ) : (
        <>
          <div
            {...getRootProps()}
            className="text-black font-bold text-sm underline cursor-pointer"
          >
            {selectedFileName ? (
              <>
                {" "}
                <div className="flex items-center text-sm">
                  <div className="mx-3">{selectedFileName}</div>
                  <DeleteIcon
                    onClick={(e) => {
                      clearFile(e);
                    }}
                    className="cursor-pointer w-4 h-4 mr-3 text-black"
                  />
                </div>
              </>
            ) : (
              <div>{"Upload File"}</div>
            )}
            <input {...getInputProps()} className="hidden" />
          </div>
        </>
      )}
    </>
  );
}

export default ImportFiles;
