import { axiosPrivate } from "../common/axiosPrivate";
import { moduleConfiguration } from "../common/constants";
import getAPIMap from "../routes/ApiUrls";

export async function getRetryGroupName({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${moduleConfiguration.retryPolicy}`;

  const queryParams = ["pagination[limit]=-1", "sort[id]=desc", "populate=*"];

  if (queryParams.length) {
    url += `?${queryParams.join("&")}`;
  }

  const response = await axiosPrivate.get(url);
  return response;
}

export async function getViewRetryGroup({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${moduleConfiguration.viewRetryPolicy}`;

  if (queryKey[3] && !isNaN(queryKey[3])) {
    url += `/${queryKey[3]}`;
  }
  const queryParams = [];
  if (queryKey[1] !== undefined) {
    queryParams.push(`pagination[pageSize]=${queryKey[1]}`);
  }
  if (queryKey[2] !== undefined) {
    queryParams.push(`pagination[page]=${queryKey[2]}`);
  }
  if (queryParams.length) {
    url += `?${queryParams.join("&")}`;
  }
  const response = await axiosPrivate.get(url);
  return response;
}
export async function getViewRetryGroupByID({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${moduleConfiguration.retryPolicy}`;

  if (queryKey[1] && !isNaN(queryKey[1])) {
    url += `/${queryKey[1]}`;
  }
  const queryParams = ["populate=*"];

  if (queryParams.length) {
    url += `?${queryParams.join("&")}`;
  }

  const response = await axiosPrivate.get(url);
  return response;
}
export async function deleteRetry(options) {
  let url = getAPIMap("listUrl");
  url = `${url}/${`retry-group-policies`}/${options.groupId}?failureError=${
    options.failureError
  }&failureResult=${options.failureResult}`;
  return await axiosPrivate.delete(url);
}
