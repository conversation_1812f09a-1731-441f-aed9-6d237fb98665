{"forms": [{"elements": [{"info": "MNP gateway type:<PERSON><PERSON>,<PERSON>is,Cache", "name": "mnpType", "size": 6, "type": "select", "title": "MNP gateway", "isInfo": true, "options": [{"label": "Redis", "value": "Redis"}, {"label": "Enum", "value": "Enum"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the MNP gateway"}], "defaultValue": ""}, {"info": "Name of the gateway.", "name": "gatewayName", "size": 6, "type": "text", "title": "Gateway name", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Gateway name"}, {"type": "matches", "regex": "^(?=.*[0-9])(?=.*[A-Za-z])[A-Za-z][A-Za-z0-9_ ]*$", "message": "Only alphanumeric characters with underscore & space allowed.Begin with alphabet"}, {"type": "min", "value": 3, "message": "Min length is 3"}, {"type": "max", "value": 15, "message": "Max length is 15"}], "defaultValue": "", "validationType": "string"}, {"info": "Maximum number of pending transactions.", "name": "max_trans", "size": 6, "type": "text", "title": "Max pending transaction", "isInfo": true, "isMandatory": false, "validations": [{"type": "matches", "regex": "^-?\\d+(\\.\\d+)?$", "message": "Please enter the Max pending transaction with numeric value"}, {"type": "matches", "regex": "^(0|[1-9][0-9]*)(\\.[0-9]+)?$", "message": "Please enter the Max pending transaction with Positive Integer value"}, {"type": "matches", "regex": "^-?[0-9]+$", "message": "Please enter the Max pending transaction with integer value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 30, "message": "Max range allowed is 30"}], "defaultValue": "", "validationType": "string"}, {"info": "Country of the operator.", "name": "zone", "size": 6, "type": "text", "title": "Zone", "isInfo": true, "dynamic": {"field": "mnpType", "value": "Enum"}, "isMandatory": false, "validations": [{"type": "min", "value": 3, "message": "Min length is 3"}, {"type": "max", "value": 50, "message": "Max length is 50"}, {"type": "matches", "regex": "^[a-zA-Z0-9._ ]+$", "message": "Only alphabets,alphanumeric,underscore,space and dot are allowed"}], "defaultValue": "", "validationType": "string"}, {"info": "LB mode type:Active - Standby,Active -Active", "name": "lbmode", "size": 6, "type": "select", "title": "LB mode", "isInfo": true, "options": [{"label": "Active-StandBy", "value": "Active-StandBy"}, {"label": "Active-Active", "value": "Active-Active"}], "defaultValue": ""}, {"name": "cache_name", "size": 6, "type": "select", "title": "Select cache", "dynamic": {"field": "mnpType", "value": "Enum"}, "options": [{"label": "MNP cache", "value": "MNP cache"}], "defaultValue": ""}, {"name": "gatewayType", "size": 6, "type": "select", "title": "Gateway type", "dynamic": {"field": "mnpType", "value": ["", "Redis", "Enum", "<PERSON><PERSON>"]}, "options": [{"label": "Commercial", "value": "cond_bypass"}, {"label": "Non-commercial", "value": "none"}], "defaultValue": ""}, {"name": "ttl_override", "size": 6, "type": "text", "title": "TTL", "dynamic": {"field": "mnpType", "value": "<PERSON><PERSON>"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the TTL value"}, {"type": "matches", "regex": "^-?\\d+(\\.\\d+)?$", "message": "Please enter the TTL with numeric value"}, {"type": "matches", "regex": "^-?[0-9]+$", "message": "Please enter the TTL with integer value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}], "defaultValue": "86400", "validationType": "string"}], "formName": "Basic information"}, {"elements": [{"name": "nodes", "size": 12, "type": "fieldArray", "validationType": "array", "validation": false, "modelType": "mnp&channel", "fieldsData": [{"name": "host", "type": "text", "validations": [{"type": "required", "message": "Please enter the Host value"}, {"type": "ip-validation-single", "message": "Invalid host address"}]}, {"name": "port", "type": "text", "validationType": "number", "validations": [{"type": "required", "message": "Please enter the Port value"}, {"type": "min", "value": 1024, "message": "Min range allowed is 1024"}, {"type": "max", "value": 65535, "message": "Max range allowed is 65535"}, {"type": "typeError", "message": "Please enter Port with numeric value"}]}, {"name": "nconn", "type": "text", "validations": [{"type": "required", "message": "Please enter value for No.of connections"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 9999, "message": "Max range allowed is 9999"}, {"type": "typeError", "message": "Please enter No.of connections with numeric value"}, {"type": "integer", "message": "Please enter No.of connections with valid integer value"}], "validationType": "number"}, {"name": "fail", "type": "text", "validations": [{"type": "required", "message": "Please enter value for Fail threshold"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 9999, "message": "Max range allowed is 9999"}, {"type": "typeError", "message": "Please enter Fail threshold with numeric value"}, {"type": "integer", "message": "Please enter Fail threshold with valid integer value"}], "validationType": "number"}, {"name": "retries", "type": "text", "validations": [{"type": "required", "message": "Please enter value for Retries"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 9999, "message": "Max range allowed is 9999"}, {"type": "typeError", "message": "Please enter Retries with numeric value"}, {"type": "integer", "message": "Please enter Retries with valid integer value"}], "validationType": "number"}, {"name": "authinfo", "type": "text", "isPassword": true, "validations": [{"type": "required", "message": "Please enter value for Authentication"}, {"type": "min", "value": 8, "message": "Min length is 8"}, {"type": "max", "value": 16, "message": "Max length is 16"}, {"type": "matches", "regex": "^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[\\W_]).*$", "message": "A-Z, a-z, 0-9,symbol"}], "validationType": "string"}, {"name": "conntype", "type": "select", "options": [{"label": "TCP", "value": "TCP"}, {"label": "UDP", "value": "UDP"}], "validations": [{"type": "required", "message": "Please select conntype"}]}, {"name": "isDelete", "type": "delete"}], "headersData": ["Host", "Port", "No. of connections", "Fail threshold", "Retries", "Authentication", "Connection type", ""], "validations": [{"type": "mnp-custom", "message": "Required"}], "initialValues": {"nodes": [{"fail": "", "host": "", "port": "", "nconn": "", "retries": "", "authinfo": "", "conntype": "", "isDelete": true}]}}], "formName": "Advanced information"}], "header": "List of MNP Gateway", "columns": [{"header": "Gateway ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "Gateway type", "accessorKey": "attributes.mnpType"}, {"header": "Gateway name", "accessorKey": "attributes.gatewayName"}, {"buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "formType": "stepper", "buttonName": "+ Add MNP gateway", "moduleData": "mnp-gateway-detail", "moduleName": "MNP gateway", "globalSearch": [{"label": "Gateway ID", "value": "id"}, {"label": "Gateway Type", "value": "mnpType"}], "dropdownOptions": {"field": "mnpType", "options": [{"label": "ALL", "query": "", "value": "ALL"}, {"label": "Redis", "query": "filters[mnpType][$eq]=Redis", "value": "Redis"}, {"label": "Enum", "query": "filters[mnpType][$eq]=Enum", "value": "Enum"}, {"label": "<PERSON><PERSON>", "query": "filters[mnpType][$eq]=Cache", "value": "<PERSON><PERSON>"}], "defaultQuery": "", "defaultValue": "ALL"}}