import { useContext, useState } from "react";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import CustomStepper from "../components/Stepper/CustomStepper";
import StepFormTemplate from "../components/Templates/StepFormTemplate";
import { CustomerSupplierContext } from "../context/CustomerSupplierContext";

const StepForm = ({
  data,
  action,
  moduleName,
  id,
  handleCancel,
  moduleNameValue,
  moduleData,
  navigationPath,
  header,
}) => {
  const { currentStep } = useContext(multiStepFormContext);
  const steps = data?.forms?.map((x) => x.formName);

  const elements = data?.forms?.[currentStep]?.elements;
  const { generalInfoData } = useContext(CustomerSupplierContext);
  const protocolType = Number(generalInfoData?.subInterfaceEsme);
  const interfaceType = generalInfoData?.interfaceType;
  const [stepperData, setStepperData] = useState([]);

  let value = null;

  if (interfaceType === "SS7") {
    value = 1;
  } else if (protocolType === 1 || interfaceType === "SMPP_ES") {
    value = 5;
  } else if (protocolType === 2) {
    value = 12;
  }

  const updatedElements = elements?.map((element) => {
    if (element.name === "protocol" && (protocolType || interfaceType)) {
      return {
        ...element,
        defaultValue: value,
        addEditable: true,
      };
    }
    return element;
  });

  return (
    <div className="flex flex-row">
      <CustomStepper
        steps={steps}
        atStep={currentStep}
        stepperData={stepperData}
        data={data?.stepperDetails}
      ></CustomStepper>
      <div
        className=" bg-white pb-5 w-full p-4 rounded-r-lg"
        style={{
          maxWidth: "70vw",
          tableLayout: "fixed",
          overflowY: "auto",
        }}
      >
        <div className="flex flex-col">
          <StepFormTemplate
            elements={updatedElements}
            currentStep={currentStep}
            steps={steps}
            action={action}
            moduleName={moduleName}
            id={id}
            handleCancel={handleCancel}
            moduleNameValue={moduleNameValue}
            moduleData={moduleData}
            navigationPath={navigationPath}
            header={header}
            setStepperData={setStepperData}
          ></StepFormTemplate>
        </div>
      </div>
    </div>
  );
};
export default StepForm;
