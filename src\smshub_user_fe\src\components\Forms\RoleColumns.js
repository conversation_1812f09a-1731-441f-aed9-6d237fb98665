import React, { useState } from "react";
import { useFormikContext } from "formik";
import { Checkbox, Switch } from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import { CssTooltip } from "../FormsUI/StyledComponent";

const RoleColumns = ({
  selectedTabElements,
  selectedTab,
  counts,
  setFieldValue,
  tabsData,
  handleCountChange,
}) => {
  const [selectAllState, setSelectAllState] = useState({}); // Store state for each tab

  const handleSelectAllChange = (tabIndex, checked) => {
    setSelectAllState((prevState) => ({
      ...prevState,
      [tabIndex]: {
        create: checked,
        view: checked,
        update: checked,
        delete: checked,
        download: checked,
      },
    }));

    // Ensure all resources under this tab reflect the Select All state
    tabsData[tabIndex].elements.forEach(({ resource }) => {
      setFieldValue(`${resource}.create`, checked);
      setFieldValue(`${resource}.view`, checked);
      setFieldValue(`${resource}.update`, checked);
      setFieldValue(`${resource}.delete`, checked);
      setFieldValue(`${resource}.download`, checked);
    });
  };

  // Helper function to check if all permissions are selected
  const isAllSelected = (tabIndex) => {
    const permissions = selectAllState[tabIndex];

    // If permissions are undefined (first render), return false
    if (!permissions) return false;

    return Object.values(permissions).every((val) => val === true);
  };

  // React.useEffect(() => {
  //   setSelectAllState((prev) => ({
  //     ...prev,
  //     [selectedTab]: allChecked, // Update selectAll if all are checked
  //   }));
  // }, [values, selectedTab]);

  // console.log("selectedTabElements", selectedTabElements);
  return selectedTabElements.columns
    .map((col) => {
      switch (col.header) {
        case "Resource":
          return { accessorKey: col.accessorKey, header: col.header };
        case "Priviledges":
          return { accessorKey: col.accessorKey, header: col.header };
        case "Report name":
          return { accessorKey: col.accessorKey, header: col.header };
        case "Create":
          return {
            accessorKey: col.accessorKey,
            header: col.header,
            size: 80,
            Cell: ({ row }) => {
              const { restrictedFields } = row.original || {};
              const { setFieldValue, values } = useFormikContext();

              // Check if all checkboxes in the current tab are checked
              // const allChecked = Object.values(values).every(
              //   (resource) => resource.create
              // );

              // Ensure selectAllState reflects the actual state of checkboxes

              const isChecked =
                values[row.original.resource]?.[col.accessorKey] || false;

              return !restrictedFields?.includes("create") ? (
                <Checkbox
                  sx={{ transform: "scale(0.8)", padding: "4px" }}
                  checked={isChecked}
                  onChange={(e) => {
                    const newValue = e.target.checked;
                    setFieldValue(
                      `${row.original.resource}.${col.accessorKey}`,
                      newValue
                    );

                    // Check if all checkboxes in the tab are selected
                    const allChecked = tabsData[selectedTab].elements.every(
                      (el) =>
                        values[el.resource]?.create &&
                        values[el.resource]?.view &&
                        values[el.resource]?.update &&
                        values[el.resource]?.delete &&
                        values[el.resource]?.download
                    );

                    setSelectAllState((prev) => ({
                      ...prev,
                      [selectedTab]: allChecked
                        ? {
                            create: true,
                            view: true,
                            update: true,
                            delete: true,
                            download: true,
                          }
                        : {
                            create: false,
                            view: false,
                            update: false,
                            delete: false,
                            download: false,
                          }, // Set false if not all are checked
                    }));
                  }}
                />
              ) : null;
            },
          };

        case "View":
          return {
            accessorKey: "view",
            header: "View",
            size: 80,
            Cell: ({ row }) => {
              const { restrictedFields } = row.original || {};

              const { resource } = row.original;
              const isViewOnly =
                resource === "Logs Management" ||
                resource === "Default Dashboard";
              const { setFieldValue, values } = useFormikContext();
              const isChecked =
                values[row.original.resource]?.[col.accessorKey] || false; // Directly check Formik values

              return !restrictedFields?.includes("view") ? (
                <Checkbox
                  sx={{ transform: "scale(0.8)", padding: "4px" }}
                  checked={isViewOnly ? isViewOnly : isChecked}
                  disabled={isViewOnly}
                  onChange={(e) => {
                    const newValue = e.target.checked;
                    setFieldValue(
                      `${row.original.resource}.${col.accessorKey}`,
                      newValue
                    );

                    // Check if all checkboxes in the tab are selected
                    const allChecked = tabsData[selectedTab].elements.every(
                      (el) =>
                        values[el.resource]?.create &&
                        values[el.resource]?.view &&
                        values[el.resource]?.update &&
                        values[el.resource]?.delete &&
                        values[el.resource]?.download
                    );

                    setSelectAllState((prev) => ({
                      ...prev,
                      [selectedTab]: allChecked
                        ? {
                            create: true,
                            view: true,
                            update: true,
                            delete: true,
                            download: true,
                          }
                        : {
                            create: false,
                            view: false,
                            update: false,
                            delete: false,
                            download: false,
                          }, // Set false if not all are checked
                    }));
                  }}
                />
              ) : null;
            },
          };

        case "Update":
          return {
            accessorKey: "update",
            header: "Update",
            size: 80,
            Cell: ({ row }) => {
              const { restrictedFields } = row.original || {};
              const { setFieldValue, values } = useFormikContext();
              const isChecked =
                values[row.original.resource]?.[col.accessorKey] || false; // Directly check Formik values

              return !restrictedFields?.includes("update") ? (
                <Checkbox
                  sx={{ transform: "scale(0.8)", padding: "4px" }}
                  checked={isChecked}
                  onChange={(e) => {
                    const newValue = e.target.checked;
                    setFieldValue(
                      `${row.original.resource}.${col.accessorKey}`,
                      newValue
                    );

                    // Check if all checkboxes in the tab are selected
                    const allChecked = tabsData[selectedTab].elements.every(
                      (el) =>
                        values[el.resource]?.create &&
                        values[el.resource]?.view &&
                        values[el.resource]?.update &&
                        values[el.resource]?.delete &&
                        values[el.resource]?.download
                    );

                    setSelectAllState((prev) => ({
                      ...prev,
                      [selectedTab]: allChecked
                        ? {
                            create: true,
                            view: true,
                            update: true,
                            delete: true,
                            download: true,
                          }
                        : {
                            create: false,
                            view: false,
                            update: false,
                            delete: false,
                            download: false,
                          }, // Set false if not all are checked
                    }));
                  }}
                />
              ) : null;
            },
          };

        case "Delete":
          return {
            accessorKey: "delete",
            header: "Delete",
            size: 80,
            Cell: ({ row }) => {
              const { restrictedFields } = row.original || {};
              const { setFieldValue, values } = useFormikContext();
              const isChecked =
                values[row.original.resource]?.[col.accessorKey] || false; // Directly check Formik values

              return !restrictedFields?.includes("delete") ? (
                <Checkbox
                  sx={{ transform: "scale(0.8)", padding: "4px" }}
                  checked={isChecked}
                  onChange={(e) => {
                    const newValue = e.target.checked;
                    setFieldValue(
                      `${row.original.resource}.${col.accessorKey}`,
                      newValue
                    );

                    // Check if all checkboxes in the tab are selected
                    const allChecked = tabsData[selectedTab].elements.every(
                      (el) =>
                        values[el.resource]?.create &&
                        values[el.resource]?.view &&
                        values[el.resource]?.update &&
                        values[el.resource]?.delete &&
                        values[el.resource]?.download
                    );

                    setSelectAllState((prev) => ({
                      ...prev,
                      [selectedTab]: allChecked
                        ? {
                            create: true,
                            view: true,
                            update: true,
                            delete: true,
                            download: true,
                          }
                        : {
                            create: false,
                            view: false,
                            update: false,
                            delete: false,
                            download: false,
                          }, // Set false if not all are checked
                    }));
                  }}
                />
              ) : null;
            },
          };

        case "Download":
          return {
            accessorKey: "download",
            header: "Download",
            size: 80,
            Cell: ({ row }) => {
              const { restrictedFields } = row.original || {};
              // const { resource } = row.original;
              // const isDownloadable =
              //   selectedTab === 0 ? resource === "CDR Search" : true;

              const { setFieldValue, values } = useFormikContext();
              const isChecked =
                values[row.original.resource]?.[col.accessorKey] || false; // Directly check Formik values

              return !restrictedFields?.includes("download") ? (
                <Checkbox
                  sx={{ transform: "scale(0.8)", padding: "4px" }}
                  checked={isChecked}
                  onChange={(e) => {
                    const newValue = e.target.checked;
                    setFieldValue(
                      `${row.original.resource}.${col.accessorKey}`,
                      newValue
                    );

                    // Check if all checkboxes in the tab are selected
                    const allChecked = tabsData[selectedTab].elements.every(
                      (el) =>
                        values[el.resource]?.create &&
                        values[el.resource]?.view &&
                        values[el.resource]?.update &&
                        values[el.resource]?.delete &&
                        values[el.resource]?.download
                    );

                    setSelectAllState((prev) => ({
                      ...prev,
                      [selectedTab]: allChecked
                        ? {
                            create: true,
                            view: true,
                            update: true,
                            delete: true,
                            download: true,
                          }
                        : {
                            create: false,
                            view: false,
                            update: false,
                            delete: false,
                            download: false,
                          }, // Set false if not all are checked
                    }));
                  }}
                />
              ) : null;
            },
          };

        case "dashboardPanelDetails": // Handling empty header case
          return {
            accessorKey: col.accessorKey,
            header: "",
            Cell: ({ row }) => {
              const { resource } = row.original;
              if (
                ["Panel Management", "Dashboard Management"].includes(resource)
              ) {
                const countType = resource.includes("Panel")
                  ? "panelCount"
                  : "dashboardCount";
                const maxLimit = resource.includes("Panel")
                  ? col.maxPanels
                  : col.maxDashBoards;
                return (
                  <div className="flex justify-between items-center w-full">
                    <div className="flex items-center">
                      <span>
                        No. of{" "}
                        {resource.includes("Panel")
                          ? "Panel Per Dashboard"
                          : "Dashboard"}{" "}
                      </span>
                    </div>
                    <CssTooltip
                      title={`Max ${maxLimit} ${
                        resource.includes("Panel")
                          ? "Panels allowed"
                          : "Dashboards allowed"
                      }`}
                      placement="top"
                      arrow
                    >
                      <InfoIcon className="w-4 h-3.5" />
                    </CssTooltip>
                    <div className="flex items-center justify-between border rounded ml-2 w-32">
                      <button
                        className="px-2"
                        onClick={() =>
                          handleCountChange(countType, "decrease", maxLimit)
                        }
                      >
                        -
                      </button>
                      <input
                        type="number"
                        className="w-12 text-center border-none outline-none"
                        min={0}
                        max={maxLimit}
                        value={counts[countType]}
                        readOnly
                      />
                      <button
                        className="px-2"
                        onClick={() =>
                          handleCountChange(countType, "increase", maxLimit)
                        }
                      >
                        +
                      </button>
                    </div>
                  </div>
                );
              }
              return null;
            },
          };

        case "Select All":
          return {
            id: "select",
            Header: ({ table }) => (
              <div
                style={{ display: "flex", alignItems: "center", gap: "5px" }}
              >
                <span>Select All</span>
                <Switch
                  checked={isAllSelected(selectedTab)} // Use per-tab state
                  onChange={(e) =>
                    handleSelectAllChange(selectedTab, e.target.checked)
                  }
                />
              </div>
            ),
            Cell: "",
          };

        default:
          return null;
      }
    })
    .filter(Boolean);
};

export default RoleColumns;
