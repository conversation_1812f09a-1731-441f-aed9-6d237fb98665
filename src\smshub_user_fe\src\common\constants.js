export const users = [
  { label: "User name", value: "name" },
  { label: "Status", value: "status" },
  { label: "Role", value: "role" },
];
export const resultPerPage = [
  { name: "10", values: "10" },
  { name: "20", values: "20" },
  { name: "50", values: "50" },
  { name: "100", values: "100" },
  { name: "200", values: "200" },
  { name: "500", values: "500" },
  { name: "999", values: "999" },
];
export const MAX_DASHBOARD = 10;
export const MAX_PANEL = 5;
export const tabItems = [
  { label: "Basic Features" },
  { label: "Static Reports" },
  { label: "Dynamic Dashboard" },
];
export const dataFields = [
  { resource: "Card Management", restrictedFields: [] },
  {
    resource: "Logs Management",
    restrictedFields: ["create", "update", "delete"],
  },
  {
    resource: "Default Dashboard",
    restrictedFields: ["create", "update", "delete"],
  },
  { resource: "Alert Management", restrictedFields: [] },
  { resource: "Group Management", restrictedFields: [] },
  { resource: "Panel Management", restrictedFields: [] },
  {
    resource: "Report Management",
    restrictedFields: ["create", "update", "delete"],
  },
  { resource: "Dashboard Management", restrictedFields: [] },
  { resource: "Role Management", restrictedFields: [] },
  {
    resource: "CDR Search",
    restrictedFields: ["create", "update", "delete"],
  },
];

export const environmentSetup = [
  { resource: "Home network", restrictedFields: [] },
  { resource: "Retry policy", restrictedFields: [] },
  { resource: "Deal management", restrictedFields: [] },
  { resource: "Channel partner", restrictedFields: [] },
  { resource: "Customer credit profile", restrictedFields: [] },
  { resource: "Credit transaction", restrictedFields: [] },
];

export const moduleConfiguration = {
  roleManagement: "role-managements",
  UserManagement: "user-managements",
};
