import * as Yup from "yup";

export const getValidationSchema = (file, customerType) => {
  return Yup.object().shape({
    lcrName: Yup.string()
      .matches(/^[A-Za-z][A-Za-z0-9_]*$/, {
        message:
          "LCR Name must start with a letter and can only contain alphanumeric characters and underscores",
        excludeEmptyString: true,
      })
      .required("Please Enter LCR Name")
      .min(4, "LCR Name must be at least 4 characters")
      .max(30, "LCR Name must be at most 30 characters"),

    lcrType: Yup.string().required("LCR type is required"),
    custType: Yup.string().required("Customer type is required"),

    parentDetails: file
      ? Yup.array()
      : Yup.array()
          .of(
            Yup.object().shape({
              policyDetails: Yup.array()
                .of(
                  Yup.object().shape({
                    supplier: Yup.string().required("Supplier is required"),

                    cost: Yup.number().when("supplier", (supplier, schema) => {
                      if (supplier[0] === "-1") {
                        return schema.nullable(true); // Skip validation if supplier is -1
                      }
                      return schema
                        .required("Cost is required")
                        .typeError("Cost must be a number")
                        .min(0, "Cost should be between 0 and 100")
                        .max(100, "Cost should be between 0 and 100");
                    }),

                    percentage: Yup.number().when(
                      "supplier",
                      (supplier, schema) => {
                        if (supplier[0] === "-1") {
                          return schema.nullable(true); // Skip validation if supplier is -1
                        }
                        return schema
                          .required("Percentage is required")
                          .typeError("Percentage must be a number")
                          .min(0, "Percentage should be between 0 and 100")
                          .max(100, "Percentage should be between 0 and 100");
                      }
                    ),

                    position: Yup.number().when(
                      "supplier",
                      (supplier, schema) => {
                        if (supplier[0] === "-1") {
                          return schema.nullable(true); // Skip validation if supplier is -1
                        }
                        return schema
                          .required("Position is required")
                          .typeError("Position must be a number")
                          .integer("Position must be a number")
                          .min(1, "Position should be between 1 and 10")
                          .max(10, "Position should be between 1 and 10");
                      }
                    ),
                  })
                )
                .test(
                  "validate-supplier-routes",
                  "Multiple suppliers cannot be added with no routes",
                  function (policyDetails, context) {
                    const suppliers = policyDetails.map(
                      (item) => item.supplier
                    );
                    const hasNoRoute = suppliers.includes("-1");
                    const hasOtherSuppliers =
                      suppliers.filter((supplier) => supplier !== "-1").length >
                      0;
                    if (hasNoRoute && hasOtherSuppliers) {
                      const lastIndex = policyDetails.length - 1;
                      return this.createError({
                        path: `${context.path}[${lastIndex}].supplier`,
                        message:
                          "Multiple suppliers cannot be added with no routes",
                      });
                    }
                    return true;
                  }
                )
                .test(
                  "total-percentage",
                  "Total percentage must equal 100",
                  function (policyDetails, context) {
                    const filteredPolicyDetails = policyDetails.filter(
                      (item) => item.supplier !== "-1" // Ignore supplier -1
                    );

                    if (filteredPolicyDetails.length === 0) return true;

                    const totalPercentage = filteredPolicyDetails.reduce(
                      (acc, item) => acc + (item.percentage || 0),
                      0
                    );

                    if (totalPercentage !== 100) {
                      const lastIndex = policyDetails.length - 1;
                      return context.createError({
                        path: `${context.path}[${lastIndex}].percentage`,
                        message: "Total percentage must equal 100",
                      });
                    }

                    return true;
                  }
                ),
              sourceOpId:
                customerType === "H"
                  ? Yup.string().required("Source operator is required")
                  : Yup.string().nullable(),
              shortCode: Yup.string().required("Short code is required"),
            })
          )
          .test(
            "file-or-lcrPolicies",
            "At least one LCR policy is required if file is not uploaded",
            function (value) {
              return value && value.length > 0;
            }
          )
          .test(
            "unique-combination",
            customerType === "S"
              ? "Short code already chosen"
              : "Combination already exists",
            function (value) {
              const seenCombinations = new Set();
              const seenShortCodes = new Set();

              for (let i = 0; i < value.length; i++) {
                const { shortCode, sourceOpId } = value[i];
                if (customerType === "S") {
                  if (seenShortCodes.has(shortCode)) {
                    return this.createError({
                      path: `parentDetails[${i}].shortCode`,
                      message: "Short code already chosen",
                    });
                  }
                  seenShortCodes.add(shortCode);
                } else {
                  const combination = `${shortCode}-${sourceOpId}`;
                  if (seenCombinations.has(combination)) {
                    return this.createError({
                      path: `parentDetails[${i}].shortCode`,
                      message: "Combination already exists",
                    });
                  }
                  seenCombinations.add(combination);
                }
              }
              return true;
            }
          ),
  });
};

const defaultPolicyDetails = {
  supplier: "",
  cost: "",
  percentage: "",
  position: "",
};

const defaultParentDetails = {
  shortCode: "",
  sourceOpId: "",
  region: "",
  mccMnc: "",
  policyDetails: [defaultPolicyDetails],
};

export const getInitialValues = (formData) => ({
  lcrName: formData?.lcrName || "",
  lcrType: formData?.lcrType ?? 2,
  custType: formData?.custType || "H",
  parentDetails: formData?.parentDetails?.map((policy) => ({
    shortCode: policy.shortCode || null,
    sourceOpId: policy.sourceOpId || null,
    region: policy.region,
    mccMnc: policy.mccMnc,
    policyDetails: policy.policyDetails?.map((item) => {
      return {
        supplier: item.supplier || "",
        cost: item.cost ?? null,
        percentage:
          item.percentage !== undefined && item.percentage !== null
            ? Number(item.percentage)
            : null,
        position:
          item.position !== undefined && item.position !== null
            ? Number(item.position)
            : null,
      };
    }) || [defaultPolicyDetails],
  })) || [defaultParentDetails],
});
