import { CssTextField } from "../StyledComponent";
import { useField } from "formik";
import InputLabel from "../InputLabel";

const TextFieldWrapper = ({
  name,
  label,
  labelColor,
  labelClassName,
  layoutClassName,
  isMandatory,
  onChange,
  isDisabled,
  applyOnChange,
  minHeight,
  ...rest
}) => {
  const [field, meta] = useField(name);

  const configTextField = {
    ...field,
    ...rest,
    type: "text",
    fullWidth: true,
    autoComplete: "off",
    error: meta.touched && meta.error ? true : false,
    helperText: meta.touched && meta.error ? meta.error : "",
    //  onChange: handleChange,

    onChange: applyOnChange === true ? onChange : field.onChange,
    value: field.value ?? "",
  };
  if (meta && meta.touched && meta.error) {
    configTextField.error = true;
    configTextField.helperText = meta.error;
  }
  return (
    <div className={layoutClassName} style={{ position: "relative" }}>
      {/* <div className="text-formLabelColor text-medium mb-2">{t(label)}</div> */}

      {label && (
        <InputLabel
          label={label}
          color={labelColor}
          labelClassName={labelClassName}
          isMandatory={isMandatory}
        />
      )}
      <CssTextField
        {...configTextField}
        disabled={isDisabled}
        inputProps={{
          autoComplete: "off",
        }}
      ></CssTextField>
    </div>
  );
};

export default TextFieldWrapper;
