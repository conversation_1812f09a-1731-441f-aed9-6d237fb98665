{"header": "List of credit transaction", "columns": [{"header": "ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "Transaction type", "accessorKey": "attributes.transactionType"}, {"header": "Transaction amount", "accessorKey": "attributes.transactionAmount"}, {"id": "transactionDate", "header": "Transaction date", "accessorKey": "attributes.transactionDate", "isDate": true, "filterVariant": "date-range", "dateFormat": "ddd DD MMM YYYY"}, {"header": "<PERSON><PERSON><PERSON><PERSON>", "accessorKey": "attributes.transactionCurrency"}, {"header": "Exchange rate", "accessorKey": "attributes.transactionExchangeRate"}, {"header": "Unbilled amount", "accessorKey": "attributes.unbilledAmount"}, {"id": "lastTrafficDate", "header": "Last traffic date", "accessorKey": "attributes.lastTrafficDate", "isDate": true, "filterVariant": "date-range", "dateFormat": "ddd DD MMM YYYY"}, {"header": "Average traffic", "accessorKey": "attributes.average<PERSON><PERSON><PERSON><PERSON>"}, {"header": "Remaining credit limit", "accessorKey": "attributes.remainingCreditLimit"}, {"accessorKey": "actions"}], "moduleName": "Credit Transaction", "isDeleteIcon": false, "confirmButton": false, "globalSearch": [{"label": "Id", "value": "id"}, {"label": "Transaction Type", "value": "transactionType"}], "moduleData": "credit-transaction", "elements": [{"name": "transactionType", "title": "Transaction Type", "options": [{"label": "In invoice", "value": 1}, {"label": "Out invoice", "value": 2}, {"label": "Adjustment +ve", "value": 3}, {"label": "Adjustment –ve", "value": 4}, {"label": "Payment Made", "value": 5}, {"label": "Payment Received", "value": 6}]}, {"name": "transactionCurrency", "title": "<PERSON><PERSON><PERSON><PERSON>", "options": [{"label": "EURO", "value": 1}, {"label": "USD", "value": 2}]}]}