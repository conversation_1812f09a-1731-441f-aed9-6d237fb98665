import React, { useContext, useState, useRef, useEffect } from "react";
import { AuthContext } from "../context/AuthContext";
import Avatar from "react-avatar";
import theme from "../tailwind-theme";
import dayjs from "dayjs";


function Header() {
  const { logout, user } = useContext(AuthContext);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  const time = window.localStorage.getItem("loginTime");
  const formattedTime = dayjs(time).format("DD-MM-YYYY HH:mm:ss");

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <header className="z-50 py-1 mx-2 bg-bgPrimary shadow-bottom dark:bg-gray-800">
      <div className="flex items-center justify-between px-1 mx-auto text-purple-600 dark:text-purple-300">
        {/* Logo */}
        <div className="flex flex-1 lg:mr-32">
          <div className="bg-logo h-[62px] w-[100px] bg-contain bg-no-repeat" />
        </div>

        {/* Profile Section */}
        <ul className="flex items-center flex-shrink-0 space-x-6 pr-6">
          <li className="relative" ref={dropdownRef}>
            <div className="flex items-center mt-2">
              <div className="mx-4 flex flex-col">
                <p className="text-sm font-bold text-black">
                  Welcome {user?.name}
                </p>
                <p className="text-[10px] font-normal text-titleColor">
                  Login Time: {formattedTime}
                </p>
              </div>

              {/* Profile Button */}
              <button
                className="rounded-full focus:shadow-outline-purple focus:outline-none"
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              >
                {user?.profileImage ? (
                  <img
                    className="w-10 h-10 rounded-full"
                    src={user.profileImage}
                    alt="User Profile"
                  />
                ) : (
                  <Avatar
                    size="35"
                    round
                    color={theme.backgroundColor.bgSecondary}
                    name={user?.name || ""}
                  />
                )}
              </button>
            </div>

            {/* Logout Dropdown */}
            {isDropdownOpen && (
              <div className="absolute right-0 z-50 mt-2 w-44 origin-top-right bg-white divide-y divide-gray-100 rounded-md shadow-md ring-1 ring-black ring-opacity-5">
                <button
                  onClick={logout}
                  className="block w-full px-4 py-2 text-sm text-gray-700 text-left hover:bg-gray-100"
                >
                  Log out
                </button>
              </div>
            )}
          </li>
        </ul>
      </div>
    </header>
  );
}

export default Header;
