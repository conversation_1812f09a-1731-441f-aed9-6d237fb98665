{"forms": [{"elements": [{"name": "", "size": 12, "type": "none", "title": ""}, {"name": "", "size": 12, "type": "none", "title": ""}, {"name": "", "size": 3, "type": "none", "title": ""}, {"info": "Name of the template", "name": "templateName", "size": 6, "type": "text", "title": "HTTP name", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter Template Name"}, {"type": "alphaNumeric_space", "message": "Only alphanumeric characters,space and underscore allowed for Template Name"}, {"type": "min", "value": 3, "message": "Min length is 3"}, {"type": "max", "value": 50, "message": "Max length is 50"}], "defaultValue": "", "validationType": "string"}, {"name": "", "size": 3, "type": "none", "title": ""}, {"name": "", "size": 3, "type": "none", "title": ""}, {"info": "HTTP Template created for customer or supplier", "name": "relationType", "size": 6, "type": "select", "title": "Relation type", "isInfo": true, "options": [{"label": "Supplier", "value": "S"}], "isMandatory": true, "defaultValue": "S"}, {"name": "templateParams", "size": 12, "type": "none", "title": ""}], "formName": "Basic"}, {"elements": [{"info": "Push Parameters(USERNAME to MPCRID) decide how the message would be \ntranslated while sending to the supplier over HTTP, which applies to all methods: GET, POST JSON (REST interface) & POST XML.\n You can enable or disable the fields", "name": "PUSH", "size": 12, "type": "templateTable", "isInfo": true, "fieldsData": [{"name": "COMVIVA_PARAM", "type": "text"}, {"name": "THIRD_PARTY_PARAM", "type": "text"}, {"name": "DEFAULT_VAL", "type": "text"}, {"name": "ACTIVE", "type": "switch"}, {"name": "isDelete", "type": "delete"}], "headersData": ["Comviva parameter name", "Third-party parameter name", "Default value", "Status", ""], "validations": [{"type": "custom", "message": "Required", "maxlength": 50}], "initialValues": {"PUSH": [{"REQ": "Y", "ACTIVE": 1, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "Username", "THIRD_PARTY_PARAM": ""}, {"REQ": "Y", "ACTIVE": 1, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "Password", "THIRD_PARTY_PARAM": ""}, {"REQ": "Y", "ACTIVE": 1, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "Source_address", "THIRD_PARTY_PARAM": ""}, {"REQ": "Y", "ACTIVE": 1, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "Destination_address", "THIRD_PARTY_PARAM": ""}, {"REQ": "Y", "ACTIVE": 1, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "SMS", "THIRD_PARTY_PARAM": ""}, {"REQ": "N", "ACTIVE": 1, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "Validity", "THIRD_PARTY_PARAM": ""}, {"REQ": "Y", "ACTIVE": 1, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "SRR", "THIRD_PARTY_PARAM": ""}, {"REQ": "N", "ACTIVE": 1, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "DR_URL", "THIRD_PARTY_PARAM": ""}, {"REQ": "Y", "ACTIVE": 1, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "Sequence number", "THIRD_PARTY_PARAM": ""}, {"REQ": "N", "ACTIVE": 1, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "TP", "THIRD_PARTY_PARAM": ""}, {"REQ": "N", "ACTIVE": 1, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "CP", "THIRD_PARTY_PARAM": ""}, {"REQ": "N", "ACTIVE": 1, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "MP_CRID", "THIRD_PARTY_PARAM": ""}]}, "isHTTPTemplate": true}], "formName": "Push parameter"}, {"elements": [{"info": "DLR Parameters decide what should be the format of the delivery report recieved by SMS Hub. You can enable or disable the fields.", "name": "DLR", "size": 12, "type": "templateTable", "isInfo": true, "fieldsData": [{"name": "COMVIVA_PARAM", "type": "text"}, {"name": "THIRD_PARTY_PARAM", "type": "text"}, {"name": "DEFAULT_VAL", "type": "text"}, {"name": "ACTIVE", "type": "switch"}, {"name": "isDelete", "type": "delete"}], "headersData": ["Comviva parameter name", "Third-party parameter name", "Default value", "Status", ""], "validations": [{"type": "custom", "message": "Required", "maxlength": 50}], "initialValues": {"DLR": [{"REQ": "Y", "info": "Originator Address of the Message, it may be MSISDN or short code", "ACTIVE": 1, "isInfo": true, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "DLR_source_address", "THIRD_PARTY_PARAM": ""}, {"REQ": "Y", "info": "Destination Mobile No", "ACTIVE": 1, "isInfo": true, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "DLR_destination_address", "THIRD_PARTY_PARAM": ""}, {"REQ": "N", "info": "Some optional text information that <PERSON><PERSON><PERSON> wants to send along with additional parameters. Max 160 Characters", "ACTIVE": 1, "isInfo": true, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "DLR_text", "THIRD_PARTY_PARAM": ""}, {"REQ": "Y", "info": "Same ID, as it was sent in the message sent towards the supplier, to correlate the delivery report with the message sent", "ACTIVE": 1, "isInfo": true, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "DLR_ID", "THIRD_PARTY_PARAM": ""}, {"REQ": "Y", "info": "Supplier can send their own message ID in this parameter. Currently only used for logging purposes.", "ACTIVE": 1, "isInfo": true, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "DLR_sequence_number", "THIRD_PARTY_PARAM": ""}, {"REQ": "Y", "info": "The status of the SMS delivery", "ACTIVE": 1, "isInfo": true, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "DLR_status", "THIRD_PARTY_PARAM": ""}, {"REQ": "Y", "info": "SMPP done date", "ACTIVE": 1, "isInfo": true, "isDelete": false, "DEFAULT_VAL": "", "COMVIVA_PARAM": "Done_date_timing", "THIRD_PARTY_PARAM": ""}]}, "isHTTPTemplate": true}], "formName": "DLR parameter"}], "header": "List of HTTP templates", "columns": [{"header": "Template id", "accessorKey": "id"}, {"header": "Template name", "accessorKey": "attributes.templateName"}, {"header": "Relation type", "accessorKey": "attributes.relationType"}, {"buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "formType": "stepper", "buttonName": "+ Add Http template ", "moduleData": "http-template", "moduleName": "HTTP templates", "globalSearch": [{"label": "Template id", "value": "id"}, {"label": "Template name", "value": "templateName"}]}