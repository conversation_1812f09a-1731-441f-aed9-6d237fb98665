import React, { useContext, useEffect, useState } from "react";
import { Formik, Form } from "formik";
import Grid from "@mui/material/Grid";
import InputLabel from "../../FormsUI/InputLabel";
import TextFieldWrapper from "../../FormsUI/TextField";
import Select from "../../FormsUI/Select";
import RadioButtonGroup from "../../RadioButton/RadioButtonGroup";
import CustomSwitch from "../../ToggleSwitch/CustomSwitchHttp";
import { CustomerSupplierContext } from "../../../context/CustomerSupplierContext";
import ConfirmNNextButton from "../../Buttons/Button";
import Button from "../../Buttons/OutlinedButton";
import DatePickerFieldWrapper from "../../FormsUI/DatePicker";
import SlabPricingTable from "../../Table/SlabPricingTable";
import SlabPricingDifferentialTable from "../../Table/SlabPricingDifferentialTable";
import customerSupplier from "../../../collections/customer-supplier-managements.json";
import {
  BillingInfoPayload,
  billingInitialValue,
  getValidationSchema,
} from "../../../payloadDetails/BillingInfoPayload";

import {
  billingModelType,
  fixedFeeAndPerSmsType,
  operatorType,
  slabOperator,
  slabPricingType,
} from "../../../common/constants";

const BillingModelFields = ({
  type,
  values,
  setFieldValue,
  isCustomer,
  isView,
}) => {
  const label = isCustomer ? "Customer" : "Supplier";

  const isMonthlyRecurring =
    values[`cbmBillingPlanType${type}`] ===
    billingModelType["Monthly recurring"];
  const isFixedFeeAndPerSms =
    values[`cbmBillingPlanType${type}`] ===
    billingModelType["Fixed fee and per SMS"];
  const isFixedFeeUsageOrPerSms =
    values[`cbmBillingPlanType${type}`] ===
    billingModelType["Fixed fee usage or per SMS"];
  const isSlabWisePricing =
    values[`cbmBillingPlanType${type}`] ===
    billingModelType["Slab wise pricing"];
  const isFixedFeeAndSlab =
    values[`cbmBillingPlanType${type}`] ===
    billingModelType["Fixed fee and slab"];

  const isSinglePrice =
    values[`slabPricingType${type}`] === slabPricingType["Single price"];
  const isDifferentialPrice =
    values[`slabPricingType${type}`] === slabPricingType["Differential price"];

  const isAllOperators =
    Number(values[`slabOperator${label}`]) === slabOperator["All operators"];
  const isAddOperators =
    Number(values[`slabOperator${label}`]) === slabOperator["Add operators"];

  const dropdownValues = customerSupplier?.elements.filter(
    (element) => element?.billingInfo === true
  );
  const getDropdownOptions = (key) => {
    const fieldConfig = dropdownValues.find((item) => item.name === key);
    if (!fieldConfig || !fieldConfig.options) {
      return [];
    }
    return fieldConfig.options;
  };
  return (
    <>
      <Grid item xs={12} md={6}>
        <InputLabel
          label={`Billing model type (${label})`}
          isMandatory={true}
          info={"Type of billing model to be applied for the customer/supplier"}
          isInfo={true}
        />

        <Select
          name={`cbmBillingPlanType${type}`}
          options={getDropdownOptions("cbmBillingPlanType")}
          isDisabled={isView}
          onChange={(selectedOption) => {
            if (
              selectedOption.value === billingModelType["Slab wise pricing"] ||
              selectedOption.value === billingModelType["Fixed fee and slab"]
            ) {
              setFieldValue(
                `slabPricingType${type}`,
                slabPricingType["Single price"]
              );
            }
          }}
        />
      </Grid>
      <Grid item xs={12} md={6} />
      <Grid item xs={12} md={6}>
        <InputLabel
          label={`Billing model start date (${label})`}
          isMandatory={true}
          info={"Start date of the billing for the entity"}
          isInfo={true}
        />
        <DatePickerFieldWrapper
          name={`cbmFromDate${type}`}
          isDisabled={isView}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <InputLabel
          label={`Billing model end date (${label})`}
          isMandatory={true}
          info={"End date of the billing for the entity"}
          isInfo={true}
        />
        <DatePickerFieldWrapper name={`cbmToDate${type}`} isDisabled={isView} />
      </Grid>
      {(isMonthlyRecurring ||
        isFixedFeeAndPerSms ||
        isFixedFeeUsageOrPerSms ||
        isFixedFeeAndSlab) && (
        <Grid item xs={12} md={6}>
          <InputLabel label={`Fixed fee (${label})`} isMandatory={true} />
          <TextFieldWrapper
            name={`fixedFee${type}`}
            placeholder={"Enter fixed fee"}
            isDisabled={isView}
          />
        </Grid>
      )}
      {isMonthlyRecurring || isFixedFeeUsageOrPerSms ? (
        <Grid item xs={12} md={6} />
      ) : null}
      {isFixedFeeAndPerSms && (
        <Grid item xs={12} md={6}>
          <InputLabel
            label={`Fixed fee and per SMS type (${label})`}
            isMandatory={true}
          />
          <RadioButtonGroup
            name={`fixedFeeAndPerSmsType${type}`}
            value={values[`fixedFeeAndPerSmsType${type}`]}
            onChange={(e) =>
              setFieldValue(
                `fixedFeeAndPerSmsType${type}`,
                Number(e.target.value)
              )
            }
            options={getDropdownOptions("fixedFeeAndPerSmsType")}
            isDisabled={isView}
          />
        </Grid>
      )}
      {isFixedFeeAndPerSms &&
        values[`fixedFeeAndPerSmsType${type}`] ===
          fixedFeeAndPerSmsType.Volume && (
          <>
            <Grid item xs={12} md={6}>
              <InputLabel label={`Volume (${label})`} isMandatory={true} />
              <TextFieldWrapper
                name={`volume${type}`}
                placeholder={"Enter volume"}
                isDisabled={isView}
              />
            </Grid>
            <Grid item xs={12} md={6}></Grid>
          </>
        )}
      {(isSlabWisePricing || isFixedFeeAndSlab) && (
        <Grid item xs={12} md={6}>
          <InputLabel
            label={`Slab pricing type (${label})`}
            isMandatory={true}
            info={"Type of Slab wise pricing"}
            isInfo={true}
          />
          <RadioButtonGroup
            name={`slabPricingType${type}`}
            value={values[`slabPricingType${type}`]}
            onChange={(e) =>
              setFieldValue(`slabPricingType${type}`, Number(e.target.value))
            }
            options={getDropdownOptions("slabPricingType")}
            isDisabled={isView}
          />
        </Grid>
      )}
      {(isSlabWisePricing || isFixedFeeAndSlab) && isDifferentialPrice && (
        <Grid item xs={12} md={6}>
          <InputLabel
            label={`Select operators (${label})`}
            isMandatory={true}
          />
          <RadioButtonGroup
            name={`slabOperator${label}`}
            value={values[`slabOperator${label}`]}
            onChange={(e) =>
              setFieldValue(`slabOperator${label}`, Number(e.target.value))
            }
            options={getDropdownOptions("slabOperator")}
            isDisabled={isView}
          />
        </Grid>
      )}
      {((isSlabWisePricing || isFixedFeeAndSlab) && isSinglePrice) ||
      ((isSlabWisePricing || isFixedFeeAndSlab) &&
        isDifferentialPrice &&
        isAllOperators) ? (
        <Grid item xs={12} md={12}>
          <InputLabel label={`Slab pricing (${label})`} isMandatory={true} />
          <SlabPricingTable
            name={`slabPricing${type}`}
            values={values}
            headers={["S.No", "Slab", "Cost"]}
            fields={[{ name: "sno" }, { name: "slab" }, { name: "cost" }]}
            setFieldValue={setFieldValue}
            isView={isView}
          />
        </Grid>
      ) : null}
      {(isSlabWisePricing || isFixedFeeAndSlab) &&
      isDifferentialPrice &&
      isAddOperators ? (
        <Grid item xs={12} md={12}>
          <InputLabel label={`Slab pricing (${label})`} isMandatory={true} />
          <SlabPricingDifferentialTable
            name={`slabPricingAddOperator${type}`}
            values={values}
            headers={["S.No", "Slab", "Cost"]}
            fields={[{ name: "sno" }, { name: "slab" }, { name: "cost" }]}
            setFieldValue={setFieldValue}
            isView={isView}
          />
        </Grid>
      ) : null}
    </>
  );
};

function BillingInformationForm({
  handleNext,
  handleBack,
  editDetails,
  isView,
}) {
  const {
    billingInfo,
    generalInfoData,
    setBillingInfo,
    validationDetail,
    setValidationDetail,
  } = useContext(CustomerSupplierContext);
  //console.log("editDetails", editDetails);
  const [formValues, setFormValues] = useState(billingInitialValue || {});

  useEffect(() => {
    if (billingInfo) {
      setFormValues({
        ...formValues,
        ...billingInfo,
      });
    }
  }, [billingInfo]);

  useEffect(() => {
    if (generalInfoData) {
      if (generalInfoData.operatorType !== "C,S") {
        setBillingInfo((prevData) => {
          return {
            ...prevData,
            cbmBindType: "",
          };
        });
      }
    }
  }, [generalInfoData]);

  useEffect(() => {
    if (editDetails) {
      setFormValues({
        ...formValues,
        ...editDetails,
        cbmBindType:
          editDetails.cbmBindType === "S,C" ? "C,S" : editDetails.cbmBindType,
      });
    }
  }, [editDetails]);

  const dropdownValues = customerSupplier?.elements;
  const getDropdownOptions = (key) => {
    const fieldConfig = dropdownValues.find((item) => item.name === key);
    if (!fieldConfig || !fieldConfig.options) {
      return [];
    }
    return fieldConfig.options;
  };

  return (
    <Formik
      initialValues={formValues}
      validationSchema={getValidationSchema(generalInfoData, validationDetail)}
      validateOnMount={true}
      enableReinitialize={true}
      onSubmit={(values) => {
        BillingInfoPayload(values, generalInfoData, setBillingInfo, handleNext);
      }}
    >
      {({ values, setFieldValue, errors }) => (
        <Form>
          {setValidationDetail(values)}
          <div className="mx-4">
            <Grid container spacing={4} sx={{ p: 2 }}>
              {/* Billing Contact Name */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Billing contact name"}
                  isMandatory={true}
                  info={
                    "Billing contact name for the customer/supplier to whom the bill is sent"
                  }
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="billingContact"
                  placeholder={"Enter billing contact name"}
                  isDisabled={isView}
                />
              </Grid>
              {/* Delivery Flag */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Delivery Flag"}
                  info={"Flag to enable or disable the delivery notification"}
                  isInfo={true}
                />
                <CustomSwitch
                  name="deliveryFlag"
                  checked={values.deliveryFlag}
                  generalInfo={true}
                  isDisabled={isView}
                />
              </Grid>
              {/* Billing Address */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Billing address"}
                  info={
                    "Billing address of the customer/supplier at which the bill is sent"
                  }
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="address"
                  placeholder={"Enter billing address"}
                  isDisabled={isView}
                />
              </Grid>
              {/* Date of Activation */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Date of activation"}
                  isMandatory={true}
                  info={"Date of activation of the account"}
                  isInfo={true}
                />
                <DatePickerFieldWrapper
                  name="activationDate"
                  isDisabled={true}
                />
              </Grid>
              {/* Contact No */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Contact No."}
                  info={"Contact number for the customer/supplier"}
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="contact"
                  placeholder={"Enter contact number"}
                  isDisabled={isView}
                />
              </Grid>
              {/* Billing Type */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Billing type"}
                  isMandatory={true}
                  info={
                    "Commercial is applicable for Live Customer.Non Commercial is applicable for Not Live Customer."
                  }
                  isInfo={true}
                />
                <RadioButtonGroup
                  name="billingType"
                  value={values.billingType}
                  onChange={(e) => setFieldValue("billingType", e.target.value)}
                  options={getDropdownOptions("billingType")}
                  isDisabled={isView}
                />
              </Grid>
              {/* Email Id */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Email ID"}
                  info={"Email ID of the customer/supplier"}
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="emailId"
                  placeholder={"Enter email ID"}
                  isDisabled={isView}
                />
              </Grid>
              {/* Billing Logic */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Billing logic"}
                  isMandatory={true}
                  info={
                    "Bills are generated for all submitted/delivered/successfully terminated messages"
                  }
                  isInfo={true}
                />
                <RadioButtonGroup
                  name="billingLogic"
                  value={values.billingLogic}
                  onChange={(e) =>
                    setFieldValue("billingLogic", e.target.value)
                  }
                  options={getDropdownOptions("billingLogic")}
                  isDisabled={isView}
                />
              </Grid>

              {/* Billing Email To */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Billing email ID (To)"}
                  info={"Billing email ID (To) of the customer"}
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="billingEmailTo"
                  placeholder={"Enter billing email ID (To)"}
                  isDisabled={isView}
                />
              </Grid>
              {/* Billing Address Cc */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Billing address(Cc)"}
                  info={"Billing email ID (cc) of the customer"}
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="billingEmailCc"
                  placeholder={"Enter billing address(Cc)"}
                  isDisabled={isView}
                />
              </Grid>
              {/* B Number Billing Type */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"B number billing type"}
                  info={
                    "Billing will be performed for the  destination operator/destination visited operator"
                  }
                  isInfo={true}
                />
                <Select
                  name="bPartyBillLogic"
                  options={getDropdownOptions("bPartyBillLogic")}
                  isDisabled={isView}
                />
              </Grid>
              {/* A Number Report Needed */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"A number report needed"}
                  info={
                    "Flag to enable source number or OM number report required"
                  }
                  isInfo={true}
                />
                <CustomSwitch
                  name="enableAnumberRpt"
                  checked={values.enableAnumberRpt}
                  isDisabled={isView}
                />
              </Grid>
              {/* SBI Billing Status */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"SRI billing status"}
                  info={
                    "Flag to enable or disable SRI billing. If enabled, billing takes places based on routing information"
                  }
                  isInfo={true}
                />
                <CustomSwitch
                  name="sriFlag"
                  checked={values.sriFlag}
                  generalInfo={true}
                  isDisabled={isView}
                />
              </Grid>
              {generalInfoData.operatorType === operatorType.Both ? (
                <Grid item xs={12} md={6}>
                  <InputLabel
                    label={"Billing model bind type"}
                    isMandatory={true}
                  />

                  <Select
                    name="cbmBindType"
                    options={getDropdownOptions("cbmBindType")}
                    isDisabled={isView}
                  />
                </Grid>
              ) : (
                <Grid item xs={12} md={6} />
              )}

              {/* Message Fee Currency Incoming */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Message fee currency (incoming)"}
                  isMandatory={true}
                  info={"Currency for the fee charged for the incoming message"}
                  isInfo={true}
                />
                <Select
                  name="currencyIn"
                  options={getDropdownOptions("currencyIn")}
                  isDisabled={isView}
                />
              </Grid>
              {/* Message Fee Currency Outgoing */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Message fee currency (outgoing)"}
                  isMandatory={true}
                  info={"Currency for the fee charged for the outgoing message"}
                  isInfo={true}
                />
                <Select
                  name="currencyOut"
                  options={getDropdownOptions("currencyOut")}
                  isDisabled={isView}
                />
              </Grid>

              {/* Conditional rendering based on operatorType and cbmBindType */}
              {(generalInfoData.operatorType === operatorType.Customer ||
                (generalInfoData.operatorType === operatorType.Both &&
                  (values.cbmBindType === operatorType.Customer ||
                    values.cbmBindType === operatorType.Both))) && (
                <BillingModelFields
                  type="Customer"
                  values={values}
                  setFieldValue={setFieldValue}
                  isCustomer={true}
                  isView={isView}
                />
              )}
              {(generalInfoData.operatorType === operatorType.Supplier ||
                (generalInfoData.operatorType === operatorType.Both &&
                  (values.cbmBindType === operatorType.Supplier ||
                    values.cbmBindType === operatorType.Both))) && (
                <BillingModelFields
                  type="Supplier"
                  values={values}
                  setFieldValue={setFieldValue}
                  isCustomer={false}
                  isView={isView}
                />
              )}
            </Grid>
          </div>
          <div className="flex justify-center gap-5 pt-5 pb-10">
            <Button
              label={"Back"}
              buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
              onClick={() => {
                handleBack();
                setValidationDetail([]);
              }}
            />
            <ConfirmNNextButton
              label={"Next"}
              buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
              type="submit"
            />
          </div>
        </Form>
      )}
    </Formik>
  );
}

export default BillingInformationForm;
