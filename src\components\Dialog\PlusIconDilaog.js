import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import { CheckMarkIcon, CloseIcon } from "../../icons";
import Button from "../Buttons/Button";
import FormTemplate from "../Templates/FormTemplate";
import { useNavigate } from "react-router-dom";

function PlusIconDialog({
  show,
  onHide,
  message,
  buttonName,
  elements,
  action,
  id,
  moduleName,
  route01,
  route02,
  module,
  moduleNameValue,
  moduleData,
  formType,
}) {
  const navigate = useNavigate();

  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 411,
          maxHeight: 435,
          fontFamily: "OpenSansHebrew",
          borderRadius: "10px",
        },
      }}
      maxWidth="xs"
      open={show}
      onClose={onHide}
      className="p-6 font-sans"
    >
      <DialogTitle className="mt-2 flex justify-end">
        <CloseIcon
          className="w-2.5 h-2.5 cursor-pointer"
          onClick={() => onHide()}
        />
      </DialogTitle>

      <div className="text-base mt-4 mx-auto">
        <FormTemplate
          elements={elements}
          action={action}
          id={id}
          //formData={formData}
          moduleName={moduleName}
          route01={route01}
          route02={route02}
          handleCancel={() => {
            navigate(`/app/list/${module}`);
          }}
          moduleNameValue={moduleNameValue}
          moduleData={moduleData}
          formType={formType}
        ></FormTemplate>
      </div>
    </Dialog>
  );
}

export default PlusIconDialog;
