import React, { useEffect, useState, useContext, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useQuery, useMutation } from "react-query";
import {
  getPageSchema,
  getListTemplateData,
  deleteList,
  deleteAllApi,
  extractXmlDialog,
  getGlobalSearchList,
  updateRecord,
  getXmlUpload,
} from "../../lib/list-api";
import SearchComponent from "../SearchComponent";
import ConfirmNNextButton from "../Buttons/Button";
import CustomTable from "../Table";
import SuccessDialog from "../Dialog/SuccessDialog";
import useDebounce from "../../common/useDebounce";
import editing from "../../icons/editing.png";
import thrash from "../../icons/bin.png";
import viewing from "../../icons/viewing.png";
import { ShareIcon, DeleteIcon } from "../../icons";
import viewlist from "../../icons/view-list.png";
import viewAccount from "../../icons/view-account.png";
import viewRules from "../../icons/view-rules.png";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import ErrorDialog from "../Dialog/ErrorDialog";
import DeleteDialog from "../Dialog/DeleteDialog";
import { DownloadContext } from "../../context/DownloadContext";
import { CssTooltip } from "../FormsUI/StyledComponent";
import ExportPopup from "../Dialog/exportpopup";
import XmlUploadDialog from "../Dialog/XmlUploadDialog";
import { multiStepFormContext } from "../../context/MultiStepFormContext";
import { CustomerSupplierContext } from "../../context/CustomerSupplierContext";
import exportIcon from "../../icons/export.png";
import { lcrType, moduleConfiguration } from "../../common/constants";
import { TransformData } from "../TransformData/TransformData";
import { DEFAULT_PAGE_SIZE } from "../../common/config";
import CreditTransactionFilter from "../Filters/CreditTransactionFilter";
import {
  ActionIcon,
  getDefaultColumnConfig,
  getEsmeAccountsColumnConfig,
  handleConfirmationButton,
  handleEditClick,
  handleViewAccountClick,
  handleViewClick,
  handleViewDataClick,
  handleViewListClick,
  renderPagination,
  getDetailBtnConfig,
} from "../../common/listPageUtils";
import { ExpandListCellValue } from "../../common/ExpandList";
import Button from "../Buttons/OutlinedButton";
import ListPageDropdownFilter from "../Filters/ListPageDropdownFilter";
import DeleteAllDialog from "../Dialog/DeleteAllDialog";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { checkAndDeleteIndexedDB } from "../HelperFunction/localStorage";
import { DataContext } from "../../context/DataContext";
import timezone from "dayjs/plugin/timezone";
import HoverList from "../../common/HoverList";
dayjs.extend(utc);
dayjs.extend(timezone);

const ListTemplate = ({ collectionName }) => {
  const navigate = useNavigate();

  const [currentPage, setCurrentPage] = useState(1);
  const [limitPerPage, setLimitPerPage] = useState(DEFAULT_PAGE_SIZE);
  const [searchText, setSearchText] = useState("");

  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErroDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [deleteAllDialog, setDeleteAllDialog] = useState(false);
  const [message, setMessage] = useState("");

  const [jsonData, setJsonData] = useState({});
  const [id, setId] = useState(null);
  const [listData, setListData] = useState([]);
  const [columnsData, setColumnsData] = useState([]);
  const [respData, setRespData] = useState([]);

  const [rowSelection, setRowSelection] = useState({});
  const [exportAllDownload, setExportAllDownload] = useState(false);
  const [exportPath, setExportPath] = useState("");
  const debouncedValue = useDebounce(searchText, 500);
  const [showExportConfirmation, setShowExportConfirmation] = useState(false);
  const [exportFlag, setExportFlag] = useState(false);
  const [extensionType, setExtensionType] = useState("");
  const [selectedIds, setSelectedIds] = useState([]);
  const [pagination, setPagination] = useState("");
  const [filteredRow, setFilteredRow] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [filteredData, setFilteredData] = useState([]);

  const [showXmlDialog, setShowXmlDialog] = useState(false);
  const [selectFile, setSelectFile] = useState(null);
  const [showLastColumn, setShowLastColumn] = useState(false);

  const [dropdownFilter, setDropdownFilter] = useState("");
  const [creditCustomer, setCreditCustomer] = useState("all");

  const [globalSearch, setGlobalSearch] = useState([]);
  const [globalSearchSelect, setGlobalSearchSelect] = useState("");
  const [exportLcr, setExportLcr] = useState(false);
  const [lcrTypee, setLcrType] = useState("");
  const [uiSchemaDetails, setUiSchemaDetails] = useState({});

  const [isDeleteAll, setIsDeleteAll] = useState(false);
  const [rowData, setRowData] = useState([]);
  const [refetchData, setRefetchData] = useState(false);
  const rowDataRef = useRef(rowData);
  const [_, setRender] = useState(false);
  const [xmlFileId, setXmlFileId] = useState(null);
  const [exportXml, setExportXml] = useState(false);
  const { setIsDownloading } = useContext(DownloadContext);
  const { setCurrentStep } = useContext(multiStepFormContext);
  const { setGeneralInfo, setBillingInfo, setPaymentInfo, setRequiredInfo } =
    useContext(CustomerSupplierContext);

  const { mutate: deleteAPI, isLoading: deleteLoading } =
    useMutation(deleteList);
  const { mutate: deleteALLAPI, isLoading: deleteAllLoading } =
    useMutation(deleteAllApi);
  const { mutate: createXmlDialog, isLoading: createLoading } =
    useMutation(extractXmlDialog);
  const { mutate: updateRecordAPI } = useMutation(updateRecord);
  const { setRowDetails, connType, setConnType } = useContext(DataContext);

  const location = useLocation();
  const { accType } = location.state || {};
  dayjs.extend(utc);

  const handleLimitChange = (e) => {
    setLimitPerPage(e.target.value);
    setCurrentPage(1);
  };

  const handleDelete = (row) => {
    setId(row.original.id);
    setDeleteDialog(true);
    if (row?.original?.attributes?.connType) {
      setConnType(
        row.original.attributes.connType === "SMPP"
          ? "S"
          : row.original.attributes.connType === "HTTP"
          ? "H"
          : "M"
      );
    }
  };
  const prevCollectionName = useRef(collectionName);
  useEffect(() => {
    if (prevCollectionName.current !== collectionName) {
      setCurrentPage(1);
      window.location.reload();
    }
    prevCollectionName.current = collectionName;
  }, [collectionName]);

  useEffect(() => {
    if (collectionName) {
      checkAndDeleteIndexedDB("navigationDetails");
    }
  }, [collectionName]);

  // Export All API
  const exportReport = (type) => {
    if (type !== "") {
      setExportAllDownload(true);
      setIsDownloading(true);
      let extension = type === "EXCEL" ? "xls" : "csv";
      setExtensionType(extension);
    }
  };
  useEffect(() => {
    const selectedIds = Object.keys(rowSelection)
      .filter((key) => rowSelection[key] === true)
      .map((key) => Number(key));
    setSelectedIds(selectedIds);
  }, [rowSelection, extensionType]);

  useEffect(() => {
    setRowData(filteredRow);
  }, [filteredRow]);

  useEffect(() => {
    rowDataRef.current = rowData;
  }, [rowData]);

  const handleExportCLick = () => {
    if (rowDataRef.current === 0) {
      setErroDialog(true);
      setMessage("No data to export");
      setRefetchData(true);
    } else {
      setShowExportConfirmation(true);
    }
  };
  const handleDeleteClick = () => {
    if (rowDataRef.current === 0) {
      setErroDialog(true);
      setMessage("No data to delete");
      setRefetchData(true);
    } else {
      setIsDeleteAll(true);
      setDeleteDialog(true);
    }
  };
  const hasRowSelection = Object.keys(rowSelection).length > 0;

  const idsValue = (() => {
    if (id && !isDeleteAll) {
      return `[${id}]`;
    }
    if (!isSearching) {
      return hasRowSelection ? `[${selectedIds}]` : "*";
    }
    if (isSearching) {
      return hasRowSelection ? `[${selectedIds}]` : `[${filteredData}]`;
    }
    return "";
  })();

  const [filterKey, filterValue] = dropdownFilter?.split("=");
  const lcrTypeValue = lcrType.find(
    (item) => Number(filterValue) === item.value
  )?.label;
  const moduleFilter =
    collectionName === moduleConfiguration.esmeSessions
      ? moduleConfiguration.moduleFilterESME
      : undefined;

  useQuery(
    [
      collectionName,
      exportPath === moduleConfiguration.scATLCRImportName
        ? moduleConfiguration.scATLCRImportName
        : exportPath === moduleConfiguration.lcrImportConfiguration
        ? moduleConfiguration.lcrImportConfiguration
        : exportPath,
      globalSearchSelect,
      debouncedValue,
      exportFlag,
      idsValue,
      extensionType,
      dropdownFilter,
      accType,
      exportLcr,
      uiSchemaDetails,
      limitPerPage,
      currentPage,
      creditCustomer,
      moduleFilter,
    ],
    getGlobalSearchList,
    {
      enabled: (!!globalSearchSelect && !!debouncedValue) || exportFlag,
      onSuccess: (resp) => {
        if (exportFlag) {
          setIsDownloading(false);
          const url = URL.createObjectURL(resp.data);
          const link = document.createElement("a");
          link.href = url;
          link.download = `${
            lcrTypee.lcrType && lcrTypee.lcrName
              ? `${lcrTypee.lcrType}-${lcrTypee.lcrName}`
              : exportPath === moduleConfiguration.lcrImportConfiguration
              ? lcrTypeValue
              : exportPath
          }-${dayjs(Date.now()).format(
            "DD-MM-YYYY HH:mm:ss"
          )}.${extensionType}`;
          link.click();
          setExportAllDownload(false);
          if (!hasRowSelection) {
            setSelectedIds([]);
          }
          setExportFlag(false);
          setExportLcr(false);
          setId(null);
          setLcrType("");
        } else {
          setPagination(resp?.data || []);
          setRespData(resp?.data?.data);
          setListData(
            TransformData(
              resp?.data?.data || [],
              jsonData?.schemadetails?.elements || [],
              jsonData?.schemadetails || []
            )
          );
        }
      },
      onError: () => {
        setIsDownloading(false);
        setErroDialog(true);
        setMessage(exportFlag ? "Download failed" : "");
        setExportFlag(false);
        setId(null);
        setLcrType("");
      },
    }
  );

  useQuery([xmlFileId], getXmlUpload, {
    enabled:
      exportXml === true && xmlFileId !== null && xmlFileId !== undefined,
    onSuccess: (resp) => {
      setIsDownloading(false);
      const blob = new Blob([resp.data], {
        type: resp.headers["content-type"],
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `${exportPath}-${dayjs().format(
        "DD-MM-YYYY_HH-mm-ss"
      )}.${extensionType}`;
      link.click();
      URL.revokeObjectURL(url);
      setExportXml(false);
      setXmlFileId(null);
      setExportAllDownload(false);
      if (!hasRowSelection) {
        setSelectedIds([]);
      }
    },
    onError: (error) => {
      setErroDialog(true);
      setMessage("Download failed");
      setXmlFileId(null);
      setExportXml(false);
      setIsDownloading(false);
    },
  });

  const { isLoading, refetch } = useQuery(
    [
      "collectionName" + collectionName,
      collectionName === moduleConfiguration.hubRuleConfiguration ||
      collectionName === moduleConfiguration.ruleConfiguration
        ? moduleConfiguration.rules
        : collectionName,
      limitPerPage,
      currentPage,
      collectionName,
      creditCustomer,
      uiSchemaDetails,
      dropdownFilter,
    ],
    getListTemplateData,
    {
      enabled: !globalSearchSelect || !debouncedValue,
      onSuccess: (resp) => {
        let updatedData = resp?.data?.data || [];

        if (
          updatedData.length > 0 &&
          updatedData[0].id !== undefined &&
          collectionName !== "redirectional-accounts"
        ) {
          updatedData = updatedData.sort((a, b) => b.id - a.id);
        }

        setRespData(resp?.data?.data);
        setListData(
          TransformData(
            resp?.data?.data,
            jsonData?.schemadetails?.elements || [],
            jsonData?.schemadetails || [],
            collectionName
          )
        );

        setPagination(resp?.data || []);
      },
    }
  );

  useQuery([collectionName, selectedIds], getPageSchema, {
    refetchOnWindowFocus: false,
    onSuccess: (resp) => {
      const schemaDetails = resp?.data?.data?.[0]?.attributes?.schemadetails;
      const uiSchemaDetails =
        resp?.data?.data?.[0]?.attributes?.uischemadetails?.tableFields;
      const columnsData = schemaDetails?.columns;
      const columnsSecondary = schemaDetails?.columnsSecondary;
      const modulePath = schemaDetails?.moduleData;
      const search = schemaDetails?.globalSearch;

      //setDropdownFilter(schemaDetails?.dropdownOptions?.defaultQuery || "");
      setUiSchemaDetails(uiSchemaDetails);
      setExportPath(modulePath);
      setGlobalSearch(search);
      const processColumn = (x) => {
        if (x.accessorKey === "actions") {
          return {
            accessorKey: x.accessorKey,
            header: (
              <div
                className={`flex justify-center items-center gap-6 ${
                  collectionName === moduleConfiguration.esmeSessions
                    ? ""
                    : "p-5"
                } ${x.align === "center" ? "ml-12" : "ml-0"}`}
              >
                <CssTooltip title={"Export All"} placement="top" arrow>
                  <ShareIcon
                    className="text-black cursor-pointer"
                    onClick={() => {
                      handleExportCLick();
                    }}
                  />
                </CssTooltip>
                {schemaDetails?.isDeleteIcon !== false ? (
                  <CssTooltip title={"Delete All"} placement="top" arrow>
                    <DeleteIcon
                      className="w-4 h-4 cursor-pointer"
                      onClick={() => {
                        handleDeleteClick();
                      }}
                    ></DeleteIcon>
                  </CssTooltip>
                ) : null}
              </div>
            ),
            enableColumnFilter: false,
            enableSorting: false,
            enableColumnPinning: true,
            size:
              collectionName === moduleConfiguration.esmeSessions ? 60 : 150,
            Cell: ({ row }) => (
              <div className="flex justify-center items-center gap-4">
                {x.buttons?.map((y) => {
                  switch (y.title) {
                    case "Edit":
                      return (
                        <ActionIcon
                          src={editing}
                          alt={y.title}
                          title={"Edit"}
                          onClick={(e) => {
                            handleEditClick(
                              e,
                              row,
                              schemaDetails,
                              collectionName,
                              navigate,
                              setCurrentStep,
                              setGeneralInfo,
                              setBillingInfo,
                              setPaymentInfo,
                              setRequiredInfo,
                              setRowDetails
                            );
                          }}
                        />
                      );
                    case "View":
                      return (
                        <ActionIcon
                          src={viewing}
                          alt={y.title}
                          title={"View"}
                          className="w-5 h-4 cursor-pointer"
                          onClick={(e) => {
                            setCurrentStep(0);
                            handleViewClick(
                              e,
                              row,
                              schemaDetails,
                              collectionName,
                              navigate
                            );
                          }}
                        />
                      );
                    case "View List":
                      return (
                        <ActionIcon
                          src={viewlist}
                          alt={y.title}
                          title={"View List"}
                          className="w-5 h-4 cursor-pointer"
                          onClick={(e) => {
                            handleViewListClick(
                              e,
                              row,
                              collectionName,
                              navigate,
                              setRowDetails
                            );
                          }}
                        />
                      );
                    case "Export":
                      if (dropdownFilter === "filters[lcrType]=4") {
                        return null;
                      }
                      return (
                        <ActionIcon
                          src={exportIcon}
                          alt={y.title}
                          title={"Export"}
                          className="w-5 h-4 mt-1 cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowExportConfirmation(true);
                            setExportLcr(true);
                            setId(row.id);
                            setLcrType(row.original.attributes);
                          }}
                        />
                      );

                    case "Delete":
                      return (
                        <ActionIcon
                          src={thrash}
                          alt={y.title}
                          title={"Delete"}
                          className="w-5 h-4 cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            setIsDeleteAll(false);
                            handleDelete(row);
                          }}
                        />
                      );
                    case "View account":
                      return (
                        <ActionIcon
                          src={viewAccount}
                          alt={y.title}
                          className="w-5 h-4 cursor-pointer"
                          title={"View account"}
                          onClick={(e) => {
                            handleViewAccountClick(
                              e,
                              row,
                              collectionName,
                              navigate
                            );
                          }}
                        />
                      );
                    case "View rules":
                      return (
                        <>
                          <CssTooltip
                            title={"View Rules"}
                            placement="top"
                            arrow
                          >
                            <img
                              key="view rules"
                              className="w-4 h-4 cursor-pointer"
                              src={viewRules}
                              alt={y.title}
                            />
                          </CssTooltip>
                        </>
                      );
                    case "viewData":
                      return (
                        <>
                          <ActionIcon
                            src={viewing}
                            alt={y.title}
                            title={"View"}
                            className="h-4 w-6 cursor-pointer"
                            onClick={(e) => {
                              handleViewDataClick(
                                e,
                                row,
                                collectionName,
                                navigate,
                                setRowDetails
                              );
                            }}
                          />
                        </>
                      );
                    case "ExportXml":
                      return row?.original?.attributes?.uploadedFileId ? (
                        <ActionIcon
                          src={exportIcon}
                          alt={y.title}
                          title={"Download XML File"}
                          className="w-5 h-4 mt-1 cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            setId(row.id);
                            setXmlFileId(
                              row?.original?.attributes?.uploadedFileId
                            );
                            setExportXml(true);
                            setExtensionType("xml");
                          }}
                        />
                      ) : (
                        ""
                      );
                    default:
                      return null;
                  }
                })}
              </div>
            ),
          };
        } else if (x.accessorKey === "Sno") {
          return {
            accessorKey: x.accessorKey,
            header: x.header,
            Cell: ({ row }) => {
              const totalRows = pagination?.meta?.pagination?.total;
              const currentRowIndex = row.index + 1;
              return (
                totalRows -
                ((currentPage - 1) * limitPerPage + currentRowIndex) +
                1
              );
            },
            enableSorting: false,
          };
        } else if (x.isExpandList) {
          return {
            accessorKey: x.accessorKey,
            header: x.header,
            Cell: ({ row }) => <ExpandListCellValue row={row} />,
          };
        } else if (x.isBg) {
          setShowLastColumn(true);
          return {
            accessorKey: x.accessorKey,
            header: x.header,
            Cell: ({ cell }) => {
              const currentStatus = cell.row.original.attributes.status;
              const isStatus =
                currentStatus.toLowerCase() === "a" ||
                currentStatus.toLowerCase() === "node up" ||
                currentStatus.toLowerCase() === "smsc up";
              const showStatus =
                collectionName === "node-sessions"
                  ? isStatus
                    ? "NODE UP"
                    : "NODE DOWN"
                  : collectionName === "smsc-sessions"
                  ? isStatus
                    ? "SMSC UP"
                    : "SMSC DOWN"
                  : null;
              return (
                <div className="flex items-center gap-1">
                  <div
                    className={`flex rounded-[2px] p-1 font-bold ${
                      isStatus
                        ? "text-statusUp bg-bgStatusUp"
                        : "text-statusDown bg-bgStatusDown"
                    }`}
                  >
                    {showStatus}
                  </div>
                </div>
              );
            },
          };
        } else if (x.isToggle) {
          if (collectionName === "esme-accounts") {
            return getEsmeAccountsColumnConfig(x, refetch);
          } else {
            return getDefaultColumnConfig(x, collectionName, updateRecordAPI);
          }
        } else if (x.isDetail) {
          setShowLastColumn(true);
          return getDetailBtnConfig(
            x,
            collectionName,
            updateRecordAPI,
            navigate
          );
        } else if (x.isDateTime) {
          return {
            id: x.id,
            header: x.header,
            filterVariant: x.filterVariant,
            accessorFn: (originalRow) => {
              const date = x.id
                .split(".")
                .reduce((acc, part) => acc?.[part], originalRow.attributes);
              return date ? new Date(date) : null;
            },
            Cell: ({ cell }) => {
              const date = cell.getValue();
              if (date instanceof Date && !isNaN(date)) {
                const localDate = dayjs.utc(date).local();
                return localDate.format(x.dateTimeFormat);
              }
              return "";
            },
          };
        } else if (x.isDate) {
          return {
            id: x.id,
            header: x.header,
            filterVariant: x.filterVariant,
            accessorFn: (originalRow) => {
              const date = x.id
                .split(".")
                .reduce((acc, part) => acc?.[part], originalRow.attributes);
              return date ? new Date(date) : null;
            },
            Cell: ({ cell }) => {
              const date = cell.getValue();
              if (date instanceof Date && !isNaN(date)) {
                return dayjs.utc(date).format(x.dateFormat);
              }
              return "";
            },
          };
        } else if (x.isHovered) {
          return {
            accessorKey: x.accessorKey,
            header: x.header,
            Cell: ({ row }) => (
              <HoverList row={row} x={x} collectionName={collectionName} />
            ),
          };
        } else {
          if (x.renderFunction) {
            x.Cell = new Function("row", `return ${x.renderFunction}`);
          }
          return { ...x, enableSorting: true };
        }
      };

      let cData = [];

      if (Array.isArray(columnsData) && columnsData[0]?.columns) {
        cData = columnsData[0].columns.map(processColumn);
        setColumnsData(cData);
      } else if (Array.isArray(columnsData)) {
        cData = columnsData.map(processColumn);
        setColumnsData(cData);
      } else if (Array.isArray(columnsSecondary)) {
      }

      setJsonData(resp?.data?.data?.[0]?.attributes);
      const uniqueColumnsData = cData.reduce((acc, current) => {
        const isDuplicate = acc.some((item) => item.header === current.header);
        if (!isDuplicate) {
          acc.push(current);
        }
        return acc;
      }, []);
      setColumnsData(uniqueColumnsData);
    },
  });
  const handleDeleteAll = (modulePath, idData) => {
    if (modulePath) {
      deleteALLAPI(
        { modulePath, ids: idsValue, dropdownFilter },
        {
          onSuccess: ({ data }) => {
            if (
              data?.result?.failedList?.message !== "" &&
              data?.result?.failedList?.message !== undefined
            ) {
              setDeleteDialog(false);
              setDeleteAllDialog(true);
              setMessage(data?.result?.failedList?.message);
              setIsDeleteAll(false);
              setSelectedIds([]);
              setRowSelection(false);
            } else {
              setDeleteDialog(false);
              setSuccessDialog(true);
              setMessage(
                data?.result?.successList?.message ||
                  "Record(s) deleted successfully"
              );
              setIsDeleteAll(false);
              setSelectedIds([]);
              setRowSelection(false);
            }
            setCurrentPage(1);
            refetch();
          },
          onError: ({ response }) => {
            setDeleteDialog(false);
            setErroDialog(true);
            setMessage(response?.data?.error?.message);
            refetch();
          },
        }
      );
    }
  };
  useEffect(() => {
    if (listData && jsonData?.schemadetails) {
      const transformedData = TransformData(
        listData,
        jsonData?.schemadetails?.elements,
        collectionName,
        jsonData?.schemadetails
      );
      setListData(transformedData);
    }
  }, [jsonData]);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  return (
    <>
      <div className="flex flex-col">
        <div className="font-bold text-2xl">
          {jsonData?.schemadetails?.header}
        </div>

        <div className="flex items-center justify-between my-2">
          {" "}
          <div className="flex gap-5">
            {collectionName === "credit-transactions" ||
            collectionName === "customer-credit-profiles" ? (
              <CreditTransactionFilter
                setCreditCustomer={setCreditCustomer}
                setIsSearching={setIsSearching}
                // setSelectedIds={setSelectedIds}
              />
            ) : (
              <SearchComponent
                onChange={(e) => {
                  setSearchText(e.target.value.trim());
                  setCurrentPage(1);
                }}
                globalSearch={globalSearch}
                setGlobalSearchSelect={setGlobalSearchSelect}
                dropdownFilter={dropdownFilter}
              />
            )}

            {jsonData?.schemadetails?.dropdownOptions ? (
              <ListPageDropdownFilter
                setDropdownFilter={setDropdownFilter}
                dropdownDetails={
                  jsonData?.schemadetails?.dropdownOptions?.options || []
                }
                defaultValue={
                  jsonData?.schemadetails?.dropdownOptions?.defaultValue
                }
                defaultQuery={
                  jsonData?.schemadetails?.dropdownOptions?.defaultQuery
                }
                collectionName={collectionName}
                limitPerPage={limitPerPage}
                currentPage={currentPage}
              />
            ) : null}
          </div>
          {collectionName === moduleConfiguration.retryPolicy && (
            <div className="flex gap-5 my-2">
              {jsonData?.schemadetails?.secondaryButtonName ? (
                <Button
                  label={jsonData?.schemadetails?.secondaryButtonName || ""}
                  buttonClassName="h-[40px] text-sm"
                  isDisabled={respData?.length <= 0}
                  onClick={() => navigate("/app/list/error-table")}
                />
              ) : null}
            </div>
          )}
          {jsonData?.schemadetails?.confirmButton !== false ? (
            <ConfirmNNextButton
              label={jsonData?.schemadetails?.buttonName || ""}
              buttonClassName="h-[40px] text-sm"
              onClick={(e) => {
                if (
                  jsonData?.schemadetails?.moduleData ===
                  moduleConfiguration.customerSupplierImport
                ) {
                  setCurrentStep(0);
                  setGeneralInfo([]);
                  setBillingInfo([]);
                  setPaymentInfo([]);
                  setRequiredInfo([]);
                }
                handleConfirmationButton(
                  e,
                  jsonData,
                  collectionName,
                  navigate,
                  setShowXmlDialog,
                  setCurrentStep
                );
              }}
            />
          ) : null}
        </div>
        <div className="my-4">
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <CustomTable
              data={listData}
              rowSelection={rowSelection}
              onRowSelectionChange={setRowSelection}
              columns={columnsData}
              onRowClick={() => {}}
              showLastColumn={showLastColumn}
              globalSearch={globalSearch}
              isLoading={isLoading}
              setFilteredRow={setFilteredRow}
              setIsSearching={setIsSearching}
              setSelectedIds={setSelectedIds}
              setFilteredData={setFilteredData}
              collectionName={collectionName}
              creditCustomer={creditCustomer}
              isEsmeSessionColumns={
                collectionName === moduleConfiguration.esmeSessions
                  ? true
                  : false
              }
              isMinimumWidth={
                collectionName === moduleConfiguration.esmeSessions
                  ? true
                  : false
              }
            />
          </LocalizationProvider>
        </div>

        {renderPagination({
          pagination,
          limitPerPage,
          currentPage,
          handleLimitChange,
          isSearching,
          filteredRow,
          handlePageChange,
        })}
        <DeleteDialog
          show={deleteDialog}
          onHide={() => setDeleteDialog(false)}
          onConfirm={() => {
            if (isDeleteAll) {
              setId(null);
              handleDeleteAll(exportPath, selectedIds);
            } else {
              deleteAPI(
                { rowId: id, collectionName, connType: connType, filterValue },
                {
                  onSuccess: (resp) => {
                    setDeleteDialog(false);
                    setSuccessDialog(true);
                    setMessage(`Record(s) deleted successfully`);
                    setCurrentPage(1);
                    refetch();
                  },
                  onError: ({ response }) => {
                    setDeleteDialog(false);
                    setErroDialog(true);
                    setMessage(response?.data?.error?.message);
                    refetch();
                  },
                }
              );
            }
          }}
          title={"Are you sure you want to delete?"}
          isLoading={deleteLoading || deleteAllLoading}
        />
        <ExportPopup
          show={showExportConfirmation}
          onHide={() => setShowExportConfirmation(false)}
          onConfirm={(type) => {
            exportReport(type);
            setExportAllDownload(true);
            setShowExportConfirmation(false);
            setExportFlag(true);
          }}
          title={"Export Report"}
          identity={"Reports"}
        />
        <SuccessDialog
          show={successDialog}
          onHide={() => {
            setSuccessDialog(false);

            refetch();
          }}
          message={message}
        />

        <ErrorDialog
          show={errorDialog}
          onHide={() => {
            if (!refetchData) {
              refetch();
            }
            setErroDialog(false);
            setDeleteDialog(false);
          }}
          message={message}
        />
        <DeleteAllDialog
          show={deleteAllDialog}
          onHide={() => {
            if (!refetchData) {
              refetch();
            }
            setDeleteAllDialog(false);
            setDeleteDialog(false);
          }}
          message={message}
          moduleName={jsonData?.schemadetails?.moduleName}
        />
        <XmlUploadDialog
          show={showXmlDialog}
          onClose={() => {
            setShowXmlDialog(false);
          }}
          onConfirm={() => {
            createXmlDialog({
              onSuccess: (resp) => {
                setShowXmlDialog(true);
                refetch();
              },
              onError: ({ response }) => {
                setShowXmlDialog(true);
                refetch();
              },
            });
          }}
          selectFile={selectFile}
          setSelectFile={setSelectFile}
          createLoading={createLoading}
        />
      </div>
    </>
  );
};
export default ListTemplate;
