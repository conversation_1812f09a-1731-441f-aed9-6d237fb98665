{"header": "List of Point code", "columns": [{"header": "Point code id", "accessorKey": "id", "filterVariant": "range"}, {"header": "Point code", "accessorKey": "attributes.pointCode"}, {"header": "Operator name", "accessorKey": "attributes.operatorName"}, {"header": "Operator country", "accessorKey": "attributes.operatorCountry"}, {"header": "SAP ID", "accessorKey": "attributes.sapId", "filterVariant": "range"}, {"header": "TT", "accessorKey": "attributes.tt", "filterVariant": "range"}, {"header": "NP", "accessorKey": "attributes.np"}, {"header": "RI", "accessorKey": "RI", "renderFunction": "0"}, {"header": "SSN", "accessorKey": "attributes.ssn"}, {"header": "PC status", "accessorKey": "attributes.status"}, {"header": "Delay", "accessorKey": "attributes.delay", "filterVariant": "range"}, {"buttons": [{"type": "image", "title": "View List", "iconUrl": ""}, {"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"info": "Unique number assigned to a node in a network", "name": "pointCode", "size": 6, "type": "text", "title": "Point code", "isInfo": true, "isMandatory": true, "nonEditable": true, "validations": [{"type": "required", "message": "Please enter the Point code"}, {"type": "min", "value": 1000, "message": "Min length is 4"}, {"type": "max", "value": 9999999999, "message": "Max length is 10"}, {"type": "typeError", "message": "Please enter a valid numeric value for point code"}], "defaultValue": "", "validationType": "number"}, {"info": "Name of the operator", "name": "operatorName", "size": 6, "type": "text", "title": "Operator name", "isInfo": true, "isMandatory": true, "validations": [{"type": "matches", "regex": "^[a-zA-Z][a-zA-Z0-9_]*$", "message": "Only alphanumeric characters and underscores are allowed, and it must start with a letter"}, {"type": "required", "message": "Please enter the Operator name"}, {"type": "min", "value": 2, "message": "Min length is 2"}, {"type": "max", "value": 50, "message": "Max length is 50"}], "defaultValue": "", "validationType": "string"}, {"info": "Country in which the operator resides", "name": "operatorCountry", "size": 6, "type": "text", "title": "Operator country", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Operator country"}, {"type": "only_alphabet", "message": "Only alphabetic character underscore and space are allowed"}, {"type": "min", "value": 2, "message": "Min length is 2"}, {"type": "max", "value": 50, "message": "Max length is 50"}], "defaultValue": "", "validationType": "string"}, {"info": "Service Access Point (SAP) is an unique number assigned to a point code", "name": "sapId", "size": 6, "type": "text", "title": "SAP id", "isInfo": true, "isMandatory": true, "validations": [{"type": "typeError", "message": "Please enter valid numeric value for SAP Id"}, {"type": "required", "message": "Please enter the SAP id"}, {"type": "min", "value": 0, "message": "Please enter valid SAP Id"}, {"type": "max", "value": 255, "message": "Please enter valid SAP Id"}], "defaultValue": "", "validationType": "number"}, {"info": "Translation type", "name": "tt", "size": 6, "type": "text", "title": "TT", "isInfo": true, "isMandatory": true, "validations": [{"type": "typeError", "message": "Please enter a valid numeric value for tt"}, {"type": "required", "message": "Please enter a TT"}, {"type": "min", "value": 0, "message": "Invalid"}, {"type": "max", "value": 256, "message": "Invalid"}], "defaultValue": "", "validationType": "number"}, {"info": "Numbering plan", "name": "np", "size": 6, "type": "select", "title": "NP", "isInfo": true, "options": [{"label": "Unknown", "value": 0}, {"label": "ISDN", "value": 1}, {"label": "Telephony(E.164, E<PERSON>163)", "value": 2}, {"label": "Data(X.121)", "value": 3}, {"label": "Telex (5.69)", "value": 4}, {"label": "Maritime mobile", "value": 5}, {"label": "Land mobile(E.212)", "value": 6}, {"label": "Private", "value": 7}, {"label": "ANSI SS7 PC and SSN", "value": 13}, {"label": "Internet IP", "value": 14}, {"label": "Extension", "value": 15}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select NP"}], "defaultValue": "", "validationType": "string"}, {"info": "Sub system number", "name": "ssn", "size": 6, "type": "select", "title": "SSN", "isInfo": true, "options": [{"label": "HLR", "value": 6}, {"label": "VLR", "value": 7}, {"label": "MSC", "value": 8}, {"label": "FNR", "value": 253}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select SSN"}], "defaultValue": "", "validationType": "string"}, {"info": "Status of the point code", "name": "status", "size": 6, "type": "select", "title": "PC status ", "isInfo": true, "options": [{"label": "Active", "value": "A"}, {"label": "Inactive", "value": "I"}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select PC status"}], "defaultValue": "", "validationType": "string"}, {"info": "Delay time to send message to point code.", "name": "delay", "size": 6, "type": "text", "title": "Delay", "isInfo": true, "isMandatory": true, "validations": [{"type": "typeError", "message": "Please enter a valid numeric value for delay"}, {"type": "required", "message": "Please enter the delay time"}, {"type": "matches", "regex": "^[0-9]{1,5}$", "message": "Please enter a valid numeric value for delay"}], "defaultValue": "", "validationType": "string"}], "formType": "simple", "buttonName": "+ Add point code", "moduleData": "point-code", "moduleName": "Point code", "globalSearch": [{"label": "Point code id", "value": "id"}, {"label": "Point code", "value": "pointCode"}, {"label": "Operator name", "value": "operatorName"}, {"label": "Operator country", "value": "operatorCountry"}, {"label": "SAP ID", "value": "sapId"}]}