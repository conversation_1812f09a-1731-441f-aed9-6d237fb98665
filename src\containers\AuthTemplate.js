// const AuthTemplate = ({ children }) => {
//   return (
//     <div className="flex flex-col bg-gradient-to-r from-[#FFFFFF] via-[#A3CDAF] to-[#FFFFFF] h-screen justify-center overflow-y-auto">
//       <main className="md:w-full max-w-screen-lg mx-auto">
//         <div className="w-full shadow-lg rounded-2xl bg-white px-12 py-5 my-auto">
//           {children}
//         </div>
//       </main>
//     </div>
//   );
// };

// export default AuthTemplate;

import Button from "../components/Buttons/OutlinedButton";
import { AirtelIcon } from "../icons";

const AuthTemplate = ({ children }) => {
  return (
    <div className="h-screen flex">
      <div className="bg-black w-1/2 pl-5">
        <div className="h-full pl-5 flex flex-col justify-center">
          <AirtelIcon />
          <div className="pl-3 text-white">
            <div className="  text-3xl">
              Welcome to <span className="text-red-600">SMS HUB</span>
            </div>
            <div className=" my-3">
              Lorem ipsum dolor sit amet consectetur. Non orci arcu non aliquet
              rhoncus. Non orci arcu non aliquet rhoncus
            </div>
            <Button label={"Contact us"} buttonClassName={"w-28 text-sm"} />
          </div>
        </div>
      </div>
      <div className="bg-white w-1/2 pl-10">{children}</div>
    </div>
  );
};

export default AuthTemplate;
