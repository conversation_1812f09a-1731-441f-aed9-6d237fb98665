import React, { useRef } from "react";
import { TextField, Chip, Box } from "@mui/material";
import { styled } from "@mui/system";
import { CloseIcon } from "../../icons";
import { useField } from "formik";
import FullScreenLoader from "./worker";

const ChipContainer = styled(Box, {
  shouldForwardProp: (prop) => prop !== "hasError" && prop !== "isDisabled",
})(({ theme, hasError, isDisabled }) => ({
  display: "flex",
  flexWrap: "wrap",
  gap: theme.spacing(1),
  border: `1px solid ${hasError ? "#DC3833" : "#808080"}`,
  padding: theme.spacing(0.1),
  borderRadius: "10px",
  minHeight: 180,
  maxHeight: 210,
  overflowY: "auto",
  width: "100%",
  fontSize: 12,
  backgroundColor: "#F3F3F3",
  color: "#666666",
  pointerEvents: isDisabled ? "none" : "auto",
}));

const ShortCodeInput = ({ name, value, onChange, isDisabled, moduleName }) => {
  const [inputValue, setInputValue] = React.useState("");
  const [processing, setProcessing] = React.useState(false);
  const inputRef = useRef(null);

  const [field, meta] = useField(name);
  const [inputWidth, setInputWidth] = React.useState(150);

  const processChunks = (allValues, chunkSize = 1000) => {
    let processedValues = [...value];

    const processChunk = (chunk) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          processedValues = [...processedValues, ...chunk];
          onChange(processedValues);
          resolve();
        }, 0);
      });
    };

    const processAllChunks = async () => {
      for (let i = 0; i < allValues.length; i += chunkSize) {
        const chunk = allValues.slice(i, i + chunkSize);
        await processChunk(chunk);
      }
      setProcessing(false); // Stop processing after all chunks are done
    };

    processAllChunks();
  };

  const handleInputChange = (event) => {
    const newValue = event.target.value;
    setInputValue(newValue);
    setInputWidth(
      newValue.length <= 2 ? newValue.length * 60 : newValue.length * 16
    );
  };

  const handleKeyDown = (event) => {
    if (
      moduleName === "serieses" &&
      event.key === "Enter" &&
      inputValue.trim() !== ""
    ) {
      event.preventDefault();
      setProcessing(true); // Start processing

      // Split the input by spaces, trim each part, and remove empty values
      const allValues = inputValue
        .split(" ")
        .map((chip) => chip.trim())
        .filter((chip) => chip !== "");

      setInputValue("");
      processChunks(allValues);
    } else if (
      moduleName === "operators" &&
      (event.key === "Enter" || event.key === ",") &&
      inputValue.trim() !== ""
    ) {
      event.preventDefault();
      const newChips = [
        ...value,
        ...inputValue.split(",").map((chip) => chip.trim()),
      ];
      setInputValue("");
      onChange(newChips);
    } else if (event.key === "Enter" && inputValue.trim() !== "") {
      event.preventDefault();
      const newChips = [...value, inputValue.trim()];
      setInputValue("");
      onChange(newChips);
    }
  };

  const handleDelete = (indexToDelete) => () => {
    const newChips = value.filter((_, index) => index !== indexToDelete);
    onChange(newChips);
  };

  return (
    <div>
      <FullScreenLoader open={processing} />
      <ChipContainer hasError={meta.touched && meta.error}>
        <div style={{ gap: "0px" }}>
          {value.map((shortCode, index) => (
            <Chip
              key={index}
              label={
                <div
                  style={{
                    whiteSpace: "normal",
                    wordBreak: "break-word",
                    maxWidth: "100%",
                    overflowWrap: "break-word",
                    position: "relative",
                    paddingRight: "8px",
                  }}
                >
                  {shortCode}
                </div>
              }
              onDelete={handleDelete(index)}
              deleteIcon={
                !isDisabled ? (
                  <CloseIcon
                    style={{
                      position: "absolute",
                      top: "4px",
                      right: "4px",
                      fontSize: "16px",
                      width: "8px",
                      height: "8px",
                      cursor: "pointer",
                    }}
                  />
                ) : (
                  <></>
                )
              }
              color="primary"
              sx={{
                backgroundColor: "rgba(53, 118, 235, 0.3)",
                borderRadius: "4px",
                color: "black",
                marginTop: "6px",
                marginLeft: "6px",
                height: "auto",
                padding: "8px",
              }}
            />
          ))}
          <TextField
            inputRef={inputRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            size="small"
            disabled={isDisabled || processing}
            autoComplete="off"
            placeholder={processing ? "Processing..." : ""}
            sx={{
              width: inputWidth,
              maxHeight: 200,
              maxWidth: "350px",
              outline: "none",
              padding: "0px",
              "& .MuiOutlinedInput-root": {
                "& fieldset": {
                  border: "none",
                },
                "& .MuiInputBase-input": {
                  fontSize: 12,
                },
              },
            }}
          />
        </div>
      </ChipContainer>
      {meta.touched && meta.error && (
        <div className="text-errorColor text-[11px] mt-0.5">
          {Array.isArray(meta.error)
            ? meta.error[meta.error.length - 1]
            : meta.error}
        </div>
      )}
    </div>
  );
};

export default ShortCodeInput;
