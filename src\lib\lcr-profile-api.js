import { axiosPrivate } from "../common/axiosPrivate";
import getAPIMap from "../routes/ApiUrls";

/** This method is used to get the page schema
 * @param {*} options
 * @returns
 */
export async function getPageSchema({ queryKey }) {
  let url = getAPIMap("pageSchemaUrl");
  url = url + queryKey[0];
  if (queryKey[0] === "home-page") {
    url += `&populate=*`;
  }
  let response = axiosPrivate.get(url);
  return response;
}

/**
 *This method is used to get the list data
 * @param {*} param0
 * @returns
 */
export async function getSupplierListData({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${queryKey[1]}?sort=createdAt:desc`;

  if (queryKey[2]) {
    url += `&pagination[limit]=${queryKey[2].limit}`;
  }
  if (queryKey[3]) {
    url += `&filters[operatorType][$eq]=${queryKey[3]}`;
  }
  if (queryKey[4]) {
    url += `&filters[operatorType][$eq]=${queryKey[4]}`;
  }
  if (queryKey[5]) {
    url += `&fields[0]=${queryKey[5]}`;
  }

  let response = await axiosPrivate.get(url);
  return response;
}

/**
 * This method is used to create the add LCR
 * @param {*} options - Contains the module data and request data
 * @returns {Promise} - Axios response promise
 */
export async function createLcr(options) {
  let url = `${getAPIMap("lcrCreate")}?model=${options.moduleData}`;
  let formData = new FormData();

  const { fileUpload, data } = options.reqData;
  const fields = [
    "lcrName",
    "custType",
    "isReve",
    "reveCusType",
    "trafType",
    "reveLcrStatus",
    "dateReq",
    "startDate",
    "lcrType",
  ];

  formData.append("file", fileUpload);

  fields.forEach((field) => {
    if (data[field] !== undefined) {
      formData.append(field, data[field]);
    }
  });

  return axiosPrivate.post(url, formData, options.reqData);
}

/**
 * This method is used to create the add LCR
 * @param {*} options - Contains the module data and request data
 * @returns {Promise} - Axios response promise
 */
export async function updateLcr(options) {
  let url = `${getAPIMap("lcrCreate")}/${options.id}?model=${
    options.moduleData
  }`;
  let formData = new FormData();

  const { fileUpload, data } = options.reqData;
  const fields = [
    "lcrName",
    "custType",
    "isReve",
    "reveCusType",
    "trafType",
    "reveLcrStatus",
    "dateReq",
    "startDate",
    "lcrType",
  ];

  formData.append("file", fileUpload);

  fields.forEach((field) => {
    if (data[field] !== undefined) {
      formData.append(field, data[field]);
    }
  });

  return axiosPrivate.put(url, formData, options.reqData);
}

/**
 * This method is used to create the list
 * @param {*} param0
 * @returns
 */
export async function updateRecord(options) {
  let url = getAPIMap("listUrl");
  url = url + "/" + options.moduleName + "/" + options.id;
  let response = axiosPrivate.put(url, options.reqData);
  return response;
}

/**
/**
 * This method is used to create the list
 * @param {*} param0
 * @returns
 */
export async function createRecord(options) {
  let url = getAPIMap("listUrl");
  url = url + "/" + options?.moduleName;
  let response = axiosPrivate.post(url, options.reqData);
  return response;
}
export async function getShortCode(options) {
  let url = getAPIMap("shortCodeList");

  let response = axiosPrivate.get(url);
  return response;
}
/**
/**
 * This method is used to get short code list
 * @param {*} param0
 * @returns
 */
export async function getShortCodeLCR({ queryKey }) {
  let url = `${getAPIMap("shortCodeList")}?lcrView=true`;

  if (queryKey[1]) {
    url += `&lcrModel=${queryKey[1]}`;
  }
  if (queryKey[2]) {
    url += `&lcrId=${queryKey[2]}`;
  }

  const response = await axiosPrivate.get(url);
  return response;
}

/**
/**
 * This method is used to get Lcr details
 * @param {*} param0
 * @returns
 */
export async function getLCRData({ queryKey }) {
  let url = `${getAPIMap("lcrGetDetails")}`;

  const queryParams = [];
  queryParams.push("sort[id]=desc");
  if (queryKey[2]?.limit !== undefined) {
    queryParams.push(`pagination[limit]=${queryKey[2].limit}`);
  }
  if (queryKey[3] !== undefined) {
    queryParams.push(`pagination[pageSize]=${queryKey[3]}`);
  }
  if (queryKey[4] !== undefined) {
    queryParams.push(`pagination[page]=${queryKey[4]}`);
  }
  if (queryKey[6] || queryKey[6] === 0) {
    queryParams.push(`filters[lcrType]=${queryKey[6]}`);
  }
  if (queryParams.length) {
    url += `${url.includes("?") ? "&" : "?"}${queryParams.join("&")}`;
  }

  const response = await axiosPrivate.get(url);
  return response;
}

export async function getLCRDataById({ queryKey }) {
  const [resourceType, id, parentId] = queryKey;

  let url = `${getAPIMap("listUrl")}/${resourceType}/${id}`;

  const params = new URLSearchParams({
    "populate[parentDetails][populate]": "*",
    "populate[policyDetails][populate]": "*",
  });

  if (parentId) {
    params.append("parentId", parentId);
  }

  const queryString = params.toString();
  if (queryString) {
    url += `?${queryString}`;
  }

  const response = await axiosPrivate.get(url);
  return response;
}

/**
 *This method is used to update parent details
 * @param {*} param0
 * @returns
 */
export async function updateParentDetail(options) {
  let url = `${getAPIMap("listUrl")}/${options.moduleName}/${options.lcrId}/${
    options.parentDetail
  }/${options.parentId}`;
  return axiosPrivate.put(url, options.reqData);
}

/**
 * This method is used to delete the parent details
 * @param {*} param0
 * @returns
 */
export async function deleteParentDetail(options) {
  let url = `${getAPIMap("listUrl")}/${options.moduleName}/${options.id}/${
    options.parentDetail
  }/${options.parentId}?filters[lcrType]=${options.lcrTypeValue}`;

  let response = axiosPrivate.delete(url);
  return response;
}

/**
/**
 * This method is used to get all parentDetails
 * @param {*} param0
 * @returns
 */

export async function getParentDetails({ queryKey }) {
  const [basePath, id, parentId, lcrTypeValue, pageSize, page] = queryKey;
  const url = new URL(`${getAPIMap("listUrl")}/${basePath}/${id}/${parentId}`);

  const params = new URLSearchParams();

  if (pageSize) params.set("pagination[pageSize]", pageSize);
  if (page) params.set("pagination[page]", page);
  if (lcrTypeValue != null) {
    params.set("filters[lcrType]", lcrTypeValue);
  }
  url.search = params.toString();

  const response = await axiosPrivate.get(url.toString());
  return response;
}

export async function getParentDetailsById({ queryKey }) {
  const [moduleName, parentDetails, id, parentId, lcrTypeValue] = queryKey;
  const url = new URL(
    `${getAPIMap("listUrl")}/${moduleName}/${id}/${parentDetails}/${parentId}`
  );

  const params = new URLSearchParams({
    "populate[parentDetails][populate]": "*",
    "populate[policyDetails][populate]": "*",
  });

  if (lcrTypeValue != null) {
    params.set("filters[lcrType]", lcrTypeValue);
  }

  url.search = params.toString();

  const response = await axiosPrivate.get(url.toString());
  return response;
}

export async function getLinkedOperator({ queryKey }) {
  let url = `${getAPIMap("linkedOperator")}`;
  if (queryKey[1]) {
    url += `?linkedModel=${queryKey[1]}`;
  }
  if (queryKey[2]) {
    url += `&linkedId=${queryKey[2]}`;
  }
  if (queryKey[3]) {
    url += `&fieldName=${queryKey[3]}`;
  }
  const response = await axiosPrivate.get(url);
  return response;
}
