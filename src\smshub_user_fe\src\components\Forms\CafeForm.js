import React, { useState, useContext, useEffect, useRef } from "react";
import { Form, Formik } from "formik";
import Button from "../Buttons/Button";
import CancelButton from "../Buttons/OutlinedButton";
import CommonTable from "../FormsUI/Table/RolesTable";
import RoleColumnsTab from "./RoleColumnsTab";
import { multiStepFormUserRoleContext } from "../../context/UserRoleContext";
import { createRole, updateRole } from "../../lib/role-management-api";
import { useMutation } from "react-query";
import { moduleConfiguration } from "../../../../common/constants";
import SuccessDialog from "../../../../components/Dialog/SuccessDialog";
import ErrorDialog from "../../../../components/Dialog/ErrorDialog";
import { useNavigate } from "react-router-dom";

function CafeForm(props) {
  const [selectAll, setSelectAll] = useState(false);
  const [selectedModule, setSelectedModule] = useState(0);
  const {
    setFormData,
    formData,
    handleNextClickStep,
    currentStep,
    setCurrentStep,
  } = useContext(multiStepFormUserRoleContext);

  const currentTabData = props?.props?.data?.cafeForms?.[currentStep]?.tabs;

  const modules = currentTabData?.map((tab) => tab.moduleName);

  const selectedTabElements = currentTabData?.[selectedModule];

  const listData = selectedTabElements?.elements;

  const action = props?.props?.action;

  const { mutate: createRoleAPI } = useMutation(createRole);
  const { mutate: updateRoleAPI } = useMutation(updateRole);

  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErroDialog] = useState(false);
  const [message, setMessage] = useState("");
  const navigate = useNavigate();

  const formValuesRef = useRef({});

  const columns = (setFieldValue, values) =>
    RoleColumnsTab({
      selectedTabElements,
      selectedModule,
      setFieldValue,
      currentTabData,
    });

  const formValues = props?.props?.formValues;
  useEffect(() => {
    if (formData && formValues) {
      const mergedData = { ...formData, ...formValues };
      setFormData(mergedData);
    }
  }, [formValues]);

  const handleSubmit = (values) => {
    if (action === "add") {
      createRoleAPI(
        {
          moduleName: moduleConfiguration.roleManagement,
          formData,
        },
        {
          onSuccess: (resp) => {
            setSuccessDialog(true);
            setMessage(`Role management created successfully`);
          },
          onError: ({ response }) => {
            setErroDialog(true);
            setMessage(response?.data?.error?.message);
          },
        }
      );
    } else {
      updateRoleAPI(
        {
          moduleName: moduleConfiguration.roleManagement,
          id: props?.props?.id,
          formData,
        },
        {
          onSuccess: (resp) => {
            setSuccessDialog(true);
            setMessage(`Role management updated successfully`);
          },
          onError: ({ response }) => {
            setErroDialog(true);
            setMessage(response?.data?.error?.message);
          },
        }
      );
    }
    handleNextClickStep();
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  useEffect(() => {
    if (Object.keys(formValuesRef.current).length > 0) {
      // Update stored form values on tab change
      formValuesRef.current = { ...formValuesRef.current };
    }
  }, [selectedModule, formData, currentStep]);

  return (
    <Formik
      initialValues={{
        ...(formData?.resources?.reduce((acc, item) => {
          acc[item.name] = {
            view: item.permissions.view === 1,
            update: item.permissions.update === 1,
            delete: item.permissions.delete === 1,
            create: item.permissions.create === 1,
            download: item.permissions.download === 1,
          };
          return acc;
        }, {}) || {}), // Default fallback
      }}
      validateOnMount={true}
      enableReinitialize={true}
      onSubmit={handleSubmit}
    >
      {({ values, setFieldValue }) => (
        <Form>
          <div className="flex md:mx-10 md:my-5">
            {/* Module Names Tab */}
            <div className="w-100 m-5 rounded-md ">
              <div className="font-bold border-b border-tableBorder pb-1 text-sm">
                Module names
              </div>
              <div className="mt-2.5">
                {modules?.map((module, index) => (
                  <div
                    key={index}
                    className={`p-2 text-sm cursor-pointer ${
                      selectedModule === index ? "bg-bgTab" : ""
                    }`}
                    onClick={() => {
                      // Save current form values before switching
                      formValuesRef.current = {
                        ...formValuesRef.current,
                        ...values,
                      };
                      setSelectedModule(index);
                    }}
                  >
                    {module}
                  </div>
                ))}
              </div>
            </div>

            <div className="flex-1 ">
              <CommonTable
                columns={columns(setFieldValue, values)}
                data={listData}
                selectAll={selectAll}
                setSelectAll={setSelectAll}
              />
            </div>
          </div>

          {/* Buttons */}
          <div className="flex justify-end items-end mt-10 mr-10 mb-5">
            <CancelButton
              label="< Back"
              buttonClassName="w-full md:w-[154px] h-[40px] text-base mb-3 rounded-[5px] ml-5"
              onClick={() => handleBack()}
            />
            <Button
              type="submit"
              label="Save"
              value="submit"
              buttonClassName="w-full md:w-[154px] h-[40px] text-base mb-3 rounded-[5px] ml-5"
            />
          </div>
          <SuccessDialog
            show={successDialog}
            onHide={() => {
              navigate("/app/list/role-management");
              setSuccessDialog(false);
            }}
            message={message}
          ></SuccessDialog>
          <ErrorDialog
            show={errorDialog}
            onHide={() => {
              setErroDialog(false);
            }}
            message={message}
          ></ErrorDialog>
        </Form>
      )}
    </Formik>
  );
}

export default CafeForm;
