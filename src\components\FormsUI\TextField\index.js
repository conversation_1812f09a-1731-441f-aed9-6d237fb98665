import { CssTextField } from "../StyledComponent";
import { useField } from "formik";
import InputLabel from "../InputLabel";
import { AuthContext } from "../../../context/AuthContext";
import { useContext, useState } from "react";
import styled from "@emotion/styled";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { CssTooltip } from "../../FormsUI/StyledComponent";
import { MAX_COMMENT_CHAR } from "../../../common/config";

const IconWrapper = styled.div`
  position: absolute;
  right: 8%;
  top: 20%;
  transform: translateY(-30%);
  cursor: pointer;
`;

const TextFieldWrapper = ({
  name,
  label,
  labelColor,
  labelClassName,
  layoutClassName,
  isMandatory,
  onChange,
  isDisabled,
  applyOnChange,
  minHeight,
  isPassword,
  maxLength,
  noError = false,
  isObject,
  ...rest
}) => {
  const { template } = useContext(AuthContext);
  const [field, meta] = useField(name);
  const [showPassword, setShowPassword] = useState(false);

  const configTextField = {
    ...field,
    ...rest,
    type: isPassword ? (showPassword ? "password" : "text") : "text",
    fullWidth: true,
    autoComplete: "off",
    error:
      (meta.touched && meta.error) || (noError && meta.error) ? true : false,
    helperText:
      meta.touched && meta.error && noError !== true ? (
        <span style={{ whiteSpace: "pre-wrap" }}>{meta.error}</span>
      ) : (
        ""
      ),
    // onChange: handleChange,

    onChange: applyOnChange === true ? onChange : field.onChange,
    value:
      field.name.includes("sscpCarrierInfo") && isObject === true
        ? ""
        : field.value ?? "",
  };
  if (meta && meta.touched && meta.error) {
    configTextField.error = true;
    configTextField.helperText = (
      <span style={{ whiteSpace: "pre-wrap" }}>{meta.error}</span>
    );
  }

  return (
    <div className={layoutClassName} style={{ position: "relative" }}>
      {label && (
        <InputLabel
          label={label}
          color={labelColor}
          labelClassName={labelClassName}
          isMandatory={isMandatory}
        />
      )}
      <CssTooltip
        title={field.value?.length > 80 ? field.value : ""}
        placement="top"
        arrow
      >
        <CssTextField
          {...configTextField}
          template={template}
          disabled={isDisabled}
          inputProps={{
            maxLength: maxLength || MAX_COMMENT_CHAR,
            autoComplete: "off",
            style: { paddingRight: isPassword ? "2.5rem" : "0rem" },
          }}
        />
      </CssTooltip>
      {isPassword && (
        <IconWrapper
          style={{ marginTop: "4px", cursor: "pointer", right: 12 }}
          onClick={() => setShowPassword((prev) => !prev)}
        >
          {showPassword ? (
            <VisibilityOff sx={{ fontSize: "14px" }} />
          ) : (
            <Visibility sx={{ fontSize: "14px" }} />
          )}
        </IconWrapper>
      )}
    </div>
  );
};

export default TextFieldWrapper;
