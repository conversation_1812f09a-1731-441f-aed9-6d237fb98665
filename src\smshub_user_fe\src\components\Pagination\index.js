import Pagination from "@mui/material/Pagination";
import { useEffect, useState } from "react";
import Stack from "@mui/material/Stack";
const TablePagination = (props) => {
  const [page, setPage] = useState(1);
  useEffect(() => {
    setPage(props.page);
  }, [props.page]);
  const handleChange = (event, value) => {
    setPage(value);
    props.setPageNumber(value);
  };
  return (
    <Stack spacing={2}>
      <Pagination
        count={props.pageCount}
        page={page}
        onChange={handleChange}
        className="mx-auto"
      />
    </Stack>
  );
};
export default TablePagination;
