import { Formik, Form } from "formik";
import React, { useEffect, useState } from "react";
import Select from "../FormsUI/Select";

function RetryPolicyFilter({ setRetryGrpName, retryGrpName, dropdownDetails }) {
  const filteredRetryGroup = [
    { label: "All", value: "all" },
    ...dropdownDetails,
  ];

  const [initialValue, setInitialValue] = useState("all"); // Default to "all"

  useEffect(() => {
    if (retryGrpName !== initialValue) {
      setInitialValue(retryGrpName);
    }
  }, [retryGrpName, initialValue]);

  return (
    <div>
      <Formik
        enableReinitialize
        initialValues={{
          name: "all",
        }}
        onSubmit={() => {}}
      >
        {({ values, setFieldValue }) => (
          <Form>
            <div className="w-[450px] my-5">
              <Select
                name="name"
                options={filteredRetryGroup}
                placeholder={"Select a group name"}
                bgColor="#f3f3f3"
                borderRadius="24px"
                paddingX="4px"
                onChange={(selectedOption) => {
                  setFieldValue("name", selectedOption.value); // Update Formik state
                  setRetryGrpName(selectedOption.value); // Update parent state
                }}
                value={filteredRetryGroup.find(
                  (option) => option.value === values.name // Sync dropdown dynamically
                )}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}

export default RetryPolicyFilter;
