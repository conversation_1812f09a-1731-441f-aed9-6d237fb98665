import { useField, useFormikContext } from "formik";
import dayjs from "dayjs";
import { useEffect, useState, useRef } from "react";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import theme from "../../tailwind-theme";

const DatePickerFieldWrapper = ({
  name,
  label,
  maxDate,
  labelClassName,
  labelColor,
  isMandatory,
  isDisabled,
  defaultValue,
  action,
  isCurrentDate,
  minDate,
  fromTmrwDate,
  showMinDate,
  ...rest
}) => {
  const [field, meta] = useField(name);
  const { setFieldValue } = useFormikContext();
  const [selectedValue, setSelectedValue] = useState(null);
  const [dropdownPosition, setDropdownPosition] = useState("");
  const [maxListHeight, setMaxListHeight] = useState(150);
  const calRef = useRef(null);

  useEffect(() => {
    const buttonRect = calRef.current?.getBoundingClientRect();
    if (!buttonRect) return;

    const spaceBelow = Math.round(window.innerHeight - buttonRect.bottom);
    const spaceAbove = buttonRect.top;

    if (spaceBelow < 200 && spaceAbove >= spaceBelow) {
      setDropdownPosition("top");
      setMaxListHeight(spaceAbove - 50); // leave some buffer
    } else {
      setDropdownPosition("bottom");
      setMaxListHeight(spaceBelow - 50);
    }
  }, [window.innerHeight, calRef.current]);

  useEffect(() => {
    if (action === "add" && defaultValue) {
      setSelectedValue(dayjs());
    } else if (isCurrentDate && action === "edit") {
      setSelectedValue(dayjs());
    } else if (field.value) {
      setSelectedValue(dayjs(field.value));
    }
  }, [field.value, defaultValue, isCurrentDate, action]);

  const handleDateChange = (val) => {
    setSelectedValue(val);
    setFieldValue(name, val ? val.format("YYYY-MM-DD") : "");
  };

  const configDateField = {
    ...field,
    ...rest,
    fullWidth: true,
    InputLabelProps: { shrink: true },
  };

  if (meta && meta.touched && meta.error) {
    configDateField.error = true;
    configDateField.helperText = meta.error;
  }
  return (
    <div>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          ref={calRef}
          className="w-full"
          maxDate={maxDate}
          format={"DD-MM-YYYY"}
          //  minDate={minDate === true ? dayjs() :fromTmrwDate===true?dayjs() +1: rest.minDate}
          minDate={
            minDate === true
              ? dayjs()
              : fromTmrwDate === true
              ? dayjs().add(2, "day")
              : showMinDate
              ? dayjs(showMinDate).add(1, "day")
              : rest.minDate
          }
          value={selectedValue ? dayjs(selectedValue) : null}
          disabled={isDisabled}
          onChange={handleDateChange}
          slotProps={{
            popper: {
              sx: {
                zIndex: 1300,
                ".MuiPaper-root": {
                  maxHeight: `300px`,
                  overflowY: "hidden",
                  borderRadius: "10px",
                  scrollbarWidth: "none",
                  border: "black",
                },
              },
            },
          }}
          sx={{
            "& label.Mui-focused": {
              color: "#2D2D2D",
            },
            "& .MuiOutlinedInput-root": {
              height: "40px",
              fontSize: "12px",
              fontWeight: "500",
              backgroundColor: "#FFFFFF",
              flexDirection: "row-reverse",
              "& .MuiOutlinedInput-notchedOutline": {
                border:
                  meta.error && meta.touched
                    ? `1px solid ${theme.borderColor.errorBorder} !important`
                    : "1px solid #808080 !important",
                borderRadius: "10px",
              },
              "&:hover fieldset": {
                border:
                  meta.error && meta.touched
                    ? `1px solid ${theme.borderColor.errorBorder} !important`
                    : "1px solid #808080 !important",
              },
              "&.Mui-focused fieldset": {
                border:
                  meta.error && meta.touched
                    ? `1px solid ${theme.borderColor.errorBorder} !important`
                    : "1px solid #808080 !important",
              },
            },
          }}
        />
      </LocalizationProvider>

      {meta.error && meta.touched && (
        <p className="text-[11px] text-errorColor mt-0.5">{meta.error}</p>
      )}
    </div>
  );
};

export default DatePickerFieldWrapper;
