import { axiosPrivate } from "../common/axiosPrivate";
import getAPIMap from "../routes/ApiUrls";

/**
 * This method is used to get users
 * @param {*} options
 * @returns
 */
export async function getRoleDetail(options) {
  let url = getAPIMap("getRoles");
  if (options.queryKey[1] && options.queryKey[1] !== "") {
    url = url + "?page=" + options.queryKey[1];
  }
  if (options.queryKey[2] && options.queryKey[2] !== "") {
    url = url + "&limit=" + options.queryKey[2];
  }

  let response = axiosPrivate.get(url);
  return response;
}

/**
 * This method is used to create the role
 * @param {*} param0
 * @returns
 */
export async function createRole(options) {
  let url = getAPIMap("getRoles");

  return await axiosPrivate.post(url, options.reqData);
}

/**
 * This method is used to create the list
 * @param {*} param0
 * @returns
 */
export async function updateRole(options) {
  let url = getAPIMap("getRoles");
  const moduleName = options?.moduleName;

  url = `${url}/${moduleName}/${options.id}`;
  return await axiosPrivate.put(url, options.reqData);
}

/**
 *This method is used to get the data by Id
 * @param {*} param0
 * @returns
 */
export async function getRole({ queryKey }) {
  let url = `${getAPIMap("getRoles")}`;

  const response = await axiosPrivate.get(url);
  return response;
}
