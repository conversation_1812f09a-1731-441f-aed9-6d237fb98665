{"header": "List of channel partner", "columns": [{"header": "Channel ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "Partner name", "accessorKey": "attributes.partnerName"}, {"header": "Partnership type", "accessorKey": "attributes.partnershipType"}, {"header": "Commission type", "accessorKey": "attributes.commissionType"}, {"header": "Commission percentage type", "accessorKey": "attributes.comPerType"}, {"header": "Commission currency", "accessorKey": "attributes.currency"}, {"buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"info": "Name for the channel partner", "name": "partner<PERSON>ame", "size": 6, "type": "text", "title": "Channel partner", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Channel partner"}, {"type": "matches", "regex": "^[a-zA-Z](?=.*\\d)[a-zA-Z0-9_]*$", "message": "Only alphanumeric characters and underscore allowed. Begin with alphabet."}, {"type": "min", "value": 3, "message": "Min length allowed is 3"}, {"type": "max", "value": 30, "message": "Max length is 30"}], "defaultValue": "", "validationType": "string"}, {"info": "select:\nFixed Rate: If you select fixed rate the commission percentage is charged with e fixed amount.\nSlab-Wise: If you select slab-wise the commission percentage is charged based on the defined slabs. You can as many as 3 slabs", "name": "comPerType", "size": 6, "type": "select", "title": "Commission percentage type", "isInfo": true, "options": [{"label": "Fixed rate", "value": 1}, {"label": "Slab wise", "value": 2}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Commission percentage type"}], "defaultValue": "", "validationType": "string"}, {"info": "Type of partnership", "name": "partnershipType", "size": 6, "type": "select", "title": "Partnership type", "isInfo": true, "options": [{"label": "Customer wise", "value": 1}, {"label": "Customer destination operator wise", "value": 2}, {"label": "Customer destination country wise", "value": 3}, {"label": "Supplier wise", "value": 4}, {"label": "Supplier destination operator wise", "value": 5}, {"label": "Supplier destination country wise", "value": 6}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Partnership type"}], "defaultValue": "", "validationType": "string"}, {"info": "Type of commission", "name": "commissionType", "size": 6, "type": "select", "title": "Commission type", "isInfo": true, "options": [{"label": "Gross revenue", "value": 1}, {"label": "Net revenue", "value": 2}, {"label": "Savings", "value": 3}], "dynamicList": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Commission type"}], "defaultValue": "", "dynamicOptions": [{"options": [{"label": "Savings", "value": 3}], "partnershipType": [4, 5, 6]}, {"options": [{"label": "Gross revenue", "value": 1}, {"label": "Net revenue", "value": 2}, {"label": "Savings", "value": 3}], "partnershipType": [1, 2, 3]}], "validationType": "number"}, {"name": "customerWise", "size": 12, "type": "fieldArray", "title": "Customer wise", "dynamic": {"field": "partnershipType", "value": 1}, "modelType": "mnp&channel", "fieldsData": [{"info": "Types of partnerships based on which channel is being created", "name": "customer", "type": "select", "isInfo": true, "isCustomer": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select the Customer"}], "isDropDownApi": true}, {"name": "targetPrice", "type": "text", "hideField": {"commissionType": [2]}, "validations": [{"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 9999, "message": "Max range allowed is 9999"}, {"type": "typeError", "message": "Please enter Target price with numeric value"}], "defaultValue": "", "validationType": "number"}, {"name": "isDelete", "type": "delete"}], "validation": false, "headersData": ["Customer", "Target price", ""], "initialValues": {"customerWise": [{"customer": "", "targetPrice": ""}]}, "validationType": "array"}, {"name": "customerDestinationOperatorWise", "size": 12, "type": "fieldArray", "title": "Customer destination operator wise", "dynamic": {"field": "partnershipType", "value": 2}, "modelType": "mnp&channel", "fieldsData": [{"info": "Types of partnerships based on which channel is being created", "name": "customer", "type": "select", "isInfo": true, "isCustomer": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select the Customer"}], "isDropDownApi": true}, {"name": "destinationOperator", "type": "select", "onClickPath": "operators", "validations": [{"type": "required", "message": "Please select the Destination operator"}], "isDropDownApi": true}, {"name": "targetPrice", "type": "text", "hideField": {"commissionType": [2]}, "validations": [{"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 9999, "message": "Max range allowed is 9999"}, {"type": "typeError", "message": "Please enter Target price with numeric value"}], "defaultValue": "", "validationType": "number"}, {"name": "isDelete", "type": "delete"}], "validation": false, "headersData": ["Customer", "Destination operator", "Target price", ""], "initialValues": {"customerDestinationOperatorWise": [{"customer": "", "targetPrice": "", "destinationOperator": ""}]}, "validationType": "array"}, {"name": "customerDestinationCountryWise", "size": 12, "type": "fieldArray", "title": "Customer destination country wise", "dynamic": {"field": "partnershipType", "value": 3}, "modelType": "mnp&channel", "fieldsData": [{"info": "Types of partnerships based on which channel is being created", "name": "customer", "type": "select", "isInfo": true, "isCustomer": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select the Customer"}], "isDropDownApi": true}, {"name": "destinationCountry", "type": "select", "isCountry": true, "onClickPath": "operators", "validations": [{"type": "required", "message": "Please select the Destination country"}], "isDropDownApi": true}, {"name": "targetPrice", "type": "text", "hideField": {"commissionType": [2]}, "validations": [{"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 9999, "message": "Max range allowed is 9999"}, {"type": "typeError", "message": "Please enter Target price with numeric value"}], "validationType": "number"}, {"name": "isDelete", "type": "delete"}], "validation": false, "headersData": ["Customer", "Destination country", "Target price", ""], "initialValues": {"customerDestinationCountryWise": [{"customer": "", "targetPrice": "", "destinationCountry": ""}]}, "validationType": "array"}, {"name": "supplierDestinationOperatorWise", "size": 12, "type": "fieldArray", "title": "Supplier destination operator wise", "dynamic": {"field": "partnershipType", "value": 5}, "modelType": "mnp&channel", "fieldsData": [{"info": "Types of partnerships based on which channel is being created", "name": "supplier", "type": "select", "isInfo": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select the Supplier"}], "isDropDownApi": true}, {"name": "destinationOperator", "type": "select", "onClickPath": "operators", "validations": [{"type": "required", "message": "Please select the Destination operator"}], "isDropDownApi": true}, {"name": "targetPrice", "type": "text", "validations": [{"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 9999, "message": "Max range allowed is 9999"}, {"type": "typeError", "message": "Please enter Target price with numeric value"}], "validationType": "number"}, {"name": "isDelete", "type": "delete"}], "validation": false, "headersData": ["Supplier", "Destination operator", "Target price", ""], "initialValues": {"supplierDestinationOperatorWise": [{"supplier": "", "targetPrice": "", "destinationOperator": ""}]}, "validationType": "array"}, {"name": "supplierDestinationCountryWise", "size": 12, "type": "fieldArray", "title": "Supplier destination country wise", "dynamic": {"field": "partnershipType", "value": 6}, "modelType": "mnp&channel", "fieldsData": [{"info": "Types of partnerships based on which channel is being created", "name": "supplier", "type": "select", "isInfo": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select the Supplier"}], "isDropDownApi": true}, {"name": "destinationCountry", "type": "select", "isCountry": true, "onClickPath": "operators", "validations": [{"type": "required", "message": "Please select the Destination operator"}], "isDropDownApi": true}, {"name": "targetPrice", "type": "text", "validations": [{"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 9999, "message": "Max range allowed is 9999"}, {"type": "typeError", "message": "Please enter Target price with numeric value"}], "validationType": "number"}, {"name": "isDelete", "type": "delete"}], "validation": false, "headersData": ["Supplier", "Destination country", "Target price", ""], "initialValues": {"supplierDestinationCountryWise": [{"supplier": "", "targetPrice": "", "destinationCountry": ""}]}, "validationType": "array"}, {"name": "supplierWise", "size": 12, "type": "fieldArray", "title": "Supplier wise", "dynamic": {"field": "partnershipType", "value": 4}, "modelType": "mnp&channel", "fieldsData": [{"info": "Types of partnerships based on which channel is being created", "name": "supplier", "type": "select", "isInfo": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select the Supplier"}], "isDropDownApi": true}, {"name": "targetPrice", "type": "text", "validations": [{"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 9999, "message": "Max range allowed is 9999"}, {"type": "typeError", "message": "Please enter Target price with numeric value"}], "validationType": "number"}, {"name": "isDelete", "type": "delete"}], "validation": false, "headersData": ["Supplier", "Target price", ""], "initialValues": {"supplierWise": [{"supplier": "", "targetPrice": ""}]}, "validationType": "array"}, {"name": "slabDetails", "size": 12, "type": "fieldArray", "title": "Slab wise", "dynamic": {"field": "comPerType", "value": 2}, "modelType": "mnp&channel", "fieldsData": [{"info": "If you select slab-wise the commission percentage is charged based on the defined slabs. You can as many as 3 slabs", "name": "slab", "type": "text", "isInfo": true, "validations": [{"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 9999, "message": "Max range allowed is 9999"}, {"type": "typeError", "message": "Please enter Slab with numeric value"}, {"type": "integer", "message": "Please enter Slab with numeric value"}], "validationType": "number"}, {"name": "commissionPercent", "type": "text", "validations": [{"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 100, "message": "Max range allowed is 100"}, {"type": "integer", "message": "Please enter Commission Percentage with numeric value"}, {"type": "typeError", "message": "Please enter Commission Percentage with numeric value"}], "validationType": "number"}, {"name": "isDelete", "type": "delete"}], "validation": false, "headersData": ["Slab", "Commission Percentage", ""], "initialValues": {"slabDetails": [{"slab": "", "commissionPercent": ""}]}, "validationType": "array"}, {"info": "Date till which channel will be considered as active/valid", "name": "toDate", "size": 6, "type": "date", "title": "To date", "isInfo": true, "defaultValue": "", "fromTmrwDate": true}, {"info": "% of commission", "name": "comFixedPercent", "size": 6, "type": "text", "title": "Commision percentage", "isInfo": true, "dynamic": {"field": "comPerType", "value": 1}, "validations": [{"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 100, "message": "Max range allowed is 100"}, {"type": "typeError", "message": "Please enter Commission Percentage with numeric value"}], "defaultValue": "", "validationType": "number"}, {"info": "Currency type of commission", "name": "currency", "size": 6, "type": "select", "title": "Commission currency", "isInfo": true, "options": [{"label": "EURO", "value": "1"}, {"label": "USD", "value": "2"}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Commision currency"}], "defaultValue": "", "validationType": "string"}, {"name": "percentageThereafter", "size": 6, "type": "text", "title": "Percentage thereafter", "dynamic": {"field": "comPerType", "value": 2}, "validations": [{"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 100, "message": "Max range allowed is 100"}, {"type": "typeError", "message": "Please enter Percentage thereafter with numeric value"}], "defaultValue": "", "validationType": "number"}], "formType": "simple", "buttonName": "+ Add new channel partner", "moduleData": "channel-partner", "moduleName": "Channel partner", "globalSearch": [{"label": "Channel ID", "value": "Id"}, {"label": "Partner name", "value": "partner<PERSON>ame"}], "dropdownOptions": {"field": "commissionType", "options": [{"label": "All Commission type", "query": "", "value": "ALL"}, {"label": "Gross revenue", "query": "filters[commissionType][$eq]=1", "value": 1}, {"label": "Net revenue", "query": "filters[commissionType][$eq]=2", "value": 2}, {"label": "Savings", "query": "filters[commissionType][$eq]=3", "value": 3}], "defaultQuery": "", "defaultValue": "ALL"}}