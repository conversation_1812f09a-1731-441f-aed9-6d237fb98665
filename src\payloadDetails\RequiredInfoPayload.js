import * as Yup from "yup";
import {
  gtType,
  interfaceType,
  ocCompliance,
  operatorType,
  subInterfaceEsme,
} from "../common/constants";

export const requiredvalidationSchema = (generalInfoData) =>
  Yup.object().shape({
    esme_accounts:
      generalInfoData.interfaceType !== interfaceType["SMPP ES"]
        ? Yup.array().test(
            "is-required",
            `Please select  ${
              generalInfoData?.interfaceType === interfaceType["SMPP ES"]
                ? "SMPP ES"
                : Number(generalInfoData?.subInterfaceEsme) ===
                  subInterfaceEsme.SMPP
                ? "SMPP"
                : "HTTP"
            } account`,
            function (value) {
              if (
                generalInfoData.operatorType === operatorType.Customer ||
                generalInfoData.operatorType === operatorType.Both
              ) {
                return value && value.length > 0;
              }
              return true;
            }
          )
        : generalInfoData.interfaceType === interfaceType["SMPP ES"] &&
          (generalInfoData.operatorType === operatorType.Both ||
            generalInfoData.operatorType === operatorType.Customer)
        ? Yup.string().required(`Please select SMPP ES account`)
        : Yup.mixed(),
    paths: Yup.string().test(
      "is-required-based-on-operatorType",
      "Path is required",
      function (value) {
        if (
          generalInfoData.operatorType === operatorType.Supplier ||
          generalInfoData.operatorType === operatorType.Both
        ) {
          return !!value;
        }
        return true;
      }
    ),
  });

export const requiredSS7validationSchema = (generalInfoData, formikRef) => {
  return Yup.object().shape({
    operator_clusters: Yup.array().test(
      "is-required",
      "Please select at least one operator cluster",
      function (value) {
        if (
          (generalInfoData.operatorType === operatorType.Customer ||
            generalInfoData.operatorType === operatorType.Both) &&
          generalInfoData.hubFlag === ocCompliance.hubFlagNo
        ) {
          return value && value.length > 0;
        }
        return true;
      }
    ),
    gtValue: Yup.string().test(
      "is-required",
      `Please select a value`,
      function (value) {
        if (
          generalInfoData.operatorType === operatorType.Customer ||
          generalInfoData.operatorType === operatorType.Both
        ) {
          return !!value;
        }
        return true;
      }
    ),
    calledGtPrefix: Yup.string().test(
      "is-required",
      "Special prefix is required",
      function (value) {
        if (formikRef?.current?.values?.calledAddrType === 3) {
          return !!value;
        }
        return true;
      }
    ),
    paths: Yup.string().test(
      "is-required-based-on-operatorType",
      "Path is required",
      function (value) {
        if (
          generalInfoData.operatorType === operatorType.Supplier ||
          generalInfoData.operatorType === operatorType.Both
        ) {
          return !!value;
        }
        return true;
      }
    ),
  });
};
