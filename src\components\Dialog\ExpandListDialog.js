import { Dialog } from "@mui/material";
import { CloseIcon, SearchIcon } from "../../icons";
import { useState, useEffect } from "react";
import { getDataByIdSeries } from "../../lib/list-api";
import { useQuery } from "react-query";
import { Virtuoso } from "react-virtuoso";

function useDebounce(value, delay) {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

function ExpandListDialog({ show, onHide, row }) {
  const [data, setData] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const [matchedNumbers, setMatchedNumbers] = useState([]);

  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const COLUMN_COUNT = 4;

  const { isLoading } = useQuery(
    ["series", row?.original?.id],
    getDataByIdSeries,
    {
      enabled: show,
      onSuccess: ({ data }) => {
        const numbers = data?.data?.attributes?.msisdn || [];
        setData(numbers);
        setMatchedNumbers(numbers);
      },
    }
  );

  useEffect(() => {
    const trimmedSearchTerm = debouncedSearchTerm
      .replace(/\s+/g, "")
      .toLowerCase();

    if (trimmedSearchTerm) {
      setMatchedNumbers(
        data.filter((num) =>
          num
            .toString()
            .replace(/\s+/g, "")
            .toLowerCase()
            .includes(trimmedSearchTerm)
        )
      );
    } else {
      setMatchedNumbers(data);
    }
  }, [debouncedSearchTerm, data]);

  // Create rows with fixed number of columns
  const createRows = () => {
    const rows = [];
    for (let i = 0; i < matchedNumbers.length; i += COLUMN_COUNT) {
      rows.push(matchedNumbers.slice(i, i + COLUMN_COUNT));
    }
    return rows;
  };

  const rows = createRows();

  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 750, // Reduced from 900 to 750 as requested
          height: 454,
          borderRadius: "10px",
          padding: "10px",
          fontFamily: "OpenSanHebrew",
        },
      }}
      maxWidth="md"
      open={show}
      onClose={() => {
        setSearchTerm("");
        onHide();
      }}
    >
      <div className="p-3">
        <div className="flex items-center justify-between mb-4">
          <div className="font-bold text-sm">Group number list</div>
          <CloseIcon
            className="h-3 w-3 cursor-pointer"
            onClick={() => {
              setSearchTerm("");
              onHide();
            }}
          />
        </div>

        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            border: `1px solid ${isFocused ? "#4B93FF" : "#D9D9D9"}`,
            borderRadius: "20px",
            maxWidth: "428px",
            height: "34px",
            fontSize: "14px",
            padding: "0 10px",
          }}
        >
          <div className="flex items-center w-full">
            <SearchIcon className="w-4 h-4 mr-2" />
            <input
              type="text"
              placeholder="Search group number"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              style={{
                width: "76%",
                border: "none",
                outline: "none",
                marginLeft: "4px",
              }}
            />
          </div>
          {isFocused && searchTerm.trim() && (
            <span className="whitespace-nowrap">
              {matchedNumbers.length} matched
            </span>
          )}
        </div>

        <div className="mt-5 h-[320px] border border-gray-300 p-4 rounded-lg text-[#707070]">
          {matchedNumbers.length > 0 ? (
            <Virtuoso
              style={{ height: "300px" }}
              totalCount={rows.length}
              itemContent={(index) => (
                <div className="grid grid-cols-4 gap-4 mb-4">
                  {rows[index].map((number, idx) => (
                    <div
                      key={idx}
                      className="p-2 bg-[#D9D9D933] text-xs text-black text-center"
                    >
                      {number.toString()}
                    </div>
                  ))}
                  {/* Add placeholder cells for the last row if needed */}
                  {rows[index].length < COLUMN_COUNT &&
                    Array(COLUMN_COUNT - rows[index].length)
                      .fill(null)
                      .map((_, emptyIdx) => (
                        <div key={`empty-${emptyIdx}`} className="p-2"></div>
                      ))}
                </div>
              )}
              components={{
                Footer: () => (
                  <div className="py-2">{/* Empty space at bottom */}</div>
                ),
              }}
            />
          ) : isLoading ? (
            <div className="text-center text-gray-500">Loading...</div>
          ) : (
            <div className="text-center text-gray-500">No matches found</div>
          )}
        </div>
      </div>
    </Dialog>
  );
}

export default ExpandListDialog;
