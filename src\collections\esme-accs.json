{"forms": [{"elements": [{"info": "Protocol used for communication: SMPP, HTTP", "name": "protocol", "size": 6, "type": "select", "title": "Protocol", "isInfo": true, "options": [{"label": "SMPP", "value": 5}, {"label": "HTTP", "value": 12}], "isMandatory": true, "nonEditable": true, "validations": [{"type": "required", "message": "Please select Protocol option"}], "defaultValue": 5, "validationType": "string"}, {"info": "System type of the ESME. For example, VMA for Voice Mail Application or BULK for bulk messages.", "name": "systemType", "size": 6, "type": "text", "title": "System type", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the System type"}, {"type": "matches", "regex": "^(?!^[a-zA-Z]+$)(?!^[0-9]+$)[a-zA-Z0-9_]+$", "message": "Only alphanumeric characters and underscore are allowed"}, {"type": "max", "value": 11, "message": "Max length allowed is 11"}], "defaultValue": "", "validationType": "string", "visibilityConditions": {"protocol": [5]}}, {"info": "The URL on which Delivery report has to be shared with customer.", "name": "responseUrl", "size": 6, "type": "text", "title": "DR response URL", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the DR response URL"}, {"type": "matches", "regex": "^(?!^[a-zA-Z]+$)(?!^[0-9]+$)[a-zA-Z0-9_]+$", "message": "Only alphanumeric characters and underscore are allowed"}, {"type": "max", "value": 11, "message": "Max length allowed is 11"}], "defaultValue": "", "validationType": "string", "visibilityConditions": {"protocol": [12]}}, {"info": "Port on which SMS Hub connects to an ESME application.", "name": "port", "size": 6, "type": "select", "title": "Port", "isInfo": true, "isAddButton": true, "isMandatory": true, "onClickPath": "ports", "validations": [{"type": "required", "message": "Please select the Port"}], "isDropDownApi": true, "alloweDropDownEdit": true}, {"info": "Login ID used by the client to connect to SMS Hub. The field validates the identity of the client attempting to connect to SMS Hub.", "name": "systemId", "size": 6, "type": "text", "title": "System ID", "isInfo": true, "isMandatory": true, "nonEditable": true, "validations": [{"type": "required", "message": "Please enter the System ID"}, {"type": "min", "value": 2, "message": "Min length allowed is 2"}, {"type": "max", "value": 16, "message": "Max length allowed is 16"}, {"type": "matches", "regex": "^(?![a-zA-Z]+$)[a-zA-Z0-9._-]+$", "message": "Numeric/alphanumeric characters with dot,underscore,hyphen are allowed"}], "defaultValue": "", "validationType": "string", "visibilityConditions": {"protocol": [5]}}, {"info": "Login ID used by the client to connect to SMS Hub. The field validates the identity of the client attempting to connect to SMS Hub.", "name": "username", "size": 6, "type": "text", "title": "Username", "isInfo": true, "isMandatory": true, "nonEditable": true, "validations": [{"type": "required", "message": "Please enter the Username"}, {"type": "min", "value": 2, "message": "Min length allowed is 2"}, {"type": "max", "value": 17, "message": "Max length allowed is 16"}, {"type": "alphaWithDot", "message": "Numeric/alphanumeric characters with dot,underscore,hyphen are allowed"}], "defaultValue": "", "validationType": "string", "visibilityConditions": {"protocol": [12]}}, {"info": "Account type of a client application. SMS Hub supports two types of accounts:\nSend Only: ESME client can send messages\nSend and Receive: ESME client can send and receive messages.", "name": "accountType", "size": 6, "type": "radio", "title": "Account type", "isInfo": true, "options": [{"label": "Send", "value": 0}, {"label": "Send and receive", "value": 1}], "defaultValue": 0}, {"info": "Delivery report should be shared over which HTTP method (GET/POST)", "name": "dlv<PERSON><PERSON><PERSON><PERSON>", "size": 6, "type": "radio", "title": "DR HTTP method", "isInfo": true, "options": [{"label": "Get", "value": 0}, {"label": "Post", "value": 1}], "isMandatory": true, "defaultValue": 0, "visibilityConditions": {"protocol": [12]}}, {"info": "Maximum number of parallel connections (sessions) that the client application can maintain with SMS Hub to transmit message packets.", "name": "maxTransmit", "size": 6, "type": "text", "title": "Maximum transmit(MSLA)", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Maximum transmit(MSLA)"}, {"type": "min", "value": 1, "message": "Min range allowed is 1"}, {"type": "max", "value": 30, "message": "Max range allowed is 30"}, {"type": "typeError", "message": "Please enter the Maximum transmit(MSLA) with numeric value"}], "defaultValue": "", "validationType": "number", "visibilityConditions": {"protocol": [5]}}, {"info": "Maximum number of parallel connections (sessions) \nthat the client application can maintain with SMS Hub to receive message packets.\n This option is enabled when Send and Receive radio button is selected in Account Type.", "name": "maxReceive", "size": 6, "type": "text", "title": "Maximum receiver(MSLA)", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Maximum Receiver(MSLA)"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 32, "message": "Max range allowed is 32"}, {"type": "typeError", "message": "Please enter the Maximum Receiver(MSLA) with numeric value"}], "defaultValue": "", "validationType": "number", "visibilityConditions": {"protocol": [5], "accountType": [1]}}, {"info": "Default Source TON to be considered for incoming message OM.", "name": "srcTon", "size": 6, "type": "select", "title": "Default Source TON", "isInfo": true, "options": [{"label": "Unknown", "value": 0}, {"label": "International", "value": 1}, {"label": "National", "value": 2}, {"label": "Alphanumeric", "value": 5}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Default Source TON"}], "defaultValue": "", "visibilityConditions": {"protocol": [12]}}, {"info": "Default Destination TON to be considered for incoming message destination MSISDN.", "name": "destTon", "size": 6, "type": "select", "title": "Default Destination TON", "isInfo": true, "options": [{"label": "Unknown", "value": 0}, {"label": "International", "value": 1}, {"label": "National", "value": 2}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Default Destination TON"}], "defaultValue": "", "visibilityConditions": {"protocol": [12]}}, {"info": "Password of the client. It is used to authenticate the identity of the client attempting to connect to SMS Hub.", "name": "passwd", "size": 6, "type": "text", "title": "Password", "isInfo": true, "isPassword": true, "isMandatory": true, "defaultValue": ""}, {"info": "Confirmation of the password.", "name": "confirmPassword", "size": 6, "type": "text", "title": "Confirm password", "isInfo": true, "isPassword": true, "isMandatory": true, "defaultValue": "", "validatePassword": true}, {"info": "Path of the SSL certificate to be used for DR over HTTPS", "name": "httpsDrFilepath", "size": 6, "type": "text", "title": "File path for DR over HTTPS", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the File path for DR over HTTPS"}], "defaultValue": "", "validationType": "string", "visibilityConditions": {"protocol": [12]}}, {"info": "Date on which a client account is activated.", "name": "activationDate", "size": 6, "type": "date", "title": "Date of activation", "isInfo": true, "minDate": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Date of activation"}], "defaultValue": ""}, {"info": "Date on which the client account expires.", "name": "terminationDate", "size": 6, "type": "date", "title": "Date of expiry", "isInfo": true, "minDate": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Date Of expiry"}, {"type": "greaterThan", "field": "activationDate", "message": "Date of Expiry cannot be before the Date of Activation"}], "defaultValue": ""}, {"info": "Default source address of the client. Message packets transmitted by a client contain the ’source address’ and the ’destination address’. A client transmitting messages to SMS Hub may, however, enter NULL in the Default Address field. In such a case, SMS Hub substitutes the source address with the default address. The parameter is particularly useful for interfaces unfamiliar with the concept of source address for a short message (for instance, voice mail systems).", "name": "defaultAddress", "size": 6, "type": "text", "title": "Default address", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the <PERSON><PERSON><PERSON> address"}, {"type": "onlySpecialCharNotAllowed", "regex": "^(?=.*[\\d,a-zA-Z])[a-zA-Z\\d,. _\\-]*[a-zA-Z\\d][a-zA-Z\\d,. _\\-]*$", "message": "Only hyphen,comma,dot,underscore and space are allowed with numeric/alphanumeric"}, {"type": "matches", "regex": "^(?:(?=.*[0-9])(?=.*[a-zA-Z])[a-zA-Z0-9._,\\-\\s]{1,11}|[0-9._,\\-\\s]{1,16})$", "message": "Only numeric (max 16) and alphanumeric(max 11) allowed. Only alphabets not allowed"}], "defaultValue": "", "validationType": "string"}, {"info": "Transmission precedence assigned by SMS Hub to the messages submitted by the client application. Four priority levels are supported:", "name": "maxPriority", "size": 6, "type": "select", "title": "Maximum priority", "isInfo": true, "options": [{"label": "0", "value": 0}, {"label": "1", "value": 1}, {"label": "2", "value": 2}, {"label": "3", "value": 3}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Maximum priority"}], "defaultValue": "", "validationType": "string"}, {"info": "Flag to enable or disable retry of messages on failure at SRI/MTS. Retry policy and retry rules should also be configured for this retry of message.", "name": "retryFlag", "size": 6, "type": "switch", "title": "Retry flag status", "isInfo": true, "options": [{"label": "Enabled", "value": 1, "checked": true}, {"label": "Disabled", "value": 0, "checked": false}], "defaultValue": 1}, {"info": "Indicates whether ESME is allowed to send messages in binary formats such as ring tones, logos, and pictures. The selection depends on the account type opted by the client:\nYes: Select if ESME is allowed to send messages in binary formats.\nNo: Select if ESME is not allowed to send binary messages.", "name": "binaryAllowed", "size": 6, "type": "switch", "title": "Binary number status", "isInfo": true, "options": [{"label": "Yes", "value": "Y", "checked": true}, {"label": "No", "value": "N", "checked": false}], "generalInfo": true, "defaultValue": "Y"}, {"info": "Indicates whether clients are allowed to transmit messages from the service numbers listed in the Access list:\nYes: Select to allow the ESME client to transmit messages with any numeric sender address. Applications do not receive an acknowledgement for the messages transmitted if this option is selected.\nNo: Select to allow the ESME client to transmit messages with numeric sender address configured for the account.", "name": "senderAllowed", "size": 6, "type": "switch", "title": "All service no allowed status", "isInfo": true, "options": [{"label": "Yes", "value": 1, "checked": true}, {"label": "No", "value": 0, "checked": false}], "defaultValue": 1}, {"info": "Indicates whether ESME can send alphanumeric sender address. The selection depends on the account type opted by the client.\nYes: ESME can send alphanumeric sender address.\nNo: ESME cannot send alphanumeric sender address\nAll Service Numbers Allowed is allowed, the sender address is taken from Source Whitelist Alphanumeric Senders.", "name": "alphaNumeric", "size": 6, "type": "switch", "title": "Alpha numeric allowed", "isInfo": true, "options": [{"label": "Yes", "value": "Y", "checked": true}, {"label": "No", "value": "N", "checked": false}], "generalInfo": true, "defaultValue": "N"}, {"info": "Type of operator:\nHome Network\nOTA (Over the Air)", "name": "operatorType", "size": 6, "type": "select", "title": "Operator type", "isInfo": true, "options": [{"label": "Home network", "value": 1}, {"label": "OTA", "value": 0}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Operator type"}], "defaultValue": "", "validationType": "number", "visibilityConditions": {"protocol": [5]}}, {"info": "Range of source address whitelisted (eg:1-10)", "name": "sourceWhitelistNumRange", "size": 6, "type": "ChipInput", "title": "Source whitelist number range (eg: 1-10)", "isInfo": true, "validation": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Source whitelist number range"}, {"type": "emptyArray", "message": "Please enter atleast one Source whitelist number range"}, {"type": "max", "value": 10, "message": "Max range allowed is 10"}, {"type": "numRange", "regex": "^\\d+\\s*-\\s*\\d+$", "message": "Please enter valid number range"}, {"type": "no-duplicates", "message": "Duplicate values not allowed"}], "defaultValue": "", "validationType": "array", "visibilityConditions": {"senderAllowed": [0]}}, {"info": "Alphanumeric source whitelisted", "name": "sourceWhitelistAlphaNumeric", "size": 6, "type": "ChipInput", "title": "Source whitelist alpha numeric (eg: abc12!@#,8)", "isInfo": true, "validation": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Source whitelist alpha numeric"}, {"type": "emptyArray", "message": "Please enter atleast one Source whitelist alpha numeric value"}, {"type": "max", "value": 10, "message": "Max allowed is 10"}, {"type": "alphanumeric-array", "message": "Please enter a valid alphanumeric value (format: alphanumeric word,length)"}, {"type": "no-duplicates", "message": "Duplicate values not allowed"}], "defaultValue": "", "validationType": "array", "visibilityConditions": {"alphaNumeric": ["Y"]}}, {"info": "List of IP addresses that can be used by a client to connect to SMS Hub.", "name": "accessList", "size": 6, "type": "ChipInput", "title": "Access list", "isInfo": true, "validation": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Access list"}, {"type": "emptyArray", "message": "Please enter atleast one Access list"}, {"type": "ip-validation", "message": "Please enter a valid IPv4 or IPv6 address"}, {"type": "unique-error-message", "message": "Please enter unique valid IPv4 or IPv6 address"}, {"type": "no-duplicates", "message": "Duplicate values not allowed"}], "defaultValue": "", "validationType": "array"}, {"name": "sourceWhitelislAlnum", "type": "none", "options": [{"label": "sourceWhitelistAlphaNumeric", "value": "A"}, {"label": "sourceWhitelistNumRange", "value": "N"}]}], "formName": "Account information"}, {"elements": [{"info": "Criteria to truncate a message to a predefined length and deliver or reject a message when the message exceeds the predefined length.\nYou can choose any one option:\nTruncate: SMS Hub truncates the message to its predefined length and delivers the message if the message length exceeds the predefined length.\nReject: SMS Hub rejects the message if the message exceeds the predefined length.", "name": "truncateMsg", "size": 6, "type": "radio", "title": "Message acceptance criteria", "isInfo": true, "options": [{"label": "Truncate", "value": 1}, {"label": "Reject", "value": 0}], "defaultValue": 0}, {"info": "Indicates whether SMS Hub converts special characters as per GSM standards:\nYes: Select if SMS Hub should convert special characters, such as @ and #, to conform to GSM standards.\nNo: Select if GSM conversion is not required.", "name": "gsmConvReqAt", "size": 6, "type": "switch", "title": "GSM conversion request(AT)", "isInfo": true, "options": [{"label": "Yes", "value": 1, "checked": true}, {"label": "No", "value": 0, "checked": false}], "fieldType": "number", "defaultValue": 0}, {"info": "Indicates whether SMS Hub converts special characters as per GSM standards:\nYes: Select if SMS Hub should convert special characters, such as @ and #, to conform to GSM standards.\nNo: Select if GSM conversion is not required.", "name": "gsmConvReqAo", "size": 6, "type": "switch", "title": "GSM conversion request(AO)", "isInfo": true, "options": [{"label": "Yes", "value": 1, "checked": true}, {"label": "No", "value": 0, "checked": false}], "defaultValue": 0}, {"info": "Indicates the validity period of AO messages.", "name": "aoValidityPeriod", "size": 6, "type": "text", "title": "AO validity period (secs)", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the AO validity period (secs)"}, {"type": "typeError", "message": "Please enter the AO validity period (secs) with numeric value"}, {"type": "integer", "message": "Please enter the AO validity period (secs) with integer value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 2592000, "message": "Max range allowed is 2592000"}], "defaultValue": "", "validationType": "number"}, {"info": "Indicates the validity period of MO-AT messages.", "name": "validityPeriod", "size": 6, "type": "text", "title": "MO-AT validity period (secs)", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the MO-AT validity period (secs)"}, {"type": "typeError", "message": "Please enter the MO-AT validity period (secs) with numeric value"}, {"type": "integer", "message": "Please enter the MO-AT validity period (secs) with integer value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 864000, "message": "Max range allowed is 864000"}], "defaultValue": "", "validationType": "number"}, {"info": "Indicates whether SMS Hub would send a delivery report to the MSISDNs after delivering the message.", "name": "del_rep_ao_mt", "size": 6, "type": "switch", "title": "Delivery report (AO-MT) status", "isInfo": true, "options": [{"label": "Enable", "value": 66, "checked": true}, {"label": "Disabled", "value": 67, "checked": false}], "defaultValue": 67}, {"info": "Indicates whether SMS Hub would send a delivery report to the ESME client after delivering the message.", "name": "fakeDelReport", "size": 6, "type": "switch", "title": "Delivery report (MO-AT) status", "isInfo": true, "options": [{"label": "Enable", "value": 1, "checked": true}, {"label": "Disabled", "value": 0, "checked": false}], "defaultValue": 0}, {"info": "Indicates whether SMS Hub should store and forward messages if DDA fails.", "name": "storeForwardEnable", "size": 6, "type": "radio", "title": "Store and forward enable", "isInfo": true, "options": [{"label": "AT", "value": 1}, {"label": "MT", "value": 2}, {"label": "Both", "value": 3}, {"label": "None", "value": 0}], "defaultValue": 0}, {"info": "Enable to send outbind messages from the ESME server to the ESME application if it is disconnected, so that the application can bind to the ESME server.", "name": "outBindEnable", "size": 6, "type": "switch", "title": "Out bind status", "isInfo": true, "options": [{"label": "Yes", "value": 1, "checked": true}, {"label": "No", "value": 0, "checked": false}], "defaultValue": 0, "visibilityConditions": {"protocol": [5]}}, {"info": "Flag to enable or disable previlage feature", "name": "priv_feature", "size": 6, "type": "switch", "title": "Previlage feature status", "isInfo": true, "options": [{"label": "Enable", "value": 1, "checked": true}, {"label": "Disabled", "value": 0, "checked": false}], "defaultValue": 0, "visibilityConditions": {"protocol": [5]}}, {"info": "Indicates whether the ESME account can receive in data_sm format.", "name": "supportDataSm", "size": 6, "type": "switch", "title": "Support data_sm status", "isInfo": true, "options": [{"label": "Enable", "value": 1, "checked": true}, {"label": "Disabled", "value": 0, "checked": false}], "defaultValue": 0, "visibilityConditions": {"protocol": [5]}}, {"info": "Indicates whether the ESME account address behaves as an MSISDN. One Mobile number is a normal MSISDN and work as a short code. Any messages received by SMS Hub with the destination as One Mobile number,\n the message is delivered to an application instead of a mobile.", "name": "one_mobile", "size": 6, "type": "switch", "title": "One mobile status", "isInfo": true, "options": [{"label": "Enable", "value": 1, "checked": true}, {"label": "Disabled", "value": 0, "checked": false}], "defaultValue": 0, "visibilityConditions": {"protocol": [5]}}, {"info": "Indicates whether the UDH information is to be sent through SMPP optional tags or as part of the message content.", "name": "sar_feature", "size": 6, "type": "switch", "title": "SAR feature status", "isInfo": true, "options": [{"label": "Enable", "value": 1, "checked": true}, {"label": "Disabled", "value": 0, "checked": false}], "defaultValue": 0, "visibilityConditions": {"protocol": [5]}}, {"info": "Prefix to be displayed with the sender’s number on the receiver’s handset at the time of message delivery.", "name": "senderPrefix", "size": 6, "type": "text", "title": "Sender prefix", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Sender prefix value"}, {"type": "alphaWithHyphen", "message": "Please enter only alphabets which should ends with hyphen"}, {"type": "max", "value": 4, "message": "Max length allowed is 3"}], "defaultValue": ""}, {"info": "Indicates whether to display prefix series with the sender’s number at the time of \nmessage delivery for application terminated and mobile terminated messages.", "name": "senderPrefixStatus", "size": 6, "type": "radio", "title": "Sender prefix status", "isInfo": true, "options": [{"label": "AT", "value": 1}, {"label": "MT", "value": 2}, {"label": "Both", "value": 3}, {"label": "None", "value": 0}], "defaultValue": 0, "validationType": "string"}, {"info": "Select the ID of the type of messages (submit_sm response messages, deliver_sm response messages).", "name": "msgIdType", "size": 6, "type": "select", "title": "Message type ID", "isInfo": true, "options": [{"label": "SubsmResp:Dec, Divr. Dec", "value": 0}, {"label": "SubsmResp:<PERSON><PERSON>, Divr Dec", "value": 1}, {"label": "SubsmResp:Dec, Divr: Hex", "value": 2}, {"label": "SubsmResp: Hex, DIvr: Hex", "value": 3}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Message type ID "}], "defaultValue": "", "validationType": "string"}, {"info": "Flag to enable or disable the nick name feature for the ESME Accounts. Enable to replace the sender address with the configured nick name. Disable if the configured ESME account does not want the number to be replaced with the nick name.", "name": "nick_name_enable", "size": 6, "type": "switch", "title": "Nick name status", "isInfo": true, "options": [{"label": "Yes", "value": 1, "checked": true}, {"label": "No", "value": 0, "checked": false}], "defaultValue": 1}, {"info": "Flag to enable or disable the cell broadcast feature.", "name": "cb_enable", "size": 6, "type": "switch", "title": "Cell broadcast status", "isInfo": true, "options": [{"label": "Yes", "value": 1, "checked": true}, {"label": "No", "value": 0, "checked": false}], "defaultValue": 0}], "formName": "Message information"}, {"elements": [{"info": "For a ”Send Only” account, Messages Per Second specifies the number of messages that the client application can transmit in a second.\nFor a ”Send and Receive” account, Messages Per Second specifies the number of messages that the client application can send and receive in a second.", "name": "msgPerSecond", "size": 6, "type": "text", "title": "Messages per second", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Messages per second"}, {"type": "typeError", "message": "Please enter the Messages per second with numeric value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 900, "message": "Max range allowed is 900"}, {"type": "integer", "message": "Please enter the Messages per second with integer value"}], "defaultValue": "", "validationType": "number", "visibilityConditions": {"protocol": [5]}}, {"info": "Transmission threshold for a minute. The field is automatically populated when an operator assigns the number of message per second for a client.\nFor example, if a client can transmit 1 message per second, the number of messages per minute is 60.", "name": "msgPerMinute", "size": 6, "type": "text", "title": "Messages per minute", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please Enter the Messages per minute"}, {"type": "typeError", "message": "Please enter the Messages per minute with numeric value"}, {"type": "greaterThan", "field": "msgPerSecond", "message": "Value cannot be less than Messages per second"}, {"type": "integer", "message": "Please enter the Messages per minute with integer value"}], "defaultValue": "", "validationType": "number", "visibilityConditions": {"protocol": [5]}}, {"info": "Transmission threshold for an hour. The field is automatically populated when an operator assigns the number of message per second for a client.", "name": "msgPerHour", "size": 6, "type": "text", "title": "Messages per hour", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please Enter the Messages per hour"}, {"type": "typeError", "message": "Please enter the Messages per hour with numeric value"}, {"type": "greaterThan", "field": "msgPerMinute", "message": "Value cannot be less than Messages per minute"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 39600, "message": "Max range allowed is 39600"}, {"type": "integer", "message": "Please enter the Messages per hour with integer value"}], "defaultValue": "", "validationType": "number", "visibilityConditions": {"protocol": [5]}}, {"info": "Transmission threshold for an hour. The field is automatically populated when an operator assigns the number of message per second for a client.", "name": "MsgsPerDay", "size": 6, "type": "text", "title": "Messages per day", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please Enter the Messages per day"}, {"type": "typeError", "message": "Please enter the Messages per day with numeric value"}, {"type": "greaterThan", "field": "msgPerHour", "message": "Value cannot be less than Messages per hour"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 39600, "message": "Max range allowed is 39600"}, {"type": "integer", "message": "Please enter the Messages per day with integer value"}], "defaultValue": "", "validationType": "number", "visibilityConditions": {"protocol": [5]}}, {"info": "Maximum length of the message that can be transmitted by a client application.", "name": "maxMessage<PERSON><PERSON><PERSON>", "size": 6, "type": "text", "title": "Message length", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Message length"}, {"type": "typeError", "message": "Please enter the Message length with numeric value"}, {"type": "integer", "message": "Please enter the Message length with integer value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 8000, "message": "Max range allowed is 8000"}], "defaultValue": "", "validationType": "number"}, {"info": "Maximum chunk of messages to be sent in a burst.", "name": "atWindowSize", "size": 6, "type": "text", "title": "AT window size", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the AT window size"}, {"type": "typeError", "message": "Please enter the AT window size with numeric value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 99999, "message": "Max range allowed is 99999"}, {"type": "integer", "message": "Please enter the AT window size with integer value"}], "defaultValue": "", "validationType": "number"}, {"info": "Flag to enable or disable ESME prepaid", "name": "creditBalance", "size": 6, "type": "switch", "title": "ESME prepaid status", "isInfo": true, "options": [{"label": "Yes", "value": 1, "checked": true}, {"label": "No", "value": 0, "checked": false}], "defaultValue": 0, "visibilityConditions": {"protocol": [5]}}, {"info": "Flag to enable or disable the prepaid configuration. If enabled, the credits information has to be added.", "name": "esme_ssl_enabled", "size": 6, "type": "switch", "title": "ESME prepaid enable", "isInfo": true, "options": [{"label": "Yes", "value": 1, "checked": true}, {"label": "No", "value": 0, "checked": false}], "defaultValue": 0, "visibilityConditions": {"protocol": [12]}}, {"info": "Number of messages to be allowed per sec.", "name": "secthrotblock", "size": 6, "type": "switch", "title": "Reject as throttle per sec", "isInfo": true, "options": [{"label": "Enabled", "value": 1, "checked": true}, {"label": "Disabled", "value": 0, "checked": false}], "defaultValue": 0, "visibilityConditions": {"protocol": [5]}}, {"name": "accountEnable", "type": "none", "options": [{"label": "Active", "value": "Y"}, {"label": "Inactive", "value": "N"}, {"label": "Expired", "value": "E"}, {"label": "Disabled", "value": "D"}]}], "formName": "Capacity information"}], "header": "List of ESME accounts", "columns": [{"header": "Account ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "System ID", "accessorKey": "attributes.systemId"}, {"header": "Account/Protocol type", "accessorKey": "attributes.protocol"}, {"id": "activationDate", "header": "Activation date", "isDateTime": true, "accessorKey": "attributes.activationDate", "filterVariant": "datetime-range", "dateTimeFormat": "DD-MMM-YYYY"}, {"id": "terminationDate", "header": "Expiry date", "isDateTime": true, "accessorKey": "attributes.terminationDate", "filterVariant": "datetime-range", "dateTimeFormat": "DD-MMM-YYYY"}, {"header": "Account status", "isToggle": true, "accessorKey": "attributes.accountEnable"}, {"buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "formType": "stepper", "buttonName": "+ Add ESME account", "moduleData": "esme-account", "moduleName": "ESME Account", "globalSearch": [{"label": "Account ID", "value": "id"}, {"label": "System ID", "value": "systemId"}], "navigationPath": true, "dropdownOptions": {"field": "protocol", "options": [{"label": "All protocol type", "query": "", "value": "ALL"}, {"label": "SMPP", "query": "filters[protocol][$eq]=5", "value": 5}, {"label": "HTTP", "query": "filters[protocol][$eq]=12", "value": 12}], "defaultQuery": "", "defaultValue": "ALL"}}