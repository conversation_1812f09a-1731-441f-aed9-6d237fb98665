import React, { useState, useMemo } from "react";

// create context
export const HeaderContext = React.createContext();

export const HeaderProvider = ({ children }) => {
  const [headerTitle, setHeaderTitle] = useState("Dashboard");

  const value = useMemo(() => {
    const setPageTitle = (val) => {
      setHeaderTitle(val);
    };

    return {
      headerTitle,
      setPageTitle,
    };
  }, [headerTitle]);

  return (
    <HeaderContext.Provider value={value}>{children}</HeaderContext.Provider>
  );
};
