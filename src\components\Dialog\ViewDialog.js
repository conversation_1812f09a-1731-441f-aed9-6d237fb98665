import Dialog from "@mui/material/Dialog";
import { CancelIcon } from "../../icons";
import { useQuery } from "react-query";
import { useState, useEffect } from "react";
import { getDataById } from "../../lib/list-api";

function ViewDialog({ show, onHide, viewDetails }) {
  const [formData, setFormData] = useState({});
  const [columnData, setColumnData] = useState([]);

  const id = viewDetails?.row?.original?.id;
  const collectionName = viewDetails?.collectionName;
  const moduleName = viewDetails?.schemaDetails?.moduleName;

  useEffect(() => {
    if (viewDetails?.schemaDetails?.columns) {
      const columns = viewDetails.schemaDetails.columns.filter(
        (item) => item.header && item.accessorKey && item.header !== "S.no"
      );
      setColumnData(columns);
    }
  }, [viewDetails]);

  useQuery([collectionName, id], getDataById, {
    enabled: id > 0 ? true : false,
    refetchOnWindowFocus: false,
    onSuccess: ({ data }) => {
      setFormData(data?.data?.attributes);
    },
  });

  console.log("formData", formData);

  const getSelectLabel = (column, value) => {
    const element = viewDetails?.schemaDetails?.elements?.find(
      (el) => el.name === column.accessorKey.split(".").pop()
    );
    if (element?.type === "select") {
      const option = element.options.find((opt) => opt.value === value);
      return option ? option.label : value;
    }
    return value;
  };

  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 565,
          maxHeight: 571,
          borderRadius: "10px",
        },
      }}
      maxWidth="xs"
      open={show}
      onClose={onHide}
    >
      <div className="bg-bgBox w-full h-14 justify-center flex items-center">
        <div className="text-white">{`View ${moduleName}`}</div>
      </div>
      <div
        className="flex justify-end absolute right-0 top-0"
        style={{ cursor: "pointer" }}
        onClick={() => onHide()}
      >
        <CancelIcon className="flex items-center mx-3 my-3 " />
      </div>
      <div className="flex justify-center my-10">
        <div className="grid grid-cols-2 gap-x-32 gap-5">
          {columnData.length > 0 ? (
            columnData.map((column, index) => (
              <div key={index} className="flex flex-col">
                <div className="text-textNAColor text-sm font-normal">
                  {column.header}
                </div>
                <div className="text-black text-sm font-normal mt-2">
                  {column.header === "RI"
                    ? 0
                    : getSelectLabel(
                        column,
                        formData[column.accessorKey.split(".").pop()]
                      )}
                </div>
                <div className="border-b border-viewBorder mt-2 w-5" />
              </div>
            ))
          ) : (
            <div>No data available</div>
          )}
        </div>
      </div>
    </Dialog>
  );
}

export default ViewDialog;
