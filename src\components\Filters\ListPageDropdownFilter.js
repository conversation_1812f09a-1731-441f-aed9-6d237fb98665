import { Formik, Form } from "formik";
import React, { useEffect, useState } from "react";
import Select from "../FormsUI/Select";
import { useQuery } from "react-query";
import { getCountryNameData } from "../../lib/list-api";
import { moduleConfiguration } from "../../common/constants";

function ListPageDropdownFilter({
  setDropdownFilter,
  dropdownDetails,
  defaultValue,
  defaultQuery,
  collectionName,
  limitPerPage,
  currentPage,
}) {
  const [optionsData, setOptionsData] = useState([]);

  useEffect(() => {
    if (defaultQuery) {
      setDropdownFilter(defaultQuery);
    }
  }, []);

  useQuery(
    ["operators", "countryName", { limit: -1 }, limitPerPage, currentPage],
    getCountryNameData,
    {
      enabled: collectionName === moduleConfiguration.operators,
      onSuccess: (response) => {
        const countryName = response?.data?.data.map((item) => ({
          label: item.attributes.countryName,
          value: item.attributes.countryName.toLowerCase(),
        }));

        const uniqueCountryNames = Array.from(
          new Map(countryName.map((item) => [item.value, item])).values()
        );

        uniqueCountryNames.sort((a, b) => a.label.localeCompare(b.label));

        const allOption = {
          value: "All",
          label: "All country name",
          query: "",
        };
        setOptionsData([allOption, ...uniqueCountryNames]);
      },
    }
  );

  return (
    <div>
      {" "}
      <Formik
        initialValues={{
          type: defaultValue ? defaultValue : "",
        }}
      >
        {({ values, errors, status }) => (
          <Form>
            <div className="w-[200px] ">
              <Select
                name="type"
                options={
                  collectionName === "operators" ? optionsData : dropdownDetails
                }
                placeholder={"Select LCR type"}
                bgColor="#FFFFFF"
                borderRadius="20px"
                onChange={(selectedOption) => {
                  setDropdownFilter(
                    collectionName === "operators"
                      ? selectedOption.value === "All"
                        ? selectedOption.query
                        : `filters[countryName][$eq]=${selectedOption.value}`
                      : selectedOption.query
                  );
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}

export default ListPageDropdownFilter;
