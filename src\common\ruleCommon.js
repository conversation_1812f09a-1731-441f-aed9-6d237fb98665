import React from "react";
import { Grid } from "@mui/material";
import InputLabel from "../components/FormsUI/InputLabel";
import TextFieldWrapper from "../components/FormsUI/TextField";
import Select from "../components/FormsUI/Select";
import DatePickerFieldWrapper from "../components/FormsUI/DatePicker";
import CustomSwitch from "../components/ToggleSwitch/CustomSwitch";
import CustomRadioButton from "../components/RadioButton/RadioButton";
import Dropdown from "../components/FormsUI/MultiSelect/MultiSelect";
import * as Yup from "yup";
import generateValidations from "./generateValidations";
import { getTrimmedUrl } from "./urlUtils";
import ShortCodeInput from "../components/Chip";
import OutlinedButton from "../components/Buttons/OutlinedButton";
import { ErrorMessage } from "formik";
export const renderFields = (
  fields,
  values,
  setFieldValue,
  navigate,
  formData,
  action,
  moduleName
) => {
  const url = getTrimmedUrl();
  return fields.map((field, i) => {
    let {
      name,
      title,
      size,
      type,
      isMandatory,
      options,
      placeholder,
      isAddButton,
      onClickPath,
      defaultValue,
      apiOptions,
      isSearch = true,
      nonEditable,
      isPassword,
      visibilityConditions,
      isDropDownApi,
      queryInfo,
      navigationLink,
      buttonName,
      generalInfo,
      isInfo,
      info,
      filter,
      isDisabled,
      isCurrentDate,
    } = field;

    let shouldShowField = false;

    if (visibilityConditions) {
      shouldShowField = Object.keys(visibilityConditions).every(
        (conditionKey) =>
          visibilityConditions[conditionKey].some(
            (conditionValue) =>
              String(conditionValue).toLowerCase() ===
                String(values[conditionKey]).toLowerCase() ||
              String(conditionValue).toLowerCase() ===
                String(formData[conditionKey]).toLowerCase()
          )
      );

      if (!shouldShowField) return <></>;
    }

    return (
      <Grid item xs={12} md={size} key={i}>
        <InputLabel
          label={title}
          isMandatory={isMandatory}
          isAddButton={isAddButton}
          color={"text-bgSecondary"}
          isInfo={isInfo}
          info={info}
          action={action}
          onClick={
            isAddButton
              ? () => {
                  navigate(`/app/list/${onClickPath}/add`, {
                    state: {
                      url: url,
                    },
                  });
                }
              : null
          }
        ></InputLabel>
        {(() => {
          switch (type) {
            case "text":
              return (
                <TextFieldWrapper
                  name={name}
                  isDisabled={
                    action === "viewData" ||
                    (action === "edit" && nonEditable === true)
                      ? true
                      : false
                  }
                  placeholder={
                    placeholder ? placeholder : "Enter " + title.toLowerCase()
                  }
                  applyOnChange={
                    moduleName === "redirectional-accounts" ? true : false
                  }
                  isPassword={isPassword}
                  type={isPassword === true ? "password" : "text"}
                ></TextFieldWrapper>
              );
            case "select":
              return (
                <Select
                  name={name}
                  isDisabled={action === "edit" && nonEditable ? true : false}
                  options={options}
                  placeholder={
                    placeholder ? placeholder : "Select " + title.toLowerCase()
                  }
                  isDropDownApi={isDropDownApi}
                  collectionName={onClickPath}
                  values={values}
                  filter={filter}
                  queryInfo={queryInfo}
                  moduleName={moduleName}
                ></Select>
              );
            case "date":
              return (
                <>
                  <DatePickerFieldWrapper
                    name={name}
                    isDisabled={
                      isDisabled ||
                      action === "viewData" ||
                      (action === "edit" && nonEditable === true)
                        ? true
                        : false
                    }
                    defaultValue={defaultValue}
                    action={action}
                    isCurrentDate={isCurrentDate}
                    {...field}
                  ></DatePickerFieldWrapper>
                </>
              );
            case "switch":
              return (
                <CustomSwitch
                  name={name}
                  generalInfo={generalInfo}
                  {...field}
                  isDisabled={
                    action === "viewData" ||
                    (action === "edit" && nonEditable === true)
                      ? true
                      : false
                  }
                  options={options}
                  defaultValue={defaultValue}
                  action={action}
                ></CustomSwitch>
              );
            case "radio":
              return (
                <CustomRadioButton
                  name={name}
                  {...field}
                  isDisabled={
                    action === "viewData" ||
                    (action === "edit" && nonEditable === true)
                      ? true
                      : false
                  }
                  options={options}
                  defaultValue={defaultValue}
                ></CustomRadioButton>
              );
            case "multiselect":
              return (
                <>
                  <Dropdown
                    data={[]}
                    onSelectionChange={(selectedDetail) => {
                      const ids = selectedDetail
                        .map((item) => (item.id !== "" ? item.id : null))
                        .filter((id) => id !== null);
                      setFieldValue(field.name, ids);
                    }}
                    isSearch={isSearch}
                    defaultSelectedData={defaultValue}
                    apiOptions={apiOptions}
                    collectionName={onClickPath}
                    values={values}
                    moduleName={moduleName}
                    name={name}
                    disabled={
                      action === "viewData" ||
                      (action === "edit" && nonEditable === true)
                        ? true
                        : false
                    }

                    // setOptionsDataOut={setOptionsDataOut}
                  />
                  <ErrorMessage
                    name={name}
                    component="div"
                    className="text-errorColor text-[11px] mt-0.5"
                  />
                </>
              );

            case "ChipInput":
              return (
                <ShortCodeInput
                  name={name}
                  value={values[name] || []}
                  onChange={(chips) => {
                    setFieldValue(name, chips);
                  }}
                  isDisabled={action === "viewData" ? true : false}
                />
              );
            case "button":
              return (
                <OutlinedButton
                  label={buttonName}
                  buttonClassName="w-[150px] h-[40px] text-xs mt-4 rounded-[10px]"
                  onClick={() => {
                    navigate(navigationLink);
                  }}
                />
              );

            case "none":
              return <div></div>;
            default:
              return <div>No element found</div>;
          }
        })()}
      </Grid>
    );
  });
};

export const getStepperDefaultInitialValues = (elements, formData) => {
  let values = {};
  elements.forEach((x) => {
    values[x.name] = formData[x.name] ? formData[x.name] : x.defaultValue || "";
  });

  return values;
};

export const generateValidationSchema = (elements, formData) => {
  let validationFields = {};

  elements?.forEach((x) => {
    const schema = generateValidations(x);
    if (x.visibilityConditions) {
      const conditionKeys = Object.keys(x.visibilityConditions);

      validationFields[x.name] = Yup.string().when(conditionKeys, {
        is: (...values) => {
          const conditionMet = conditionKeys?.every((key, index) => {
            const conditionValues = x?.visibilityConditions[key];
            const fieldValue = formData?.[key] ?? values[index] ?? "";
            return Array.isArray(conditionValues)
              ? conditionValues.some((value) => {
                  return value === fieldValue;
                })
              : conditionValues === fieldValue;
          });

          return conditionMet;
        },
        then: () => schema,
        otherwise: () => Yup.string(),
      });
    } else {
      validationFields[x.name] = schema;
    }
  });

  return validationFields;
};
