import * as React from "react";
import Box from "@mui/material/Box";
import Stepper from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import StepConnector from "@mui/material/StepConnector";
import { styled } from "@mui/material/styles";

const CustomConnector = styled(StepConnector)(() => ({
  [`& .MuiStepConnector-line`]: {
    borderWidth: 3, // Thicker line
    marginTop: 5, // Adjust margin
  },
}));

function StepperHeader(props) {
  return (
    <Box sx={{ width: "100%" }}>
      <Stepper
        activeStep={props.atStep}
        alternativeLabel
        connector={<CustomConnector />}
      >
        {props?.steps?.map((label, index) => (
          <Step
            key={label}
            sx={{
              "& .MuiStepLabel-root .Mui-completed": {
                color: "#DC3833", // circle color (COMPLETED)
              },
              "& .MuiStepLabel-root .Mui-active": {
                color: "#DC3833", // circle color (ACTIVE)
              },
              "& .MuiStepLabel-label": {
                color: index === props.atStep ? "#DC3833" : "black", // active step red, others black
                fontWeight: index === props.atStep ? "bold" : "normal", // bold for the active step
              },
              "& .MuiStepLabel-root .Mui-active .MuiStepIcon-text": {
                fill: "white", // circle's number (ACTIVE)
              },
              "& .MuiStepIcon-root": {
                width: 40, // increase circle size
                height: 40, // increase circle size
                color: "#D9D9D9",
              },

              "& .MuiStepConnector-line": {
                borderColor:
                  props.atStep >= index
                    ? "#DC3833 !important"
                    : "#D9D9D9 !important", // Change color dynamically
              },
            }}
          >
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
    </Box>
  );
}

export default StepperHeader;
