import React from "react";
import { useState } from "react";
import { Box, Link, NavLink } from "@mui/material";
import { useQuery } from "react-query";
import { getAboutUsData } from "../lib/list-api";
import Button from "../components/Buttons/OutlinedButton";
import EndUserLicenseAgreement from "../components/Dialog/EndUserLicenseAgreement";

const AboutUs = () => {
  const [open, setOpen] = useState(false);
  const styles = {
    leftPanel: "font-semibold text-white py-4",
    rightPanel: "py-4 border-b border-black border-opacity-50",
    noBorder: "py-4",
    link: "text-errorColor decoration-customColor cursor-pointer",
    linkBlack: "text-black cursor-pointer",
  };

  const { data, isLoading } = useQuery("aboutus", getAboutUsData, {
    onSuccess: (data) => {},
  });

  if (isLoading) return <div>Loading...</div>;

  const aboutusData = data?.data?.data[0]?.attributes?.schemadetails;

  if (!aboutusData) return <div>No data available</div>;

  const { aboutusInfo, leftPanel, values } = aboutusData;

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  // Define the order of the keys for both panels
  const orderedKeys = [
    "productName",
    "productVersion",
    "productPlatform",
    "vendor",
    "svnRevisionDate",
    "patchDate",
    "supportContact",
    "aboutUs",
    "smspLiscense",
    "endUserLiscenseAgreement",
  ];

  return (
    <Box>
      <div className="mb-4 text-title font-bold sticky top-0 bg-bgBody h-[60px] z-10 content-center mt-0">
        About us
      </div>
      <Box className="flex text-subtitle">
        <div className="w-1/3 px-8 py-4 rounded-tl-lg rounded-bl-lg bg-bgAboutUs">
          {orderedKeys.map((key, index) => (
            <div key={index} className={styles.leftPanel}>
              {leftPanel[key]}
            </div>
          ))}
        </div>

        <Box className="w-2/3 bg-white px-8 py-4 rounded-tr-lg rounded-br-lg">
          {orderedKeys.map((key, index) => {
            const value = aboutusInfo[key];
            const rightPanelStyle =
              key === "endUserLiscenseAgreement"
                ? styles.noBorder
                : styles.rightPanel;
            const href =
              key === "supportContact"
                ? values.href
                : key === "aboutUs"
                ? values.aboutus
                : values.view;
            const linkClass =
              key === "endUserLiscenseAgreement"
                ? `${styles.linkBlack} ${"ml-0 pl-0 underline"}`
                : `${styles.link} `;

            if (
              key === "supportContact" ||
              key === "aboutUs" ||
              key === "smspLiscense"
            ) {
              return (
                <Box key={index} className={rightPanelStyle}>
                  <Link
                    href="https://www.google.com"
                    target="_blank"
                    rel="noopener noreferrer"
                    className={linkClass}
                  >
                    {value}
                  </Link>
                </Box>
              );
            }

            if (key === "endUserLiscenseAgreement") {
              return (
                <Box className={`${rightPanelStyle} py-0`}>
                  <Button
                    label={"View"}
                    onClick={handleClickOpen}
                    rel="noopener"
                    target="_blank"
                    buttonClassName={`${linkClass} border-none`}
                  />
                </Box>
              );
            }

            return (
              <div key={index} className={rightPanelStyle}>
                {value}
              </div>
            );
          })}
        </Box>
      </Box>
      <EndUserLicenseAgreement open={open} handleClose={handleClose} />
    </Box>
  );
};

export default AboutUs;
