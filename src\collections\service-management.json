{"forms": [{"elements": [{"name": "block_del_rpt", "size": 6, "type": "switch", "title": "Blocking of delivery report", "fieldType": "number", "defaultValue": 0, "options": [{"checked": true, "label": "Active", "value": 57}, {"checked": false, "label": "Inactive", "value": 0}]}, {"name": "anti_spamming_enable", "size": 6, "type": "switch", "title": "Anti - spamming enable", "fieldType": "number", "defaultValue": 0, "options": [{"checked": true, "label": "Active", "value": 1}, {"checked": false, "label": "Inactive", "value": 0}]}, {"name": "override_reply_path", "size": 6, "type": "switch", "title": "Override reply path", "fieldType": "number", "defaultValue": 255, "options": [{"checked": true, "label": "Active", "value": 127}, {"checked": false, "label": "Inactive", "value": 255}]}, {"size": 6, "type": "none"}, {"name": "max_len_at_addr", "size": 6, "type": "text", "title": "Max length of short code", "isMandatory": true, "validations": [{"type": "required", "message": "Please Enter Max Length of Short Code"}, {"type": "typeError", "message": "Please enter the Max Length of Short Code with numeric value"}, {"type": "min", "value": 1, "message": "Please enter the Max Length of Short Code with valid Integer value.\nIt Should be greater than 0 and less or equal to 16"}, {"type": "max", "value": 16, "message": "Please enter the Max Length of Short Code with valid Integer value.\nIt Should be greater than 0 and less or equal to 16"}, {"type": "integer", "message": "Please Enter the Max Length of Short Code with valid Integer value"}], "defaultValue": "", "validationType": "number"}, {"name": "min_len_msisdn", "size": 6, "type": "text", "title": "Max length of MSISDN", "isMandatory": true, "validations": [{"type": "required", "message": "Please Enter Max Length of MSISDN"}, {"type": "typeError", "message": "Please enter the Min Length of MSISDN with numeric value."}, {"type": "min", "value": 2, "message": "Please enter the Min length of MSISDN Code with valid Integer value.\nIt should be greater than 2 and less or equal to 16"}, {"type": "max", "value": 16, "message": "Please enter the Min length of MSISDN Code with valid Integer value.\nIt should be greater than 2 and less or equal to 16"}, {"type": "integer", "message": "Please Enter the Min Length of MSISDN with valid Integer value."}], "defaultValue": "", "validationType": "number"}, {"name": "max_message_len", "size": 6, "type": "text", "title": "Maximum message length", "isMandatory": true, "validations": [{"type": "required", "message": "Please Enter Maximum message length"}, {"type": "typeError", "message": "Please enter the Max Length of Message with numeric value."}, {"type": "min", "value": 1, "message": "Please enter the Max Length of Message with valid Integer value.\nIt Should be greater than 0 or equal to 16"}, {"type": "max", "value": 16, "message": "Please enter the Max Length of Message with valid Integer value.\nIt Should be greater than 0 and less or equal to 16"}, {"type": "integer", "message": "Please enter the Max Length of Message with valid Integer value."}], "defaultValue": "", "validationType": "number"}, {"name": "redirection_retry_enable", "size": 6, "type": "switch", "title": "Redirection retry enable", "fieldType": "number", "defaultValue": 0, "options": [{"checked": true, "label": "Active", "value": 1}, {"checked": false, "label": "Inactive", "value": 0}]}, {"name": "sender_id_prefix_flag", "size": 6, "type": "radio", "title": "Sender Id prefix enable", "options": [{"label": "AT", "value": "AT"}, {"label": "MT", "value": "MT"}, {"label": "Both", "value": "Both"}], "visible": true, "defaultValue": null, "validationType": "string"}, {"name": "sender_prefix", "size": 6, "type": "text", "title": "Sender prefix", "isMandatory": true, "validations": [{"type": "matches", "regex": "^[A-Za-z]+-$", "message": "Only alphabets is allowed and ends with hypen(-)"}, {"type": "min", "value": 3, "message": "Min length of sender profile is 3"}, {"type": "max", "value": 5, "message": "Max length of sender profile is 5"}, {"type": "required", "message": "Please enter sender profile values"}], "defaultValue": "", "validationType": "string"}, {"name": "default_validity", "size": 6, "type": "text", "title": "Default validity", "isMandatory": true, "validations": [{"type": "required", "message": "Please enter Default Validity"}, {"type": "matches", "regex": "^\\d+$", "message": "Please enter only positive numeric value for Default Validity."}, {"type": "matches", "regex": "^[1-9]\\d*(?:\\.\\d+)?$|^0?\\.\\d*[1-9]\\d*$", "message": "Default Validity should be greater than or equal to 1 and not more than 720 digits"}, {"type": "max", "value": 720, "message": "Default Validity should be greater than or equal to 1 and not more than 720 digits"}], "defaultValue": "", "validationType": "string"}, {"name": "special_prefix", "size": 6, "type": "ChipInput", "title": "Special prefix (Press enter to add Special prefix)", "fieldBased": "senderPrefix", "validation": true, "isMandatory": true, "defaultValue": [], "validationType": "array", "validationConfig": {"maxValue": {"value": 10, "message": "Maximum 10 digits allowed"}, "minValue": {"value": 2, "message": "Minimum 2 digits required"}, "uniqueError": "Repeated values not allowed", "integerError": "Only numeric values are allowed", "maxArrayLength": {"value": 10, "message": "Max 10 values allowed"}, "minArrayLength": {"value": 1, "message": "Please enter special prefix values"}}}, {"name": "sys_currency", "size": 6, "type": "select", "title": "System currency", "options": [{"label": "EURO", "value": "EURO"}, {"label": "USD", "value": "USD"}], "isMandatory": true, "defaultValue": "EURO"}, {"size": 6, "type": "none"}, {"name": "euro_usd", "size": 6, "type": "text", "title": "Currency multiplier (EURO to USD)", "validations": [{"type": "matches", "regex": "^\\d{1,3}(\\.\\d{1,4})?$", "message": "Please enter currency multiplier as 'xxx.xxxx'"}, {"type": "nullable"}], "defaultValue": null, "validationType": "string"}, {"name": "usd_euro", "size": 6, "type": "text", "title": "Currency multiplier (USD to EURO)", "validations": [{"type": "matches", "regex": "^\\d{1,3}(\\.\\d{1,4})?$", "message": "Please enter currency multiplier as 'xxx.xxxx'"}, {"type": "nullable"}], "defaultValue": null, "validationType": "string"}, {"name": "is_disabled_mnp_exp", "size": 6, "type": "switch", "title": "Is exceptional CC for MNP required?", "fieldType": "number", "defaultValue": 0, "options": [{"checked": true, "label": "Active", "value": 1}, {"checked": false, "label": "Inactive", "value": 0}]}, {"size": 6, "type": "none", "dynamic": {"field": "is_disabled_mnp_exp", "value": 0}}, {"name": "exp_cc_for_mnp", "size": 6, "type": "ChipInput", "title": "Exceptional CC for MNP Disable (Press enter to add)", "dynamic": {"field": "is_disabled_mnp_exp", "value": 1}, "fieldBased": "senderPrefix", "validation": true, "isMandatory": true, "defaultValue": [], "validationType": "array", "validationConfig": {"maxValue": {"value": 5, "message": "Maximum 5 digits allowed"}, "minValue": {"value": 2, "message": "Minimum 2 digits required"}, "uniqueError": "Repeated values not allowed", "integerError": "Only numeric values are allowed", "maxArrayLength": {"value": 1, "message": "Max 1 values allowed"}, "minArrayLength": {"value": 1, "message": "Please enter exceptional CC for MNP Disable"}}}, {"name": "is_enabled_dnd_exp", "size": 6, "type": "switch", "title": "Is CC for DND required?", "fieldType": "number", "defaultValue": 0, "options": [{"checked": true, "label": "Active", "value": 1}, {"checked": false, "label": "Inactive", "value": 0}]}, {"size": 6, "type": "none", "dynamic": {"field": "is_enabled_dnd_exp", "value": 0}}, {"name": "exp_cc_for_dnd", "size": 6, "type": "ChipInput", "title": "Exceptional CC for DND enable (Press enter to add)", "dynamic": {"field": "is_enabled_dnd_exp", "value": 1}, "fieldBased": "senderPrefix", "validation": true, "isMandatory": true, "defaultValue": [], "validationType": "array", "validationConfig": {"maxValue": {"value": 5, "message": "Maximum 5 digits allowed"}, "minValue": {"value": 2, "message": "Minimum 2 digits required"}, "uniqueError": "Repeated values not allowed", "integerError": "Only numeric values are allowed", "maxArrayLength": {"value": 1, "message": "Max 1 values allowed"}, "minArrayLength": {"value": 1, "message": "Please enter exceptional CC for DND enable"}}}, {"name": "is_enabled_vip", "size": 6, "type": "switch", "title": "VIP customer configuration", "fieldType": "number", "defaultValue": 0, "options": [{"checked": true, "label": "Active", "value": 1}, {"checked": false, "label": "Inactive", "value": 0}]}, {"size": 6, "type": "none", "dynamic": {"field": "is_enabled_vip", "value": 1}}, {"name": "A2P_cust_id", "size": 6, "type": "select", "title": "A2P customer ID", "filter": {"operatorType": ["C"], "protocolType": "1", "interfaceType": "SS7"}, "dynamic": {"field": "is_enabled_vip", "value": 1}, "fieldType": "number", "isMandatory": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select A2P customer ID"}], "defaultValue": "", "isDropDownApi": true, "validationType": "number"}, {"name": "P2P_cust_id", "size": 6, "type": "select", "title": "P2P customer ID", "filter": {"operatorType": ["C"], "protocolType": "2", "interfaceType": "SS7"}, "dynamic": {"field": "is_enabled_vip", "value": 1}, "fieldType": "number", "isMandatory": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select P2P customer ID"}], "defaultValue": "", "isDropDownApi": true, "validationType": "number"}, {"name": "is_enabled_ATI", "size": 6, "type": "switch", "title": "ATI supplier configuration", "fieldType": "number", "defaultValue": 0, "options": [{"checked": true, "label": "Active", "value": 1}, {"checked": false, "label": "Inactive", "value": 0}]}, {"name": "ATI_supp_id", "size": 6, "type": "select", "title": "Supplier ID", "filter": {"operatorType": ["S"], "interfaceType": "SS7"}, "dynamic": {"field": "is_enabled_ATI", "value": 1}, "fieldType": "number", "isMandatory": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select supplier ID"}], "defaultValue": "", "isDropDownApi": true, "validationType": "number"}], "formName": "Basic"}, {"children": [{"elements": [{"name": "mt_ind_resp_type", "size": 6, "type": "select", "title": "Type", "options": [{"label": "Mask", "value": 1}, {"label": "Unmask", "value": 0}], "fieldType": "number", "isMandatory": true, "defaultValue": 0}, {"name": "mt_ind_mask_error_dtls", "size": 6, "type": "select", "title": "Mask all error", "dynamic": {"field": "mt_ind_resp_type", "value": 1}, "options": [{"label": "Absent Subscriber for SRI", "value": 27}, {"label": "Absent Subscriber", "value": 6}, {"label": "Called Barred", "value": 13}, {"label": "SS Incompatibility", "value": 20}, {"label": "Facility not Supported", "value": 21}, {"label": "Subscriber Busy for MT-SMS", "value": 31}, {"label": "SM-Delivery Failure", "value": 32}, {"label": "Message Waiting List Full", "value": 33}, {"label": "System Failure", "value": 34}, {"label": "Data Missing", "value": 35}, {"label": "Unexpected Data Value", "value": 36}, {"label": "Busy Subscriber", "value": 45}, {"label": "No Subscriber Reply", "value": 46}], "fieldType": "number", "isMandatory": true, "validations": [{"type": "required", "message": "Please select MT all error"}], "defaultValue": ""}, {"name": "mt_ind_mask_error_dtls", "size": 6, "type": "select", "title": "Mask all error", "dynamic": {"field": "mt_ind_resp_type", "value": 0}, "options": [], "fieldType": "number", "isMandatory": true, "notEditable": true, "defaultValue": ""}, {"name": "mt_mask_error_dtls", "size": 6, "type": "select", "title": "Mask content failure error", "options": [{"label": "Absent Subscriber for SRI", "value": 27}, {"label": "Absent Subscriber", "value": 6}, {"label": "Called Barred", "value": 13}, {"label": "SS Incompatibility", "value": 20}, {"label": "Facility not Supported", "value": 21}, {"label": "Subscriber Busy for MT-SMS", "value": 31}, {"label": "SM-Delivery Failure", "value": 32}, {"label": "Message Waiting List Full", "value": 33}, {"label": "System Failure", "value": 34}, {"label": "Data Missing", "value": 35}, {"label": "Unexpected Data Value", "value": 36}, {"label": "Busy Subscriber", "value": 45}, {"label": "No Subscriber Reply", "value": 46}, {"label": "Provider <PERSON>", "value": 30007}], "fieldType": "number", "isMandatory": true, "validations": [{"type": "required", "message": "Please select MT failure error"}], "defaultValue": ""}], "formName": "MT Indication response"}, {"elements": [{"name": "sri_ind_resp_type", "size": 6, "type": "select", "title": "Type", "options": [{"label": "Mask", "value": 1}, {"label": "Unmask", "value": 0}], "fieldType": "number", "isMandatory": true, "defaultValue": 0}, {"name": "sri_ind_mask_error_dtls", "size": 6, "type": "select", "title": "Mask all error", "dynamic": {"field": "sri_ind_resp_type", "value": 1}, "options": [{"label": "Absent Subscriber for SRI", "value": 27}, {"label": "Absent Subscriber", "value": 6}, {"label": "Called Barred", "value": 13}, {"label": "SS Incompatibility", "value": 20}, {"label": "Facility not Supported", "value": 21}, {"label": "Subscriber Busy for MT-SMS", "value": 31}, {"label": "SM-Delivery Failure", "value": 32}, {"label": "Message Waiting List Full", "value": 33}, {"label": "System Failure", "value": 34}, {"label": "Data Missing", "value": 35}, {"label": "Unexpected Data Value", "value": 36}, {"label": "Busy Subscriber", "value": 45}, {"label": "No Subscriber Reply", "value": 46}], "fieldType": "number", "isMandatory": true, "validations": [{"type": "required", "message": "Please select SRI all error"}], "defaultValue": ""}, {"name": "sri_ind_mask_error_dtls", "size": 6, "type": "select", "title": "Mask all error", "dynamic": {"field": "sri_ind_resp_type", "value": 0}, "options": [], "fieldType": "number", "isMandatory": true, "notEditable": true, "defaultValue": ""}], "formName": "SRI Indication response"}], "formName": "Indication Response", "hasChildren": true}, {"elements": [{"name": "dummy_gt", "size": 6, "type": "ChipInput", "title": "Dummy GT (Press enter to add Dummy GT)", "fieldBased": "gtValue", "validation": true, "isMandatory": true, "defaultValue": [], "validationType": "array", "validationConfig": {"maxValue": {"value": 15, "message": "Maximum 15 digits allowed"}, "minValue": {"value": 3, "message": "Minimum 3 digits required"}, "typeError": "Value of Dummy GT is not proper. Please Check", "uniqueError": "Repeated values not allowed", "integerError": "Value of Dummy GT is not proper. Please Check", "minArrayLength": {"value": 1, "message": "Please enter Dummy GT values"}}}, {"name": "a2p_gt", "size": 6, "type": "ChipInput", "title": "A2P GT (Press enter to add A2P GT)", "fieldBased": "gtValue", "validation": true, "isMandatory": true, "defaultValue": [], "validationType": "array", "validationConfig": {"maxValue": {"value": 15, "message": "Maximum 15 digits allowed"}, "minValue": {"value": 3, "message": "Minimum 3 digits required"}, "typeError": "Value of A2P GT is not proper. Please Check", "uniqueError": "Repeated values not allowed", "integerError": "Value of A2P GT is not proper. Please Check", "minArrayLength": {"value": 1, "message": "Please enter A2P GT values"}}}, {"name": "p2p_gt", "size": 6, "type": "ChipInput", "title": "P2P GT (Press enter to add P2P GT)", "fieldBased": "gtValue", "validation": true, "isMandatory": true, "defaultValue": [], "validationType": "array", "validationConfig": {"maxValue": {"value": 15, "message": "Maximum 15 digits allowed"}, "minValue": {"value": 3, "message": "Minimum 3 digits required"}, "typeError": "Value of P2P GT is not proper. Please Check", "uniqueError": "Repeated values not allowed", "integerError": "Value of P2P GT is not proper. Please Check", "minArrayLength": {"value": 1, "message": "Please enter P2P GT values"}}}], "formName": "GT definition"}], "formType": "multiple", "moduleName": "Service parameters"}