import React, { useState, useEffect } from "react";
import { Field, FieldArray } from "formik";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import TextFieldWrapper from "../FormsUI/TextField";
import CustomSwitch from "../ToggleSwitch/CustomSwitch";
import { PlusCircleIcon, DeleteIcon } from "../../icons";

const ParameterTable = ({ headers, values, name }) => {
  const [parameters, setParameters] = useState([]);

  useEffect(() => {
    if (values[name] && values[name]?.[name]) {
      setParameters(values[name]?.[name]);
    }
  }, [values, name]);

  const handleAddNewParameter = (push) => {
    const newParameter = {
      comvivaParameter: "NA",
      thirdPartyParameter: "",
      defaultValue: "",
      status: true,
      isDelete: true,
      isStatusDisable: false,
    };

    push(newParameter);
  };

  const handleDeleteParameter = (remove, index) => {
    remove(index);
  };

  return (
    <TableContainer component={Paper} sx={{ boxShadow: "none" }}>
      <Table
        sx={{ minWidth: 550, border: "1.5px solid #BEBEBE" }}
        aria-label="simple table"
      >
        <TableHead sx={{ backgroundColor: "#37415033" }}>
          <TableRow>
            {headers.map((header, index) => (
              <TableCell key={index}>{header}</TableCell>
            ))}
            <TableCell />
          </TableRow>
        </TableHead>
        <TableBody>
          <FieldArray name={`${name}.${name}`}>
            {({ remove, push }) => (
              <>
                {parameters.map((row, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <TextFieldWrapper
                        name={`${name}.${name}.[${index}].comvivaParameter`}
                        disabled
                      />
                    </TableCell>
                    <TableCell>
                      <TextFieldWrapper
                        name={`${name}.${name}.[${index}].thirdPartyParameter`}
                      />
                    </TableCell>
                    <TableCell>
                      <TextFieldWrapper
                        name={`${name}.${name}.[${index}].defaultValue`}
                      />
                    </TableCell>
                    <TableCell>
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <Field
                          name={`${name}.${name}.[${index}].status`}
                          as={CustomSwitch}
                          checked={row.status}
                          disabled={row.isStatusDisable}
                        />
                        <span style={{ marginLeft: 8 }}>
                          {row.status ? "Active" : "Inactive"}
                        </span>
                      </div>
                    </TableCell>
                    {row.isDelete && (
                      <TableCell>
                        <DeleteIcon
                          onClick={() => handleDeleteParameter(remove, index)}
                        />
                      </TableCell>
                    )}
                  </TableRow>
                ))}
                <TableRow>
                  <TableCell />
                  <TableCell />
                  <TableCell />
                  <TableCell />
                  <TableCell colSpan={headers.length + 1} align="right">
                    <PlusCircleIcon
                      onClick={() => handleAddNewParameter(push)}
                    />
                  </TableCell>
                </TableRow>
              </>
            )}
          </FieldArray>
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default ParameterTable;
