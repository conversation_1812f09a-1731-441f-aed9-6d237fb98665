import { axiosPrivate } from "../common/axiosPrivate";
import getAPIMap from "../routes/ApiUrls";

export async function getCommonFilterData({ queryKey }) {
  const queryData = queryKey[1];
  const { moduleName, fields, filters } = queryData;

  let url = `${getAPIMap("listUrl")}/${moduleName}?sort=createdAt:desc`;

  if (fields && fields.length > 0) {
    fields.forEach((field, index) => {
      url += `&fields[${index}]=${field}`;
    });
  }

  if (filters && filters.length > 0) {
    filters.forEach((filter) => {
      const { field, value } = filter;
      url += `&filters[${field}][$eq]=${value}`;
    });
  }
  if (moduleName === "retry-policies") {
    url += `&populate=*`;
  }
  url += `&pagination[limit]=-1`;

  let response = await axiosPrivate.get(url);
  return response;
}
