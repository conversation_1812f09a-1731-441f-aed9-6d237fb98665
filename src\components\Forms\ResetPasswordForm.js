import React, { useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { useParams, useNavigate } from "react-router-dom";
import { userService } from "../Services/user.service";
import SuccessDialog from "../Dialog/SuccessDialog";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { passwordValidation } from "../../common/yupValidation";
import InputLabel from "../FormsUI/InputLabel";
import TextFieldWrapper from "../FormsUI/TextField";
import ConfirmNNextButton from "../Buttons/Button";

function ResetPasswordForm() {
  const [message, setMessage] = useState("");
  const [successDialog, setSuccessDialog] = useState(false);

  const navigate = useNavigate();
  const { token } = useParams();

  const handleResetSuccess = () => {
    setSuccessDialog(false);
    navigate("/auth/login");
  };

  return (
    <div>
      <Formik
        initialValues={{
          password: "",
          confirmPassword: "",
        }}
        validationSchema={Yup.object().shape({
          password: passwordValidation.required("New Password is Required"),
          confirmPassword: Yup.string()
            .oneOf([Yup.ref("password"), null], "Password must match")
            .required("Confirm Password is Required"),
        })}
        onSubmit={async ({ password }, { setSubmitting }) => {
          setSubmitting(true);
          try {
            const response = await userService.resetPassword(token, password);
            setSuccessDialog(true);
            setMessage("Your password has been changed successfully");
          } catch (error) {
            toast.error(
              error?.response?.data?.message ||
                "An error occurred. Please try again."
            );
          } finally {
            setSubmitting(false);
          }
        }}
      >
        {({ isSubmitting }) => (
          <Form>
            <InputLabel label={"New Password"} isMandatory={true} />
            <TextFieldWrapper
              name="password"
              isPassword={true}
              placeholder="Enter the new password"
            />

            <div className="h-9"></div>

            <InputLabel label={"Confirm Password"} isMandatory={true} />
            <TextFieldWrapper
              name="confirmPassword"
              isPassword={true}
              placeholder="Enter the confirm password"
            />
            <ConfirmNNextButton
              label={"Reset"}
              buttonClassName="mt-8  text-white text-sm  w-full py-2 rounded-md"
              type="submit"
              disabled={isSubmitting}
            />
          </Form>
        )}
      </Formik>
      <ToastContainer position="top-center" autoClose={3000} />
      <SuccessDialog
        show={successDialog}
        onHide={handleResetSuccess}
        message={message}
        btnName={"Login"}
      />
    </div>
  );
}

export default ResetPasswordForm;
