import { lazy } from "react";
import { moduleConfiguration } from "../common/constants";

const ListPage = lazy(() => import("../pages/List"));
const FormPage = lazy(() => import("../pages/SimpleForm"));
const AboutUs = lazy(() => import("../pages/AboutUs"));
const ContactUs = lazy(() => import("../pages/ContactUs"));
const LCRTable = lazy(() => import("../components/Table/LCRTable"));
const HomePage = lazy(() => import("../pages/HomePage"));
const ScAtLcrPolicy = lazy(() => import("../pages/LCRProfile/ScAtLcrPolicy"));
const LcrPolicy = lazy(() => import("../pages/LCRProfile/LcrPolicy"));
const CustomerSupplier = lazy(() =>
  import("../pages/CustomerSupplier/CustomerSupplier")
);
const ViewAccountTable = lazy(() =>
  import("../components/Templates/ViewAccountTable")
);
const ShortCodeEsme = lazy(() =>
  import("../components/Templates/ShortCodeEsme")
);
const ErrorTable = lazy(() => import("../pages/RetryErrorTable"));
/**
 * ⚠ These are internal routes!
 * They will be rendered inside the app, using the default `containers/Layout`.
 * If you want to add a route to, let's say, a landing page, you should add
 * it to the `App`'s router, exactly like `Login`, `CreateAccount` and other pages
 * are routed.
 *
 * If you're looking for the links rendered in the SidebarContent, go to
 * `routes/sidebar.js`
 */

const routes = [
  {
    path: "/list/:module",
    component: ListPage,
  },
  {
    path: "/list/:module/:action",
    component: FormPage,
  },
  {
    path: "/list/:module/:action/:id",
    component: FormPage,
  },
  {
    path: "/list/:module/:action/:id/:connType",
    component: FormPage,
  },
  {
    path: "/list/:moduleName/records/:id",
    component: LCRTable,
  },
  {
    path: "/about-us",
    component: AboutUs,
  },
  {
    path: "/home",
    component: HomePage,
  },
  {
    path: "/contact-us",
    component: ContactUs,
  },
  {
    path: "/list/sc-at-lcrs/add",
    component: ScAtLcrPolicy,
  },
  {
    path: "/list/sc-at-lcrs/edit/:id",
    component: ScAtLcrPolicy,
  },
  {
    path: `/list/${moduleConfiguration.lcrConfiguration}/add`,
    component: LcrPolicy,
  },
  {
    path: `/list/${moduleConfiguration.lcrConfiguration}/edit/:id`,
    component: LcrPolicy,
  },
  {
    path: "/list/customer-supplier-managements/add",
    component: CustomerSupplier,
  },
  {
    path: "/list/customer-supplier-managements/edit/:id",
    component: CustomerSupplier,
  },
  {
    path: "/list/customer-supplier-managements/view/:id",
    component: CustomerSupplier,
  },
  {
    path: "/list/:collectionName/table/:id",
    component: ViewAccountTable,
  },
  {
    path: "/list/number-of-sessions/:id",
    component: ShortCodeEsme,
  },
  {
    path: "/list/error-table",
    component: ErrorTable,
  },
];

export default routes;
