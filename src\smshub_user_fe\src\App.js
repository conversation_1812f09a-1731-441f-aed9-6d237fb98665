import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import CommonListPage from "./pages/CommonListPage";
import UserManagement from "./pages/RoleManagement";
import RoleManagement from "./pages/RoleManagement";

function App() {
  const { i18n } = useTranslation();
  const val = i18n.resolvedLanguage;

  useEffect(() => {
    if (val === "ar") {
      document.body.dir = "rtl";
    } else {
      document.body.dir = "ltr";
    }
  }, [val, i18n]);

  return (
    <Router basename="/">
      <Routes>
        {/* Route for rendering the CommonListPage */}
        <Route path="/app/list/user" element={<RoleManagement />} />{" "}
        {/* <Route path="/app/list/user" element={<UserManagement />} /> */}
      </Routes>
    </Router>
  );
}

export default App;
