import LoadingButton from "@mui/lab/LoadingButton";
import React from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";

const Button = ({ buttonClassName, textClassName, isDisabled, ...rest }) => {
  const { t } = useTranslation();

  return (
    <LoadingButton
      {...rest}
      disabled={isDisabled}
      style={{
        fontFamily: "OpenSanHebrew",
      }}
      variant="outlined"
      className={twMerge(
        `h-10 rounded-[40px] border border-outerBorder normal-case text-base ${
          isDisabled
            ? "bg-gray-300 text-gray-500 border-gray-300 " // Disabled styles
            : "bg-bgPrimary text-headerColor border-outerBorder" // Default styles
        } ${buttonClassName}`
      )}
    >
      {t(`${rest.label}`)}
    </LoadingButton>
  );
};

export default Button;
