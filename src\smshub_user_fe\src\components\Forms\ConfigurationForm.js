import React, { useState, useContext, useEffect, useRef } from "react";
import { Form, Formik } from "formik";
import Button from "../Buttons/Button";
import CancelButton from "../Buttons/OutlinedButton";
import CommonTable from "../FormsUI/Table/RolesTable";
import RoleColumnsTab from "./RoleColumnsTab";
import { multiStepFormUserRoleContext } from "../../context/UserRoleContext";
import { getPageSchema } from "../../../../lib/list-api";
import { useQuery } from "react-query";

function ConfigurationForm(props) {
  const [selectAll, setSelectAll] = useState(false);
  const [selectedModule, setSelectedModule] = useState(0);
  const [relations, setRelations] = useState({});
  const {
    handleNextClick,
    setFormData,
    formData,
    handleNextClickStep,
    currentStep,
    setCurrentStep,
  } = useContext(multiStepFormUserRoleContext);

  const formValues = props?.props?.formValues;
  useEffect(() => {
    if (formData && formValues) {
      const mergedData = { ...formData, ...formValues };
      setFormData(mergedData);
    }
  }, [formValues]);

  const currentTabData = props?.props?.data?.forms?.[props.currentStep]?.tabs;

  const modules = currentTabData.map((tab) => tab.moduleName);

  const selectedTabElements = currentTabData?.[selectedModule];

  const listData = selectedTabElements?.elements;

  const formValuesRef = useRef({});

  const columns = (setFieldValue, values) =>
    RoleColumnsTab({
      selectedTabElements,
      selectedModule,
      setFieldValue,
      currentTabData,
      relations,
      values,
    });

  const handleSubmit = (values) => {
    formValuesRef.current = { ...formValuesRef.current, ...values };

    const formattedData = currentTabData.flatMap((tab) => {
      return (tab.elements || []).map((item) => ({
        name: item.resource,
        permissions: {
          view: formValuesRef.current[item.resource]?.view ? 1 : 0,
          update: formValuesRef.current[item.resource]?.update ? 1 : 0,
          delete: formValuesRef.current[item.resource]?.delete ? 1 : 0,
          create: formValuesRef.current[item.resource]?.create ? 1 : 0,
          download: formValuesRef.current[item.resource]?.download ? 1 : 0,
        },
      }));
    });
    setFormData((prev) => ({
      ...prev,
      resources: [...(prev.resources || []), ...formattedData],
    }));
    handleNextClick();
    handleNextClickStep();
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  useEffect(() => {
    if (Object.keys(formValuesRef.current).length > 0) {
      // Update stored form values on tab change
      formValuesRef.current = { ...formValuesRef.current };
    }
  }, [selectedModule, formData, currentStep]);

  useQuery("constants", getPageSchema, {
    refetchOnWindowFocus: false,
    onSuccess: (resp) => {
      setRelations(resp?.data?.data?.[0]?.attributes?.schemadetails?.relations);
    },
  });

  return (
    <Formik
      initialValues={{
        ...(formData?.resources?.reduce((acc, item) => {
          acc[item.name] = {
            view: item.permissions.view === 1,
            update: item.permissions.update === 1,
            delete: item.permissions.delete === 1,
            create: item.permissions.create === 1,
            download: item.permissions.download === 1,
          };
          return acc;
        }, {}) || {}),
      }}
      validateOnMount={true}
      enableReinitialize={true}
      onSubmit={handleSubmit}
    >
      {({ values, setFieldValue }) => (
        <Form>
          <div className="flex md:mx-10 md:my-5">
            {/* Module Names Tab */}
            <div className="w-100 m-5 rounded-md ">
              <div className="font-bold border-b border-tableBorder pb-1 text-sm">
                Module names
              </div>
              <div className="mt-2.5">
                {modules.map((module, index) => (
                  <div
                    key={index}
                    className={`p-2 text-sm cursor-pointer ${
                      selectedModule === index ? "bg-bgTab" : ""
                    }`}
                    onClick={() => {
                      // Save current form values before switching
                      formValuesRef.current = {
                        ...formValuesRef.current,
                        ...values,
                      };
                      setSelectedModule(index);
                    }}
                  >
                    {module}
                  </div>
                ))}
              </div>
            </div>

            <div className="flex-1 ">
              <CommonTable
                columns={columns(setFieldValue, values)}
                data={listData}
                selectAll={selectAll}
                setSelectAll={setSelectAll}
              />
            </div>
          </div>

          {/* Buttons */}
          <div className="flex justify-end items-end mt-10 mr-10 mb-5">
            <CancelButton
              label="< Back"
              buttonClassName="w-full md:w-[154px] h-[40px] text-base mb-3 rounded-[5px] ml-5"
              onClick={() => handleBack()}
            />
            <Button
              type="submit"
              label="Next >"
              value="submit"
              buttonClassName="w-full md:w-[154px] h-[40px] text-base mb-3 rounded-[5px] ml-5"
            />
          </div>
        </Form>
      )}
    </Formik>
  );
}

export default ConfigurationForm;
