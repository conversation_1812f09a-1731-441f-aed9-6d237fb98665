export const urlBase = process.env.REACT_APP_API_URL;
export const loginUrlBase = process.env.REACT_APP_API_USER_URL;

var APIMapping = {
  listUrl: "",
  pageSchemaUrl: "/form-schemas?filters[Name][$eq]=",
  dynamicSchemaUrl: "/dynamic-json",
  aboutusUrl: "/form-schemas?filters[Name][$eq]=about-us",
  sideBarMenu: "/dynamic-routes?route=config-routes",
  smsConfig: "/form-schemas?filters[Name][$eq]=config",
  getPointCode: "/view-accounts/{id}",
  getPointCodeList: "/related-model/{id}",
  exportData: "/export",
  deleteAll: "/delete-many",
  lcrCreate: "/create-entry",
  xmlDialog: "/extract",
  lcrPolicyId: "/sc-at-lcr-configurations/{id}",
  contactUsUrl: "/form-schemas?filters[Name][$eq]=contact-us",
  endUserLicenseAgreement: "/end-user-license",
  errorListUrl: "/error-details",
  shortCodeList: "/short-codes",
  lcrGetDetails: "/combined-lcr-data",
  saveServiceManagement: "/update-many",
  globalSearch: "/global-search",
  linkedOperator: "/linked-operators",
  creditTransactionCustomers: "/linked-modules",
  updateStatus: "/update-status",
  countryName: "/operators",
  exportXml: "/export-uploaded-file",
  seriesStatus: "/series-status",
  globalConfig: "/global-config",
};
function getAPIMap(name) {
  if (name === "globalConfig") {
    return loginUrlBase + "/v1" + APIMapping[name];
  }
  //console.log("finalurl", process.env.REACT_APP_API_URL);
  return urlBase + "/api" + APIMapping[name];
}

export default getAPIMap;
