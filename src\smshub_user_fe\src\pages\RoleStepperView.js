import React, { useContext, useEffect, useState } from "react";
import { useQuery } from "react-query";
import StepperHeader from "../components/Stepper/StepperHeader";
import { multiStepFormUserRoleContext } from "../context/UserRoleContext";
import InputLabel from "../components/FormsUI/InputLabel";
import { CloseIcon } from "../icons";
import RoleManagemtForm from "../components/Forms/RoleManagemtForm";
import RoleReportingForm from "../components/Forms/RoleReportingForm";
import ConfigurationForm from "../components/Forms/ConfigurationForm";
import OperationForm from "../components/Forms/OperationForm";
import CafeForm from "../components/Forms/CafeForm";
import { useNavigate } from "react-router-dom";
import { DropdownContext } from "../../../context/DropDownContext";
import { getRole } from "../lib/role-management-api";
import { moduleConfiguration } from "../../../common/constants";

function RoleStepperView(props) {
  const { currentStep, setCurrentStep } = useContext(
    multiStepFormUserRoleContext
  );
  const { selectedData, multiSelectedData } = useContext(DropdownContext);
  const formSteps = props?.data?.forms.map((x) => x.formName);
  const cafeFormSteps = props?.data?.cafeForms.map((x) => x.formName);

  const [steps, setSteps] = useState(formSteps);
  const [formValues, setFormValues] = useState({});

  const navigate = useNavigate();

  useEffect(() => {
    if (selectedData?.value === "Cafe") {
      setSteps(cafeFormSteps);
    } else {
      const allSteps = [
        "Basic Details",
        "Reporting",
        "Configuration",
        "Operation",
        "Save",
      ];
      const selectedStepNames = multiSelectedData?.map((item) => item.value);

      // Always include "Basic Details" and "Save"
      const filteredSteps =
        selectedStepNames && selectedStepNames.length > 0
          ? ["Basic Details", ...selectedStepNames, "Save"]
          : allSteps;

      setSteps(filteredSteps);
    }
  }, [selectedData, multiSelectedData]);

  const moduleName = moduleConfiguration.roleManagement;
  const id = props.id;

  useQuery([moduleName, id, "getAll"], getRole, {
    enabled: id > 0 ? true : false,
    refetchOnWindowFocus: false,
    onSuccess: (resp) => {
      setCurrentStep(0);

      let filteredAttributes = Object.fromEntries(
        Object.entries(resp?.data?.data?.attributes || {}).filter(
          ([_, value]) => value !== null
        )
      );

      setFormValues(filteredAttributes);
    },
  });

  const displayStepForms = (currentStep) => {
    const stepName = steps[currentStep];

    if (selectedData?.value === "Cafe") {
      switch (stepName) {
        case "Basic Details":
          return (
            <RoleManagemtForm
              props={props}
              currentStep={currentStep}
              formValues={formValues}
            />
          );
        case "Cafe":
          return <CafeForm props={props} formValues={formValues} />;
        default:
          return "";
      }
    } else {
      switch (stepName) {
        case "Basic Details":
          return (
            <RoleManagemtForm
              props={props}
              currentStep={currentStep}
              formValues={formValues}
            />
          );
        case "Reporting":
          return (
            <RoleReportingForm
              props={props}
              currentStep={currentStep}
              formValues={formValues}
            />
          );
        case "Configuration":
          return (
            <ConfigurationForm
              props={props}
              currentStep={currentStep}
              formValues={formValues}
            />
          );
        case "Operation":
          return <OperationForm props={props} formValues={formValues} />;
        default:
          return "";
      }
    }
  };

  const getCompletionPercentage = () => {
    const totalSteps = steps.length;
    const percentage = Math.floor((currentStep / (totalSteps - 1)) * 100);
    return `${percentage}% completed`;
  };

  return (
    <>
      <div>
        {/* <div className="sticky top-0 z-10 h-10 bg-bgBody flex gap-2 font-medium mb-5">
          <div
            className="text-textNAColor text-header cursor-pointer"
            onClick={() => navigate("/app/list/role-management")}
          >
            {"List of Roles >"}
          </div>
          <div className="text-header font-bold">Add Role</div>
        </div> */}
        <div className="border border-listBorder bg-white w-full">
          <div className="flex flex-row p-5">
            <div>
              <InputLabel
                label={"Enter Details"}
                labelClassName="font-semibold text-formHead font-subtitle"
              />
            </div>
            <div className="flex-grow flex justify-end items-center">
              <CloseIcon
                className="w-2.5 h-2.5 cursor-pointer"
                onClick={() => navigate(`/app/list/${props.moduleName}`)}
              />
            </div>
          </div>
          <div className="mx-3 mb-3 border-b-2 border-listBorder" />
          <div className="my-10">
            <StepperHeader atStep={currentStep} steps={steps} />
          </div>
          <div className="my-14 border bg-bgStepper w-full">
            <div className="flex justify-between items-center text-base font-bold my-3">
              <div className="flex-1 text-center">
                <div>Step {currentStep + 1}</div>
              </div>
              <div className="mr-8">{getCompletionPercentage()}</div>
            </div>
          </div>

          <div className="mt-[45px]">{displayStepForms(currentStep)}</div>
        </div>
      </div>
    </>
  );
}

export default RoleStepperView;
