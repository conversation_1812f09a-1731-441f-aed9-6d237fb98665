export const Pagination = {
  pageSize: 10,
};

export const nameRegex = /^(?=.*[0-9])(?=.*[A-Za-z])[A-Za-z][A-Za-z0-9_ ]*$/; //Only alphanumeric characters and underscore,space are allowed.should start with an alphabet only.

export const onlyNumSpaceNotAllowed =
  /^(?=.*[a-zA-Z])[a-zA-Z0-9_\s!@#$%^&*()\-+=~"`'<>,./?;:{}[\]|\\]*$/; //Only space,only special char,only numerics not allowed.Alphabets with space,numbers and special chars allowed.
export const onlyAlphawithHyphen = /^[A-Za-z]+-$/; //only alphabets ends with a hyphen
export const onlyNumeric = /^-?\d+(\.\d+)?$/;
export const onlyIntNoSpace = /^\d+$/; //only positive integer without space

export const onlyPositiveDecimal = /^(?!0*$)(?!.*\..*\..*)[0-9]*\.[0-9]+$/;
export const onlyPositiveInteger = /^(0|[1-9][0-9]*)(\.[0-9]+)?$/;
export const alphaNumeric = /^[A-Za-z0-9_ ]+$/; //Only alphanumeric characters and underscore are allowed.
export const alphabetUnderscoreSpaceRegex = /^[A-Za-z][A-Za-z_ ]*$/;
export const onlyInt = /^-?[0-9]+$/;
export const integerAtBeginning = /^[0-9]/;
export const nameRegexNoSpace = /^(?![a-zA-Z]+$)[a-zA-Z0-9][a-zA-Z0-9_]*$/; //Only alphanumeric characters and underscore are allowed.It should start with an alphabet or a number.Only alphabets not allowed.

export const onlyAlphaNumeric = /^(?!^[a-zA-Z]+$)(?!^[0-9]+$)[a-zA-Z0-9_]+$/;
export const alphaWithDot = /^(?![a-zA-Z]+$)[a-zA-Z0-9._-]+$/;
export const alphaNumeric_space = /^[A-Za-z][A-Za-z0-9_ ]*$/;

//only numeric,alphanumeric, dot,hyphan underscore allowed
//  /^[._-]*[a-zA-Z0-9]+([._-]*[a-zA-Z0-9]+)*[._-]*$/;
export const onlyAlphawithUnderscore = /^[a-zA-Z](?=.*\d)[a-zA-Z0-9_]*$/; // only alphanumeric, underscore alowed. It should start with alphabet. Only numbers/only alphabets not allowed

export const IPV4_regex =
  /^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])$/;
export const IPV6_regex =
  /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^(?:[0-9a-fA-F]{1,4}:){1,7}:|^::(?:[0-9a-fA-F]{1,4}:){0,7}[0-9a-fA-F]{0,4}$/;
export const aplha11_Num16_regex =
  /^(?:(?=.*[0-9])(?=.*[a-zA-Z])[a-zA-Z0-9._,\-\s]{1,11}|[0-9._,\-\s]{1,16})$/; //Only numeric (max 16) and alphanumeric(max 11) allowed. Only alphabets not allowed

export const onlySpecialCharNotAllowed =
  /^(?=.*[\d,a-zA-Z])[a-zA-Z\d,. _\-]*[a-zA-Z\d][a-zA-Z\d,. _\-]*$/; // Only hyphen,comma,dot,underscore and space are allowed with numeric/alphanumeric

export const websiteRegex =
  /^https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/;
export const prefixRegex = /-$/;
export const currencyMultiplierRegex = /^\d{2}\.\d{4}$/;
export const senderPrefixRegex = /^(?=.{3,5}$)[A-Za-z0-9]{1,4}-$/;

// export const IPV6_REGEX =
//   /^(?:[a-fA-F0-9]{1,4}:){7}[a-fA-F0-9]{1,4}$|^::(?:[a-fA-F0-9]{1,4}:){0,6}[a-fA-F0-9]{1,4}$|^(?:[a-fA-F0-9]{1,4}:){1,7}:$|^::$/;
export const numRangeRegex = /^\d+\s*-\s*\d+$/; //1-10,20-30 like that range validation
export const alphaNumWithOptionalSymbolsRegex =
  /^(?=.*[a-zA-Z])(?=.*[0-9])[a-zA-Z0-9!@#$%^&*(),.?":{}|<>]*$/; //only alphanumeric allowed with or without symbols

export const moduleConfiguration = {
  scATLCRImportName: "sc-at-lcr",
  scAtLCRModuleName: "sc-at-lcrs",
  timeBasedLCRImportName: "time-based-lcr",
  timeBasedLCRModuleName: "time-based-lcrs",
  pointCodeListModuleName: "point-code-lists",
  pointCodeListImportName: "point-code-list",
  redirectionalListModuleName: "redirectional-lists",
  redirectionalListImportName: "redirectional-list",
  parentDetails: "parent-details",
  customerSupplier: "customer-supplier-managements",
  customerSupplierImport: "customer-supplier-management",
  serviceManagement: "service-managements",
  operators: "operators",
  hubRuleConfiguration: "hub-rule-configurations",
  hubRuleConfigurationImport: "hub-rule-configuration",
  ruleConfiguration: "rule-configurations",
  ruleConfigurationImport: "rule-configuration",
  rules: "rules",
  esmeSessions: "number-of-sessions",
  smscSessions: "smsc-sessions",
  sessionManagement: "esme-sessions",
  esmeModel: "esme-session",
  operatorCluster: "operator-clusters",
  pointCodeModuleName: "point-codes",
  pointCodeImportName: "point-code",
  redirectionalAccModuleName: "redirectional-accounts",
  redirectionalAccImportName: "redirectional-account",
  lcrConfiguration: "lcr-configurations",
  lcrImportConfiguration: "lcr-configuration",
  esmeAccounts: "esme-accounts",
  paths: "paths",
  creditTransactionImportName: "credit-transaction",
  customerCreditProfile: "customer-credit-profile",
  retryPolicy: "retry-policies",
  viewRetryPolicy: "retry-group-policies",
  series: "serieses",
  seriesImport: "series",
  groupCustomer: "group-customer-suppliers",
  dealManagement: "deal-managements",
  mnpGatewayDetails: "mnp-gateway-details",
  moduleFilterESME: "numberOfSessions",
};

export const populate = {
  customerSupplier:
    "?populate[billingInformation][populate][operatorGroupDetails][populate]=*&populate[billingInformation][populate][slabWiseDifferentialRate][populate]=*&populate[billingInformation][populate][fixedFeePerSms][populate]=*&populate[custShortCodes][populate]=*&populate[billingInformation][populate][mrc][populate]=*&populate[billingInformation][populate][fixedFeeUsagePerSms][populate]=*&populate[billingInformation][populate][slabWiseSingleRate][populate]=*&populate[fraudManagement]=*",
  scAtLCR:
    "?populate[parentDetails][populate]=*&populate[policyDetails][populate]=*",
};

export const operatorType = {
  Customer: "C",
  Supplier: "S",
  Both: "C,S",
};

export const interfaceType = {
  ESME: "SMPP",
  SS7: "SS7",
  "SMPP ES": "SMPP_ES",
};

export const protocolType = {
  A2P: "1",
  P2P: "2",
  "A2P / P2P": "6",
};

export const subInterfaceEsme = {
  SMPP: 1,
  HTTP: 2,
};

export const subInterface = {
  SIGTRAN: 1,
  SS7: 2,
};

export const ocCompliance = {
  ocCompliance: "Y",
  ocComplianceNo: "N",
  hubFlag: "Y",
  hubFlagNo: "N",
  customerStatus: 1,
  enableFraudManagement: 1,
  enableFraudManagementNo: 0,
  avoidCustRevalidation: 0,
  aNumberReportNeeded: 1,
  sriFlag: "Y",
};

export const billingModelType = {
  "Per SMS": 1,
  "Monthly recurring": 2,
  "Fixed fee and per SMS": 3,
  "Fixed fee usage or per SMS": 4,
  "Slab wise pricing": 5,
  "Fixed fee and slab": 6,
};

export const slabPricingType = {
  "Single price": 1,
  "Differential price": 2,
};

export const slabOperator = {
  "All operators": 1,
  "Add operators": 2,
};

export const fixedFeeAndPerSmsType = {
  Volume: 1,
  Revenue: 2,
};

export const billingType = {
  Commercial: "C",
  "Non-commercial": "N",
};

export const billingLogic = {
  Submission: "S",
  Delivery: "D",
  "Successful termination": "T",
};

export const gtType = [
  { label: "Dummy GT", value: 1 },
  { label: "A2P GT", value: 2 },
  { label: "P2P GT", value: 3 },
];
export const lcrType = [
  {
    label: "Default LCR",
    value: 0,
  },
  {
    label: "SC_MT",
    value: 1,
  },
  {
    label: "SC_AT",
    value: 2,
  },
  {
    label: "SPEC_LCR",
    value: 3,
  },
  {
    label: "Time based LCR",
    value: 4,
  },
];

//for hub rule
export const LCRType = [
  {
    label: "lcr_profile",
    value: 0,
  },
  {
    label: "SC_LCR_MT",
    value: 1,
  },
  {
    label: "AT_LCR",
    value: 2,
  },
  {
    label: "spec_route",
    value: 3,
  },
  {
    label: "Time_Based_LCR",
    value: 4,
  },
];

export const calledAddrType = [
  { label: "A2P GT", value: 1 },
  { label: "P2P GT", value: 2 },
  { label: "Special prefix", value: 3 },
  { label: "MSISDN", value: 4 },
];

export const scATLCRGlobalSearch = [
  { label: "Source operator", value: "sourceOpId" },
  // { label: "Region/Country", value: "regionCountryName" },
  { label: "MCC/MNC", value: "mccMnc" },
  { label: "Short code", value: "shortCode" },
  { label: "Supplier", value: "supplier" },
];
export const LCRGlobalSearch = [
  { label: "Destination operator", value: "destOpId" },
  // { label: "Region/Country", value: "regionCountryName" },
  { label: "MCC/MNC", value: "mccMnc" },
  { label: "Supplier", value: "supplier" },
];
export const trafficType = {
  A2P: "-1",
  P2P: "-2",
};
export const transmissonMode = {
  Buffer: "2",
  Transit: "-1",
};
export const ruleStatus = {
  Active: "1",
  Inactive: "2",
};
export const SubscriberStatus = {
  Roaming: "1",
  "Non-Roaming": "2",
};
export const PortedSubscriber = {
  Ported: "-1",
  Normal: "-2",
};

export const ALLOWED_KEYS = {
  M: [
    "smscName",
    "connType",
    "ss7DestGt",
    "gtAddrType",
    "tt",
    "np",
    "callingTt",
    "callingNp",
    "pointcodeId",
    "mapVersion",
    "gsmConvReqFlag",
  ],
  S: [
    "smscName",
    "connType",
    "msgCapacityPerSec",
    "msgCapacityPerMin",
    "smscHost",
    "msgCapacityPerHour",
    "smscPort",
    "smscUserName",
    "altHost",
    "altPort",
    "smscPwd",
    "systemType",
    "addressRange",
    "maximumPending",
    "tranceiverMode",
    "keepAlive",
    "valPeriod",
    "gsmConvReqFlag",
    "srcAddressTon",
    "srcAddressNpi",
    "dstAddressTon",
    "dstAddressNpi",
    "msgIdType",
    "reconnectDelay",
    "statRefreshDelay",
    "activeNodes",
  ],
  H: [
    "smscName",
    "connType",
    "msgCapacityPerSec",
    "msgCapacityPerMin",
    "msgCapacityPerHour",
    "smscUserName",
    "smscPwd",
    "httpTemplateId",
    "supplierUrl",
    "httpMethod",
    "supplierFilePath",
    "drUrl",
    "valPeriod",
    "valPeriodType",
    "gsmConvReqFlag",
  ],
};

export const IPV4_REGEX =
  /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

export const IPV6_REGEX =
  /^(?:[a-fA-F0-9]{1,4}:){7}[a-fA-F0-9]{1,4}$|^::(?:[a-fA-F0-9]{1,4}:){0,6}[a-fA-F0-9]{1,4}$|^(?:[a-fA-F0-9]{1,4}:){1,7}:$|^::$/;
export const authCharRegex = /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_]).*$/;

export const countryCodeArr = [
  "+93",
  "+355",
  "+213",
  "+376",
  "+244",
  "+1-268",
  "+54",
  "+374",
  "+61",
  "+43",
  "+994",
  "+1-242",
  "+973",
  "+880",
  "+1-246",
  "+375",
  "+32",
  "+501",
  "+229",
  "+975",
  "+591",
  "+387",
  "+267",
  "+55",
  "+673",
  "+359",
  "+226",
  "+257",
  "+855",
  "+237",
  "+1",
  "+238",
  "+236",
  "+235",
  "+56",
  "+86",
  "+57",
  "+269",
  "+242",
  "+506",
  "+385",
  "+53",
  "+357",
  "+420",
  "+45",
  "+253",
  "+1-767",
  "+1-809",
  "+1-829",
  "+1-849",
  "+593",
  "+20",
  "+503",
  "+240",
  "+291",
  "+372",
  "+268",
  "+251",
  "+679",
  "+358",
  "+33",
  "+241",
  "+220",
  "+995",
  "+49",
  "+233",
  "+30",
  "+1-473",
  "+502",
  "+224",
  "+245",
  "+592",
  "+509",
  "+504",
  "+852",
  "+36",
  "+354",
  "+91",
  "+62",
  "+98",
  "+964",
  "+353",
  "+972",
  "+39",
  "+225",
  "+1-876",
  "+81",
  "+962",
  "+7",
  "+254",
  "+686",
  "+965",
  "+996",
  "+856",
  "+371",
  "+961",
  "+266",
  "+231",
  "+218",
  "+423",
  "+370",
  "+352",
  "+853",
  "+261",
  "+265",
  "+60",
  "+960",
  "+223",
  "+356",
  "+692",
  "+222",
  "+230",
  "+52",
  "+691",
  "+373",
  "+377",
  "+976",
  "+382",
  "+1-664",
  "+212",
  "+258",
  "+95",
  "+264",
  "+674",
  "+977",
  "+31",
  "+687",
  "+64",
  "+505",
  "+227",
  "+234",
  "+683",
  "+850",
  "+389",
  "+47",
  "+968",
  "+92",
  "+680",
  "+970",
  "+507",
  "+675",
  "+595",
  "+51",
  "+63",
  "+48",
  "+351",
  "+1-787",
  "+1-939",
  "+974",
  "+40",
  "+7",
  "+250",
  "+1-869",
  "+1-758",
  "+1-784",
  "+685",
  "+378",
  "+239",
  "+966",
  "+221",
  "+381",
  "+248",
  "+232",
  "+65",
  "+421",
  "+386",
  "+677",
  "+252",
  "+27",
  "+82",
  "+211",
  "+34",
  "+94",
  "+249",
  "+597",
  "+46",
  "+41",
  "+963",
  "+886",
  "+992",
  "+255",
  "+66",
  "+670",
  "+228",
  "+690",
  "+676",
  "+1-868",
  "+216",
  "+90",
  "+993",
];
export const dialCodeToCountryCodeMap = {
  "+93": "AF",
  "+355": "AL",
  "+213": "DZ",
  "+376": "AD",
  "+244": "AO",
  "+1-268": "AG",
  "+54": "AR",
  "+374": "AM",
  "+61": "AU",
  "+43": "AT",
  "+994": "AZ",
  "+1-242": "BS",
  "+973": "BH",
  "+880": "BD",
  "+1-246": "BB",
  "+375": "BY",
  "+32": "BE",
  "+501": "BZ",
  "+229": "BJ",
  "+975": "BT",
  "+591": "BO",
  "+387": "BA",
  "+267": "BW",
  "+55": "BR",
  "+673": "BN",
  "+359": "BG",
  "+226": "BF",
  "+257": "BI",
  "+855": "KH",
  "+237": "CM",
  "+1": "CA",
  "+238": "CV",
  "+236": "CF",
  "+235": "TD",
  "+56": "CL",
  "+86": "CN",
  "+57": "CO",
  "+269": "KM",
  "+242": "CG",
  "+506": "CR",
  "+385": "HR",
  "+53": "CU",
  "+357": "CY",
  "+420": "CZ",
  "+45": "DK",
  "+253": "DJ",
  "+1-767": "DM",
  "+1-809": "DO",
  "+1-829": "DO",
  "+1-849": "DO",
  "+593": "EC",
  "+20": "EG",
  "+503": "SV",
  "+240": "GQ",
  "+291": "ER",
  "+372": "EE",
  "+268": "SZ",
  "+251": "ET",
  "+679": "FJ",
  "+358": "FI",
  "+33": "FR",
  "+241": "GA",
  "+220": "GM",
  "+995": "GE",
  "+49": "DE",
  "+233": "GH",
  "+30": "GR",
  "+1-473": "GD",
  "+502": "GT",
  "+224": "GN",
  "+245": "GW",
  "+592": "GY",
  "+509": "HT",
  "+504": "HN",
  "+852": "HK",
  "+36": "HU",
  "+354": "IS",
  "+91": "IN",
  "+62": "ID",
  "+98": "IR",
  "+964": "IQ",
  "+353": "IE",
  "+972": "IL",
  "+39": "IT",
  "+225": "CI",
  "+1-876": "JM",
  "+81": "JP",
  "+962": "JO",
  "+7": "KZ",
  "+254": "KE",
  "+686": "KI",
  "+965": "KW",
  "+996": "KG",
  "+856": "LA",
  "+371": "LV",
  "+961": "LB",
  "+266": "LS",
  "+231": "LR",
  "+218": "LY",
  "+423": "LI",
  "+370": "LT",
  "+352": "LU",
  "+853": "MO",
  "+261": "MG",
  "+265": "MW",
  "+60": "MY",
  "+960": "MV",
  "+223": "ML",
  "+356": "MT",
  "+692": "MH",
  "+222": "MR",
  "+230": "MU",
  "+52": "MX",
  "+691": "FM",
  "+373": "MD",
  "+377": "MC",
  "+976": "MN",
  "+382": "ME",
  "+1-664": "MS",
  "+212": "MA",
  "+258": "MZ",
  "+95": "MM",
  "+264": "NA",
  "+674": "NR",
  "+977": "NP",
  "+31": "NL",
  "+687": "NC",
  "+64": "NZ",
  "+505": "NI",
  "+227": "NE",
  "+234": "NG",
  "+683": "NU",
  "+850": "KP",
  "+389": "MK",
  "+47": "NO",
  "+968": "OM",
  "+92": "PK",
  "+680": "PW",
  "+970": "PS",
  "+507": "PA",
  "+675": "PG",
  "+595": "PY",
  "+51": "PE",
  "+63": "PH",
  "+48": "PL",
  "+351": "PT",
  "+1-787": "PR",
  "+1-939": "PR",
  "+974": "QA",
  "+40": "RO",
  "+7": "RU",
  "+250": "RW",
  "+1-869": "KN",
  "+1-758": "LC", // Saint Lucia
  "+1-784": "VC", // Saint Vincent and the Grenadines
  "+685": "WS", // Samoa
  "+378": "SM", // San Marino
  "+239": "ST", // Sao Tome and Principe
  "+966": "SA", // Saudi Arabia
  "+221": "SN", // Senegal
  "+381": "RS", // Serbia
  "+248": "SC", // Seychelles
  "+232": "SL", // Sierra Leone
  "+65": "SG", // Singapore
  "+421": "SK", // Slovakia
  "+386": "SI", // Slovenia
  "+677": "SB", // Solomon Islands
  "+252": "SO", // Somalia
  "+27": "ZA", // South Africa
  "+82": "KR", // South Korea
  "+211": "SS", // South Sudan
  "+34": "ES", // Spain
  "+94": "LK", // Sri Lanka
  "+249": "SD", // Sudan
  "+597": "SR", // Suriname
  "+46": "SE", // Sweden
  "+41": "CH", // Switzerland
  "+963": "SY", // Syria
  "+886": "TW", // Taiwan
  "+992": "TJ", // Tajikistan
  "+255": "TZ", // Tanzania
  "+66": "TH", // Thailand
  "+670": "TL", // Timor-Leste
  "+228": "TG", // Togo
  "+690": "TK", // Tokelau
  "+676": "TO", // Tonga
  "+1-868": "TT", // Trinidad and Tobago
  "+216": "TN", // Tunisia
  "+90": "TR", // Turkey
  "+993": "TM", // Turkmenistan
};
export const TIME_ZONE = "Asia/Kolkata";
