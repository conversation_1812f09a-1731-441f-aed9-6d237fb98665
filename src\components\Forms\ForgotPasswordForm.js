import React, { useContext, useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { AuthContext } from "../../context/AuthContext";
import { emailValidation } from "../../common/yupValidation";
import TextFieldWrapper from "../FormsUI/TextField";
import LoginButton from "../../components/Buttons/OutlinedButton";

function ForgotPasswordForm({ setShowSuccess }) {
  const { forgotPassword } = useContext(AuthContext);
  return (
    <div>
      <Formik
        initialValues={{
          email: "",
        }}
        validationSchema={Yup.object().shape({
          email: emailValidation,
        })}
        onSubmit={({ email }, { setStatus, setSubmitting }) => {
          setSubmitting(true);
          setStatus();
          forgotPassword(email)
            .then(() => {
              setShowSuccess(true);
            })
            .catch((error) => {
              if (error.response) {
                setStatus(error.response.data.message);
              } else {
                setStatus("Some error occured. Please try again.");
              }
              setSubmitting(false);
            });
        }}
      >
        {({ status, isSubmitting }) => (
          <Form>
            <TextFieldWrapper
              label={"Email ID"}
              name="email"
              type="email"
              placeholder="Enter the Email ID"
              isMandatory={true}
            />
            {status && (
              <p className="text-errorColor text-xs" valid={false}>
                {status}
              </p>
            )}
            <LoginButton
              type="submit"
              label={"Request a reset link"}
              buttonClassName="w-full  text-sm mb-3 rounded  mt-8 bg-bgLogin text-white border-errorBorder shadow-xl"
              loading={isSubmitting}
            />
          </Form>
        )}
      </Formik>
    </div>
  );
}

export default ForgotPasswordForm;
