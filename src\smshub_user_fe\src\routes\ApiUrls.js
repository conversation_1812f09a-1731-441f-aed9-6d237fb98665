export const urlBase = process.env.REACT_APP_API_USER_URL;
export const urlBaseReporting = process.env.REACT_APP_API_REPORTING_URL;
export const apiUrl = process.env.REACT_APP_API_URL;

const APIMapping = {
  getUser: { url: "/v1/users", base: urlBase },
  getRoles: { url: "/v1/roles", base: urlBase },
  getDashboard: { url: "/v1/reporting/dashboard", base: urlBase },
  getReporting: { url: "/v1/reporting", base: urlBase },
  // viewDashboard: "/v1/dashboard/view/{id}",
};

function getAPIMap(name) {
  if (APIMapping[name]) {
    return APIMapping[name].base + APIMapping[name].url;
  }
  console.error(`API mapping not found for: ${name}`);
  return null;
}

export default getAPIMap;
