export const listFormTemplate = ({
  moduleName,
  values,
  filteredValues,
  elements,
  setErroDialog,
  setMessage,
  MAX_PRIORITY,
}) => {
  if (filteredValues.redirectionType === 3) {
    if (moduleName === "redirectional-lists") {
      if (values.smsc_hosts && values.smsc_hosts.length > 0) {
        filteredValues.priorityTable = filteredValues.priorityTable.map(
          ({ "SMSC account": _, Traffic: __, ...rest }) => rest
        );
      } else {
        delete filteredValues.priorityTable;
      }
    } else {
      filteredValues.priorityTable = filteredValues.priorityTable.map(
        ({ "SMSC account": _, Traffic: __, ...rest }) => rest
      );
    }
    filteredValues.redirectionListType = elements[0].defaultValue;
  }

  if (filteredValues.redirectionType === 2) {
    if (filteredValues?.priorityTable?.length > MAX_PRIORITY) {
      setErroDialog(true);
      setMessage(
        `Only ${MAX_PRIORITY} entries are permitted in the priority table`
      );
      return null; // Indicate an error occurred
    }
    delete filteredValues.sampleVal;
    if (moduleName === "redirectional-lists") {
      if (values.smsc_hosts && values.smsc_hosts.length > 0) {
        filteredValues.priorityTable = filteredValues.priorityTable.map(
          ({ Account, ...rest }) => rest
        );
      } else {
        delete filteredValues.priorityTable;
      }
    } else {
      filteredValues.priorityTable = filteredValues.priorityTable.map(
        ({ Account, ...rest }) => rest
      );
    }
    filteredValues.redirectionListType = elements[0].defaultValue;
  }

  if (filteredValues.redirectionType === 1) {
    delete filteredValues.sampleVal;

    if (moduleName === "point-code-lists") {
      filteredValues.priorityTable = values.point_codes.map((code) => ({
        redirectedEntityId: code,
      }));
    }

    if (moduleName === "redirectional-lists") {
      filteredValues.priorityTable = values.smsc_hosts.map((code) => ({
        redirectedEntityId: code,
      }));
    }

    filteredValues.redirectionListType = elements[0].defaultValue;
  }

  return filteredValues;
};

export const processRetryPolicies = (filteredValues, storeRetry) => {
  if (
    !filteredValues?.retryPolicies ||
    filteredValues.retryPolicies.length === 0
  ) {
    return filteredValues;
  }

  const transformedRetryPolicies = filteredValues.retryPolicies.reduce(
    (acc, policy) => {
      acc.push(
        {
          retryattempt: acc.length + 1,
          retryInterval:
            policy.retryInterval1 === "" ? 0 : policy.retryInterval1,
        },
        {
          retryattempt: acc.length + 2,
          retryInterval:
            policy.retryInterval2 === "" ? 0 : policy.retryInterval2,
        }
      );
      return acc;
    },
    []
  );

  const updatedValues = {
    retryGrpName: filteredValues.retryGrpName,
    retryGroupPolicyDetails: [
      {
        failureError: filteredValues.failureError,
        failureResult: filteredValues.failureResult,
        retryPolicies: transformedRetryPolicies,
      },
    ],
  };

  return updatedValues;
};

export const formatRetryGroupPolicy = (attributes) => {
  if (!attributes?.retryGrpName) return attributes;

  const { failureError, failureResult, retryPolicies } =
    attributes.retryGroupPolicyDetails.at(-1);

  const retryPoliciesFormatted = [];

  for (let i = 0; i < retryPolicies.length; i += 2) {
    const policy = {};

    if (retryPolicies[i]?.retryInterval !== undefined) {
      policy["retryInterval1"] = retryPolicies[i].retryInterval.toString();
    }

    if (
      i + 1 < retryPolicies.length &&
      retryPolicies[i + 1]?.retryInterval !== undefined
    ) {
      policy["retryInterval2"] = retryPolicies[i + 1].retryInterval.toString();
    }

    retryPoliciesFormatted.push(policy);
  }

  attributes.retryPolicies = retryPoliciesFormatted;
  attributes.failureError = failureError;
  attributes.failureResult = failureResult;

  delete attributes.retryGroupPolicyDetails;

  return attributes;
};

export const handleFieldChange = (e, field, setFieldValue, formikProps) => {
  const { name, value } = e.target;
  setFieldValue(name, value);
  if (name === "msgCapacityPerSec") {
    const numericValue = Number(value);
    const isValidNumber = value !== "" && !isNaN(numericValue);
    const minMsg = isValidNumber ? numericValue * 60 : null;
    const hourMsg = isValidNumber ? numericValue * 60 * 60 : null;
    setFieldValue("msgCapacityPerHour", isValidNumber ? hourMsg : "", false);
    setFieldValue("msgCapacityPerMin", isValidNumber ? minMsg : "", false);
    setTimeout(() => {
      formikProps.setTouched({
        msgCapacityPerHour: true,
        msgCapacityPerMin: true,
      });
      formikProps.validateForm();
    }, 5);
  } else if (name === "msgPerSecond") {
    const numericValue = Number(value);
    const isValidNumber = value !== "" && !isNaN(numericValue);
    const minMsg = isValidNumber ? numericValue * 60 : null;
    const hourMsg = isValidNumber ? numericValue * 60 * 60 : null;
    const dayMsg = isValidNumber ? numericValue * 60 * 60 * 24 : null;
    setFieldValue("msgPerMinute", isValidNumber ? minMsg : "", false);
    setFieldValue("msgPerHour", isValidNumber ? hourMsg : "", false);
    setFieldValue("MsgsPerDay", isValidNumber ? dayMsg : "", false);
    setTimeout(() => {
      formikProps.setTouched({
        msgPerMinute: true,
        msgPerHour: true,
        MsgsPerDay: true,
      });
      formikProps.validateForm();
    }, 5);
  }
};
