import React from "react";
import { JsonForms } from "@jsonforms/react";
import { materialRenderers } from "@jsonforms/material-renderers";
import { addRenderer } from "@jsonforms/core";
import CustomInput from "../../components/CustomInput";
import customInputTester from "../../components/customInputTester";

// Registering custom renderer
const renderers = [
  ...materialRenderers,
  //register custom renderers
  { tester: customInputTester, renderer: CustomInput },
];
const CustomForm = () => {
  const schema = {
    type: "object",
    properties: {
      name: { type: "string" },
      email: { type: "string", format: "email" },
      age: { type: "integer", minimum: 0 },
    },
  };

  const uischema = {
    type: "VerticalLayout",
    elements: [
      {
        type: "Control",
        label: "Name",
        scope: "#/properties/name",
      },
      {
        type: "Control",
        label: "Email",
        scope: "#/properties/email",
      },
      {
        type: "Control",
        label: "Age",
        scope: "#/properties/age",
      },
    ],
  };

  return (
    <div>
      <h2>Custom Form</h2>
      <JsonForms schema={schema} uischema={uischema} renderers={renderers} />
    </div>
  );
};

export default CustomForm;
