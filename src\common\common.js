import React, { useContext } from "react";
import { Grid } from "@mui/material";
import InputLabel from "../components/FormsUI/InputLabel";
import TextFieldWrapper from "../components/FormsUI/TextField";
import Select from "../components/FormsUI/Select";
import DatePickerFieldWrapper from "../components/FormsUI/DatePicker";
import CustomSwitch from "../components/ToggleSwitch/CustomSwitch";
import CustomRadioButton from "../components/RadioButton/RadioButton";
import Dropdown from "../components/FormsUI/MultiSelect/MultiSelect";
import * as Yup from "yup";
import FieldArrayTable from "../components/Table/FieldArrayTable";
import generateValidations from "./generateValidations";
import SimpleFieldTable from "../components/Table/SimpleFieldTable";
import { getActionAndModuleNameFromURL, getTrimmedUrl } from "./urlUtils";
import TemplateTable from "../components/Table/TemplateTable";
import CustomerCreditTable from "../components/Table/CustomerCreditTable";
import ShortCodeInput from "../components/Chip";
import MultiChipInput from "../components/MultiChip/MultiChip";
import FieldArrayWithPagination from "../components/Table/FieldArrayMultiplePages";
import OutlinedButton from "../components/Buttons/OutlinedButton";
import { ErrorMessage } from "formik";
import { DataContext } from "../context/DataContext";
import { useLocation } from "react-router-dom";
import { DropdownContext } from "../context/DropDownContext";
import {
  IPV4_REGEX,
  IPV6_REGEX,
  authCharRegex,
  moduleConfiguration,
} from "./constants";
import { handleFieldChange } from "./formTemplateUtils";
import {
  getIndexedDBDataById,
  updateIndexedDBDataById,
} from "../components/HelperFunction/localStorage";
import { multiStepFormContext } from "../context/MultiStepFormContext";
import { CustomerSupplierContext } from "../context/CustomerSupplierContext";

export const renderFields = (
  fields,
  values,
  setFieldValue,
  navigate,
  formData,
  action,
  setMultiSelectValue,
  multiSelectValue,
  fieldArrayError,
  setFieldArrayError,
  moduleName,
  singleSelectValue,
  setSingleSelectValue,
  previousFormData,
  header,
  setFieldTouched,
  formikProps
) => {
  return fields.map((field, i) => {
    let {
      name,
      title,
      size,
      type,
      isMandatory,
      isMandatoryy,
      options,
      dynamic,
      placeholder,
      isAddButton,
      onClickPath,
      defaultValue,
      apiOptions,
      isSearch = true,
      headersData,
      fieldsData,
      nonEditable,
      addEditable,
      data,
      tableTitle,
      isPassword,
      visibilityConditions,
      isDropDownApi,
      queryInfo,
      singleApiOptions,
      navigationLink,
      buttonName,
      initialValues,
      generalInfo,
      isInfo,
      info,
      filter,
      isDisabled,
      isCurrentDate,
      minDate,
      dynamicList,
      dynamicOptions,
      isCountry,
      isCustomer,
      gtType,
      isUnique,
      fromTmrwDate,
      dynamicEndDate,
      alloweDropDownEdit,
      maxLength,
      isViewLCR = false,
      ...rest
    } = field;
    const { updateShowFieldState } = useContext(DataContext);
    const { selectedData } = useContext(DropdownContext);
    const location = useLocation();
    const { setCurrentStep } = useContext(multiStepFormContext);
    const { setGeneralInfo, setBillingInfo, setPaymentInfo, setRequiredInfo } =
      useContext(CustomerSupplierContext);

    const navigatePath =
      name === "supplierListId"
        ? values.interfaceType === 3
          ? moduleConfiguration.esmeAccounts
          : [2, 4].includes(values.interfaceType)
          ? moduleConfiguration.redirectionalListModuleName
          : onClickPath
        : onClickPath;
    const pathTitle =
      name === "supplierListId"
        ? values.interfaceType === 3
          ? "Add ESME account"
          : [2, 4].includes(values.interfaceType)
          ? "Add redirection list"
          : placeholder || `Select ${title}`
        : placeholder || `Select ${title}`;

    let showField = dynamic
      ? typeof dynamic["value"] === "string" ||
        typeof dynamic["value"] === "number"
        ? values[dynamic["field"]] === dynamic["value"]
        : dynamic["mnpTypeVisible"]
        ? selectedData &&
          selectedData.mnpType === dynamic["dynamicVisibleValue"] &&
          Array.isArray(dynamic["dynamicSubModuleValue"]) &&
          dynamic["dynamicSubModuleValue"].includes(
            values[dynamic["dynamicSubModule"]]
          )
        : Array.isArray(dynamic["value"]) &&
          dynamic["value"].includes(values[dynamic["field"]])
      : true;

    let fieldTitle =
      showField &&
      dynamic &&
      dynamic["field"] &&
      dynamic["dynamicValue"] &&
      dynamic["dynamicTitle"] &&
      values[dynamic["field"]] === dynamic["dynamicValue"]
        ? dynamic["dynamicTitle"]
        : title;

    let fieldType =
      showField &&
      dynamic &&
      dynamic["field"] &&
      dynamic["dynamicValue"] &&
      dynamic["dynamicType"] &&
      values[dynamic["field"]] === dynamic["dynamicValue"]
        ? dynamic["dynamicType"]
        : type;

    let showMinDate =
      showField && dynamicEndDate ? values[dynamicEndDate["field"]] : null;

    if (!showField) return <></>;

    let shouldShowField = false;

    if (visibilityConditions) {
      shouldShowField = Object.keys(visibilityConditions).every(
        (conditionKey) =>
          visibilityConditions[conditionKey].some((conditionValue) => {
            const conditionValueStr = String(conditionValue).toLowerCase();
            const value = values[conditionKey] ?? formData[conditionKey];
            const valuesStr =
              value !== undefined && value !== null
                ? String(value).toLowerCase()
                : "";
            if (moduleName === "paths" || moduleName === "esme-accounts") {
              return conditionValueStr === valuesStr;
            } else {
              const formDataStr = String(formData[conditionKey]).toLowerCase();
              return (
                conditionValueStr === valuesStr ||
                conditionValueStr === formDataStr
              );
            }
          })
      );
      if (!shouldShowField) return <></>;
    }

    if (showField === true) {
      updateShowFieldState(moduleName, name);
    }

    let hideFieldMatches;
    if (dynamic && type === "fieldArray") {
      const { field, value } = dynamic;

      if (values[field] === value) {
        // Inserting initial values if not already present
        for (const [key, initialValue] of Object.entries(initialValues)) {
          if (!values.hasOwnProperty(key)) {
            values[key] = initialValue;
          }
        }

        // Finding the field definition for the dynamic field
        const fieldDefinition = fields.find((f) => f.name === field);
        if (fieldDefinition && fieldDefinition.options) {
          // Getting the titles of other options that do not match dynamic.value
          const otherOptionsTitles = fieldDefinition.options
            .filter((option) => option.value !== value)
            .map((option) => option.label);

          // Removing previously selected values from other options of the same field
          for (const optionTitle of otherOptionsTitles) {
            for (const fieldName in values) {
              const fieldDef = fields.find((f) => f.name === fieldName);
              if (fieldDef && fieldDef.title === optionTitle) {
                delete values[fieldName];
              }
            }
          }
        }
      }
      // Additional logic to dynamically hide the targetPrice field based on commissionType selection
      fields.forEach((field) => {
        if (field.type === "fieldArray" && values[field.name]) {
          field.fieldsData.forEach((fieldArrayElement) => {
            if (fieldArrayElement.hideField) {
              hideFieldMatches = false;
              Object.entries(fieldArrayElement.hideField).forEach(
                ([key, hideValues]) => {
                  if (values[key] && hideValues.includes(values[key])) {
                    hideFieldMatches = true;
                  }
                }
              );
              if (hideFieldMatches) {
                // Removing targetPrice object from that fieldArray in values
                values[field.name].forEach((item) => {
                  delete item.targetPrice;
                });
              } else {
                // Add targetPrice if it doesn't exist
                values[field.name].forEach((item) => {
                  if (!item.hasOwnProperty("targetPrice")) {
                    item.targetPrice = "";
                  }
                });
              }
            }
          });
        }
      });
    }

    let dataValue = [];
    if (onClickPath === "point-codes") {
      dataValue = previousFormData?.point_codes
        ? previousFormData.point_codes.map((item) => item)
        : formData?.point_codes?.map((item) => item.id) || [];
    }
    if (onClickPath === "operators") {
      dataValue = previousFormData?.operatorIds
        ? previousFormData?.operatorIds.map((item) => item)
        : formData?.operatorIds?.map((item) => item.id) || [];
    }
    if (onClickPath === "customer-supplier-managements") {
      dataValue = formData?.custIds?.map((item) => Number(item)) || [];
    }

    if (onClickPath === "redirectional-accounts") {
      dataValue = previousFormData?.smsc_hosts
        ? previousFormData.smsc_hosts.map((item) => item)
        : formData?.smsc_hosts?.data?.map((item) => item.id);
    }

    if (onClickPath === "point-code-lists" && name === "listId") {
      dataValue = formData?.listId;
    }
    if (onClickPath === "point-code-lists" && name === "roamListId") {
      dataValue = formData?.roamListId;
    }
    const url = getTrimmedUrl();
    const searchParams = new URLSearchParams(location.search);
    const currentId = searchParams.get("currentId");
    //console.log("url", url);
    const handleAddButtonClick = async () => {
      if (moduleName === moduleConfiguration.groupCustomer) {
        setGeneralInfo([]);
        setBillingInfo([]);
        setPaymentInfo([]);
        setRequiredInfo([]);
      }
      const nextPath = `/app/list/${navigatePath}/add`;
      const currentPath = url;
      const formData = values;
      const id = parseInt(currentId, 10);
      const moduleNameValue = moduleName;
      const previousModuleName = getActionAndModuleNameFromURL(
        window.location.href
      );
      const listHeader = header;
      setCurrentStep(0);
      try {
        const existingData = await getIndexedDBDataById(
          "navigationDetails",
          "FormDB",
          id
        );

        const updatedData = existingData || { data: [] };

        const existingIndex = updatedData.data.findIndex(
          (entry) => entry.moduleNameValue === moduleNameValue
        );

        if (existingIndex !== -1) {
          updatedData.data.splice(existingIndex, 1);
        }

        const newEntry = {
          currentPath,
          formData,
          previousPath:
            updatedData.data[updatedData.data.length - 1]?.currentPath || null,
          moduleNameValue,
          previousModuleName,
        };

        if (updatedData.data.length === 0) {
          newEntry.listHeader = listHeader;
        }

        updatedData.data.push(newEntry);

        await updateIndexedDBDataById(
          "navigationDetails",
          "FormDB",
          id,
          updatedData
        );

        navigate(`${nextPath}?currentId=${id}`);
      } catch (error) {
        console.error("Error updating IndexedDB:", error);
      }
    };
    const onClickHandler = isAddButton ? handleAddButtonClick : null;

    const thresholdData = Array.from({ length: 4 }, (_, index) => ({
      thresholdLevel: index + 1,
      thresholdValue: "",
      thresholdMessage: "",
    }));

    const handleSelectFieldChange = (e, field) => {
      const { label, value } = e;
      setFieldValue(name, value);
      if (
        (label === "Percentage usage based" && value === 1) ||
        (label === "Remaining usage days" && value === 2)
      ) {
        setFieldValue("threshold", thresholdData);
      }
    };
    return (
      <Grid item xs={12} md={size} key={i}>
        <InputLabel
          label={
            name === "supplierListId"
              ? values.interfaceType === 3
                ? "Add ESME account"
                : [2, 4].includes(values.interfaceType)
                ? "Add redirection list"
                : fieldTitle || title
              : fieldTitle || title
          }
          isMandatory={isMandatory}
          isMandatoryy={isMandatoryy}
          isAddButton={isAddButton}
          color={"text-bgSecondary"}
          isInfo={isInfo}
          info={info}
          isViewLCR={isViewLCR}
          LCRName={field.isViewLCR ? field.name : ""}
          values={values}
          formData={formData}
          action={action}
          onClick={onClickHandler}
        ></InputLabel>
        {(() => {
          switch (fieldType) {
            case "text":
              return (
                <TextFieldWrapper
                  name={name}
                  isDisabled={
                    (action === "edit" && nonEditable) ||
                    addEditable ||
                    action === "viewData"
                      ? true
                      : false
                  }
                  placeholder={placeholder ? placeholder : "Enter " + title}
                  applyOnChange={
                    moduleName === "redirectional-accounts" ||
                    moduleName === "esme-accounts"
                      ? true
                      : false
                  }
                  onChange={(e) => {
                    handleFieldChange(e, field, setFieldValue, formikProps);
                    // setFieldTouched(name, true, false);
                  }}
                  isPassword={isPassword}
                  type={isPassword === true ? "password" : "text"}
                  maxLength={maxLength ? maxLength : ""}
                ></TextFieldWrapper>
              );
            case "select":
              return (
                <Select
                  name={name}
                  singleApiOptions={singleApiOptions}
                  singleSelectValue={singleSelectValue}
                  setSingleSelectValue={setSingleSelectValue}
                  isCountry={isCountry}
                  isCustomer={isCustomer}
                  isDisabled={
                    (action === "edit" && nonEditable) ||
                    addEditable ||
                    action === "viewData"
                      ? true
                      : false
                  }
                  options={options}
                  dynamicList={dynamicList}
                  dynamicOptions={dynamicOptions}
                  placeholder={pathTitle}
                  isDropDownApi={isDropDownApi}
                  collectionName={navigatePath}
                  values={values}
                  filter={filter}
                  queryInfo={queryInfo}
                  gtType={gtType}
                  moduleName={moduleName}
                  isUnique={isUnique}
                  onChange={(e) => handleSelectFieldChange(e, field)}
                  alloweDropDownEdit={alloweDropDownEdit}
                  header={header}
                  setCurrentStep={setCurrentStep}
                  formData={formData}
                  selectiveDetail={rest}
                ></Select>
              );
            case "date":
              return (
                <>
                  <DatePickerFieldWrapper
                    name={name}
                    isDisabled={
                      isDisabled ||
                      action === "viewData" ||
                      (action === "edit" && nonEditable === true)
                        ? true
                        : false
                    }
                    defaultValue={defaultValue}
                    action={action}
                    isCurrentDate={isCurrentDate}
                    minDate={minDate}
                    fromTmrwDate={fromTmrwDate}
                    showMinDate={showMinDate}
                    {...field}
                  ></DatePickerFieldWrapper>
                </>
              );
            case "switch":
              return (
                <CustomSwitch
                  name={name}
                  generalInfo={generalInfo}
                  {...field}
                  isDisabled={
                    action === "viewData" ||
                    addEditable ||
                    (action === "edit" && nonEditable === true)
                      ? true
                      : false
                  }
                  options={options}
                  defaultValue={defaultValue}
                  action={action}
                  values={values}
                ></CustomSwitch>
              );
            case "radio":
              return (
                <CustomRadioButton
                  name={name}
                  {...field}
                  isDisabled={
                    action === "viewData" ||
                    (action === "edit" && nonEditable === true)
                      ? true
                      : false
                  }
                  options={options}
                  defaultValue={defaultValue}
                  moduleName={moduleName}
                ></CustomRadioButton>
              );
            case "multiselect":
              return (
                <>
                  <Dropdown
                    data={[]}
                    onSelectionChange={(selectedDetail) => {
                      const ids = selectedDetail
                        .map((item) => (item.id !== "" ? item.id : null))
                        .filter((id) => id !== null);
                      setFieldValue(field.name, ids);
                    }}
                    isSearch={isSearch}
                    defaultSelectedData={dataValue ? dataValue : defaultValue}
                    apiOptions={apiOptions}
                    collectionName={onClickPath}
                    setMultiSelectValue={setMultiSelectValue}
                    multiSelectValue={multiSelectValue}
                    values={values}
                    moduleName={moduleName}
                    name={name}
                    disabled={
                      action === "viewData" ||
                      (action === "edit" && nonEditable === true)
                        ? true
                        : false
                    }
                    header={header}
                    setCurrentStep={setCurrentStep}
                    placeholder={placeholder ? placeholder : "Select " + title}
                  />
                  <ErrorMessage
                    name={name}
                    component="div"
                    className="text-errorColor text-[11px] mt-0.5"
                  />
                </>
              );
            case "fieldArray":
              return (
                <FieldArrayTable
                  name={name}
                  values={values}
                  headers={headersData}
                  fields={fieldsData}
                  hideFieldMatches={hideFieldMatches}
                  setFieldValue={setFieldValue}
                  isDisabled={action === "viewData" ? true : false}
                  isInfo={isInfo}
                  info={info}
                  moduleName={moduleName}
                  setFieldTouched={setFieldTouched}
                ></FieldArrayTable>
              );
            case "templateTable":
              return (
                <TemplateTable
                  name={name}
                  values={values}
                  headers={headersData}
                  fields={fieldsData}
                  setFieldValue={setFieldValue}
                  isDisabled={action === "viewData" ? true : false}
                ></TemplateTable>
              );
            case "customerCreditTable":
              return (
                <CustomerCreditTable
                  name={name}
                  values={values}
                  headers={headersData}
                  fields={fieldsData}
                  hideFieldMatches={hideFieldMatches}
                  setFieldValue={setFieldValue}
                  isDisabled={action === "viewData" ? true : false}
                  isInfo={isInfo}
                  info={info}
                  moduleName={moduleName}
                ></CustomerCreditTable>
              );
            case "FieldTable":
              return (
                <SimpleFieldTable
                  values={values}
                  headers={headersData}
                  fields={fieldsData}
                  name={name}
                  collectionName={onClickPath}
                  multiSelectValue={multiSelectValue}
                  tableTitle={tableTitle}
                  fieldArrayError={fieldArrayError}
                  setFieldArrayError={setFieldArrayError}
                  moduleName={moduleName}
                  action={action}
                />
              );

            case "FieldTableMultiple":
              return (
                <FieldArrayWithPagination
                  name={field?.name}
                  parameters={data}
                  headers={headersData}
                  values={values}
                  isDisabled={action === "viewData" ? true : false}
                />
              );
            case "ChipInput":
              return (
                <ShortCodeInput
                  name={name}
                  value={values[name] || []}
                  onChange={(chips) => {
                    setFieldValue(name, chips);
                  }}
                  isDisabled={action === "viewData" ? true : false}
                  moduleName={moduleName}
                />
              );
            case "MultiChipInput":
              return (
                <MultiChipInput
                  name={name}
                  value={values[name] || []}
                  onChange={(chips) => {
                    setFieldValue(name, chips);
                  }}
                  isDisabled={action === "viewData" ? true : false}
                  moduleName={moduleName}
                />
              );
            case "button":
              return (
                <OutlinedButton
                  label={buttonName}
                  buttonClassName="w-[150px] h-[40px] text-xs mt-4 rounded-[10px]"
                  onClick={() => {
                    navigate(navigationLink);
                  }}
                />
              );

            case "none":
              return <div></div>;
            default:
              return <div>No element found</div>;
          }
        })()}
      </Grid>
    );
  });
};

export const getDefaultInitialValues = (elements, formData, moduleName) => {
  let values = {};

  elements.forEach((x) => {
    if (moduleName === "paths" && x.blockName === "ss7PathExtended") {
      if (formData.ss7PathExtended) {
        const formDataArray = formData.ss7PathExtended;
        const initialArray = x.initialValues.ss7PathExtended || [];
        const template = initialArray[0] || {};
        values.ss7PathExtended = formDataArray.map((formItem) => ({
          ...template,
          ...formItem,
        }));
        if (formDataArray.length < initialArray.length) {
          values.ss7PathExtended = [
            ...values.ss7PathExtended,
            ...initialArray.slice(formDataArray.length).map((item) => ({
              ...template,
              ...item,
            })),
          ];
        }
      }
    }
    // General logic for other cases
    if (x.type === "fieldArray") {
      values[x.name] =
        formData[x.name] !== undefined
          ? formData[x.name]
          : (x.initialValues && x.initialValues[x.name]) || [];
    } else if (x.type === "FieldTable") {
      values[x.name] = x.initialValues && x.initialValues.items;
    } else if (x.type === "FieldTableMultiple") {
      const { defaultCount, defaultValues } = x.initialValues.retryPolicies;
      values[x.name] = Array(defaultCount).fill({
        ...defaultValues,
      });
    } else if (x.type === "customerCreditTable") {
      values[x.name] = formData[x.name]
        ? formData[x.name]
        : (x.initialValues && x.initialValues?.threshold) || [];
    } else {
      values[x.name] =
        x.replaceValue !== undefined
          ? x.replaceValue
          : formData[x.name] !== undefined
          ? formData[x.name]
          : formData[x.name] === null
          ? ""
          : x.defaultValue !== undefined
          ? x.defaultValue
          : "";
    }
  });

  return values;
};
export const getRetryInitialValues = (elements, formData) => {
  return Object.fromEntries(
    elements.map((x) => [
      x.name,
      formData?.[x.name] !== undefined
        ? formData[x.name] === null
          ? ""
          : formData[x.name]
        : x.defaultValue !== undefined
        ? x.defaultValue
        : "",
    ])
  );
};

export const getStepperDefaultInitialValues = (
  elements,
  formData,
  moduleName
) => {
  let values = {};
  elements.forEach((x) => {
    if (x.type === "fieldArray") {
      if (moduleName === "operators") {
        const fieldArrayData = (formData[x.name] || []).map((item) => ({
          ...item,
          dpc: item.dpc || (x.initialValues?.dpc ? x.initialValues.dpc : []),
        }));
        values[x.name] = fieldArrayData.length
          ? fieldArrayData
          : x.initialValues && x.initialValues[x.name];
      } else {
        values[x.name] =
          formData[x.name] ||
          (x.initialValues && x.initialValues[x.name]) ||
          x?.elements?.[0]?.initialValues?.items ||
          [];
      }
    } else if (x.type === "templateTable") {
      const initialValue =
        formData[x.name] ||
        x.initialValues?.[x.name] ||
        x.elements?.[0]?.initialValues?.items ||
        x.defaultValue;

      values[x.name] = initialValue;
    } else {
      values[x.name] =
        formData[x.name] !== null && formData[x.name] !== undefined
          ? formData[x.name]
          : x.defaultValue !== undefined
          ? x.defaultValue
          : "";
    }
  });

  return values;
};

//http-template and mnp validations

const generateFieldArraySchema = (x, formData) => {
  if (x.validations[0]?.type === "custom") {
    const maxLength = x.validations[0].maxlength;
    return Yup.array().of(
      Yup.object().shape({
        COMVIVA_PARAM: Yup.string()
          .test("conditionalRequired", "Required", function (value) {
            if (this.parent.ACTIVE === 1 && !value) {
              return false;
            }
            return true;
          })
          .max(maxLength, `Max length is ${maxLength}`)
          .test(
            "noOnlySpaces",
            "Only space not allowed",
            (value) => !/^\s*$/.test(value)
          )
          .test("noNA", "Invalid", (value) => {
            return value && value !== "na";
          }),
        THIRD_PARTY_PARAM: Yup.string()
          .test("conditionalRequired", "Required", function (value) {
            if (this.parent.ACTIVE === 1 && !value) {
              return false;
            }
            return true;
          })
          .max(maxLength, `Max length is ${maxLength}`)
          .test(
            "noOnlySpaces",
            "Only space not allowed",
            (value) => !/^\s*$/.test(value)
          ),
        DEFAULT_VAL: Yup.string()
          .test("conditionalRequired", "Required", function (value) {
            if (this.parent.ACTIVE === 1 && !value) {
              return false;
            }
            return true;
          })
          .max(maxLength, `Max length is ${maxLength}`)
          .test(
            "noOnlySpaces",
            "Only space not allowed",
            (value) => !/^\s*$/.test(value)
          ),
      })
    );
  } else if (x.validations[0]?.type === "mnp-custom") {
    const validation = x?.validations[0];
    const portValidation = x?.validations[1];
    const nconnValidation = x?.validations[2];
    const fail = x?.validations[3];
    const retriesValidation = x?.validations[4];
    const authInfoValidation = x?.validations[5];
    const required = validation?.message;
    const inValidHost = validation?.inValidHost;
    return Yup.array().of(
      Yup.object().shape({
        host: Yup.string()
          .required(required)
          .test("is-valid-host", inValidHost, (value) => isValidHost(value)),

        port: Yup.number()
          .typeError(portValidation?.portTypeError)
          .required(required)
          .min(portValidation?.portMin, portValidation?.minMsg)
          .max(portValidation?.portMax, portValidation?.maxMsg)
          .test("is-integer", portValidation?.portInteger, (value) =>
            Number.isInteger(value)
          ),
        nconn: Yup.number()
          .typeError(nconnValidation?.nconnTypeError)
          .required(required)
          .min(nconnValidation?.minCon, nconnValidation?.minMsg)
          .max(nconnValidation?.maxCon, nconnValidation?.maxMsg)
          .test("is-integer", nconnValidation?.isInteger, (value) =>
            Number.isInteger(value)
          ),
        fail: Yup.number()
          .required(required)
          .min(fail?.minFail, fail?.minMsg)
          .max(fail?.maxFail, fail?.maxMsg)
          .typeError(fail?.failTypeError)
          .test("is-integer", fail?.isInteger, (value) =>
            Number.isInteger(value)
          ),
        retries: Yup.number()
          .typeError(retriesValidation?.retTypeError)
          .integer(retriesValidation?.isInteger)
          .required(required)
          .min(retriesValidation?.minRet, retriesValidation?.minMsg)
          .max(retriesValidation?.maxRet, retriesValidation?.maxMsg),
        authinfo: Yup.string()
          .required(required)
          .matches(authCharRegex, authInfoValidation?.authMsg)
          .min(authInfoValidation?.minAuth, authInfoValidation?.minMsg)
          .max(authInfoValidation?.maxAuth, authInfoValidation?.maxMsg),

        conntype: Yup.string().required(required),
      })
    );
  } else if (x.validations[0]?.type === "fieldArray-numberOnly") {
    const numberOnly = x.validations[0]?.message;
    return Yup.array().of(
      Yup.object().shape({
        cc: Yup.number().typeError(numberOnly),
        ndc: Yup.number().typeError(numberOnly),
        snRangeStart: Yup.number().typeError(numberOnly),
        snRangeStop: Yup.number().typeError(numberOnly),
      })
    );
  } else if (x.validations[0]?.type === "threshHoldValidation") {
    const threshHoldValidationMsg = x.validations[0];
    return Yup.array().of(
      Yup.object().shape({
        thresholdValue: Yup.number()
          .min(0, threshHoldValidationMsg.notNegative)
          .test("orderValidation", function (value) {
            let optionsHistory = [];
            const { parent, options } = this;
            optionsHistory.push({ ...options });

            const index = options.index;
            const previousOptions = optionsHistory[optionsHistory.length - 1];
            const previousValue =
              index > 0
                ? parseFloat(
                    options.from?.[1]?.value.threshold[index - 1].thresholdValue
                  )
                : null;
            const currentValue = parseFloat(value);

            if (value === undefined || value === null || value === "") {
              return this.createError({
                message: `Please enter threshold level ${index + 1} value`,
              });
            }

            if (value < 0) {
              return this.createError({
                message: `Threshold value at level ${
                  index + 1
                } cannot be negative.`,
              });
            }

            if (options?.from?.[1]?.value?.thresholdType === 1) {
              if (previousValue !== null && currentValue <= previousValue) {
                return this.createError({
                  message: `Threshold value at level ${
                    index + 1
                  } must be greater than the value at level ${index}.`,
                });
              }
              if (currentValue > 100) {
                return this.createError({
                  message: `Threshold value at level ${
                    index + 1
                  } cannot exceed 100.`,
                });
              }
            } else if (options?.from?.[1]?.value?.thresholdType === 2) {
              if (previousValue !== null && currentValue >= previousValue) {
                return this.createError({
                  message: `Threshold value at level ${
                    index + 1
                  } must be less than the value at level ${index}.`,
                });
              }
            }

            return true;
          })
          .typeError(threshHoldValidationMsg.onlyNumber)
          .integer(threshHoldValidationMsg.onlyWholeNumber),

        thresholdMessage: Yup.string().test(
          "orderValidation",
          function (value) {
            const { options } = this;
            const index = options?.index;
            const currentLevel = index + 1;

            if (!value || value.trim() === "") {
              return this.createError({
                message: `Please enter threshold level ${currentLevel} message`,
              });
            }

            return true;
          }
        ),
      })
    );
  }
};

const isValidHost = (value) => {
  return (
    IPV4_REGEX.test(value) || IPV6_REGEX.test(value)
    // ||
    // hostnameRegex.test(value)
  );
};

const generateMultiselectSchema = (x) => {
  if (x.validations && x.validations[0]?.type === "required") {
    return Yup.array().min(1, x.validations[0]?.message);
  }
};

export const generateValidationSchema = (elements, formData, formikRef) => {
  let validationFields = {};

  elements?.forEach((x) => {
    if (
      x.type === "ChipInput" ||
      (x.type === "MultiChipInput" &&
        x.validation === true &&
        x.validationType === "array")
    ) {
      const schema = generateValidations(x);

      if (x.dynamic) {
        validationFields[x.name] = Yup.string().when(x.dynamic["field"], {
          is: (val) => {
            if (
              typeof x.dynamic["value"] === "string" ||
              typeof x.dynamic["value"] === "boolean" ||
              typeof x.dynamic["value"] === "number"
            ) {
              return x.dynamic["value"] == val;
            } else if (Array.isArray(x.dynamic["value"])) {
              //  return x.dynamic["value"].includes(val);
              return x.dynamic["value"].some((item) => item === val);
            }
            return false;
          },
          then: () => schema,
          otherwise: () => Yup.string(),
        });
        //validationFields[x.name] = check;
      } else {
        validationFields[x.name] = schema;
      }
    }
    if (
      x.validations ||
      x.type === "FieldTableMultiple" ||
      x.modelType === "mnp&channel"
    ) {
      if (
        (x.type === "fieldArray" && x.modelType !== "mnp&channel") ||
        x.type === "templateTable" ||
        x.type === "customerCreditTable"
      ) {
        validationFields[x.name] = generateFieldArraySchema(x, formData);
      } else if (x.type === "multiselect") {
        validationFields[x.name] = generateMultiselectSchema(x);
      } else {
        const schema = generateValidations(x);

        if (x.dynamic && x.modelType !== "mnp&channel") {
          validationFields[x.name] = Yup.string().when(x.dynamic["field"], {
            is: (val) => {
              if (
                typeof x.dynamic["value"] === "string" ||
                typeof x.dynamic["value"] === "boolean" ||
                typeof x.dynamic["value"] === "number"
              ) {
                if (x.sharedFieldName === true && val !== "M") {
                  return true;
                } else {
                  return x.dynamic["value"] == val;
                }
              } else if (Array.isArray(x.dynamic["value"])) {
                return x.dynamic["value"].some((item) => item == val);
              }

              return false;
            },
            then: () => schema,
            otherwise: () => Yup.string(),
          });
        } else if (x.visibilityConditions) {
          const conditionKeys = Object.keys(x.visibilityConditions);

          validationFields[x.name] = Yup.string().when(conditionKeys, {
            is: (...values) => {
              const conditionMet = conditionKeys?.every((key, index) => {
                const conditionValues = x?.visibilityConditions[key];
                const fieldValue = values[index] ?? formData?.[key] ?? "";
                return Array.isArray(conditionValues)
                  ? conditionValues.some((value) => {
                      return value == fieldValue;
                    })
                  : conditionValues == fieldValue;
              });
              return conditionMet;
            },
            then: () => schema,
            otherwise: () =>
              Yup[x.validationType ? x.validationType : "string"](),
          });
        } else {
          validationFields[x.name] = schema;
        }
      }
    }
    if (x?.blockType === "customizedBlocks") {
      const schema = generateValidations(x, formikRef);
      validationFields[x.blockName] = schema;
    }
  });
  if (elements.some((x) => x.validatePassword === true)) {
    validationFields.passwd = Yup.string()
      .required("Password is required")
      .min(4, "Min allowed is 4")
      .max(32, "Max allowed is 32");
    validationFields.confirmPassword = Yup.string()
      .oneOf([Yup.ref("passwd"), null], "Passwords must match")
      .required("Confirm Password is required")
      .min(4, "Min allowed is 4")
      .max(32, "Max allowed is 32");
  }
  return validationFields;
};
