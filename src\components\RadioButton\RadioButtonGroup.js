import React from "react";
import { FormControlLabel, Radio, RadioGroup } from "@mui/material";

const RadioButtonGroup = ({ name, value, onChange, options, isDisabled }) => {
  return (
    <RadioGroup
      name={name}
      value={value}
      onChange={onChange}
      row
      disabled={isDisabled} // Disable the entire group if isDisabled is true
    >
      {options.map((option) => (
        <FormControlLabel
          key={option.value}
          value={option.value}
          control={
            <Radio
              sx={{
                "& .MuiSvgIcon-root:not(.MuiSvgIcon-root ~ .MuiSvgIcon-root)": {
                  color: "#000000",
                },
                "& .MuiSvgIcon-root + .MuiSvgIcon-root": {
                  color: "#3576EB",
                },
                "& .MuiSvgIcon-root": {
                  fontSize: "15px",
                  width: "20px",
                  height: "20px",
                  borderRadius: "1px",
                },
              }}
            />
          }
          label={option.label}
          sx={{
            "& .MuiFormControlLabel-label": {
              fontSize: "12px",
              marginTop: "2px",
            },
          }}
          disabled={isDisabled}
        />
      ))}
    </RadioGroup>
  );
};

export default RadioButtonGroup;
