import React, { useState } from "react";
import Dialog from "@mui/material/Dialog";
import Button from "../Buttons/OutlinedButton";
import CancelButton from "../Buttons/Button";
import { CloseIcon } from "../../icons";
import Checkbox from "@mui/material/Checkbox";
import FormControlLabel from "@mui/material/FormControlLabel";

function ExportPopup({ show, onHide, onConfirm, isLCRDetails }) {
  const [selectedOptions, setSelectedOptions] = useState("CSV");

  const handleOptionChange = (event) => {
    setSelectedOptions(event.target.value);
  };

  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 278,
          maxHeight: 232,
        },
      }}
      maxWidth="xs"
      open={show}
      onClose={onHide}
      className="p-6 font-sans"
    >
      <div className="mx-5">
        <div className="mt-4 text-black text-base font-medium flex items-center justify-between">
          {"Export Reports"}
          <CloseIcon
            onClick={() => {
              onHide();
            }}
            className="w-2.5 h-2.5"
          />
        </div>

        <div className="mt-2 mb-3 border-b border-tableBorder" />
        <div style={{ display: "flex", flexDirection: "column", padding: 0 }}>
          <FormControlLabel
            control={
              <Checkbox
                value="CSV"
                checked={selectedOptions === "CSV"}
                onChange={handleOptionChange}
                sx={{
                  "& .MuiSvgIcon-root": {
                    fontSize: 14,
                    color: "#000000",
                  },
                }}
              />
            }
            label={<span className="text-xs font-medium">CSV</span>}
          />
          {!isLCRDetails && (
            <FormControlLabel
              control={
                <Checkbox
                  value="EXCEL"
                  checked={selectedOptions === "EXCEL"}
                  onChange={handleOptionChange}
                  sx={{
                    "& .MuiSvgIcon-root": {
                      fontSize: 14,
                      color: "#000000",
                    },
                  }}
                />
              }
              label={<span className="text-xs font-medium">EXCEL</span>}
            />
          )}
        </div>
        <div>
          <div className="text-center mt-5 gap-5 mb-4 flex">
            <Button
              onClick={onHide}
              label={"Cancel"}
              buttonClassName="w-[100px] h-9 text-xs"
            />
            <CancelButton
              onClick={() => onConfirm(selectedOptions)}
              label={"Export"}
              buttonClassName="w-[100px] h-9 text-xs"
            />
          </div>
        </div>
      </div>
    </Dialog>
  );
}

export default ExportPopup;
