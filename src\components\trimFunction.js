const TrimFunction = (columns) => {
  return columns?.map((column) => ({
    ...column,
    ...(column.disableFuture === true && {
      muiFilterDateTimePickerProps: { disableFuture: true },
      muiFilterDatePickerProps: { disableFuture: true },
    }),
    filterFn: (row, columnId, filterValue) => {
      const cellValue = row.getValue(columnId);

      if (cellValue === null || cellValue === undefined) {
        if (typeof filterValue === "string") {
          return filterValue.trim() === "";
        } else if (Array.isArray(filterValue)) {
          return filterValue.every(
            (val) =>
              val === undefined ||
              (typeof val === "string" && val.trim() === "")
          );
        }
        return true;
      }

      if (typeof filterValue === "string") {
        const trimmedFilterValue = filterValue.trim().toLowerCase();
        return cellValue
          ? cellValue.toString().toLowerCase().includes(trimmedFilterValue)
          : true;
      }

      // For min-max and date filter
      if (Array.isArray(filterValue) && filterValue.length === 2) {
        const [min, max] = filterValue;

        if (typeof min === "string" || typeof max === "string") {
          const cellNumValue = Number(cellValue);
          const minValid = min ? cellNumValue >= Number(min) : true;
          const maxValid = max ? cellNumValue <= Number(max) : true;

          return minValid && maxValid;
        } else {
          if (column.filterVariant === "date-range") {
            const [minDate, maxDate] = filterValue.map((date) =>
              date ? new Date(date).setHours(0, 0, 0, 0) : null
            );

            const cellDate = new Date(cellValue).setHours(0, 0, 0, 0);

            return (
              (minDate === null || cellDate >= minDate) &&
              (maxDate === null || cellDate <= maxDate)
            );
          } else {
            // For datetime-range
            const [minDateTime, maxDateTime] = filterValue.map((date) =>
              date ? new Date(date).getTime() : null
            );
            const cellDateTime = new Date(cellValue).getTime();

            return (
              (!minDateTime || cellDateTime >= minDateTime) &&
              (!maxDateTime || cellDateTime <= maxDateTime)
            );
          }
        }
      }
      return true;
    },
    // sortingFn: (rowA, rowB, columnId) => {
    //   const valueA = rowA.getValue(columnId);
    //   const valueB = rowB.getValue(columnId);

    //   if (valueA == null && valueB == null) return 0;
    //   if (valueA == null) return 1;
    //   if (valueB == null) return -1;
    //   if (typeof valueA === "object" || typeof valueB === "object") {
    //     return -1;
    //   }
    //   return String(valueA).localeCompare(String(valueB));
    // },
  }));
};

export default TrimFunction;
