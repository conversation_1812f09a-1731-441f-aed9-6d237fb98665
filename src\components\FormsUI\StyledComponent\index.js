import styled from "@emotion/styled";
import TextField from "@mui/material/TextField";
import Select from "@mui/material/Select";
import { MenuItem } from "@mui/material";
import Tooltip, { tooltipClasses } from "@mui/material/Tooltip";
import theme from "../../../tailwind-theme";

const CssTextField = styled(TextField)(({ bgColor, minHeight }) => ({
  "& label.Mui-focused": {
    color: theme.textColor.headerColor,
  },
  "& .MuiOutlinedInput-root": {
    height: minHeight || "40px",
    fontSize: theme.fontSize.xs,
    fontWeight: theme.fontWeight.fields,
    backgroundColor: bgColor || theme.backgroundColor.bgField,
    color: theme.textColor.headerColor,

    "& fieldset": {
      border: `1px solid ${theme.borderColor.tableBorder}`,
      borderRadius: theme.borderRadius.fieldRadius,
    },
    "& input:disabled+fieldset": "transparent",

    "& input:disabled": {
      opacity: "40%",
    },

    "& .MuiOutlinedInput-input.Mui-disabled": {
      WebkitTextFillColor: "rgba(0, 0, 0, 1)",
    },

    "&.Mui-focused fieldset": {
      border: `1px solid ${theme.borderColor.tableBorder}`,
    },
    "&:hover fieldset": {
      border: `1px solid ${theme.borderColor.tableBorder}`,
    },
    "& .MuiOutlinedInput-input::placeholder": {
      color: theme.textColor.textNAColor,
      opacity: 0.8,
    },
  },

  "& .Mui-error": {
    marginLeft: "0px",
    marginTop: "0px",
    fontSize: "11px",
    fontFamily: "OpenSanHebrew",
  },
}));

const CssSelect = styled(Select)((props) => ({
  "&.MuiOutlinedInput-root": {
    height: "40px",
    fontSize: "12px",
    fontWeight: "500",
    backgroundColor: "white",
    color: "#666666",
    opacity: "80%",
    "& fieldset": {
      border: "1px solid #666666",
      borderRadius: "5px",
      // borderRight: "0px",
      // borderRadius: props.template === templates.recruiter ? "5px" : "10px",
      // borderTopLeftRadius: "10px",
      // borderBottomLeftRadius: "10px",
    },
    "&:hover fieldset": {
      border: "2px solid #2D2D2D",
    },
    "&.Mui-focused fieldset": {
      border: "2px solid #2D2D2D",
    },
  },
}));

const CssTooltip = styled(({ className, bgColor, color, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme, bgColor, color }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: bgColor || "#000000",
    color: color || "white",
    fontSize: 12,
    // border: "2px solid #7070708C",
    padding: "10px 20px",
    boxShadow: "0px 3px 6px #00000029",
    borderRadius: 10,
  },
  [`& .${tooltipClasses.arrow}`]: {
    color: theme.palette.common.white,
    "&::before": {
      backgroundColor: bgColor || "#000000",
      //boxShadow: "3px 3px 3px 3px #00000029",
      //border: "0.5px solid #7070708C",
    },
  },
}));

const CssSearchTextField = styled(TextField)((props) => ({
  "& label.Mui-focused": {
    color: "#2D2D2D",
  },
  "& .MuiOutlinedInput-root": {
    height: "40px",
    fontSize: "12px",
    fontWeight: "500",
    backgroundColor: "transparent",
    color: "#666666",
    opacity: "80%",
    width: "100%",
    //fontFamily: "Poppins",

    "& fieldset": {
      border: "1.5px solid #D9D9D9",
      borderRadius: props.borderRadius || "20px",
      borderTopLeftRadius: "0px",
      borderBottomLeftRadius: "0px",
    },

    "&:hover fieldset": {
      border: "1px solid #9C9898",
    },
    "& input:disabled+fieldset": "transparent",

    "& input:disabled": {
      // opacity: props.isDisabledBackground ? "" : "40%",
      opacity: "40%",
    },

    "& .MuiOutlinedInput-input.Mui-disabled": {
      WebkitTextFillColor: "rgba(0, 0, 0, 1)",
    },

    "&.Mui-focused fieldset": {
      border: "1px solid #9C9898",
    },
  },
}));

const CssMenuItem = styled(MenuItem)((props) => ({
  ".MuiMenuItem-root": {
    fontFamily: "Poppins",
  },
}));

const CssTextFieldWithSymbol = styled(({ className, ...props }) => (
  <div className={className}>
    <CssTextField {...props} />
    <div className="otp-symbol"></div>
  </div>
))({
  position: "relative",
  display: "inline-block",
  "& .otp-symbol": {
    position: "absolute",
    top: "50%",
    right: "8px",
    transform: "translateY(-50%)",
    fontSize: "12px",
    color: "#666666",
    transition: "color 0.3s", // Add transition for smooth color change
    "&:hover": {
      color: "rgba(0, 0, 0, 0.87)",
    },
  },
});

export {
  CssTextField,
  CssSelect,
  CssTooltip,
  CssSearchTextField,
  CssMenuItem,
  CssTextFieldWithSymbol,
};
