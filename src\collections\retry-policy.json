{"header": "List of Retry policy", "columns": [{"header": "Group ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "Group name", "isHovered": true, "accessorKey": "attributes.retryGrpName"}, {"buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "button", "title": "View rules"}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"info": "Name of the group", "name": "retryGrpName", "size": 6, "type": "text", "title": "Group name", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Group name"}, {"type": "min", "value": 2, "message": "Min length is 2"}, {"type": "max", "value": 30, "message": "Max length is 30"}, {"type": "matches", "regex": "^(?=.*[a-zA-Z])(?=.*[0-9])[a-zA-Z0-9]+$", "message": "Only alphanumeric characters allowed"}], "defaultValue": "", "validationType": "string"}, {"info": "GSM errors can be classified into the following categories", "name": "failureResult", "size": 6, "type": "select", "title": "Error category", "isInfo": true, "options": [{"label": "User error", "value": 2}, {"label": "Provider error", "value": 3}, {"label": "Stack error", "value": 4}, {"label": "Internal error", "value": -1}, {"label": "AS error", "value": 8}, {"label": "Redirection error", "value": 9}, {"label": "SMPP error", "value": 10}, {"label": "CMID error", "value": 11}, {"label": "UCP error", "value": 12}, {"label": "HTTP error", "value": 13}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Error category"}], "defaultValue": "", "validationType": "string"}, {"info": "Each temporary error type has a pre-configured error code", "name": "failureError", "size": 6, "type": "select", "title": "Error", "isInfo": true, "isMandatory": true, "onClickPath": "retry-policy", "validations": [{"type": "required", "message": "Please select the Error"}], "defaultValue": "", "isDropDownApi": true, "validationType": "number"}, {"name": "retryPolicies", "size": 12, "type": "FieldTableMultiple", "fieldsData": [{"name": "retryInterval1", "type": "text", "validations": [{"type": "matches", "regex": "^[0-9]+$", "message": "Only posive integer allowed"}, {"type": "matches", "regex": "^(?:[0-9]{1,5})$", "message": "Value must be between 0 and 99999(5 digits)"}], "validationType": "string"}, {"name": "retryInterval2", "type": "text", "validations": [{"type": "matches", "regex": "^[0-9]+$", "message": "Only posive integer allowed"}, {"type": "matches", "regex": "^(?:[0-9]{1,5})$", "message": "Value must be between 0 and 99999(5 digits)"}], "validationType": "string"}], "headersData": ["Retry attempt", "Retry interval (in seconds)", "Retry attempt", "Retry interval (in seconds)"], "isMandatory": false, "initialValues": {"retryPolicies": {"defaultCount": 1, "defaultValues": {"retryInterval1": 0, "retryInterval2": 0}}}, "validationType": "array"}], "formType": "simple", "buttonName": "+ Add new retry policy", "moduleData": "retry-policy", "moduleName": "Retry policy", "globalSearch": [{"label": "Group ID", "value": "id"}, {"label": "Group name", "value": "retryGrpName"}], "columnsSecondary": [{"header": "Error description", "columns": [{"header": "Category", "accessorKey": "failureResult"}, {"header": "Error name", "accessorKey": "failureError"}]}, {"buttons": [{"type": "image", "title": "viewRetry", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "secondaryButtonName": "View group details", "columnSecondaryHeader": " View retry policy group details"}