{"forms": [{"elements": [{"info": "Name of the rule to be configured.", "name": "ruleType", "size": 6, "type": "select", "title": "Rule type", "isInfo": true, "options": [{"label": "Other rules", "value": "Other rules"}], "isMandatory": true, "validations": [{"type": "required", "message": "Rule type is Required"}], "defaultValue": ""}, {"name": "rule", "size": 6, "type": "select", "title": "Rule", "options": [{"label": "Retry Rule", "value": "Retry Rule"}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select a Rule"}], "defaultValue": "", "validationType": "string"}, {"name": "messageType", "size": 6, "type": "select", "title": "Message Type", "options": [{"label": "Normal", "value": 1}, {"label": "Binary", "value": 2}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select Message Type"}], "defaultValue": "", "validationType": "string"}], "formName": "Basic"}, {"elements": [{"info": "Origin Inteface indicating AO and MO messages", "name": "retry_origin_interface", "size": 6, "type": "select", "title": "Origin Interface", "isInfo": true, "options": [{"label": "<PERSON><PERSON><PERSON>", "value": "-15n56"}, {"label": "All MO", "value": "0n56"}, {"label": "All AO", "value": "-1n1088$56"}, {"label": "All SMPP AO", "value": "-5n1089$56"}, {"label": "All UCP AO", "value": "-6n1090$56"}, {"label": "All CIMD AO", "value": "-8n1091$56"}, {"label": "All AT", "value": "-12n1073$1082"}, {"label": "All DR MT", "value": "-13n1083"}, {"label": "All DR AT", "value": "-14n1083"}, {"label": "Source MSISDN", "value": "1$56"}, {"label": "Destination MSISDN", "value": "2$56"}, {"label": "Source MSISDN-Destination MSISDN", "value": "1$2$56"}, {"label": "Destination Short Code", "value": "7$1082"}], "inputType": 39, "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Origin Interface"}, {"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1009, "visibilityConditions": {"rule": ["Retry Rule"]}}, {"info": "Retry Action indicating to retry or not to retry", "name": "retry_action", "size": 6, "type": "select", "title": "Retry Action", "isInfo": true, "options": [{"label": "No Retry", "value": "0"}, {"label": "Retry", "value": "55"}, {"label": "Retry Redirect", "value": "55$24"}, {"label": "<PERSON><PERSON> Relay", "value": "55$51"}], "inputType": 56, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 56, "visibilityConditions": {"rule": ["Retry Rule"], "retry_origin_interface": ["-15n56", "0n56", "-1n1088$56", "-5n1089$56", "-6n1090$56", "-8n1091$56", "-12n1073$1082", "-13n1083", "-14n1083", "1$56", "2$56", "1$2$56", "7$1082"]}}, {"info": "Retry Group of retry policies", "name": "retry_group", "size": 6, "type": "select", "title": "Retry Group", "isInfo": true, "inputType": 55, "queryInfo": {"label": "retryGrpName", "moduleName": "retry-policies"}, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 55, "visibilityConditions": {"rule": ["Retry Rule"], "retry_action": ["55", "55$24", "55$51"], "retry_origin_interface": ["-15n56", "0n56", "-1n1088$56", "-5n1089$56", "-6n1090$56", "-8n1091$56", "-12n1073$1082", "-13n1083", "-14n1083", "1$56", "2$56", "1$2$56", "7$1082"]}}, {"info": "Redirection list of SMSCs", "name": "redirection_list", "size": 6, "type": "select", "title": "Redirection List", "isInfo": true, "inputType": 24, "queryInfo": {"label": "redirectionListName", "filters": [{"field": "redirectionListType", "value": "E"}], "moduleName": "redirectional-lists"}, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 24, "visibilityConditions": {"rule": ["Retry Rule"], "retry_action": ["55$24"], "retry_origin_interface": ["-15n56", "0n56", "-1n1088$56", "-5n1089$56", "-6n1090$56", "-8n1091$56", "-12n1073$1082", "1$56", "2$56", "1$2$56", "7$1082"]}}, {"info": "SS7/SIGTRAN Relay List", "name": "relay_list", "size": 6, "type": "select", "title": "Relay List", "isInfo": true, "inputType": 51, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 51, "visibilityConditions": {"rule": ["Retry Rule"], "retry_action": ["55$51"], "retry_origin_interface": ["-15n56", "0n56", "-1n1088$56", "-5n1089$56", "-6n1090$56", "-8n1091$56", "1$56", "2$56", "1$2$56"]}}, {"info": null, "name": "all_ao_esme", "size": 6, "type": "select", "title": "ESME", "isInfo": true, "inputType": 39, "queryInfo": {"label": "systemId", "moduleName": "esme-accounts"}, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1088, "visibilityConditions": {"rule": ["Retry Rule"], "retry_origin_interface": ["-1n1088$56"]}}, {"info": null, "name": "all_smpp_ao_esme", "size": 6, "type": "select", "title": "ESME", "isInfo": true, "inputType": 39, "queryInfo": {"label": "syetemId", "moduleName": "esme-accounts"}, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1089, "visibilityConditions": {"rule": ["Retry Rule"], "retry_origin_interface": ["-5n1089$56"]}}, {"info": null, "name": "all_ucp_ao_esme", "size": 6, "type": "select", "title": "ESME", "isInfo": true, "inputType": 39, "queryInfo": {"label": "systemId", "filters": [{"field": "protocol", "value": 6}], "moduleName": "esme-accounts"}, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1090, "visibilityConditions": {"rule": ["Retry Rule"], "retry_origin_interface": ["-6n1090$56"]}}, {"info": null, "name": "all_cimd_ao_esme", "size": 6, "type": "select", "title": "ESME", "isInfo": true, "inputType": 39, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1091, "visibilityConditions": {"rule": ["Retry Rule"], "retry_origin_interface": ["-8n1091$56"]}}, {"info": null, "name": "all_at_esme", "size": 6, "type": "select", "title": "ESME", "isInfo": true, "inputType": 64, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1073, "visibilityConditions": {"rule": ["Retry Rule"], "retry_origin_interface": ["-12n1073$1082"]}}, {"info": "Source MSISDN series", "name": "src_msisdn_type", "size": 6, "type": "radio", "title": "Source MSISDN Type", "isInfo": true, "options": [{"label": "Number", "value": "2001"}, {"label": "List", "value": "32"}], "inputType": 1, "isMandatory": false, "validations": [{"type": "min", "value": null, "message": "Minimum value allowed is n1"}], "defaultValue": "2001", "guiInputType": 1, "visibilityConditions": {"rule": ["Retry Rule"], "retry_origin_interface": ["1$56", "1$2$56"]}}, {"info": "Source MSISDN Series", "name": "src_msisdn", "size": 6, "type": "text", "title": "Source MSISDN ", "isInfo": true, "inputType": 2001, "isMandatory": false, "guiInputType": 2001, "visibilityConditions": {"rule": ["Retry Rule"], "src_msisdn_type": ["2001"], "retry_origin_interface": ["1$56", "1$2$56"]}}, {"info": "Source MSISDN List", "name": "src_msisdn_list", "size": 6, "type": "select", "title": "Source MSISDN List", "isInfo": true, "inputType": 32, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 32, "visibilityConditions": {"rule": ["Retry Rule"], "src_msisdn_type": ["32"], "retry_origin_interface": ["1$56", "1$2$56"]}}, {"info": "Destination MSISDN series", "name": "dst_msisdn_type", "size": 6, "type": "radio", "title": "Destination MSISDN Type", "isInfo": true, "options": [{"label": "Number", "value": "2002"}, {"label": "List", "value": "33"}], "inputType": 2, "isMandatory": false, "validations": [{"type": "min", "value": null, "message": "Minimum value allowed is n1"}], "defaultValue": "2002", "guiInputType": 2, "visibilityConditions": {"rule": ["Retry Rule"], "retry_origin_interface": ["2$56", "1$2$56"]}}, {"info": "Destination MSISDN Series", "name": "dest_msisdn", "size": 6, "type": "text", "title": "Destination MSISDN ", "isInfo": true, "inputType": 2002, "isMandatory": false, "guiInputType": 2002, "visibilityConditions": {"rule": ["Retry Rule"], "dst_msisdn_type": ["2002"], "retry_origin_interface": ["2$56", "1$2$56"]}}, {"info": "Destination MSISDN List", "name": "dst_msisdn_list", "size": 6, "type": "select", "title": "Destination MSISDN List", "isInfo": true, "inputType": 33, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 33, "visibilityConditions": {"rule": ["Retry Rule"], "dst_msisdn_type": ["33"], "retry_origin_interface": ["2$56", "1$2$56"]}}, {"info": "Destination Short Code of ESME Application", "name": "dst_short_code_type", "size": 6, "type": "radio", "title": "Destination Short Code Type", "isInfo": true, "options": [{"label": "Number", "value": "2007"}, {"label": "List", "value": "65"}], "inputType": 7, "isMandatory": false, "validations": [{"type": "min", "value": null, "message": "Minimum value allowed is n1"}], "defaultValue": "2007", "guiInputType": 7, "visibilityConditions": {"rule": ["Retry Rule"], "retry_origin_interface": ["7$1082"]}}, {"info": "Destination Shortcode", "name": "dst_short_code", "size": 6, "type": "text", "title": "Destination Shortcode", "isInfo": true, "inputType": 2007, "isMandatory": false, "guiInputType": 2007, "visibilityConditions": {"rule": ["Retry Rule"], "dst_short_code_type": ["2007"], "retry_origin_interface": ["7$1082"]}}, {"info": "Destination Short Code List", "name": "dest_short_code_list", "size": 6, "type": "select", "title": "Dest Short Code List", "isInfo": true, "inputType": 65, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 65, "visibilityConditions": {"rule": ["Retry Rule"], "dst_short_code_type": ["65"], "retry_origin_interface": ["7$1082"]}}, {"info": "Rule Status indicating active or not active", "name": "rule_status", "size": 6, "type": "select", "title": "Rule Status", "isInfo": true, "options": [{"label": "Active", "value": "1"}, {"label": "Inactive", "value": "2"}], "inputType": 1011, "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Rule Status"}, {"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1011, "visibilityConditions": {"rule": ["Retry Rule"]}}, {"info": "Reason for the rule configured", "name": "rule_comment", "size": 6, "type": "text", "title": "Reason", "isInfo": true, "inputType": 1012, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Reason"}, {"type": "min", "value": 0, "message": "Minimum value allowed is 0"}, {"type": "max", "value": 128, "message": "Maximum value allowed is 128"}], "guiInputType": 1012, "visibilityConditions": {"rule": ["Retry Rule"]}}], "formName": "Advanced"}], "header": "List of rules", "columns": [{"header": "Rule ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "Rule", "accessorKey": "attributes.rule"}, {"header": "Input scenarios", "accessorKey": "attributes.inputScenarios"}, {"header": "Interface", "accessorKey": "attributes.interface"}, {"header": "Action", "accessorKey": "attributes.action"}, {"header": "User", "accessorKey": "attributes.user"}, {"header": "Status", "accessorKey": "attributes.status"}, {"header": "Date", "accessorKey": "attributes.date"}, {"header": "Reason", "accessorKey": "attributes.reason"}, {"buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "formType": "stepper", "buttonName": "+ Add Rule", "moduleData": "rule-configuration", "moduleName": "Rule Configuration", "globalSearch": [{"label": "Rule ID", "value": "id"}, {"label": "Reason", "value": "reason"}]}