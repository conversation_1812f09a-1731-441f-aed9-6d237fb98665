import React, { useContext } from "react";
import { Form, Formik } from "formik";
import * as Yup from "yup";
import Button from "../Buttons/Button";
import { multiStepFormUserRoleContext } from "../../context/UserRoleContext";
import {
  renderFields,
  generateValidationSchema,
  getStepperDefaultInitialValues,
} from "../../../../common/common";
import Grid from "@mui/material/Grid";

function RoleManagemtForm(props) {
  const {
    handleNextClick,
    setFormData,
    formData,
    handleNextClickStep,
    currentStep,
  } = useContext(multiStepFormUserRoleContext);
  const handleSubmit = (values) => {
    handleNextClick();
    handleNextClickStep();
    let newFormData = Object.assign(formData, values, {
      name: values.name,
      roleType: values.roleType,
      portalName: values.portalName,
    });
    setFormData(newFormData);
  };
  const formDataValues = props?.props?.data?.forms?.[currentStep];
  const elements = formDataValues.elements;

  return (
    <Formik
      initialValues={getStepperDefaultInitialValues(
        elements,
        formData,
        props?.props?.moduleName
      )}
      validateOnMount={true}
      validationSchema={Yup.object().shape(
        generateValidationSchema(elements, formData)
      )}
      enableReinitialize={true}
      onSubmit={(values) => {
        handleSubmit(values);
      }}
    >
      {({ values, setFieldValue, errors, touched }) => (
        <Form>
          <fieldset className="md:m-4 rounded-md">
            <Grid container spacing={4}>
              {renderFields(
                elements,
                values,
                setFieldValue,
                "",
                formData,
                props.action,
                "",
                "",
                "",
                "",
                props.props?.moduleName,
                "",
                "",
                "",
                props.header
              )}
            </Grid>
          </fieldset>
          <div className="flex justify-end items-end mt-10 mr-10 mb-5">
            <Button
              type="submit"
              label="Next >"
              value="submit"
              buttonClassName="w-full md:w-[154px] h-[40px] text-base mb-3 rounded-[5px] ml-5"
            />
          </div>
        </Form>
      )}
    </Formik>
  );
}

export default RoleManagemtForm;
