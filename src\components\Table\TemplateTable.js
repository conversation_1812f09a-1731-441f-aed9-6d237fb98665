import React, { useEffect, useState } from "react";
import { FieldArray } from "formik";
import { PlusCircleIcon, DeleteIcon } from "../../icons";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
} from "@mui/material";
import TextFieldWrapper from "../FormsUI/TextField";
import Select from "../FormsUI/Select";
import CustomSwitch from "../ToggleSwitch/CustomSwitchHttp";
import ResultPerPageComponent from "../Pagination/ResultPerPage";
import Pagination from "../Pagination/Pagination";
import { DEFAULT_PAGE_SIZE, MAX_HTTP_TEMPLATE } from "../../common/config";
import theme from "../../tailwind-theme";
import DeleteDialog from "../Dialog/DeleteDialog";

const RenderTableBody = ({
  name,
  values,
  fields,
  currentPage,
  limitPerPage,
  isDisabled,
  onDeleteClick,
}) => {
  const startIndex = (currentPage - 1) * limitPerPage;
  const endIndex = startIndex + limitPerPage;
  const paginatedRows = values[name]?.slice(startIndex, endIndex);

  return (
    <FieldArray name={name}>
      {({ push, remove }) => (
        <>
          {paginatedRows?.map((row, index) => (
            <TableRow key={index + startIndex}>
              {fields.map((field, fieldIndex) => (
                <TableCell key={fieldIndex}>
                  {field.type === "select" ? (
                    <Select
                      name={`${name}.${index + startIndex}.${field.name}`}
                      options={field.options}
                      className="w-full"
                      isDisabled={isDisabled}
                    />
                  ) : field.type === "static" ? (
                    <div className="text-black">
                      {field.value.replace("{index}", index + startIndex + 1)}
                    </div>
                  ) : field.type === "text" ? (
                    <TextFieldWrapper
                      name={`${name}.${index + startIndex}.${field.name}`}
                      className="w-full"
                      isDisabled={isDisabled}
                    />
                  ) : field.type === "switch" ? (
                    <div className="flex">
                      <CustomSwitch
                        name={`${name}.${index + startIndex}.${field.name}`}
                        checked={row.ACTIVE}
                        disabled={isDisabled || row.REQ === "Y"}
                      />
                      <span style={{ marginLeft: 5, marginTop: 8 }}>
                        {values[name][index + startIndex].ACTIVE === 1
                          ? "Active"
                          : "Inactive"}
                      </span>
                    </div>
                  ) : null}
                </TableCell>
              ))}
              <TableCell>
                {values[name]?.length > 1 && row.isDelete && !isDisabled && (
                  <IconButton
                    type="button"
                    onClick={() => onDeleteClick(index + startIndex, remove)}
                  >
                    <DeleteIcon />
                  </IconButton>
                )}
              </TableCell>
            </TableRow>
          ))}
          <TableRow>
            {!isDisabled ? (
              <>
                {" "}
                {Array(fields.length - 1)
                  .fill("")
                  .map((_, idx) => (
                    <TableCell key={idx}>
                      <div
                        style={{
                          border: "2px dotted #BEBEBE",
                          padding: "8px",
                          borderRadius: "10px",
                          height: "36px",
                          width: "100%",
                        }}
                      ></div>
                    </TableCell>
                  ))}
              </>
            ) : null}

            <TableCell colSpan={fields.length + 1} align="right">
              {!isDisabled && (
                <IconButton
                  type="button"
                  onClick={() => {
                    const nonDeletableRows = values[name].filter((row) => {
                      return row.isDelete;
                    });
                    if (nonDeletableRows.length < MAX_HTTP_TEMPLATE) {
                      const newEntry = fields.reduce((acc, field) => {
                        if (field.name === "COMVIVA_PARAM") {
                          acc[field.name] = "NA";
                          acc.REQ = "N";
                        } else if (field.name === "ACTIVE") {
                          acc[field.name] = 1;
                        } else if (field.name === "isDelete") {
                          acc[field.name] = true;
                        } else {
                          acc[field.name] = "";
                        }
                        return acc;
                      }, {});
                      push(newEntry);
                    }
                  }}
                >
                  <PlusCircleIcon />
                </IconButton>
              )}
            </TableCell>
          </TableRow>
        </>
      )}
    </FieldArray>
  );
};

const RenderTable = ({
  name,
  values,
  headers,
  fields,
  currentPage,
  limitPerPage,
  isDisabled,
  onDeleteClick,
}) => (
  <TableContainer component={Paper} sx={{ boxShadow: "none", maxHeight: 400 }}>
    <Table sx={{ minWidth: 550 }} aria-label="sticky table" stickyHeader>
      <TableHead sx={{ bgcolor: theme.backgroundColor.bgTable }}>
        <TableRow>
          {headers.map((header, index) => (
            <TableCell
              key={index}
              sx={{
                position: "sticky",
                top: 0,
                zIndex: 1,
                backgroundColor: theme.backgroundColor.bgTable,
                fontWeight: "bold",
              }}
            >
              <div className="ml-0.5 text-xs font-semibold">{header}</div>
            </TableCell>
          ))}
          <TableCell
            sx={{
              position: "sticky",
              top: 0,
              zIndex: 1,
              backgroundColor: theme.backgroundColor.bgTable,
              fontWeight: "bold",
            }}
          />
        </TableRow>
      </TableHead>
      <TableBody>
        <RenderTableBody
          name={name}
          values={values}
          fields={fields}
          currentPage={currentPage}
          limitPerPage={limitPerPage}
          isDisabled={isDisabled}
          onDeleteClick={onDeleteClick}
        />
      </TableBody>
    </Table>
  </TableContainer>
);

const TemplateTable = ({ name, values, headers, fields, isDisabled }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [limitPerPage, setLimitPerPage] = useState(DEFAULT_PAGE_SIZE);
  const [previousFields, setPreviousFields] = useState(fields);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [rowToDelete, setRowToDelete] = useState(null);

  const handleLimitChange = (e) => {
    setLimitPerPage(e.target.value);
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const totalRows = values[name]?.length || 0;
  const startRange = (currentPage - 1) * limitPerPage + 1;
  const endRange = Math.min(startRange + limitPerPage - 1, totalRows);

  const onDeleteClick = (index, remove) => {
    setRowToDelete(() => () => remove(index));
    setDeleteDialog(true);
  };

  const handleDeleteConfirm = () => {
    if (rowToDelete) {
      rowToDelete();
      setRowToDelete(null);
      setDeleteDialog(false);
      //  setSuccessDialog(true);
      // setMessage("Record deleted successfully");
    }
  };

  useEffect(() => {
    if (JSON.stringify(name) !== JSON.stringify(previousFields)) {
      setCurrentPage(1);
      setPreviousFields(name);
    }
  }, [name, previousFields]);

  return (
    <>
      <RenderTable
        name={name}
        values={values}
        headers={headers}
        fields={fields}
        currentPage={currentPage}
        limitPerPage={limitPerPage}
        isDisabled={isDisabled}
        onDeleteClick={onDeleteClick}
      />
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          margin: "20px 0px 20px 20px",
          width: "95%",
        }}
      >
        <div className="flex">
          <ResultPerPageComponent
            limit={limitPerPage}
            handleLimitChange={handleLimitChange}
          />
          <div
            style={{
              display: "flex",
              fontSize: "14px",
              padding: "10px 0px 0px 10px",
              color: "gray",
            }}
          >
            {`${startRange}-${endRange} of ${totalRows} rows`}
          </div>
        </div>
        <Pagination
          className="pagination-bar"
          currentPage={currentPage}
          totalCount={totalRows}
          pageSize={limitPerPage}
          onPageChange={handlePageChange}
        />

        <DeleteDialog
          show={deleteDialog}
          onHide={() => {
            setDeleteDialog(false);
          }}
          onConfirm={handleDeleteConfirm}
          title={
            <>
              Are you sure you want to delete?
              <br />
              (Deletion will only happen on click of save)
            </>
          }
        />
      </div>
    </>
  );
};

export default TemplateTable;
