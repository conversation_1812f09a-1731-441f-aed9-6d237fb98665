{"header": "SMSC sessions", "columns": [{"header": "SMSC name", "idToName": true, "moduleName": "smsc-host", "accessorKey": "attributes.smscName", "attributeKey": "accId"}, {"header": "Session type", "accessorKey": "attributes.sessType"}, {"header": "Node IP", "accessorKey": "attributes.nodeIp"}, {"id": "bindTime", "header": "Bind date & time", "isDateTime": true, "accessorKey": "attributes.bindTime", "filterVariant": "datetime-range", "dateTimeFormat": "ddd DD MMM YYYY hh:mm:ss:SSS A"}, {"isBg": true, "header": "Session status", "accessorKey": "attributes.status"}, {"buttons": [{"type": "image", "title": "", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"name": "sessType", "options": [{"label": "Transceiver", "value": 3}, {"label": "Transmitter", "value": 2}, {"label": "Receiver", "value": 1}]}, {"name": "status", "options": [{"label": "SMSC UP", "value": "A"}, {"label": "SMSC DOWN", "value": "I"}]}, {"name": "accType", "options": [{"label": "ESME", "value": "E"}, {"label": "SMSC", "value": "R"}]}], "moduleData": "smsc-session", "moduleName": "SMSC sessions", "globalSearch": [{"label": "SMSC name", "value": "accId"}], "isDeleteIcon": false, "confirmButton": false, "dropdownOptions": {"field": "status", "options": [{"label": "ALL", "query": "", "value": "ALL"}, {"label": "SMSC UP", "query": "filters[status][$eq]=a", "value": "a"}, {"label": "SMSC DOWN", "query": "filters[status][$eq]=i", "value": "i"}], "defaultQuery": "", "defaultValue": "ALL"}}