import { CssTooltip } from "../components/FormsUI/StyledComponent";
import Pagination from "../components/Pagination/Pagination";
import ResultPerPage from "../components/Pagination/ResultPerPage";
import { moduleConfiguration } from "./constants";
import EditIcon from "../icons/editing.png";
import DeleteIcon from "../icons/trash.svg";
import { useState } from "react";
import { updateESMEStatus } from "../lib/list-api";
import CustomSwitchComponent from "../components/ToggleSwitch/CustomSwitchComponent";
export const ActionIcon = ({ title, src, onClick, className }) => (
  <CssTooltip title={title} placement="top" arrow>
    <img
      className={className ? className : "w-4 h-4 cursor-pointer"}
      src={src}
      alt={title}
      onClick={onClick}
    />
  </CssTooltip>
);

export const handleEditClick = (
  e,
  row,
  schemaDetails,
  collectionName,
  navigate,
  setCurrentStep,
  setGeneralInfo,
  setBillingInfo,
  setPaymentInfo,
  setRequiredInfo,
  setRowDetails
) => {
  e.stopPropagation();
  const rowId = row.original.id;
  const connType =
    row?.original?.attributes?.connType === ("SMPP" || "S")
      ? "S"
      : row?.original?.attributes?.connType === ("HTTP" || "H")
      ? "H"
      : "M";

  if (collectionName === moduleConfiguration.scAtLCRModuleName) {
    navigate(
      `/app/list/${moduleConfiguration.scAtLCRModuleName}/records/${rowId}`,
      {
        state: {
          selectedRow: row?.original,
          moduleData: moduleConfiguration.scATLCRImportName,
          dropdownDetails: schemaDetails?.elements,
        },
      }
    );
  } else if (collectionName === moduleConfiguration.lcrConfiguration) {
    navigate(
      `/app/list/${moduleConfiguration.lcrConfiguration}/records/${rowId}`,
      {
        state: {
          selectedRow: row?.original,
          moduleData: moduleConfiguration.lcrConfiguration,
          dropdownDetails: schemaDetails?.elements,
        },
      }
    );
  } else if (collectionName === moduleConfiguration.customerSupplier) {
    navigate(
      `/app/list/${moduleConfiguration.customerSupplier}/edit/${rowId}`,
      {
        state: {
          isEdit: true,
          isView: false,
          id: rowId,
        },
      }
    );
    setCurrentStep(0);
    setGeneralInfo([]);
    setBillingInfo([]);
    setPaymentInfo([]);
    setRequiredInfo([]);
  } else if (
    collectionName === moduleConfiguration.redirectionalAccModuleName
  ) {
    navigate(`/app/list/${collectionName}/edit/${rowId}/${connType}`, {
      state: {
        collectionName: collectionName,
        selectedRow: row?.original,
      },
    });
    setRowDetails(row?.original);
  } else {
    navigate(`/app/list/${collectionName}/edit/${rowId}`, {
      state: {
        collectionName: collectionName,
        selectedRow: row?.original,
      },
    });
    setRowDetails(row?.original);
  }
};

export const handleViewClick = (
  e,
  row,
  schemaDetails,
  collectionName,
  navigate,
  setRowDetails
) => {
  e.stopPropagation();
  const rowId = row.original.id;
  if (collectionName === moduleConfiguration.scAtLCRModuleName) {
    navigate(
      `/app/list/${moduleConfiguration.scAtLCRModuleName}/records/${rowId}`,
      {
        state: {
          selectedRow: row?.original,
          moduleData: moduleConfiguration.scATLCRImportName,
          dropdownDetails: schemaDetails?.elements,
        },
      }
    );
  } else if (collectionName === moduleConfiguration.lcrConfiguration) {
    navigate(
      `/app/list/${moduleConfiguration.lcrConfiguration}/records/${rowId}`,
      {
        state: {
          selectedRow: row?.original,
          moduleData: moduleConfiguration.lcrConfiguration,
          dropdownDetails: schemaDetails?.elements,
        },
      }
    );
  } else if (collectionName === moduleConfiguration.customerSupplier) {
    navigate(
      `/app/list/${moduleConfiguration.customerSupplier}/view/${rowId}`,
      {
        state: {
          isEdit: false,
          isView: true,
          id: rowId,
        },
      }
    );
  }
};

export const handleViewListClick = (
  e,
  row,
  collectionName,
  navigate,
  setRowDetails
) => {
  e.stopPropagation();
  const rowId = row?.original?.id;
  if (collectionName === moduleConfiguration.pointCodeModuleName) {
    navigate(
      `/app/list/${moduleConfiguration.pointCodeListModuleName}/table/${rowId}`,
      {
        state: {
          rowId: rowId,
          collectionName: moduleConfiguration.pointCodeListModuleName,
          isViewList: true,
          name: row?.original?.attributes?.pointCode,
        },
      }
    );
  } else if (
    collectionName === moduleConfiguration.redirectionalAccModuleName
  ) {
    navigate(
      `/app/list/${moduleConfiguration.redirectionalListModuleName}/table/${rowId}`,
      {
        state: {
          rowId: rowId,
          collectionName: moduleConfiguration.redirectionalListModuleName,
          isViewList: true,
          name: row?.original?.attributes?.smscName,
          connType: row?.original?.attributes?.connType,
        },
      }
    );
  }
};

export const handleViewAccountClick = (e, row, collectionName, navigate) => {
  e.stopPropagation();

  if (collectionName === moduleConfiguration.pointCodeListModuleName) {
    const pointcodeId = row?.original?.attributes?.point_codes?.map(
      (item) => item.id
    );
    navigate(
      `/app/list/${moduleConfiguration.pointCodeModuleName}/table/${row?.original?.id}`,
      {
        state: {
          rowId: row?.original?.id,
          rowData: pointcodeId,
          collectionName: moduleConfiguration.pointCodeModuleName,
          isViewAccount: true,
          name: row?.original?.attributes?.redirectionListName,
        },
      }
    );
  } else if (collectionName === "redirectional-lists") {
    const redirAccId = row?.original?.attributes?.smsc_hosts?.data?.map(
      (item) => item.id
    );
    navigate(`/app/list/redirectional-accounts/table/${row?.original?.id}`, {
      state: {
        rowId: row?.original?.id,
        rowData: redirAccId,
        collectionName: "redirectional-accounts",
        isViewAccount: true,
        name: row?.original?.attributes?.redirectionListName,
        connType: row?.original?.attributes?.connType,
      },
    });
  }
};

export const handleViewDataClick = (
  e,
  row,
  collectionName,
  navigate,
  setRowDetails
) => {
  e.stopPropagation();
  const rowId = row.original.id;
  const connType =
    row?.original?.attributes?.connType === ("SMPP" || "S")
      ? "S"
      : row?.original?.attributes?.connType === ("HTTP" || "H")
      ? "H"
      : "M";
  if (collectionName === moduleConfiguration.redirectionalAccModuleName) {
    navigate(`/app/list/${collectionName}/viewData/${rowId}/${connType}`, {
      state: {
        collectionName: collectionName,
        selectedRow: row?.original,
      },
    });
    setRowDetails(row?.original);
  } else {
    navigate(`/app/list/${collectionName}/viewData/${row.original.id}`, {
      state: {
        collectionName: collectionName,
        selectedRow: row?.original,
      },
    });
  }

  setRowDetails(row?.original);
};

export const handleConfirmationButton = (
  e,
  jsonData,
  collectionName,
  navigate,
  setShowXmlDialog,
  setCurrentStep
) => {
  if (
    jsonData?.schemadetails?.moduleData ===
    moduleConfiguration.scATLCRImportName
  ) {
    navigate(`/app/list/${moduleConfiguration.scAtLCRModuleName}/add`, {
      state: {
        dropdownDetails: jsonData?.schemadetails?.elements,
      },
    });
  } else if (collectionName === moduleConfiguration.lcrConfiguration) {
    navigate(`/app/list/${moduleConfiguration.lcrConfiguration}/add`, {
      state: {
        dropdownDetails: jsonData?.schemadetails?.elements,
      },
    });
  } else if (collectionName === "operators") {
    setShowXmlDialog(true);
  } else if (
    jsonData?.schemadetails?.moduleData === moduleConfiguration.customerSupplier
  ) {
    navigate(`/app/list/${moduleConfiguration.customerSupplier}/add`, {
      state: {
        isEdit: false,
        isView: false,
      },
    });
  } else {
    setCurrentStep(0);
    navigate(`/app/list/${collectionName}/add`, {
      state: {
        collectionName: collectionName,
      },
    });
  }
};

export const renderPagination = ({
  pagination,
  limitPerPage,
  currentPage,
  handleLimitChange,
  isSearching,
  filteredRow,
  handlePageChange,
}) => {
  const totalRows = pagination?.meta?.pagination?.total;

  if (pagination && totalRows !== undefined) {
    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          margin: "5px 0px 5px 5px",
          width: "95%",
        }}
      >
        <div className="flex">
          <ResultPerPage
            limit={limitPerPage}
            handleLimitChange={handleLimitChange}
          />
          <div
            style={{
              display: "flex",
              fontSize: "14px",
              padding: "10px 0px 0px 10px",
              color: "#808080",
            }}
          >
            {!isSearching ? (
              <>
                {totalRows === 0 ? 0 : (currentPage - 1) * limitPerPage + 1}-
                {Math.min(limitPerPage * currentPage, totalRows)} of {totalRows}{" "}
                rows
              </>
            ) : (
              <>
                {`${
                  filteredRow === 0 ? 0 : (currentPage - 1) * limitPerPage + 1
                }  - ${
                  (currentPage - 1) * limitPerPage + filteredRow
                } of ${totalRows} rows (Matched for this page)`}
              </>
            )}
          </div>
        </div>
        <Pagination
          className="pagination-bar"
          currentPage={currentPage}
          totalCount={totalRows || 0}
          pageSize={limitPerPage}
          onPageChange={handlePageChange}
          isSearching={isSearching}
        />
      </div>
    );
  }

  return null;
};
export const getColumnsConfig = ({
  navigate,
  moduleData,
  moduleConfiguration,
  dropdownDetails,
  setDeleteDialog,
  setIdToDelete,
  lcrId,
  lcrTypeValue,
  total,
  actions,
  setShowLastColumn,
}) => {
  const columns = [
    {
      accessorKey: "serialNoId",
      header: "Serial No ID",
      enableSorting: false,
    },
    {
      accessorKey: "destinationOperator",
      header:
        moduleData !== moduleConfiguration.lcrConfiguration
          ? "Source operator"
          : "Destination operator",
      enableSorting: false,
      Cell: ({ row }) => {
        const operatorName = row.original.destinationOperator || "";
        const displayText =
          operatorName.length > 25
            ? `${operatorName.substring(0, 25)}...`
            : operatorName;
        return (
          <span style={{ display: "flex" }} title={operatorName}>
            {displayText}{" "}
          </span>
        );
      },
    },
    {
      accessorKey: "regionCountry",
      header: "Region/Country",
      enableSorting: false,
    },
    {
      accessorKey: "mccMnc",
      header: "MCC/MNC",
      enableSorting: false,
    },
  ];

  if (moduleData !== moduleConfiguration.lcrConfiguration) {
    columns.push({
      accessorKey: "shortCode",
      header: "Short code",
      enableSorting: false,
    });
  }

  columns.push(
    ...Array.from({ length: 10 }, (_, i) => ({
      accessorKey: `supplier${i + 1}List`,
      header: `Supplier ${i + 1} List`,
      enableSorting: false,
      Cell: ({ row }) => {
        const value = row?.original?.[`supplier${i + 1}List`] ?? "-";
        const tooltipValue = row?.original?.[`supplier${i + 1}Tooltip`] ?? "-";
        setShowLastColumn && setShowLastColumn(!actions);
        return (
          <CssTooltip
            title={tooltipValue}
            placement="top"
            arrow
            bgColor="#D9D9D9"
            color="#000000"
          >
            <div>{value}</div>
          </CssTooltip>
        );
      },
    }))
  );

  actions !== false &&
    columns.push({
      accessorKey: "actions",
      Cell: ({ row }) => (
        <div className="flex justify-center items-center gap-3">
          <CssTooltip title="Edit" placement="top" arrow>
            <img
              className="w-4 h-4 cursor-pointer"
              src={EditIcon}
              alt="edit"
              onClick={() => {
                const moduleName =
                  moduleData === moduleConfiguration.lcrConfiguration
                    ? moduleConfiguration.lcrConfiguration
                    : moduleConfiguration.scAtLCRModuleName;

                navigate(`/app/list/${moduleName}/edit/${lcrId}`, {
                  state: {
                    isEdit: true,
                    id: lcrId,
                    parentId: row?.original?.parentId,
                    isFileUpload: false,
                    dropdownDetails: dropdownDetails,
                    isViewLCR: true,
                    moduleData: moduleData,
                    lcrTypeValue: lcrTypeValue,
                  },
                });
              }}
            />
          </CssTooltip>
          <CssTooltip title="Delete" placement="top" arrow>
            <img
              className={`w-4 h-4 ${
                total === 1 ? "cursor-not-allowed" : "cursor-pointer"
              }`}
              src={DeleteIcon}
              alt="delete"
              onClick={() => {
                if (total > 1) {
                  setIdToDelete(row?.original?.parentId);
                  setDeleteDialog(true);
                }
              }}
            />
          </CssTooltip>
        </div>
      ),
      enableColumnFilter: false,
      enableSorting: false,
      enableColumnPinning: true,
    });

  return columns;
};

export const getEsmeAccountsColumnConfig = (x, refetch) => ({
  accessorKey: x.accessorKey,
  header: x.header,
  enableColumnFilter: false,
  enableSorting: false,
  Cell: ({ cell }) => {
    const initialAccStatus = cell.row.original.attributes?.accountEnable;
    const [accStatus, setAccStatus] = useState(initialAccStatus);

    const isConnected = (accStatus || "").toLowerCase() === "y";
    const isDisabled = accStatus === "D" || accStatus === "E";

    const handleESMESwitchChange = (newValue) => {
      const newStatus = newValue ? "Y" : "N";
      setAccStatus(newStatus);

      const id = cell.row.original.id;
      const reqData = {
        formTitle: "esme-account",
        data: { accountEnable: newStatus },
      };

      updateESMEStatus({ id, reqData }, { onSuccess: () => refetch() });
    };

    return (
      <div className="display-flex">
        <CustomSwitchComponent
          name={x.accessorKey}
          onChange={handleESMESwitchChange}
          checked={isConnected}
          isDisabled={isDisabled}
        />
        {isConnected
          ? "Active"
          : accStatus === "E"
          ? "Expired"
          : accStatus === "D"
          ? "Disabled"
          : "Inactive"}
      </div>
    );
  },
});

export const getRuleConfigurationsColumnConfig = (x, refetch, setRender) => ({
  accessorKey: x.accessorKey,
  header: x.header,
  enableColumnFilter: false,
  enableSorting: false,
  Cell: ({ cell }) => {
    const ruleStatus = cell.row.original.attributes?.rule_status;
    const isConnected = (ruleStatus || "") === "Active";

    const handleStatusChange = (newValue) => {
      const newStatus = newValue ? "1" : "2";
      cell.row.original.attributes.rule_status = newStatus;
      setRender((prev) => !prev);

      const id = cell.row.original.id;
      const reqData = { formTitle: "rule", data: { rule_status: newStatus } };

      updateESMEStatus({ id, reqData }, { onSuccess: () => refetch() });
    };

    return (
      <div className="display-flex">
        <CustomSwitchComponent
          name={x.accessorKey}
          onChange={handleStatusChange}
          checked={isConnected}
        />
      </div>
    );
  },
});

export const getDefaultColumnConfig = (x, collectionName, updateRecordAPI) => ({
  accessorKey: x.accessorKey,
  header: x.header,
  enableColumnFilter: false,
  enableSorting: false,
  Cell: ({ cell }) => {
    const currentRuleStatus = cell.row.original.attributes?.status;
    const isConnected = currentRuleStatus.toLowerCase() === "a";

    const handleSwitchChange = (newValue) => {
      const newStatus = newValue ? "Connected" : "Disconnected";
      cell.row.original.attributes.status = newStatus;

      const id = cell.row.original.id;
      const reqData = { data: { status: newStatus } };

      updateRecordAPI({ moduleName: collectionName, id, reqData });
    };

    return (
      <div className="display-flex">
        <CustomSwitchComponent
          name={x.accessorKey}
          onChange={handleSwitchChange}
          checked={isConnected}
        />
        {isConnected ? "Connected" : "Disconnected"}
      </div>
    );
  },
});

export const getNestedValue = (obj, path) =>
  path.split(".").reduce((acc, key) => acc?.[key], obj);

export const getDetailBtnConfig = (
  x,
  collectionName,
  updateRecordAPI,
  navigate
) => ({
  accessorKey: x.accessorKey,
  header: x.header,
  enableColumnFilter: false,
  enableSorting: false,
  Cell: ({ cell }) => {
    const currentRuleStatus = cell.row.original.attributes?.status;

    const isConnected = currentRuleStatus.toLowerCase() === "a";
    const id = cell.row.original.attributes.accId;
    const customerBindName =
      cell.row.original.attributes.customerBindName !== null
        ? cell.row.original.attributes.customerBindName
        : "";
    const systemId = cell.row.original.attributes.systemId
      ? cell.row.original.attributes.systemId
      : "";
    const handleSwitchChange = (newValue) => {
      const newStatus = newValue ? "A" : "I";

      cell.row.original.attributes.status = newStatus;

      const moduleName = collectionName;
      const reqData = {
        data: {
          status: newStatus,
        },
      };
      updateRecordAPI({ moduleName, id, reqData });
    };

    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          gap: "10px",
        }}
      >
        <div
          key="details"
          className="border border-tableBorder p-2 rounded-md font-normal text-[10px] cursor-pointer mr-10"
          onClick={() =>
            navigate(`/app/list/number-of-sessions/${id}`, {
              state: { customerBindName, systemId },
            })
          }
        >
          Details
        </div>
        <CustomSwitchComponent
          name={x.accessorKey}
          onChange={handleSwitchChange}
          checked={isConnected}
        />
        {isConnected ? "Connected" : "Disconnected"}
      </div>
    );
  },
});
