import React from "react";
import LcrPolicyForm from "../../components/Forms/LcrPolicyForm";
import FormLayout from "../../components/FormLayout/FormLayout";
import { useLocation } from "react-router-dom";

function LcrPolicy() {
  const {
    state: {
      isEdit,
      id,
      parentId,
      dropdownDetails,
      isViewLCR,
      moduleData,
      lcrTypeValue,
    } = {},
  } = useLocation();
  return (
    <div className="mx-5 my-8">
      <FormLayout title={isEdit ? "Edit LCR" : "Add LCR"} header="List of LCR">
        <LcrPolicyForm
          isEdit={isEdit}
          id={id}
          parentId={parentId}
          dropdownDetails={dropdownDetails}
          isViewLCR={isViewLCR}
          moduleData={moduleData}
          lcrTypeValue={lcrTypeValue}
        />
      </FormLayout>
    </div>
  );
}

export default LcrPolicy;
