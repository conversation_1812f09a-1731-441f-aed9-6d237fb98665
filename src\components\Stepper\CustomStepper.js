import * as React from "react";
import Box from "@mui/material/Box";
import Stepper from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import Tooltip from "@mui/material/Tooltip";
import { styled } from "@mui/material/styles";
import { useTranslation } from "react-i18next";
import { CheckCircle, RadioIcon, GreyCircleIcon } from "../../icons";
import { StepConnector } from "@mui/material";

// Professional minimal tooltip design
const StepInfoTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .MuiTooltip-tooltip`]: {
    backgroundColor: "#ffffff",
    color: "#374151",
    maxWidth: 220,
    minWidth: 200,
    fontSize: "12px",
    borderRadius: "8px",
    padding: "0",
    boxShadow: "0 10px 25px rgba(0, 0, 0, 0.15), 0 4px 6px rgba(0, 0, 0, 0.1)",
    border: "1px solid #e5e7eb",
    fontFamily: "OpenSanHebrew",
    lineHeight: 1.4,
    overflow: "hidden",
  },
  [`& .MuiTooltip-arrow`]: {
    color: "#ffffff",
    "&::before": {
      border: "1px solid #e5e7eb",
    },
  },
}));

// Enhanced tooltip content with modern card design
const renderTooltipContent = (stepperData, index) => {
  // Use stepperData if available, otherwise use sample data
  const data = stepperData?.[index];

  // Step titles for better context
  const stepTitles = {
    0: "General Information",
    1: "MNP Information",
    2: "Tadig Information",
    3: "Table Information",
  };

  if (data) {
    return (
      <div className="p-4">
        {/* Professional header */}
        <div className="flex items-center justify-between mb-3 pb-3 border-b border-gray-200">
          <div>
            <div className="text-gray-900 font-semibold text-sm">
              Step {index + 1} Details
            </div>
            <div className="text-gray-600 text-xs font-medium">
              {stepTitles[index] || "Step Information"}
            </div>
          </div>
          <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
            <span className="text-gray-700 text-xs font-semibold">
              {index + 1}
            </span>
          </div>
        </div>

        {/* Clean content layout */}
        <div className="space-y-2">
          {Object.entries(data).map(([key, value], idx) => (
            <div
              key={key}
              className="flex p-1.5 border-black bg-[#F3F3F3] rounded"
            >
              <div className="text-gray-800 text-xs font-medium w-24 flex-shrink-0">
                {key.replace(/([A-Z])/g, " $1").trim()} :
              </div>
              <div className="text-black text-xs ml-2 flex-1">
                {value || "Not provided"}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Sample data for different steps
  const sampleData = {
    0: {
      "Operator Name": "Operator_123",
      "Country Name": "Country123",
    },
    1: {
      "MNP Status": "✓ MNP correction enabled",
      Gateway: "value",
    },
    2: {
      "TADIG Code": "ABC123",
      "Country Initials": "US",
    },
  };

  const stepData = sampleData[index];
  if (!stepData) return null;

  return (
    <>
      {/* Header with step info */}
      <div className={`text-sm font-bold text-center p-2 bg-bgHeader`}>
        {stepTitles[index] || "Step Information"}
      </div>

      {/* Content */}

      <div className="space-y-2 p-3">
        {Object.entries(stepData).map(([key, value], idx) => (
          <div
            key={key}
            className="flex p-1.5 border-black bg-[#F3F3F3] rounded"
          >
            <div className="text-gray-800 text-xs font-medium w-24 flex-shrink-0">
              {key} :
            </div>
            <div className="text-black text-xs ml-2 flex-1">{value}</div>
          </div>
        ))}
      </div>
    </>
  );
};

function CustomStepper(props) {
  const { t } = useTranslation();
  console.log("CustomStepper props", props);
  return (
    <Box className="bg-[#374150] flex flex-col min-w-[250px] max-w-[250px] h-[650px] rounded-l-lg p-4 max-h-[76vh] overflow-y-auto sticky top-0">
      <Stepper
        activeStep={props.atStep}
        orientation="vertical"
        sx={{
          width: "100%",
          marginTop: "24px",
          ".MuiStepConnector-root": {
            flex: "unset !important",
          },
        }}
        connector={
          <StepConnector
            sx={{
              "& .MuiStepConnector-lineVertical": {
                minHeight: "7vh",
                marginLeft: "164px",
              },
            }}
          />
        }
      >
        {props.steps.map((label, index) => (
          <Step
            key={label}
            sx={{
              ".MuiStepper-vertical": {
                padding: "10px !important",
                marginTop: "0px",
                marginBottom: "0px",
              },
              "& .MuiStepLabel-root .Mui-active .MuiStepIcon-root": {
                color: "#ffffff", // circle color (ACTIVE)
              },
              "& .MuiStepLabel-root .Mui-active .MuiStepIcon-text": {
                display: "none", // hide step number (ACTIVE)
              },
              "& .MuiStepLabel-root .Mui-active .MuiStepIcon-completed": {
                display: "block", // show tick mark (ACTIVE)
              },
              "& .MuiStepLabel-root .Mui-completed .MuiStepIcon-root": {
                color: "white", // circle color (COMPLETED)
              },
              "& .MuiStepLabel-root .Mui-completed .MuiStepIcon-text": {
                display: "none", // hide step number (COMPLETED)
              },
              "& .MuiStepLabel-root .Mui-completed .MuiStepIcon-completed": {
                display: "block", // show tick mark (COMPLETED)
              },
              "& .MuiStepIcon-root": {
                width: 30, // increase circle size
                height: 30, // increase circle size
              },
              "& .MuiStepLabel-root": {
                padding: 0,
                width: "220px",
              },
              "& .MuiStepLabel-iconContainer": {
                order: 2,
                marginLeft: "10px",
              },
              "& .MuiStepLabel-labelContainer": {
                width: "70%",
                padding: "10px",
              },
              "& .MuiStepLabel-label": {
                wordBreak: "break-word",
                color: "#9C9898",
              },
              "& .MuiStepLabel-label.Mui-active": {
                wordBreak: "break-word",
                color: "white",
              },
              "& .MuiStepLabel-label.Mui-completed": {
                wordBreak: "break-word",
                color: "white",
              },
              "& .MuiStep-root .MuiStepLabel-label": {
                wordBreak: "break-word",
              },
            }}
          >
            <StepLabel
              StepIconComponent={() => {
                const IconComponent = () => {
                  if (index === props.atStep) {
                    return <RadioIcon style={{ marginLeft: "-2px" }} />;
                  } else if (index < props.atStep) {
                    return <CheckCircle />;
                  } else {
                    return <GreyCircleIcon style={{ marginLeft: "4px" }} />;
                  }
                };

                // Only show tooltip for completed steps
                if (index < props.atStep) {
                  return (
                    <StepInfoTooltip
                      title={renderTooltipContent(props.stepperData, index)}
                      arrow
                      placement="right"
                      enterDelay={300}
                      leaveDelay={200}
                    >
                      <div
                        style={{
                          cursor: "pointer",
                          position: "relative",
                          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                        }}
                        className="hover:scale-110 group relative"
                      >
                        <IconComponent />
                        {/* Enhanced interactive indicators */}
                        <div className="absolute -inset-2 bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 rounded-full opacity-0 group-hover:opacity-20 transition-all duration-500 animate-pulse"></div>
                        <div className="absolute -inset-1 bg-blue-400 rounded-full opacity-10 group-hover:opacity-25 transition-all duration-300"></div>
                        {/* Tooltip indicator dot */}
                        <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full opacity-80 group-hover:opacity-100 transition-opacity duration-300 shadow-lg">
                          <div className="absolute inset-0.5 bg-white rounded-full opacity-30"></div>
                        </div>
                      </div>
                    </StepInfoTooltip>
                  );
                }

                return <IconComponent />;
              }}
            >
              <div
                className="flex flex-col"
                style={{ fontFamily: "OpenSanHebrew" }}
              >
                <b className="text-end">{`Step ${index + 1}`} </b>
                <div className="text-end whitespace-nowrap"> {t(label)}</div>
              </div>
            </StepLabel>
          </Step>
        ))}
      </Stepper>
    </Box>
  );
}

export default CustomStepper;
