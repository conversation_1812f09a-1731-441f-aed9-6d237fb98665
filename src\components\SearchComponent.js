import { Formik, Form } from "formik";
import React, { useMemo } from "react";
import Select from "./FormsUI/Select";
import { CssSearchTextField } from "../components/FormsUI/StyledComponent";
import InputAdornment from "@mui/material/InputAdornment";
import { SearchIcon } from "../icons";
import IconButton from "@mui/material/IconButton";

function SearchComponent({
  globalSearch,
  onChange,
  setGlobalSearchSelect,
  dropdownFilter,
}) {
  // Extract connType value from dropdownFilter
  const connTypeValue = dropdownFilter?.split("=")[1];

  // Filter the globalSearch options if connTypeValue is 'M'
  const filteredGlobalSearch = useMemo(() => {
    if (connTypeValue === "M") {
      return globalSearch.filter(
        (option) =>
          option.value !== "smscUserName" &&
          option.value !== "msgCapacityPerSec"
      );
    }
    return globalSearch;
  }, [connTypeValue, globalSearch]);

  return (
    <div>
      <Formik
        initialValues={{
          filter: "",
        }}
      >
        {({ values, errors, status }) => (
          <Form>
            <div className="flex items-center">
              <div className="w-[150px]">
                <Select
                  name="filter"
                  options={filteredGlobalSearch}
                  placeholder={"Select filter"}
                  borderRadius="20px"
                  borderTopRightRadius="0px"
                  borderBottomRightRadius="0px"
                  bgColor="#D9D9D9"
                  border="transparent"
                  onChange={(selectedOption) => {
                    setGlobalSearchSelect(selectedOption.value);
                  }}
                  isSearchable={false}
                  width={"150px"}
                />
              </div>
              <div className="flex-grow">
                <CssSearchTextField
                  name="search"
                  placeholder={"Search here"}
                  type={"text"}
                  onChange={onChange}
                  className="w-full md:w-[300px]"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment
                        position="start"
                        sx={{
                          marginLeft: "4px",
                          marginRight: "2px",
                          padding: "0px",
                        }}
                      >
                        <IconButton
                          aria-label="search"
                          edge="start"
                          sx={{ padding: "0px" }}
                        >
                          <SearchIcon className="w-4 h-4" />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  inputProps={{
                    maxLength: 100,
                  }}
                  autoComplete="off"
                />
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}

export default SearchComponent;
