{"header": "List of operator cluster", "columns": [{"header": "Cluster ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "Cluster name", "accessorKey": "attributes.clusterName"}, {"header": "Cluster type", "accessorKey": "attributes.clusterType"}, {"align": "center", "buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"name": "", "size": 3, "type": "none", "title": ""}, {"info": "Enter a name for the cluster", "name": "clusterName", "size": 6, "type": "text", "title": "Operator cluster name", "isInfo": true, "isMandatory": true, "nonEditable": true, "validations": [{"type": "max", "value": 21, "message": "Max length is 21"}, {"type": "required", "message": "Please enter operator cluster name"}, {"type": "matches", "regex": "^[a-zA-Z](?=.*\\d)[a-zA-Z0-9_]*$", "message": "Only alphanumeric characters and underscore allowed. Begin with alphabet."}], "defaultValue": "", "validationType": "string"}, {"name": "", "size": 3, "type": "none", "title": ""}, {"name": "", "size": 3, "type": "none", "title": ""}, {"info": "Type for operator cluster. Default or Roaming", "name": "clusterType", "size": 6, "type": "select", "title": "Cluster type", "isInfo": true, "options": [{"label": "<PERSON><PERSON><PERSON>", "value": 0}, {"label": "Roaming", "value": 1}], "isMandatory": false, "nonEditable": true, "defaultValue": 0}, {"name": "", "size": 3, "type": "none", "title": ""}, {"name": "", "size": 3, "type": "none", "title": ""}, {"info": "List of operators available in the platform", "name": "operatorIds", "size": 6, "type": "multiselect", "title": "Available operators", "isInfo": true, "apiOptions": true, "isAddButton": true, "isMandatory": true, "onClickPath": "operators", "validations": [{"type": "required", "message": "Please select atleast one operator"}], "defaultValue": [], "validationType": "array"}, {"name": "", "size": 3, "type": "none", "title": ""}], "formType": "simple", "buttonName": "+ Add operator cluster", "moduleData": "operator-cluster", "moduleName": "Operator cluster", "globalSearch": [{"label": "Cluster ID", "value": "id"}, {"label": "Cluster name", "value": "clusterName"}], "navigationPath": true, "dropdownOptions": {"field": "clusterType", "options": [{"label": "All cluster type", "query": "", "value": "ALL"}, {"label": "<PERSON><PERSON><PERSON>", "query": "filters[clusterType][$eq]=0", "value": "0"}, {"label": "Roaming", "query": "filters[clusterType][$eq]=1", "value": "1"}], "defaultQuery": "", "defaultValue": "ALL"}}