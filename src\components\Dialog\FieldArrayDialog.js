import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
} from "@mui/material";
import { FieldArray, Formik, Form } from "formik";
import TextFieldWrapper from "../FormsUI/TextField";
import { PlusCircleIconOutline, DeleteIcon, CloseIcon } from "../../icons";
import ConfirmNNextButton from "../Buttons/Button";
import ResultPerPageComponent from "../Pagination/ResultPerPage";
import Pagination from "../Pagination/Pagination";
import Button from "../Buttons/OutlinedButton";
import theme from "../../tailwind-theme";
import DeleteDialog from "../Dialog/DeleteDialog";

export const DottedBox = () => (
  <div
    style={{
      border: "2px dotted #BEBEBE",
      padding: "8px",
      borderRadius: "10px",
      height: "36px",
      width: "100%",
    }}
  ></div>
);

const FieldArrayDialog = ({
  open,
  handleClose,
  name,
  values,
  onSave,
  isDisabled,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [limitPerPage, setLimitPerPage] = useState(10);
  const [editingIndex, setEditingIndex] = useState(null);
  const [addedRows, setAddedRows] = useState([]);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [deleteIndex, setDeleteIndex] = useState(null);

  useEffect(() => {
    if (open) {
      const previouslyAddedRows = Array.isArray(values[name])
        ? values[name]
            .map((row, index) => (row && row.signature ? index : null))
            .filter((index) => index !== null)
        : [];

      setAddedRows(previouslyAddedRows);
      setCurrentPage(1);
    }
  }, [open, values, name]);

  const handleLimitChange = (e) => {
    setLimitPerPage(parseInt(e.target.value, 10));
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleFormSubmit = async (submittedValues) => {
    try {
      await onSave(submittedValues);
      handleClose();
    } catch (error) {}
  };

  const handleDeleteClick = (index) => {
    setDeleteIndex(index);
    setDeleteDialog(true);
  };

  const handleRemove = (remove, setFieldValue) => {
    setDeleteLoading(true);
    const updatedValues = [...values[name]];
    if (deleteIndex === 0 && values[name].length === 1) {
      const clearedRow = { ...values[name][0] };
      Object.keys(clearedRow).forEach((key) => {
        clearedRow[key] = Array.isArray(clearedRow[key]) ? [] : "";
      });
      // updatedValues = clearedRow;
      setFieldValue(name, [clearedRow]);
    } else {
      remove(deleteIndex);

      const totalRowsAfterRemoval = values[name].length;
      const totalPagesAfterRemoval = Math.ceil(
        totalRowsAfterRemoval / limitPerPage
      );
      if (currentPage > totalPagesAfterRemoval && totalPagesAfterRemoval > 0) {
        setCurrentPage(totalPagesAfterRemoval);
      }
    }
    setDeleteLoading(false);
    setDeleteDialog(false);
  };

  const [showToolTip, setShowTooltip] = useState(true);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      maxHeight="md"
      fullWidth
      sx={{
        "& .MuiDialogTitle-root": {
          fontFamily: "OpenSanHebrew",
          fontSize: "12px",
          fontWeight: 1000,
        },
        "& .MuiTableCell-root": {
          fontFamily: "OpenSanHebrew",
          fontSize: "12px",
          fontWeight: 1000,
        },
      }}
    >
      <DialogTitle className="p-1 h-[44px]">
        <div className="flex justify-between items-center text-[12px] font-bold pt-2">
          <span className="pl-2">DPC Information</span>
          <CloseIcon
            className="h-[24px] w-[24px] cursor-pointer justify-self-end pr-2"
            onClick={handleClose}
          />
        </div>
      </DialogTitle>
      <DialogContent className="px-1">
        <Formik initialValues={values} onSubmit={handleFormSubmit}>
          {({ values, handleSubmit, handleChange, setFieldValue }) => {
            const rows = Array.isArray(values[name])
              ? values[name].filter((row) => row !== null && row !== undefined)
              : [];

            const displayRows =
              rows.length > 0
                ? rows
                : [
                    {
                      signature: "",
                      type: "",
                      internationalDPC: "",
                      comments: "",
                    },
                  ];

            const validRows = Array.isArray(values[name])
              ? values[name].filter((row) => {
                  return Object.values(row).some(
                    (value) =>
                      value !== undefined && value !== null && value !== ""
                  );
                })
              : [];

            const totalRows = displayRows.length;
            const totalFilledRows = validRows.length;
            const startRow = (currentPage - 1) * limitPerPage + 1;
            const endRow = Math.min(
              totalFilledRows * limitPerPage,
              totalFilledRows
            );
            const totalPages = Math.ceil(totalRows / limitPerPage);

            return (
              <Form>
                <FieldArray name={name}>
                  {({ push, remove }) => (
                    <>
                      <TableContainer
                        component={Paper}
                        sx={{
                          boxShadow: "none",
                          maxHeight: "56vh",
                          overflowY: "auto",
                        }}
                      >
                        <Table
                          sx={{
                            minWidth: 550,
                            width: "100%",
                            border: "1.5px solid #BEBEBE",
                          }}
                          aria-label="simple table"
                        >
                          <TableBody sx={{ whiteSpace: "nowrap" }}>
                            <TableRow
                              className="sticky top-0 bg-bgTable h-full w-full px-0"
                              sx={{ zIndex: 1000 }}
                            >
                              <TableCell>Signature</TableCell>
                              <TableCell>Type</TableCell>
                              <TableCell>International DPC</TableCell>
                              <TableCell>Comments</TableCell>
                              <TableCell />
                            </TableRow>
                            {displayRows
                              .slice(
                                (currentPage - 1) * limitPerPage,
                                currentPage * limitPerPage
                              )
                              .map((row, index) => {
                                const globalIndex =
                                  (currentPage - 1) * limitPerPage + index;

                                return (
                                  <TableRow key={globalIndex}>
                                    <TableCell>
                                      <TextFieldWrapper
                                        name={`${name}.${globalIndex}.signature`}
                                        value={row.signature ?? ""}
                                        onChange={handleChange}
                                        isDisabled={isDisabled}
                                        onFocus={() => setShowTooltip(false)}
                                        onBlur={() => setShowTooltip(false)}
                                        onMouseEnter={() =>
                                          setShowTooltip(true)
                                        }
                                        toolTip={showToolTip}
                                      />
                                    </TableCell>
                                    <TableCell>
                                      <TextFieldWrapper
                                        name={`${name}.${globalIndex}.type`}
                                        value={row.type ?? ""}
                                        onChange={handleChange}
                                        isDisabled={isDisabled}
                                        onFocus={() => setShowTooltip(false)}
                                        onBlur={() => setShowTooltip(false)}
                                        onMouseEnter={() =>
                                          setShowTooltip(true)
                                        }
                                        toolTip={showToolTip}
                                      />
                                    </TableCell>
                                    <TableCell>
                                      <TextFieldWrapper
                                        name={`${name}.${globalIndex}.internationalDPC`}
                                        value={row.internationalDPC ?? ""}
                                        onChange={handleChange}
                                        isDisabled={isDisabled}
                                        onFocus={() => setShowTooltip(false)}
                                        onBlur={() => setShowTooltip(false)}
                                        onMouseEnter={() =>
                                          setShowTooltip(true)
                                        }
                                        toolTip={showToolTip}
                                      />
                                    </TableCell>
                                    <TableCell>
                                      <TextFieldWrapper
                                        name={`${name}.${globalIndex}.comments`}
                                        value={row.comments ?? ""}
                                        onChange={handleChange}
                                        isDisabled={isDisabled}
                                        onFocus={() => setShowTooltip(false)}
                                        onBlur={() => setShowTooltip(false)}
                                        onMouseEnter={() =>
                                          setShowTooltip(true)
                                        }
                                        toolTip={showToolTip}
                                      />
                                    </TableCell>
                                    <TableCell>
                                      {(globalIndex === 0 &&
                                        rows[0] &&
                                        Object.values(rows[0]).some(
                                          (value) =>
                                            value &&
                                            value.toString().trim() !== ""
                                        )) ||
                                      totalRows > 1 ? (
                                        <IconButton
                                          onClick={() =>
                                            handleDeleteClick(globalIndex)
                                          }
                                          disabled={isDisabled}
                                        >
                                          <DeleteIcon />
                                        </IconButton>
                                      ) : null}
                                    </TableCell>
                                  </TableRow>
                                );
                              })}
                            {currentPage === totalPages && (
                              <TableRow className="sticky bottom-0 bg-white">
                                {Array(4)
                                  .fill("")
                                  .map((_, idx) => (
                                    <TableCell key={idx}>
                                      <DottedBox />
                                    </TableCell>
                                  ))}
                                <TableCell
                                  colSpan={5}
                                  align="right"
                                  className="p-1"
                                >
                                  <IconButton
                                    onClick={() => {
                                      push({
                                        signature: "",
                                        type: "",
                                        internationalDPC: "",
                                        comments: "",
                                      });
                                      setEditingIndex(rows.length);
                                      const lastPage = Math.ceil(
                                        (totalRows + 1) / limitPerPage
                                      );
                                      setCurrentPage(lastPage);
                                    }}
                                    disabled={
                                      isDisabled ||
                                      !Object.values(
                                        rows[rows.length - 1] || {}
                                      ).some(
                                        (value) =>
                                          value &&
                                          value.toString()?.trim() !== ""
                                      )
                                    }
                                  >
                                    <PlusCircleIconOutline
                                      style={{
                                        color:
                                          isDisabled ||
                                          !Object.values(
                                            rows[rows.length - 1] || {}
                                          ).some(
                                            (value) =>
                                              value &&
                                              value.toString()?.trim() !== ""
                                          )
                                            ? theme.textColor.mildGray
                                            : "black",
                                      }}
                                    />
                                  </IconButton>
                                </TableCell>
                              </TableRow>
                            )}
                          </TableBody>
                        </Table>
                      </TableContainer>

                      <div className="flex justify-center gap-5 pt-5 sticky bottom-0 bg-white">
                        <Button
                          label={isDisabled ? "Close" : "Cancel"}
                          buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
                          onClick={handleClose}
                        />
                        {!isDisabled && (
                          <ConfirmNNextButton
                            label={"Save"}
                            buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
                            onClick={() => {
                              handleSubmit();
                              setAddedRows([...addedRows, editingIndex]);
                            }}
                            type="submit"
                          />
                        )}
                      </div>

                      <div className="flex items-center justify-between my-5 ml-5 w-[95%] sticky bottom-0">
                        <div className="flex items-center">
                          <ResultPerPageComponent
                            limit={limitPerPage}
                            handleLimitChange={handleLimitChange}
                          />
                          <div className="text-[14px] text-gray-500 ml-5">
                            {totalFilledRows > 0
                              ? `${startRow} - ${endRow} of ${totalFilledRows} rows`
                              : "0 - 0 of 0 rows"}
                          </div>
                        </div>
                        <Pagination
                          className="pagination-bar"
                          currentPage={currentPage}
                          totalCount={totalRows}
                          pageSize={limitPerPage}
                          onPageChange={handlePageChange}
                        />
                      </div>
                      <DeleteDialog
                        show={deleteDialog}
                        onHide={() => setDeleteDialog(false)}
                        onConfirm={() => handleRemove(remove, setFieldValue)}
                        title={
                          <>
                            Are you sure you want to delete?
                            <br />
                            (Deletion will only happen on click of save)
                          </>
                        }
                        isLoading={deleteLoading}
                      />
                    </>
                  )}
                </FieldArray>
              </Form>
            );
          }}
        </Formik>
      </DialogContent>
    </Dialog>
  );
};

export default FieldArrayDialog;
