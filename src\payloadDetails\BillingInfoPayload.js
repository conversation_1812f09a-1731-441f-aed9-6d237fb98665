import dayjs from "dayjs";
import * as Yup from "yup";
import {
  billingLogic,
  billingModelType,
  billingType,
  fixedFeeAndPerSmsType,
  ocCompliance,
  operatorType,
  slabOperator,
  slabPricingType,
} from "../common/constants";

// BillingInfoPayload.js
const addBillingDetails = (type, values, payload) => {
  const planTypeKey = `cbmBillingPlanType${type}`;
  const fixedFeeKey = `fixedFee${type}`;
  const fixedFeeAndPerSmsTypeKey = `fixedFeeAndPerSmsType${type}`;
  const slabPricingTypeKey = `slabPricingType${type}`;
  const slabPricingKey = `slabPricing${type}`;
  const slabOperatorKey = `slabOperator${type}`;
  const slabPricingAddOperatorKey = `slabPricingAddOperator${type}`;
  const volumeKey = `volume${type}`;
  const cbmFromDate = `cbmFromDate${type}`;
  const cbmToDate = `cbmToDate${type}`;
  const planType = values[planTypeKey];
  const slabType = values[slabPricingTypeKey];
  const slabOperatorr = values[slabOperatorKey];

  payload[planTypeKey] = planType;
  payload[cbmFromDate] = values[cbmFromDate]
    ? dayjs(values[cbmFromDate]).format("YYYY-MM-DD")
    : null;
  payload[cbmToDate] = values[cbmToDate]
    ? dayjs(values[cbmToDate]).format("YYYY-MM-DD")
    : null;
  // Monthly recurring and Fixed fee or per sms
  if (
    planType === billingModelType["Monthly recurring"] ||
    planType === billingModelType["Fixed fee usage or per SMS"]
  ) {
    payload[fixedFeeKey] = values[fixedFeeKey];
  }
  //Fixed fee and per sms
  else if (planType === billingModelType["Fixed fee and per SMS"]) {
    payload[fixedFeeKey] = values[fixedFeeKey];
    payload[fixedFeeAndPerSmsTypeKey] = values[fixedFeeAndPerSmsTypeKey];
    if (values[fixedFeeAndPerSmsTypeKey] === fixedFeeAndPerSmsType.Volume) {
      payload[volumeKey] = values[volumeKey];
    }
  }
  //Slab wise and fixed fee slab price (Single Pricing)
  else if (
    (planType === billingModelType["Slab wise pricing"] ||
      planType === billingModelType["Fixed fee and slab"]) &&
    slabType === slabPricingType["Single price"]
  ) {
    payload[slabPricingTypeKey] = slabType;
    payload[slabPricingKey] = values[slabPricingKey];
    if (planType === billingModelType["Fixed fee and slab"]) {
      payload[fixedFeeKey] = values[fixedFeeKey];
    }
  }
  //Slab wise and fixed fee slab price (Differential Pricing)
  else if (
    (planType === billingModelType["Slab wise pricing"] ||
      planType === billingModelType["Fixed fee and slab"]) &&
    slabType === slabPricingType["Differential price"]
  ) {
    payload[slabPricingTypeKey] = slabType;
    payload[slabOperatorKey] = slabOperatorr;

    if (Number(slabOperatorr) === slabOperator["All operators"]) {
      payload[slabPricingKey] = values[slabPricingKey];
    } else if (Number(slabOperatorr) === slabOperator["Add operators"]) {
      payload[slabPricingAddOperatorKey] = values[slabPricingAddOperatorKey];
    }
    if (planType === billingModelType["Fixed fee and slab"]) {
      payload[fixedFeeKey] = values[fixedFeeKey];
    }
  }
};

export const BillingInfoPayload = (
  values,
  generalInfoData,
  setBillingInfo,
  handleNext
) => {
  // Base payload with common fields
  const payload = {
    billingContact: values.billingContact,
    deliveryFlag: values.deliveryFlag,
    address: values.address,
    activationDate: values.activationDate
      ? dayjs(values.activationDate).format("YYYY-MM-DD")
      : null,
    contact: values.contact,
    billingType: values.billingType,
    billingLogic: values.billingLogic,
    emailId: values.emailId,
    billingEmailTo: values.billingEmailTo,
    billingEmailCc: values.billingEmailCc,
    bPartyBillLogic: values.bPartyBillLogic,
    enableAnumberRpt: values.enableAnumberRpt,
    sriFlag: values.sriFlag,
    currencyIn: values.currencyIn,
    currencyOut: values.currencyOut,
  };

  // Determine cbmBindType based on operatorType and cbmBindType values
  //const { operatorType } = generalInfoData;
  const { cbmBindType } = values;

  if (
    generalInfoData.operatorType === operatorType.Customer ||
    cbmBindType === operatorType.Customer
  ) {
    payload.cbmBindType = operatorType.Customer;
  } else if (
    generalInfoData.operatorType === operatorType.Supplier ||
    cbmBindType === operatorType.Supplier
  ) {
    payload.cbmBindType = operatorType.Supplier;
  } else if (
    generalInfoData.operatorType === operatorType.Both &&
    generalInfoData.operatorType === operatorType.Both
  ) {
    payload.cbmBindType = operatorType.Both;
  }

  // Add billing details based on cbmBindType
  if (
    payload.cbmBindType === operatorType.Customer ||
    payload.cbmBindType === operatorType.Both
  ) {
    addBillingDetails("Customer", values, payload);
  }
  if (
    payload.cbmBindType === operatorType.Supplier ||
    payload.cbmBindType === operatorType.Both
  ) {
    addBillingDetails("Supplier", values, payload);
  }
  // Set the billing info and proceed to the next step
  setBillingInfo(payload);
  handleNext();
};
export const getValidationSchema = (generalInfoData, validationDetail) => {
  const baseSchema = {
    billingContact: Yup.string().required("Billing contact name is required"),
    activationDate: Yup.date().required("Date of activation is required"),
    cbmBindType:
      generalInfoData.operatorType === operatorType.Both
        ? Yup.string().required("Billing model bind type is required")
        : Yup.string().nullable(),

    contact: Yup.number()
      .typeError("Please enter valid number")
      .integer("Please enter valid number"),

    emailId: Yup.string()
      .email("Please enter the correct email format")
      .max(300, "Maximum character that can be entered is 300 for Email")
      .test("unique-emailId", "Duplicate E-mail id's found", function (value) {
        const { billingEmailCc, billingEmailTo } = this.parent;

        if (value && billingEmailTo && value === billingEmailTo) {
          return this.createError({
            path: "emailId",
            message: "Duplicate E-mail ID's found",
          });
        }
        if (value && billingEmailCc && value === billingEmailCc) {
          return this.createError({
            path: "emailId",
            message: "Duplicate E-mail ID's found",
          });
        }
        return true;
      }),

    billingEmailCc: Yup.string()
      .email("Please enter the correct email format")
      .max(300, "Maximum character that can be entered is 300 for Email")
      .test(
        "unique-billingEmailCc",
        "Duplicate E-mail ID's found",
        function (value) {
          const { emailId, billingEmailTo } = this.parent;

          if (value && emailId && value === emailId) {
            return this.createError({
              path: "billingEmailCc",
              message: "Duplicate E-mail ID's found",
            });
          }
          if (value && billingEmailTo && value === billingEmailTo) {
            return this.createError({
              path: "billingEmailCc",
              message: "Duplicate E-mail ID's found",
            });
          }
          return true;
        }
      ),

    billingEmailTo: Yup.string()
      .email("Please enter the correct email format")
      .max(300, "Maximum character that can be entered is 300 for Email")
      .test(
        "unique-billingEmailTo",
        "Duplicate E-mail ID's found",
        function (value) {
          const { emailId, billingEmailCc } = this.parent;

          if (value && emailId && value === emailId) {
            return this.createError({
              path: "billingEmailTo",
              message: "Duplicate E-mail ID's found",
            });
          }
          if (value && billingEmailCc && value === billingEmailCc) {
            return this.createError({
              path: "billingEmailTo",
              message: "Duplicate E-mail ID's found",
            });
          }
          return true;
        }
      ),

    fixedFeeCustomer: Yup.number()
      .nullable()
      .min(1, "Minimum value allowed is 1")
      .max(10, "Maximum value allowed is 10")
      .typeError("Please enter a number value")
      .test("is-required", "Fixed fee is required", function (value) {
        const { cbmBindType, cbmBillingPlanTypeCustomer } = this.parent;
        const isRelevantBindType =
          [operatorType.Customer, operatorType.Both].includes(cbmBindType) ||
          [operatorType.Customer, operatorType.Both].includes(
            generalInfoData.operatorType
          );
        const isRelevantBillingPlan = [
          billingModelType["Monthly recurring"],
          billingModelType["Fixed fee and per SMS"],
          billingModelType["Fixed fee usage or per SMS"],
          billingModelType["Fixed fee and slab"],
        ].includes(Number(cbmBillingPlanTypeCustomer));
        return isRelevantBindType && isRelevantBillingPlan ? !!value : true;
      }),

    fixedFeeSupplier: Yup.number()
      .nullable()
      .min(1, "Minimum value allowed is 1")
      .max(10, "Maximum value allowed is 10")
      .typeError("Please enter a number value")
      .test("is-required", "Fixed fee is required", function (value) {
        const { cbmBindType, cbmBillingPlanTypeSupplier } = this.parent;
        const isRelevantBindType =
          [operatorType.Supplier, operatorType.Both].includes(cbmBindType) ||
          [operatorType.Supplier, operatorType.Both].includes(
            generalInfoData.operatorType
          );
        const isRelevantBillingPlan = [
          billingModelType["Monthly recurring"],
          billingModelType["Fixed fee and per SMS"],
          billingModelType["Fixed fee usage or per SMS"],
          billingModelType["Fixed fee and slab"],
        ].includes(Number(cbmBillingPlanTypeSupplier));
        return isRelevantBindType && isRelevantBillingPlan ? !!value : true;
      }),

    volumeCustomer: Yup.number()
      .nullable()
      .when(
        [
          "cbmBindType",
          "cbmBillingPlanTypeCustomer",
          "fixedFeeAndPerSmsTypeCustomer",
        ],
        {
          is: (
            cbmBindType,
            cbmBillingPlanTypeCustomer,
            fixedFeeAndPerSmsTypeCustomer
          ) =>
            ([operatorType.Customer, operatorType.Both].includes(cbmBindType) ||
              [operatorType.Customer, operatorType.Both].includes(
                generalInfoData.operatorType
              )) &&
            Number(cbmBillingPlanTypeCustomer) ===
              billingModelType["Fixed fee and per SMS"] &&
            fixedFeeAndPerSmsTypeCustomer === fixedFeeAndPerSmsType.Volume,
          then: (schema) =>
            schema
              .min(1, "Minimum value allowed is 1")
              .max(10, "Maximum value allowed is 10")
              .typeError("Please enter a number value")
              .required("Volume is required"),
          otherwise: (schema) => schema.notRequired(),
        }
      ),

    volumeSupplier: Yup.number()
      .nullable()
      .when(
        [
          "cbmBindType",
          "cbmBillingPlanTypeSupplier",
          "fixedFeeAndPerSmsTypeSupplier",
        ],
        {
          is: (
            cbmBindType,
            cbmBillingPlanTypeSupplier,
            fixedFeeAndPerSmsTypeSupplier
          ) =>
            ([operatorType.Supplier, operatorType.Both].includes(cbmBindType) ||
              [operatorType.Supplier, operatorType.Both].includes(
                generalInfoData.operatorType
              )) &&
            Number(cbmBillingPlanTypeSupplier) ===
              billingModelType["Fixed fee and per SMS"] &&
            Number(fixedFeeAndPerSmsTypeSupplier) ===
              fixedFeeAndPerSmsType.Volume,
          then: (schema) =>
            schema
              .min(1, "Minimum value allowed is 1")
              .max(10, "Maximum value allowed is 10")
              .typeError("Please enter a number value")
              .required("Volume is required"),
          otherwise: (schema) => schema.notRequired(),
        }
      ),

    cbmBillingPlanTypeCustomer: Yup.string()
      .nullable()
      .test(
        "is-required",
        "Billing model type (Customer) is required",
        function (value) {
          const { cbmBindType } = this.parent;
          return cbmBindType === operatorType.Customer ||
            cbmBindType === operatorType.Both ||
            generalInfoData.operatorType === operatorType.Customer
            ? !!value
            : true;
        }
      ),
    cbmBillingPlanTypeSupplier: Yup.string()
      .nullable()
      .test(
        "is-required",
        "Billing model type (Supplier) is required",
        function (value) {
          const { cbmBindType } = this.parent;
          return cbmBindType === operatorType.Supplier ||
            cbmBindType === operatorType.Both ||
            generalInfoData.operatorType === operatorType.Supplier
            ? !!value
            : true;
        }
      ),
    cbmFromDateCustomer: Yup.date()
      .nullable()
      .test(
        "is-required",
        "From date (Customer) is required",
        function (value) {
          const { cbmBindType } = this.parent;
          return cbmBindType === operatorType.Customer ||
            cbmBindType === operatorType.Both ||
            generalInfoData.operatorType === operatorType.Customer
            ? !!value
            : true;
        }
      ),

    cbmToDateCustomer: Yup.date()
      .nullable()
      .test("is-required", "To date (Customer) is required", function (value) {
        const { cbmBindType } = this.parent;
        return cbmBindType === operatorType.Customer ||
          cbmBindType === operatorType.Both ||
          generalInfoData.operatorType === operatorType.Customer
          ? !!value
          : true;
      })
      .test(
        "is-greater-than-from-date",
        "To date (Customer) must be greater than From date (Customer)",
        function (value) {
          const { cbmFromDateCustomer } = this.parent;
          const shouldValidate =
            this.parent.cbmBindType === operatorType.Customer ||
            this.parent.cbmBindType === operatorType.Both ||
            generalInfoData.operatorType === operatorType.Customer;

          // Only validate the date comparison if the condition is met
          if (shouldValidate && cbmFromDateCustomer) {
            return value > cbmFromDateCustomer;
          }
          return true;
        }
      ),

    cbmFromDateSupplier: Yup.date()
      .nullable()
      .test(
        "is-required",
        "From date (Supplier) is required",
        function (value) {
          const { cbmBindType } = this.parent;
          return cbmBindType === operatorType.Supplier ||
            cbmBindType === operatorType.Both ||
            generalInfoData.operatorType === operatorType.Supplier
            ? !!value
            : true;
        }
      ),

    cbmToDateSupplier: Yup.date()
      .nullable()
      .test("is-required", "To date (Supplier) is required", function (value) {
        const { cbmBindType } = this.parent;
        return cbmBindType === operatorType.Supplier ||
          cbmBindType === operatorType.Both ||
          generalInfoData.operatorType === operatorType.Supplier
          ? !!value
          : true;
      })
      .test(
        "is-greater-than-from-date",
        "To date (Supplier) must be greater than From date (Supplier)",
        function (value) {
          const { cbmFromDateSupplier } = this.parent;
          const shouldValidate =
            this.parent.cbmBindType === operatorType.Supplier ||
            this.parent.cbmBindType === operatorType.Both ||
            generalInfoData.operatorType === operatorType.Supplier;

          // Only validate the date comparison if the condition is met
          if (shouldValidate && cbmFromDateSupplier) {
            return value > cbmFromDateSupplier;
          }
          return true;
        }
      ),
  };
  const isBillingPlanTypeCustomerValid =
    validationDetail.cbmBillingPlanTypeCustomer ===
      billingModelType["Slab wise pricing"] ||
    validationDetail.cbmBillingPlanTypeCustomer ===
      billingModelType["Fixed fee and slab"];

  const isSlabPricingTypeCustomerValid =
    validationDetail.slabPricingTypeCustomer ===
      slabPricingType["Single price"] ||
    (validationDetail.slabPricingTypeCustomer ===
      slabPricingType["Differential price"] &&
      validationDetail.slabOperatorCustomer === slabOperator["All operators"]);

  const isOperatorTypeCustomerValid =
    generalInfoData.operatorType === operatorType.Customer ||
    (generalInfoData.operatorType === operatorType.Both &&
      (validationDetail.cbmBindType === operatorType.Customer ||
        validationDetail.cbmBindType === operatorType.Both));
  // Conditional Slab Pricing Customer Validation
  if (
    isBillingPlanTypeCustomerValid &&
    isSlabPricingTypeCustomerValid &&
    isOperatorTypeCustomerValid
  ) {
    baseSchema.slabPricingCustomer = Yup.array().of(
      Yup.object().shape({
        pricingDetails: Yup.array()
          .of(
            Yup.object().shape({
              slab: Yup.number()
                .integer("Slab should be a number")
                .required("Slab is required")
                .typeError("Slab should be a number")
                .min(1, "Slab must be at least 1")
                .max(99000, "Slab must be at most 99000"),
              cost: Yup.number()
                .required("Cost is required")
                .typeError("Cost should be a number")
                .min(1, "Cost must be at least 1")
                .max(10, "Cost must be at most 10"),
            })
          )
          .test(
            "is-sorted",
            "Cost and Slab should be in ascending order",
            function (list) {
              const allCosts = list.map((item) => item.cost);
              const allSlabs = list.map((item) => item.slab);

              for (let i = 0; i < allCosts.length - 1; i++) {
                if (allCosts[i] >= allCosts[i + 1]) {
                  return this.createError({
                    path: `${this.path}.${i + 1}.cost`,
                    message: "Must be greater than the previous cost.",
                  });
                }
              }

              for (let i = 0; i < allSlabs.length - 1; i++) {
                if (allSlabs[i] >= allSlabs[i + 1]) {
                  return this.createError({
                    path: `${this.path}.${i + 1}.slab`,
                    message: "Must be greater than the previous slab.",
                  });
                }
              }

              return true;
            }
          ),
        costThereafter: Yup.number()
          .required("Cost Thereafter is required")
          .typeError("Cost Thereafter should be a number")
          .min(1, "Cost Thereafter must be at least 1")
          .max(10, "Cost Thereafter must be at most 10")
          .test(
            "is-greater-than-last-cost",
            "Must be greater than the previous cost.",
            function (value) {
              const pricingDetails = this.parent.pricingDetails;
              if (pricingDetails && pricingDetails.length > 0) {
                const lastCost = pricingDetails[pricingDetails.length - 1].cost;
                if (value <= lastCost) {
                  return this.createError({
                    path: `${this.path}`,
                    message: "Must be greater than the previous cost.",
                  });
                }
              }
              return true;
            }
          ),
      })
    );
  }
  // Conditional Slab Pricing Supplier Validation
  const isBillingPlanTypeValid =
    validationDetail.cbmBillingPlanTypeSupplier ===
      billingModelType["Slab wise pricing"] ||
    validationDetail.cbmBillingPlanTypeSupplier ===
      billingModelType["Fixed fee and slab"];

  const isSlabPricingTypeValid =
    validationDetail.slabPricingTypeSupplier ===
      slabPricingType["Single price"] ||
    (validationDetail.slabPricingTypeSupplier ===
      slabPricingType["Differential price"] &&
      validationDetail.slabOperatorSupplier === slabOperator["All operators"]);

  const isOperatorTypeValid =
    generalInfoData.operatorType === operatorType.Supplier ||
    (generalInfoData.operatorType === operatorType.Both &&
      (validationDetail.cbmBindType === operatorType.Supplier ||
        validationDetail.cbmBindType === operatorType.Both));

  if (isBillingPlanTypeValid && isSlabPricingTypeValid && isOperatorTypeValid) {
    baseSchema.slabPricingSupplier = Yup.array().of(
      Yup.object().shape({
        pricingDetails: Yup.array()
          .of(
            Yup.object().shape({
              slab: Yup.number()
                .integer("Slab should be a number")
                .required("Slab is required")
                .typeError("Slab should be a number")
                .min(1, "Slab must be at least 1")
                .max(99000, "Slab must be at most 99000"),
              cost: Yup.number()
                .required("Cost is required")
                .typeError("Cost should be a number")
                .min(1, "Cost must be at least 1")
                .max(10, "Cost must be at most 10"),
            })
          )
          .test(
            "is-sorted",
            "Cost and Slab should be in ascending order",
            function (list) {
              const allCosts = list.map((item) => item.cost);
              const allSlabs = list.map((item) => item.slab);

              for (let i = 0; i < allCosts.length - 1; i++) {
                if (allCosts[i] >= allCosts[i + 1]) {
                  return this.createError({
                    path: `${this.path}.${i + 1}.cost`,
                    message: "Must be greater than the previous cost.",
                  });
                }
              }

              for (let i = 0; i < allSlabs.length - 1; i++) {
                if (allSlabs[i] >= allSlabs[i + 1]) {
                  return this.createError({
                    path: `${this.path}.${i + 1}.slab`,
                    message: "Must be greater than the previous slab.",
                  });
                }
              }

              return true;
            }
          ),
        costThereafter: Yup.number()
          .required("Cost Thereafter is required")
          .typeError("Cost Thereafter should be a number")
          .min(1, "Cost Thereafter must be at least 1")
          .max(10, "Cost Thereafter must be at most 10")
          .test(
            "is-greater-than-last-cost",
            "Must be greater than the previous cost.",
            function (value) {
              const pricingDetails = this.parent.pricingDetails;
              if (pricingDetails && pricingDetails.length > 0) {
                const lastCost = pricingDetails[pricingDetails.length - 1].cost;
                if (value <= lastCost) {
                  return this.createError({
                    path: `${this.path}`,
                    message: "Must be greater than the previous cost.",
                  });
                }
              }
              return true;
            }
          ),
      })
    );
  }
  // Conditional Slab Pricing Add operator Customer Validation
  const billingAddOperatorCustomer =
    validationDetail.cbmBillingPlanTypeCustomer ===
      billingModelType["Slab wise pricing"] ||
    validationDetail.cbmBillingPlanTypeCustomer ===
      billingModelType["Fixed fee and slab"];

  const slabPricingAddOperatorCustomer =
    validationDetail.slabPricingTypeCustomer ===
      slabPricingType["Differential price"] &&
    validationDetail.slabOperatorCustomer === slabOperator["Add operators"];

  const isOperatorTypeCustomer =
    generalInfoData.operatorType === operatorType.Customer ||
    (generalInfoData.operatorType === operatorType.Both &&
      (validationDetail.cbmBindType === operatorType.Customer ||
        validationDetail.cbmBindType === operatorType.Both));

  if (
    billingAddOperatorCustomer &&
    slabPricingAddOperatorCustomer &&
    isOperatorTypeCustomer
  ) {
    baseSchema.slabPricingAddOperatorCustomer = Yup.array().of(
      Yup.object().shape({
        pricingDetails: Yup.array()
          .of(
            Yup.object().shape({
              slab: Yup.number()
                .integer("Slab should be a number")
                .required("Slab is required")
                .typeError("Slab should be a number")
                .min(1, "Slab must be at least 1")
                .max(99000, "Slab must be at most 99000"),
              cost: Yup.number()
                .required("Cost is required")
                .typeError("Cost should be a number")
                .min(1, "Cost must be at least 1")
                .max(10, "Cost must be at most 10"),
            })
          )
          .test(
            "is-sorted",
            "Cost and Slab should be in ascending order",
            function (list) {
              const allCosts = list.map((item) => item.cost);
              const allSlabs = list.map((item) => item.slab);

              for (let i = 0; i < allCosts.length - 1; i++) {
                if (allCosts[i] >= allCosts[i + 1]) {
                  return this.createError({
                    path: `${this.path}.${i + 1}.cost`,
                    message: "Must be greater than the previous cost.",
                  });
                }
              }

              for (let i = 0; i < allSlabs.length - 1; i++) {
                if (allSlabs[i] >= allSlabs[i + 1]) {
                  return this.createError({
                    path: `${this.path}.${i + 1}.slab`,
                    message: "Must be greater than the previous slab.",
                  });
                }
              }

              return true;
            }
          ),
        destOperator: Yup.string().required("Destination operator is required"),
        costThereafter: Yup.number()
          .required("Cost Thereafter is required")
          .typeError("Cost Thereafter should be a number")
          .min(1, "Cost Thereafter must be at least 1")
          .max(10, "Cost Thereafter must be at most 10")
          .test(
            "is-greater-than-last-cost",
            "Must be greater than the previous cost.",
            function (value) {
              const pricingDetails = this.parent.pricingDetails;
              if (pricingDetails && pricingDetails.length > 0) {
                const lastCost = pricingDetails[pricingDetails.length - 1].cost;
                if (value <= lastCost) {
                  return this.createError({
                    path: `${this.path}`,
                    message: "Must be greater than the previous cost.",
                  });
                }
              }
              return true;
            }
          ),
      })
    );
  }

  // Conditional Slab Pricing Add operator Supplier Validation
  const billingAddOperatorSupplier =
    validationDetail.cbmBillingPlanTypeSupplier ===
      billingModelType["Slab wise pricing"] ||
    validationDetail.cbmBillingPlanTypeSupplier ===
      billingModelType["Fixed fee and slab"];

  const slabPricingAddOperatorSupplier =
    validationDetail.slabPricingTypeSupplier ===
      slabPricingType["Differential price"] &&
    validationDetail.slabOperatorSupplier === slabOperator["Add operators"];

  const isOperatorTypeValidSupplier =
    generalInfoData.operatorType === operatorType.Supplier ||
    (generalInfoData.operatorType === operatorType.Both &&
      (validationDetail.cbmBindType === operatorType.Supplier ||
        validationDetail.cbmBindType === operatorType.Both));

  if (
    billingAddOperatorSupplier &&
    slabPricingAddOperatorSupplier &&
    isOperatorTypeValidSupplier
  ) {
    baseSchema.slabPricingAddOperatorSupplier = Yup.array().of(
      Yup.object().shape({
        pricingDetails: Yup.array()
          .of(
            Yup.object().shape({
              slab: Yup.number()
                .integer("Slab should be a number")
                .required("Slab is required")
                .typeError("Slab should be a number")
                .min(1, "Slab must be at least 1")
                .max(99000, "Slab must be at most 99000"),
              cost: Yup.number()
                .required("Cost is required")
                .typeError("Cost should be a number")
                .min(1, "Cost must be at least 1")
                .max(10, "Cost must be at most 10"),
            })
          )
          .test(
            "is-sorted",
            "Cost and Slab should be in ascending order",
            function (list) {
              const allCosts = list.map((item) => item.cost);
              const allSlabs = list.map((item) => item.slab);

              for (let i = 0; i < allCosts.length - 1; i++) {
                if (allCosts[i] >= allCosts[i + 1]) {
                  return this.createError({
                    path: `${this.path}.${i + 1}.cost`,
                    message: "Must be greater than the previous cost.",
                  });
                }
              }

              for (let i = 0; i < allSlabs.length - 1; i++) {
                if (allSlabs[i] >= allSlabs[i + 1]) {
                  return this.createError({
                    path: `${this.path}.${i + 1}.slab`,
                    message: "Must be greater than the previous slab.",
                  });
                }
              }

              return true;
            }
          ),
        destOperator: Yup.string().required("Destination operator is required"),
        costThereafter: Yup.number()
          .required("Cost Thereafter is required")
          .typeError("Cost Thereafter should be a number")
          .min(1, "Cost Thereafter must be at least 1")
          .max(10, "Cost Thereafter must be at most 10")
          .test(
            "is-greater-than-last-cost",
            "Must be greater than the previous cost.",
            function (value) {
              const pricingDetails = this.parent.pricingDetails;
              if (pricingDetails && pricingDetails.length > 0) {
                const lastCost = pricingDetails[pricingDetails.length - 1].cost;
                if (value <= lastCost) {
                  return this.createError({
                    path: `${this.path}`,
                    message: "Must be greater than the previous cost.",
                  });
                }
              }
              return true;
            }
          ),
      })
    );
  }

  return Yup.object().shape(baseSchema);
};

export const billingInitialValue = {
  billingContact: "",
  deliveryFlag: "Y",
  address: "",
  activationDate: dayjs().format("YYYY-MM-DD"),
  contact: "",
  billingType: billingType.Commercial,
  billingLogic: billingLogic.Submission,
  emailId: "",
  billingEmailTo: "",
  billingEmailCc: "",
  bPartyBillLogic: null,
  enableAnumberRpt: ocCompliance.aNumberReportNeeded,
  sriFlag: ocCompliance.sriFlag,
  cbmBindType: "",
  cbmBillingPlanTypeCustomer: null,
  cbmBillingPlanTypeSupplier: null,
  currencyIn: "EURO",
  currencyOut: "EURO",
  cbmFromDateCustomer: null,
  cbmToDateCustomer: null,
  cbmFromDateSupplier: null,
  cbmToDateSupplier: null,
  fixedFeeAndPerSmsTypeSupplier: fixedFeeAndPerSmsType.Volume,
  fixedFeeAndPerSmsTypeCustomer: fixedFeeAndPerSmsType.Volume,
  fixedFeeCustomer: null,
  fixedFeeSupplier: null,
  volumeCustomer: null,
  volumeSupplier: null,
  slabPricingTypeCustomer: slabPricingType["Single price"],
  slabPricingTypeSupplier: slabPricingType["Single price"],
  slabPricingCustomer: [
    {
      costThereafter: "",
      pricingDetails: [
        {
          sno: "",
          slab: "",
          cost: "",
        },
      ],
    },
  ],
  slabPricingSupplier: [
    {
      costThereafter: "",
      pricingDetails: [
        {
          sno: "",
          slab: "",
          cost: "",
        },
      ],
    },
  ],
  slabOperatorCustomer: slabOperator["All operators"],
  slabOperatorSupplier: slabOperator["All operators"],
  slabPricingAddOperatorCustomer: [
    {
      destOperator: "",
      costThereafter: "",
      pricingDetails: [
        {
          sno: "",
          slab: "",
          cost: "",
        },
      ],
    },
  ],
  slabPricingAddOperatorSupplier: [
    {
      destOperator: "",
      costThereafter: "",
      pricingDetails: [
        {
          sno: "",
          slab: "",
          cost: "",
        },
      ],
    },
  ],
};
