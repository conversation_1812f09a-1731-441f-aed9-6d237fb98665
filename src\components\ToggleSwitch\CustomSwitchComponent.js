import { Formik, Form } from "formik";
import React from "react";
import CustomSwitch from "./CustomSwitchHttp";

function CustomSwitchComponent({
  name,
  onChange,
  checked,
  disabled,
  isDisabled,
  marginLeft,
  label,
}) {
  return (
    <div>
      <Formik
        initialValues={{
          attributes: {
            status: checked ? 1 : 0,
          },
        }}
        enableReinitialize
      >
        {({ values }) => (
          <Form>
            <div className="flex items-center" style={{ marginLeft }}>
              <CustomSwitch
                name={name}
                checked={values[name] === 1 || values}
                onChange={(newValue) => {
                  if (onChange) {
                    onChange(newValue);
                  }
                }}
                disabled={isDisabled || disabled}
                label={label}
                options={[]}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}

export default CustomSwitchComponent;
