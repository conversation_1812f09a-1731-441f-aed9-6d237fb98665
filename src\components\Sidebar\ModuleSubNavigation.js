import React, { useState } from "react";

const ModuleSubNavigation = ({ sideBarItems }) => {
  const [index, setIndex] = useState(0);
  const [subIndex, setSubIndex] = useState(0);
  return (
    <div className="grid grid-cols-4 bg-white mt-5">
      <div className="grid-cols-1 pt-10">
        <ol>
          {sideBarItems?.map((x, i) => {
            return x?.subMenu ? (
              <div className="font-bold text-sm ml-5 py-3 ">
                {x.name}
                <div className="border-t border-subNavBorder mt-3">
                  {x?.subMenu?.map((y, ind) => {
                    return (
                      <li
                        key={ind}
                        onClick={() => {
                          setIndex(i);
                          setSubIndex(ind);
                        }}
                        className={`py-3 pl-5 font-normal text-sm cursor-pointer ${
                          index === i && subIndex === ind
                            ? "bg-bgSubNavActive"
                            : ""
                        }`}
                      >
                        {y.name}
                      </li>
                    );
                  })}
                </div>
              </div>
            ) : (
              <li
                key={i}
                onClick={() => {
                  setIndex(i);
                  setSubIndex(null);
                }}
                className={`py-3 pl-5 text-sm font-bold cursor-pointer ${
                  index === i ? "bg-bgSubNavActive" : ""
                }`}
              >
                {x.name}
              </li>
            );
          })}
        </ol>
      </div>

      <div className="col-span-3" id="right-panel">
        {index > 0 && subIndex >= 0 && sideBarItems[index]?.subMenu
          ? React.createElement(
              sideBarItems[index]?.subMenu[subIndex]?.component
            )
          : React.createElement(sideBarItems[index].component)}
      </div>
    </div>
  );
};

export default ModuleSubNavigation;
