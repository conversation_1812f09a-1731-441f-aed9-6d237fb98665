import { useParams } from "react-router-dom";
import FormTemplate from "../components/Templates/FormTemplate";
import { useQuery } from "react-query";
import { getPageSchema } from "../lib/list-api";
import { useState } from "react";
import FormLayout from "../components/FormLayout/FormLayout";
import { useNavigate } from "react-router-dom";
import StepForm from "./StepForm";
import BreadcrumbNavigation from "../components/BreadCrumbNavigation";
import MultipleFormTemplate from "../components/Templates/MultipleFormTemplate";
import MultiBlockTemplate from "../components/Templates/MultiBlockTemplate";

const SimpleForm = () => {
  const params = useParams();
  const [elements, setElements] = useState([]);
  const [respData, setRespData] = useState([]);
  const navigate = useNavigate();
  const { isLoading } = useQuery([params.module], getPageSchema, {
    refetchOnWindowFocus: false,
    onSuccess: (resp) => {
      let elementsData =
        resp?.data?.data?.[0]?.attributes?.schemadetails.formType ===
        "multipleBlock"
          ? resp?.data?.data?.[0]?.attributes?.schemadetails?.blocks
          : resp?.data?.data?.[0]?.attributes?.schemadetails?.elements;
      // setRespData(customerData);
      // setElements(customerData?.elements);
      //  setElements(customerData?.blocks);
      setRespData(resp?.data?.data?.[0]?.attributes?.schemadetails);
      setElements(elementsData);
    },
  });
  const getTitle = (action, moduleName) => {
    const formattedModuleName =
      moduleName.includes("MNP") || moduleName.includes("ESME")
        ? moduleName
        : moduleName.toLowerCase();

    switch (action) {
      case "add":
        return `Add ${formattedModuleName}`;
      case "edit":
        return `Edit ${formattedModuleName}`;
      case "viewData":
        return `View ${formattedModuleName}`;
      default:
        return "";
    }
  };
  return (
    <>
      <div>
        {!isLoading && respData ? (
          <>
            {respData?.formType === "simple" ? (
              <FormLayout
                title={getTitle(params.action, respData.moduleName)}
                header={respData?.header}
                module={params.module}
                action={params.action}
              >
                <FormTemplate
                  elements={elements}
                  action={params.action}
                  id={params.id}
                  moduleName={params.module}
                  route01={respData?.route01}
                  route02={respData?.route02}
                  handleCancel={() => {
                    navigate(`/app/list/${params.module}`);
                  }}
                  moduleNameValue={respData?.moduleName}
                  moduleData={respData?.moduleData}
                  formType={respData?.formType}
                  navigationPath={respData?.navigationPath}
                  header={respData?.header}
                ></FormTemplate>{" "}
              </FormLayout>
            ) : respData?.formType === "multiple" ? (
              <>
                <div>
                  <BreadcrumbNavigation
                    title={respData?.moduleName}
                  ></BreadcrumbNavigation>

                  <MultipleFormTemplate
                    formData={respData}
                    action={params.action}
                    moduleName={params.module}
                    moduleNameValue={respData?.moduleName}
                    handleCancel={() => {
                      navigate("/app/home");
                    }}
                  ></MultipleFormTemplate>
                </div>
              </>
            ) : respData?.formType === "stepper" ? (
              <FormLayout
                title={getTitle(params.action, respData.moduleName)}
                header={respData?.header}
                stepper={true}
                action={params.action}
              >
                <StepForm
                  data={respData}
                  action={params.action}
                  moduleName={params.module}
                  id={params.id}
                  handleCancel={() => {
                    navigate(`/app/list/${params.module}`);
                  }}
                  moduleNameValue={respData?.moduleName}
                  moduleData={respData?.moduleData}
                  navigationPath={respData?.navigationPath}
                  header={respData?.header}
                ></StepForm>
              </FormLayout>
            ) : respData?.formType === "multipleBlock" ? (
              <>
                <div>
                  <FormLayout
                    title={getTitle(params.action, respData.moduleName)}
                    header={respData?.header}
                    module={params.module}
                    action={params.action}
                  >
                    <MultiBlockTemplate
                      blocks={elements}
                      action={params.action}
                      id={params.id}
                      moduleName={params.module}
                      handleCancel={() => {
                        navigate(`/app/list/${params.module}`);
                      }}
                      moduleNameValue={respData?.moduleName}
                      moduleData={respData?.moduleData}
                      wholeModuleData={respData}
                      navigationPath={respData?.navigationPath}
                      header={respData?.header}
                    ></MultiBlockTemplate>{" "}
                  </FormLayout>
                </div>
              </>
            ) : (
              <></>
            )}
          </>
        ) : null}
      </div>{" "}
    </>
  );
};

export default SimpleForm;
