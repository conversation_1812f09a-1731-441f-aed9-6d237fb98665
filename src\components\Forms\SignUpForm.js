import React, { useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Title from "../FormsUI/PageHeader";
import InputLabel from "../FormsUI/InputLabel";
import TextField from "../FormsUI/TextField";
import Button from "../Buttons/Button";
import Select from "../FormsUI/Select";
import { BusinessType } from "../../common/constants";
import PhoneNumberInput from "../FormsUI/PhoneNumber";
import {
  mobileNumberValidation,
  emailValidation,
  passwordValidation,
  firstNameValidation,
  lastNameValidation,
  companyNameValidation,
} from "../../common/yupValidation";
import MailSent from "../Dialog/MailSent";
import IconButton from "@mui/material/IconButton";
import InputAdornment from "@mui/material/InputAdornment";
import Visibility from "@mui/icons-material/Visibility";
import VisibilityOff from "@mui/icons-material/VisibilityOff";
import { useMutation } from "react-query";
import { signUp } from "../../lib/onBoarding-api";
import Snackbar from "@mui/material/Snackbar";
import Alert from "@mui/material/Alert";
import { CssTooltip } from "../FormsUI/StyledComponent";
import { InfoIcon } from "../../icons";

function SignUpForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [userId, setUserId] = useState("");
  const [formValues, setFormValues] = useState(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [severity, setSeverity] = useState("");

  const handleClickShowPassword = () => setShowPassword((show) => !show);
  const handleClickShowConfirmPassword = () =>
    setShowConfirmPassword((show) => !show);

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };
  const handleClose = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }

    setOpen(false);
  };

  const { mutate: registerAPI, isLoading } = useMutation(signUp);

  return (
    <>
      <Formik
        initialValues={{
          fname: "",
          lname: "",
          type: "Freelancer",
          companyName: "",
          email: "",
          mobileNumber: "",
          password: "",
          confirmPassword: "",
          countryName: "India",
        }}
        validationSchema={Yup.object().shape({
          fname: firstNameValidation,
          lname: lastNameValidation,
          type: Yup.string().required("Business Type is required"),
          companyName: companyNameValidation,
          email: emailValidation,
          mobileNumber: mobileNumberValidation,
          password: passwordValidation,
          confirmPassword: Yup.string()
            .required("Confirm password is required")
            .oneOf([Yup.ref("password"), null], "Passwords must match"),
        })}
        onSubmit={(values, { setSubmitting }) => {
          // const match = values.formattedValue.match(/^\+(\d+)\s+(.+)/);
          //const countryCode = match[1];
          //const mobileNumber = match[2].replace(/[\s()-]/g, "");

          let reqData = {
            firstName: values.fname,
            lastName: values.lname,
            businessType: values.type,
            //countryCode: countryCode,
            phoneNumber: values.mobileNumber,
            email: values.email,
            password: values.password,
          };
          setFormValues(reqData);
          registerAPI(
            { reqData },
            {
              onSuccess: ({ data }) => {
                setShowSuccess(true);
                setUserId(data?.user?.id);
                //console.log("data", data?.token?.token);
              },
              onError: (error) => {
                setOpen(true);
                setSeverity("error");
                setMessage(
                  error?.response?.data?.message
                    ? error.response.data.message
                    : error.message
                );
              },
            }
          );
        }}
      >
        {({ errors, status, touched, isSubmitting, values }) => (
          <Form>
            <div className=" grid gap-3">
              <Title title={"Sign-up"} titleClassName={"text-center mt-3"} />
              <div className="text-xs text-headingColor text-center ">
                {"To sign-up enter the details below. "}
              </div>
              <div className="md:px-10 grid gap-5 md:grid-cols-2">
                <div>
                  <div className="flex">
                    <InputLabel label={"First Name"} isMandatory={true} />
                    <CssTooltip
                      title={"Please enter your name as per the Govt. id"}
                      placement="right"
                      arrow
                    >
                      <InfoIcon className="ml-1 mr-4 w-4 h-3.5" />
                    </CssTooltip>
                  </div>
                  <TextField name="fname" className="w-full" />
                </div>
                <div>
                  <div className="flex">
                    <InputLabel label={"Last Name"} isMandatory={true} />
                    <CssTooltip
                      title={"Please enter your name as per the Govt. id"}
                      placement="right"
                      arrow
                    >
                      <InfoIcon className="ml-1 mr-4 w-4 h-3.5" />
                    </CssTooltip>
                  </div>

                  <TextField name="lname" className="w-full" />
                </div>
                <div>
                  <InputLabel label={"Business Type"} isMandatory={true} />
                  <Select name="type" options={BusinessType} placeholder="" />
                </div>
                <div>
                  <InputLabel label={"Company Name (optional) "} />
                  <TextField name="companyName" className="w-full" />
                </div>
                <div>
                  <InputLabel label={"Email ID"} isMandatory={true} />
                  <TextField name="email" className="w-full" />
                </div>
                <div className="mb-2">
                  <InputLabel label={"Phone No."} isMandatory={true} />
                  <PhoneNumberInput
                    name="mobileNumber"
                    countryName={values.countryName}
                  />
                </div>
                <div>
                  <InputLabel label={"Password"} isMandatory={true} />
                  <TextField
                    className="w-full"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            aria-label="toggle password visibility"
                            onClick={handleClickShowPassword}
                            onMouseDown={handleMouseDownPassword}
                            edge="end"
                          >
                            {showPassword ? (
                              <Visibility style={{ fontSize: 20 }} />
                            ) : (
                              <VisibilityOff style={{ fontSize: 20 }} />
                            )}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
                <div>
                  <InputLabel label={"Confirm Password"} isMandatory={true} />
                  <TextField
                    className="w-full"
                    name="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={handleClickShowConfirmPassword}
                            onMouseDown={handleMouseDownPassword}
                            edge="end"
                          >
                            {showConfirmPassword ? (
                              <Visibility style={{ fontSize: 20 }} />
                            ) : (
                              <VisibilityOff style={{ fontSize: 20 }} />
                            )}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
              </div>
              <div className="flex items-center justify-center mt-5">
                <Button
                  type="submit"
                  label="Sign up"
                  buttonClassName="w-full md:w-[50%] h-[40px] text-sm"
                  loading={isLoading}
                />
              </div>
              <div className="text-xs text-black text-center font-normal">
                By creating an account, you agree to the
                <span className="text-linkColor underline cursor-pointer mx-1">
                  Terms of use
                </span>
                and
                <span className="text-linkColor underline cursor-pointer ml-1">
                  Privacy
                </span>
              </div>
              <div className="text-small text-headingColor text-center mt-7 mb-2"></div>
            </div>
          </Form>
        )}
      </Formik>
      <MailSent
        show={showSuccess}
        onHide={() => {
          setShowSuccess(false);
        }}
        isEmail={true}
        formData={formValues}
        userId={userId}
      />
      <Snackbar
        open={open}
        autoHideDuration={3000}
        onClose={handleClose}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      >
        <Alert onClose={handleClose} severity={severity} sx={{ width: "100%" }}>
          {message}
        </Alert>
      </Snackbar>
    </>
  );
}

export default SignUpForm;
