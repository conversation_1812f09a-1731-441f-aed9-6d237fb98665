{"forms": [{"elements": [{"name": "name", "size": 6, "type": "text", "title": "Role name", "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Operator name"}, {"type": "matches", "regex": "^(?=.*[0-9])(?=.*[A-Za-z])[A-Za-z][A-Za-z0-9_ ]*$", "message": "Only alphanumeric characters,underscore and space are allowed.It should start with an alphabet"}, {"type": "min", "value": 3, "message": "Min length allowed is 3"}, {"type": "max", "value": 128, "message": "Max length allowed is 128"}], "defaultValue": "", "validationType": "string"}, {"name": "roleType", "size": 6, "type": "select", "title": "Role Type", "options": [{"label": "Customized", "value": "Customized"}, {"label": "Cafe", "value": "Cafe"}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select a MNP gateway type"}], "defaultValue": ""}, {"name": "portalAccess", "size": 6, "type": "multiselect", "title": "Portal Access", "options": [{"id": "Reporting", "label": "Reporting", "value": "Reporting"}, {"id": "Configuration", "label": "Configuration", "value": "Configuration"}, {"id": "Operation", "label": "Operation", "value": "Operation"}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select an option for diameter CDR mode"}], "defaultValue": [], "validationType": "array"}], "formName": "Basic Details", "formType": "simple"}, {"tabs": [{"columns": [{"header": "Resource", "accessorKey": "resource"}, {"header": "Create", "accessorKey": "create"}, {"header": "View", "accessorKey": "view"}, {"header": "Update", "accessorKey": "update"}, {"header": "Delete", "accessorKey": "delete"}, {"header": "Download", "accessorKey": "download", "restrictedFields": []}, {"header": "dashboardPanelDetails", "maxPanels": 5, "accessorKey": "dashboardPanelDetails", "maxDashBoards": 10}, {"header": "Select All", "accessorKey": "selectAll"}], "tabName": "Basic features", "elements": [{"resource": "Card Management", "restrictedFields": []}, {"resource": "Logs Management", "restrictedFields": ["create", "update", "delete"]}, {"resource": "Default Dashboard", "restrictedFields": ["create", "update", "delete"]}, {"resource": "Alert Management", "restrictedFields": []}, {"resource": "Group Management", "restrictedFields": []}, {"resource": "Panel Management", "restrictedFields": []}, {"resource": "Report Management", "restrictedFields": ["create", "update", "delete"]}, {"resource": "Dashboard Management", "restrictedFields": []}, {"resource": "Role Management", "restrictedFields": []}, {"resource": "CDR Search", "restrictedFields": ["create", "update", "delete"]}]}, {"columns": [{"header": "Report name", "accessorKey": "resource"}, {"header": "Download", "accessorKey": "download"}, {"header": "View", "accessorKey": "view"}, {"header": "Select All", "accessorKey": "selectAll"}, {"header": "", "accessorKey": "dashboardPanelDetails"}], "tabName": "Static Reports", "elements": [{"resource": "Customer wise destination wise traffic report", "restrictedFields": []}, {"resource": "Customer wise destination wise error report", "restrictedFields": []}, {"resource": "Supplier wise destination wise latency report", "restrictedFields": []}, {"resource": "Hourly traffic analysis", "restrictedFields": []}], "rowSelect": true}, {"columns": [{"header": "Report name", "accessorKey": "resource", "restrictedFields": []}, {"header": "Download", "accessorKey": "download", "restrictedFields": []}, {"header": "View", "accessorKey": "view", "restrictedFields": []}, {"header": "Select All", "accessorKey": "selectAll", "restrictedFields": []}], "tabName": "Dynamic Dashboard", "elements": [{"name": "dashBoardOne", "resource": "Dashboard One"}, {"name": "dashBoardTwo", "resource": "Dashboard Two"}, {"name": "dashBoardThree", "resource": "Dashboard Three"}, {"name": "dashBoardFour", "resource": "Dashboard Four"}, {"name": "dashBoardFive", "resource": "Dashboard Five"}, {"name": "dashBoardSix", "resource": "Dashboard Six"}, {"name": "dashBoardSeven", "resource": "Dashboard Seven"}]}], "formName": "Reporting", "formType": "tabsandTableForm", "operations": ["create", "view", "update", "delete", "download", "view"]}, {"tabs": [{"columns": [{"header": "Priviledges", "accessorKey": "resource"}, {"header": "Create", "accessorKey": "create"}, {"header": "View", "accessorKey": "view"}, {"header": "Update", "accessorKey": "update"}, {"header": "Delete", "accessorKey": "delete"}, {"header": "Select All", "accessorKey": "selectAll"}], "elements": [{"name": "service-management", "resource": "Service Parameters", "restrictedFields": []}], "moduleName": "Service Management"}, {"columns": [{"header": "Priviledges", "accessorKey": "resource"}, {"header": "Create", "accessorKey": "create"}, {"header": "View", "accessorKey": "view"}, {"header": "Update", "accessorKey": "update"}, {"header": "Download", "accessorKey": "download", "restrictedFields": []}, {"header": "Delete", "accessorKey": "delete"}, {"header": "Select All", "accessorKey": "selectAll"}], "elements": [{"name": "retry-policy", "resource": "Retry policy", "restrictedFields": []}, {"name": "group-customer-supplier", "resource": "Customer/Supplier group", "restrictedFields": []}, {"name": "deal-management", "resource": "Deal management", "restrictedFields": []}, {"name": "channel-partner", "resource": "Channel partner", "restrictedFields": []}, {"name": "customer-credit-profile", "resource": "Customer credit profile", "restrictedFields": []}, {"name": "credit-transaction", "resource": "Credit transaction", "restrictedFields": []}], "moduleName": "Environmental Setup"}, {"columns": [{"header": "Priviledges", "accessorKey": "resource"}, {"header": "Create", "accessorKey": "create"}, {"header": "View", "accessorKey": "view"}, {"header": "Update", "accessorKey": "update"}, {"header": "Delete", "accessorKey": "delete"}, {"header": "Select All", "accessorKey": "selectAll"}], "elements": [{"name": "series", "resource": "Series", "restrictedFields": []}, {"name": "mnp-gateway-detail", "resource": "MNP Gateway", "restrictedFields": []}, {"name": "operator", "resource": "Operator", "restrictedFields": []}, {"name": "operator-cluster", "resource": "Operator cluster", "restrictedFields": []}, {"name": "point-code", "resource": "Point code", "restrictedFields": []}, {"name": "point-code-list", "resource": "Point code list", "restrictedFields": []}, {"name": "http-template", "resource": "HTTP template", "restrictedFields": []}, {"name": "redirectional-account", "resource": "Redirectional account", "restrictedFields": []}, {"name": "redirectional-list", "resource": "Redirectional list", "restrictedFields": []}, {"name": "path", "resource": "Path", "restrictedFields": []}, {"name": "port", "resource": "Port", "restrictedFields": []}, {"name": "esme-account", "resource": "ESME account", "restrictedFields": []}, {"name": "lcr-profile", "resource": "LCR profile", "restrictedFields": []}], "moduleName": "Prerequisites"}, {"columns": [{"header": "Priviledges", "accessorKey": "resource"}, {"header": "Create", "accessorKey": "create"}, {"header": "View", "accessorKey": "view"}, {"header": "Update", "accessorKey": "update"}, {"header": "Delete", "accessorKey": "delete"}, {"header": "Select All", "accessorKey": "selectAll"}], "elements": [{"name": "rule-configuration", "resource": "Rule configuration", "restrictedFields": []}, {"name": "hub-rule-configuration", "resource": "Hub rule configuration", "restrictedFields": []}, {"name": "supplier-mnp-rule-configuration", "resource": "Supplier MNP rule configuration", "restrictedFields": []}], "moduleName": "Rules management"}, {"columns": [{"header": "Priviledges", "accessorKey": "resource"}, {"header": "Create", "accessorKey": "create"}, {"header": "View", "accessorKey": "view"}, {"header": "Update", "accessorKey": "update"}, {"header": "Delete", "accessorKey": "delete"}, {"header": "Select All", "accessorKey": "selectAll"}], "elements": [{"name": "node-session", "resource": "Node sessions", "restrictedFields": []}, {"name": "smsc-session", "resource": "SMSC sessions", "restrictedFields": []}, {"name": "esme-session", "resource": "ESME sessions", "restrictedFields": []}], "moduleName": "Session management"}, {"columns": [{"header": "Priviledges", "accessorKey": "resource"}, {"header": "Create", "accessorKey": "create"}, {"header": "View", "accessorKey": "view"}, {"header": "Update", "accessorKey": "update"}, {"header": "Delete", "accessorKey": "delete"}, {"header": "Select All", "accessorKey": "selectAll"}], "elements": [{"name": "contact-us", "resource": "Contact us", "restrictedFields": []}, {"name": "about-us", "resource": "About us", "restrictedFields": []}], "moduleName": "Others"}], "formName": "Configuration"}, {"tabs": [{"columns": [{"header": "Priviledges", "accessorKey": "resource"}, {"header": "Create", "accessorKey": "create"}, {"header": "View", "accessorKey": "view"}, {"header": "Update", "accessorKey": "update"}, {"header": "Delete", "accessorKey": "delete"}, {"header": "Select All", "accessorKey": "selectAll"}], "elements": [{"resource": "Cluster setup", "name": "cluster-setup", "restrictedFields": []}, {"resource": "Software license", "name": "software-license", "restrictedFields": []}], "moduleName": "System Configuration"}, {"columns": [{"header": "Priviledges", "accessorKey": "resource"}, {"header": "Create", "accessorKey": "create"}, {"header": "View", "accessorKey": "view"}, {"header": "Update", "accessorKey": "update"}, {"header": "Delete", "accessorKey": "delete"}, {"header": "Select All", "accessorKey": "selectAll"}], "elements": [{"resource": "SRI", "name": "sri", "restrictedFields": []}, {"resource": "MTS", "name": "mts", "restrictedFields": []}, {"resource": "ESME Server", "name": "esme-server", "restrictedFields": []}, {"resource": "ESME Client", "name": "esme-client", "restrictedFields": []}, {"resource": "DBM", "name": "dbm", "restrictedFields": []}, {"resource": "CRM", "name": "crm", "restrictedFields": []}, {"resource": "Rule Engine", "name": "rule-engine", "restrictedFields": []}, {"resource": "MARS", "name": "mars", "restrictedFields": []}, {"resource": "HLR", "name": "hlr", "restrictedFields": []}], "moduleName": "Module Configuration"}, {"columns": [{"header": "Priviledges", "accessorKey": "resource"}, {"header": "Create", "accessorKey": "create"}, {"header": "View", "accessorKey": "view"}, {"header": "Update", "accessorKey": "update"}, {"header": "Delete", "accessorKey": "delete"}, {"header": "Select All", "accessorKey": "selectAll"}], "elements": [{"resource": "<PERSON><PERSON><PERSON>", "name": "error-details", "restrictedFields": []}, {"resource": "Rule Type and Status", "name": "rule-type-status", "restrictedFields": []}, {"resource": "Omnibus Parameter", "name": "omnibus-parameter", "restrictedFields": []}, {"resource": "View Rule Priority", "name": "view-rule-priority", "restrictedFields": []}, {"resource": "Associate Rule Priority", "name": "associate-rule-priority", "restrictedFields": []}], "moduleName": "Environment Configuration"}], "formName": "Operation"}, {"formName": "Save"}], "header": "List of Roles", "formType": "role", "cafeForms": [{"elements": [{"name": "<PERSON><PERSON><PERSON>", "size": 6, "type": "text", "title": "Role name", "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Operator name"}, {"type": "matches", "regex": "^(?=.*[0-9])(?=.*[A-Za-z])[A-Za-z][A-Za-z0-9_ ]*$", "message": "Only alphanumeric characters,underscore and space are allowed.It should start with an alphabet"}, {"type": "min", "value": 3, "message": "Min length allowed is 3"}, {"type": "max", "value": 128, "message": "Max length allowed is 128"}], "defaultValue": "", "validationType": "string"}, {"name": "roleType", "size": 6, "type": "select", "title": "Role Type", "options": [{"label": "Customized", "value": "Customized"}, {"label": "Cafe", "value": "Cafe"}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select a MNP gateway type"}], "defaultValue": ""}, {"name": "portalAccess", "size": 6, "type": "multiselect", "title": "Portal Access", "options": [{"id": "Reporting", "label": "Reporting", "value": "Reporting"}, {"id": "Configuration", "label": "Configuration", "value": "Configuration"}, {"id": "Operation", "label": "Operation", "value": "Operation"}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select an option for diameter CDR mode"}], "defaultValue": [], "validationType": "array"}], "formName": "Basic Details", "formType": "simple"}, {"tabs": [{"columns": [{"header": "Priviledges", "accessorKey": "resource"}, {"header": "Create", "accessorKey": "create"}, {"header": "View", "accessorKey": "view"}, {"header": "Update", "accessorKey": "update"}, {"header": "Delete", "accessorKey": "delete"}, {"header": "Select All", "accessorKey": "selectAll"}], "elements": [{"resource": "Cafe report", "restrictedFields": []}, {"resource": "System dashboard", "restrictedFields": []}, {"resource": "Traffic dashboard", "restrictedFields": []}, {"resource": "Detailed reports", "restrictedFields": []}, {"resource": "Summary report", "restrictedFields": []}, {"resource": "Unidendified categories", "restrictedFields": []}, {"resource": "Content category", "restrictedFields": []}, {"resource": "Spam domain name", "restrictedFields": []}, {"resource": "Synonym name", "restrictedFields": []}, {"resource": "Replace words", "restrictedFields": []}], "moduleName": "Cafe management"}], "formName": "Cafe"}, {"formName": "Save"}], "buttonName": "+Add Roles", "moduleName": "Roles"}