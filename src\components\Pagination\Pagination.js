import React from "react";
import classnames from "classnames";
import { usePagination, DOTS } from "./usePagination";
import "./Pagination.css";

const Pagination = (props) => {
  const {
    onPageChange,
    totalCount,
    siblingCount = 1,
    currentPage,
    pageSize,
    className,
  } = props;

  const paginationRange = usePagination({
    currentPage,
    totalCount,
    siblingCount,
    pageSize,
  });

  const totalPageCount = Math.ceil(totalCount / pageSize);

  const onNext = () => {
    onPageChange(currentPage + 1);
  };

  const onPrevious = () => {
    onPageChange(currentPage - 1);
  };

  // if (isSearching) {
  //   return (
  //     <ul
  //       className={classnames("pagination-container", {
  //         [className]: className,
  //       })}
  //     >
  //       <li className="pagination-item disabled">
  //         <div className="arrow left" />
  //       </li>
  //       <li className="pagination-item selected">1</li>
  //       <li className="pagination-item disabled">
  //         <div className="arrow right" />
  //       </li>
  //     </ul>
  //   );
  // }

  if (totalPageCount === 0) {
    return (
      <ul
        className={classnames("pagination-container", {
          [className]: className,
        })}
      >
        <li className="pagination-item disabled">
          <div className="arrow left" />
        </li>
        <li className="pagination-item selected">1</li>
        <li className="pagination-item disabled">
          <div className="arrow right" />
        </li>
      </ul>
    );
  }

  if (totalPageCount === 1) {
    return (
      <ul
        className={classnames("pagination-container", {
          [className]: className,
        })}
      >
        <li
          className={classnames("pagination-item", {
            disabled: currentPage === 1,
          })}
          onClick={onPrevious}
        >
          <div className="arrow left" />
        </li>
        <li className="pagination-item selected">{currentPage}</li>
        <li
          className={classnames("pagination-item", {
            disabled: currentPage === totalPageCount,
          })}
          onClick={onNext}
        >
          <div className="arrow right" />
        </li>
      </ul>
    );
  }

  // Render pagination for multiple pages
  let lastPage = paginationRange[paginationRange?.length - 1];

  return (
    <ul
      className={classnames("pagination-container", { [className]: className })}
    >
      <li
        className={classnames("pagination-item", {
          disabled: currentPage === 1,
        })}
        onClick={onPrevious}
      >
        <div className="arrow left" />
      </li>
      {paginationRange.map((pageNumber) => {
        if (pageNumber === DOTS) {
          return <li className="pagination-item dots">&#8230;</li>;
        }

        return (
          <li
            className={classnames("pagination-item", {
              selected: pageNumber === currentPage,
            })}
            onClick={() => onPageChange(pageNumber)}
          >
            {pageNumber}
          </li>
        );
      })}
      <li
        className={classnames("pagination-item", {
          disabled: currentPage === lastPage,
        })}
        onClick={onNext}
      >
        <div className="arrow right" />
      </li>
    </ul>
  );
};

export default Pagination;
