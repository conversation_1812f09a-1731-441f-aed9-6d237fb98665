import React, { useState } from "react";
import { Checkbox, Switch } from "@mui/material";
import RoleConfirmDialog from "../Dialog/RoleConfirmDialog";

const RoleColumnsTab = ({
  selectedTabElements,
  selectedModule,
  setFieldValue,
  currentTabData,
  relations,
  values,
}) => {
  const [selectAllState, setSelectAllState] = useState({});
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogData, setDialogData] = useState({
    clickedModule: "",
    relatedModules: [],
    moduleResource: "",
    accessorKey: "",
  });

  // Helper function to build relation map
  const buildRelationMap = (relations) => {
    const map = {};
    relations &&
      Object.entries(relations).forEach(([parentModule, relationList]) => {
        relationList.forEach(({ model }) => {
          if (!map[parentModule]) {
            map[parentModule] = [];
          }
          if (!map[parentModule].includes(model)) {
            map[parentModule].push(model);
          }
        });
      });
    return map;
  };

  // Helper function to handle select all changes
  const handleSelectAllChange = (tabIndex, checked) => {
    setSelectAllState((prevState) => ({
      ...prevState,
      [tabIndex]: {
        create: checked,
        view: checked,
        update: checked,
        delete: checked,
        download: checked,
      },
    }));

    currentTabData[tabIndex].elements.forEach(({ resource }) => {
      setFieldValue(`${resource}.create`, checked);
      setFieldValue(`${resource}.view`, checked);
      setFieldValue(`${resource}.update`, checked);
      setFieldValue(`${resource}.delete`, checked);
      setFieldValue(`${resource}.download`, checked);
    });
  };

  // Helper function to check if all permissions are selected
  const isAllSelected = (tabIndex) => {
    const permissions = selectAllState[tabIndex];
    return permissions
      ? Object.values(permissions).every((val) => val === true)
      : false;
  };

  // Helper function to handle related view check
  const handleRelatedViewCheck = (
    moduleName,
    moduleResource,
    values,
    setFieldValue,
    currentTabData,
    relationMap
  ) => {
    const relatedModules = relationMap[moduleName] || [];
    relatedModules.forEach((mod) => {
      // Search in all tab data, not just selectedModule
      Object.values(currentTabData).forEach((tab) => {
        (tab?.elements || []).forEach(({ name, resource }) => {
          if (mod.toLowerCase() === name.toLowerCase()) {
            setFieldValue(`${resource}.view`, true);
          }
        });
      });
    });
  };

  // Create module labels mapping
  const moduleLabels = {};

  Object.values(currentTabData).forEach((tab) => {
    tab.elements.forEach((el) => {
      moduleLabels[el.resource] = el.name;
    });
  });

  // Create reversed module labels mapping
  const reversedModuleLabels = Object.entries(moduleLabels).reduce(
    (acc, [label, key]) => {
      acc[key] = label;
      return acc;
    },
    {}
  );

  // Helper function to get readable module names
  const getReadableModuleNames = (
    moduleKeys = [],
    moduleKeyToLabelMap = {}
  ) => {
    return moduleKeys.map((key) => moduleKeyToLabelMap[key] || key).join(", ");
  };

  // Helper function to get related unchecked modules
  const getRelatedUncheckedModules = (
    related = [],
    values = {},
    reversedLabelMap = {}
  ) => {
    return related.filter((modKey) => {
      const label = reversedLabelMap[modKey];
      return label && !values[label]?.view;
    });
  };

  // Common checkbox change handler
  const handleCheckboxChange = (e, row, accessorKey, setFieldValue, values) => {
    const newValue = e.target.checked;
    const clicked = row.original.name;
    const clickedModuleName = row.original.resource;
    const relationMap = buildRelationMap(relations);
    const related = relationMap[clicked] || [];
    const relatedUnchecked = getRelatedUncheckedModules(
      related,
      values,
      reversedModuleLabels
    );

    if (relatedUnchecked.length === 0 || !newValue) {
      setFieldValue(`${clickedModuleName}.${accessorKey}`, newValue);
    } else {
      setDialogData({
        clickedModule: clicked,
        relatedModules: relatedUnchecked,
        moduleResource: clickedModuleName,
        accessorKey: accessorKey,
      });
      setDialogOpen(true);
    }

    // Update Select All state
    const allChecked = currentTabData[selectedModule].elements.every(
      (el) =>
        values[el.resource]?.create &&
        values[el.resource]?.view &&
        values[el.resource]?.update &&
        values[el.resource]?.delete &&
        values[el.resource]?.download
    );

    setSelectAllState((prev) => ({
      ...prev,
      [selectedModule]: allChecked
        ? {
            create: true,
            view: true,
            update: true,
            delete: true,
            download: true,
          }
        : {
            create: false,
            view: false,
            update: false,
            delete: false,
            download: false,
          },
    }));
  };

  // Common checkbox cell renderer
  const renderCheckboxCell = (
    row,
    accessorKey,
    restrictedFields,
    options = {}
  ) => {
    const { disabled = false } = options;
    // const { setFieldValue, values } = useFormikContext();
    const isChecked = values[row.original.resource]?.[accessorKey] || false;

    if (restrictedFields?.includes(accessorKey)) return null;

    return (
      <>
        <Checkbox
          sx={{ transform: "scale(0.8)", padding: "4px" }}
          checked={isChecked}
          disabled={disabled}
          onChange={(e) =>
            handleCheckboxChange(e, row, accessorKey, setFieldValue, values)
          }
        />
        {dialogOpen && (
          <RoleConfirmDialog
            open={dialogOpen}
            onClose={() => setDialogOpen(false)}
            onConfirm={() => {
              setDialogOpen(false);
              setFieldValue(
                `${dialogData.moduleResource}.${dialogData.accessorKey}`,
                true
              );
              handleRelatedViewCheck(
                dialogData.clickedModule,
                dialogData.moduleResource,
                values,
                setFieldValue,
                currentTabData,
                buildRelationMap(relations)
              );
            }}
            message={`Please provide view option for ${getReadableModuleNames(
              dialogData.relatedModules,
              reversedModuleLabels
            )} to select ${dialogData.moduleResource}`}
          />
        )}
      </>
    );
  };

  return selectedTabElements.columns
    .map((col) => {
      switch (col.header) {
        case "Resource":
        case "Priviledges":
        case "Report name":
          return { accessorKey: col.accessorKey, header: col.header };

        case "Create":
          return {
            accessorKey: col.accessorKey,
            header: col.header,
            size: 80,
            Cell: ({ row }) => {
              const { restrictedFields } = row?.original || {};
              return renderCheckboxCell(row, col.accessorKey, restrictedFields);
            },
          };

        case "View":
          return {
            accessorKey: "view",
            header: "View",
            size: 80,
            Cell: ({ row }) => {
              const { restrictedFields } = row.original || {};
              const { resource } = row.original;
              const isViewOnly =
                resource === "Logs Management" ||
                resource === "Default Dashboard";
              return renderCheckboxCell(
                row,
                col.accessorKey,
                restrictedFields,
                { disabled: isViewOnly }
              );
            },
          };

        case "Update":
          return {
            accessorKey: "update",
            header: "Update",
            size: 80,
            Cell: ({ row }) => {
              const { restrictedFields } = row.original || {};
              return renderCheckboxCell(row, col.accessorKey, restrictedFields);
            },
          };

        case "Delete":
          return {
            accessorKey: "delete",
            header: "Delete",
            size: 80,
            Cell: ({ row }) => {
              const { restrictedFields } = row.original || {};
              return renderCheckboxCell(row, col.accessorKey, restrictedFields);
            },
          };

        case "Download":
          return {
            accessorKey: "download",
            header: "Download",
            size: 80,
            Cell: ({ row }) => {
              const { restrictedFields } = row.original || {};
              const { resource } = row.original;
              const isDownloadable =
                selectedModule === 0 ? resource === "CDR Search" : true;
              return isDownloadable
                ? renderCheckboxCell(row, col.accessorKey, restrictedFields)
                : null;
            },
          };

        case "Select All":
          return {
            id: "select",
            Header: ({ table }) => (
              <div
                style={{ display: "flex", alignItems: "center", gap: "5px" }}
              >
                <span>Select All</span>
                <Switch
                  checked={isAllSelected(selectedModule)}
                  onChange={(e) =>
                    handleSelectAllChange(selectedModule, e.target.checked)
                  }
                />
              </div>
            ),
            Cell: "",
          };

        default:
          return null;
      }
    })
    .filter(Boolean);
};

export default RoleColumnsTab;
