{"header": "List of users", "columns": [{"header": "User name", "accessorKey": "email"}, {"header": "Status", "accessorKey": "enabled"}, {"header": "Role", "accessorKey": "type"}, {"header": "Last login", "accessorKey": "updatedAt"}], "elements": {}, "formType": "simple", "buttonName": "+ Add new user", "moduleData": "user-management", "moduleName": "User management", "globalSearch": [{"label": "User name", "value": "email"}]}