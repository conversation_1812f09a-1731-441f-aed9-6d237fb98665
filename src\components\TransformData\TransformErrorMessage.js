import { operatorNameMaxLength } from "../../common/config";

export const formatErrorMessage = (
  errorMessage,
  maxLength = operatorNameMaxLength
) => {
  return errorMessage.replace(/Operator\s'([^']+)'/g, (match, operatorName) => {
    const truncatedOperatorName =
      operatorName.length > maxLength
        ? operatorName.substring(0, maxLength) + "..."
        : operatorName;

    return `Operator '${truncatedOperatorName}'`;
  });
};

export const formatDeleteErrorMessage = (message, maxLength = 15) => {
  const match = message.match(/Cannot delete operator\(s\):\s*'([^']+)'/);

  if (match) {
    const operatorNames = match[1];
    let truncatedNames = operatorNames;

    if (operatorNames.length > maxLength) {
      const start = operatorNames.slice(0, Math.floor(maxLength / 2));
      const end = operatorNames.slice(-Math.floor(maxLength / 2));
      truncatedNames = `${start}...${end}`;
    }

    return message.replace(operatorNames, truncatedNames);
  }

  return message;
};
