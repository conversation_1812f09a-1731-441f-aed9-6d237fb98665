import React, { useState, useEffect, useRef } from "react";
import CustomTable from "../components/Table";
import Button from "../components/Buttons/Button";
import SearchComponent from "../components/FormsUI/SearchComponent/Search";
import { users, moduleConfiguration } from "../common/constants";
import ResultPerPageComponent from "../components/Pagination/ResultPerPage";
import Pagination from "../components/Pagination/Pagination";
import { useQuery, useMutation } from "react-query";
import { getUserDetail, deleteList } from "../lib/user-management-api";
import { CssTooltip } from "../components/FormsUI/StyledComponent";
import EditIcon from "../icons/edit.svg";
import TrashIcon from "../icons/trash.svg";
import Avatar from "@mui/material/Avatar";
import CustomSwitch from "../components/FormsUI/Switch";
import { getRoleDetail } from "../lib/role-management-api";
import { getGlobalSearchList } from "../lib/user-management-api";
import { useNavigate } from "react-router-dom";
import ErrorDialog from "../components/Dialog/ErrorDialog";
import useDebounce from "../common/useDebounce";
import DeleteDialog from "../components/Dialog/DeleteDialog";
import SuccessDialog from "../components/Dialog/SuccessDialog";

const CommonListPage = ({ pageType }) => {
  const [data, setData] = useState([]);
  const [columns, setColumns] = useState([]);
  const [searchText, setSearchText] = useState("");
  const [globalSearchSelect, setGlobalSearchSelect] = useState("");
  const [limitPerPage, setLimitPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [filteredRow, setFilteredRow] = useState(0);
  const [filteredData, setFilteredData] = useState([]);
  const [errorDialog, setErroDialog] = useState(false);
  const [message, setMessage] = useState("");
  const debouncedValue = useDebounce(searchText, 500);
  const [rowData, setRowData] = useState([]);
  const [refetchData, setRefetchData] = useState(false);
  const [id, setId] = useState(null);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const { mutate: deleteAPI, isLoading: deleteLoading } =
    useMutation(deleteList);

  const rowDataRef = useRef(rowData);

  const navigate = useNavigate();

  useEffect(() => {
    setRowData(filteredRow);
  }, [filteredRow]);

  useEffect(() => {
    rowDataRef.current = rowData;
  }, [rowData]);

  const collectionName =
    pageType === "userManagement"
      ? moduleConfiguration.UserManagement
      : moduleConfiguration.roleManagement;

  const { isLoading, refetch } = useQuery(
    ["userDetails", currentPage, limitPerPage],
    pageType === "roleManagement" ? getRoleDetail : getUserDetail,
    {
      enabled: !globalSearchSelect || !debouncedValue,
      onSuccess: ({ data }) => {
        setData(data?.data);
        setPagination(data);
      },
    }
  );

  const handleDelete = (row) => {
    setId(row.original.id);
    setDeleteDialog(true);
  };

  const handleSwitchChange = (status, id) => {};
  useEffect(() => {
    const fetchData = async () => {
      if (pageType === "userManagement") {
        setColumns([
          {
            accessorKey: "name",
            header: "User Name",
            Cell: ({ row }) => {
              const { name, email } = row.original;
              return (
                <div className="flex items-center space-x-2">
                  <Avatar
                    sx={{
                      bgcolor: "#DC3833",
                      width: 30,
                      height: 30,
                      fontSize: 14,
                    }}
                  >
                    {name ? name.charAt(0).toUpperCase() : "?"}
                  </Avatar>
                  <div className="flex flex-col">
                    <div>{name}</div>
                    <div>{email}</div>
                  </div>
                </div>
              );
            },
          },
          {
            accessorKey: "isUserActive",
            header: "Status",
            Cell: ({ row }) => (
              <div
                style={{
                  paddingRight: "2rem",
                }}
              >
                <CustomSwitch
                  diameter={15}
                  width={36}
                  checked={true}
                  onChange={(e) => handleSwitchChange(e, row?.original?.id)}
                />{" "}
              </div>
            ),
          },
          { accessorKey: "type", header: "Role Type" },
          {
            accessorKey: "isLdapUser",
            header: "User Type",
            Cell: ({ value }) => <>{value ? "LDAP User" : "Non-LDAP User"}</>,
          },
          { accessorKey: "lastLoggedIn", header: "Last Logged In" },
          {
            accessorKey: "actions",
            header: "",
            Cell: ({ row }) => (
              <div className="flex justify-center items-center gap-5">
                <CssTooltip title="Edit" placement="top" arrow>
                  <img
                    className="w-4 h-4 cursor-pointer"
                    src={EditIcon}
                    alt="edit"
                    onClick={() => {
                      navigate(`/app/list/user-managements/edit/${row.id}`, {
                        state: {
                          userId: row.id,
                          action: "edit",
                        },
                      });
                    }}
                  />
                </CssTooltip>
                <CssTooltip title="Delete" placement="top" arrow>
                  <img
                    className={`w-4 h-4 cursor-pointer`}
                    src={TrashIcon}
                    alt="delete"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(row);
                    }}
                  />
                </CssTooltip>
              </div>
            ),
            enableColumnFilter: false,
            enableSorting: false,
            enableColumnPinning: true,
          },
        ]);
      } else if (pageType === "roleManagement") {
        setColumns([
          { accessorKey: "name", header: "Role Name" },
          { accessorKey: "noOfUsers", header: "No. of Users" },
          { accessorKey: "creationDate", header: "Creation Date" },
          { accessorKey: "noOfDashboards", header: "No. of Dashboards" },
          { accessorKey: "noOfPanels", header: "No. of Panels" },
          {
            accessorKey: "actions",
            header: "",
            Cell: ({ row }) => (
              <div className="flex justify-center items-center gap-5">
                <CssTooltip title="Edit" placement="top" arrow>
                  <img
                    className="w-4 h-4 cursor-pointer"
                    src={EditIcon}
                    alt="edit"
                    onClick={() => {}}
                  />
                </CssTooltip>
                <CssTooltip title="Delete" placement="top" arrow>
                  <img
                    className={`w-4 h-4 cursor-pointer`}
                    src={TrashIcon}
                    alt="delete"
                    onClick={() => {}}
                  />
                </CssTooltip>
              </div>
            ),
            enableColumnFilter: false,
            enableSorting: false,
            enableColumnPinning: true,
          },
        ]);
      }
    };

    fetchData();
  }, [pageType]);

  const handleLimitChange = (e) => {
    setLimitPerPage(e.target.value);
    setCurrentPage(1);
  };

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  useQuery([debouncedValue, limitPerPage, currentPage], getGlobalSearchList, {
    enabled: !!globalSearchSelect && !!debouncedValue,
    onSuccess: (resp) => {
      setPagination(resp?.data || []);
      // setRespData(resp?.data?.data);
      setData(resp?.data?.data);
    },
    onError: ({ response }) => {
      setErroDialog(true);
      setMessage(response?.data?.message);
    },
  });

  return (
    <div>
      <div className="font-bold text-2xl mt-1">
        {pageType === "userManagement" ? "List of Users" : "List of Roles"}
      </div>
      <div className="relative flex justify-between my-5 z-10">
        <SearchComponent
          onChange={(e) => {
            setSearchText(e.target.value.trim());
          }}
          setGlobalSearchSelect={setGlobalSearchSelect}
          dropdownFilter={users}
        />
        <div className="text-end mb-4">
          <Button
            type="submit"
            label={
              pageType === "userManagement" ? "+Add New User" : "+Add New Role"
            }
            buttonClassName="w-[200px] h-[40px] text-sm ml-5"
            onClick={() => {
              navigate(
                pageType === "userManagement"
                  ? "/app/list/user-managements/add"
                  : "/app/list/role-managements/add",
                {
                  state: {
                    action: "add",
                  },
                }
              );
            }}
          />
        </div>
      </div>
      <CustomTable
        data={data}
        columns={columns}
        isLoading={isLoading}
        isMinimumWidth={false}
        setFilteredRow={setFilteredRow}
        setFilteredData={setFilteredData}
        setIsSearching={setIsSearching}
      />
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          margin: "20px 0px 20px 20px",
          width: "95%",
        }}
      >
        <div className="flex">
          <ResultPerPageComponent
            limit={limitPerPage}
            handleLimitChange={handleLimitChange}
          />
          <div
            style={{
              display: "flex",
              fontSize: "14px",
              padding: "10px 0px 0px 10px",
              color: "#808080",
            }}
          >
            {!isSearching ? (
              <>
                {(pagination?.totalCount === 0
                  ? 0
                  : (currentPage - 1) * limitPerPage + 1) +
                  " - " +
                  Math.min(
                    limitPerPage * currentPage,
                    pagination?.totalCount
                  )}{" "}
                of {pagination?.totalCount} rows
              </>
            ) : (
              <>
                {`${
                  filteredRow === 0 ? 0 : (currentPage - 1) * limitPerPage + 1
                }  - ${(currentPage - 1) * limitPerPage + filteredRow} of ${
                  pagination?.totalCount
                } rows (Matched for this page)`}
              </>
            )}
          </div>
        </div>
        <Pagination
          className="pagination-bar"
          currentPage={currentPage}
          totalCount={pagination?.totalCount || 0}
          pageSize={limitPerPage}
          onPageChange={handlePageChange}
          isSearching={isSearching}
        />
      </div>
      <ErrorDialog
        show={errorDialog}
        onHide={() => {
          setErroDialog(false);
        }}
        message={message}
      ></ErrorDialog>
      <SuccessDialog
        show={successDialog}
        onHide={() => {
          setSuccessDialog(false);

          refetch();
        }}
        message={message}
      />
      <DeleteDialog
        show={deleteDialog}
        onHide={() => setDeleteDialog(false)}
        onConfirm={() => {
          deleteAPI(
            { rowId: id, collectionName },
            {
              onSuccess: (resp) => {
                setDeleteDialog(false);
                setSuccessDialog(true);
                setMessage(`Record(s) deleted successfully`);
                setCurrentPage(1);
                refetch();
              },
              onError: ({ response }) => {
                setDeleteDialog(false);
                setErroDialog(true);
                setMessage(response?.data?.error?.message);
                refetch();
              },
            }
          );
        }}
        title={"Are you sure you want to delete?"}
        isLoading={deleteLoading}
      />
    </div>
  );
};

export default CommonListPage;
