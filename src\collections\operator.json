{"forms": [{"elements": [{"info": "Name of the operator", "name": "operatorName", "size": 6, "type": "text", "title": "Operator name", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the operator name"}, {"type": "matches", "regex": "^(?=.*[0-9])(?=.*[A-Za-z])[A-Za-z][A-Za-z0-9_ ]*$", "message": "Only alphanumeric characters,underscore and space are allowed.It should start with an alphabet"}, {"type": "min", "value": 3, "message": "Min length allowed is 3"}, {"type": "max", "value": 128, "message": "Max length allowed is 128"}], "defaultValue": "", "validationType": "string"}, {"info": "Country in which the operator resides", "name": "countryName", "size": 6, "type": "text", "title": "Country name", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Country name"}, {"type": "matches", "regex": "^[A-Za-z][A-Za-z_ ]*$", "message": "Only alphabetic character underscore and space are allowed"}, {"type": "min", "value": 3, "message": "Min length allowed is 3"}, {"type": "max", "value": 25, "message": "Max length allowed is 25"}], "defaultValue": "", "validationType": "string"}, {"info": "Region name of the operator", "name": "region", "size": 6, "type": "text", "title": "Region", "isInfo": true, "isMandatory": true, "placeholder": "Ex.,East,West,etc... ", "validations": [{"type": "required", "message": "Please enter the region"}, {"type": "matches", "regex": "^[A-Za-z][A-Za-z_ ]*$", "message": "Only alphabetic character underscore and space are allowed"}, {"type": "min", "value": 3, "message": "Min length allowed is 3"}, {"type": "max", "value": 50, "message": "Max length allowed is 50"}], "defaultValue": "", "validationType": "string"}, {"info": "Circle assigned to the operator", "name": "circle", "size": 6, "type": "text", "title": "Circle", "isInfo": true, "isMandatory": false, "validations": [{"type": "matches", "regex": "^[A-Za-z][A-Za-z_ ]*$", "message": "Only alphabetic character underscore and space are allowed"}, {"type": "min", "value": 2, "message": "Min length allowed is 2"}, {"type": "max", "value": 50, "message": "Max length allowed is 50"}], "defaultValue": "", "validationType": "string"}, {"info": "Enter the list of numbers. GT is a unique address which refers to only one destination", "name": "gtList", "size": 6, "type": "ChipInput", "title": "GT List", "isInfo": true, "fieldBased": "operator", "validation": true, "isMandatory": false, "defaultValue": [], "validationType": "array", "validationConfig": {"maxValue": {"value": 21, "message": "Maximum 21 digits allowed"}, "integerError": "Please enter only positive numeric values"}}, {"info": "Country code", "name": "ccode", "size": 6, "type": "text", "title": "CC", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the CC value"}, {"type": "matches", "regex": "^(0|[1-9][0-9]*)(\\.[0-9]+)?$", "message": "Only positive numeric value is allowed for country code"}, {"type": "min", "value": 1, "message": "Max length allowed is 1"}, {"type": "max", "value": 5, "message": "Max length allowed is 5"}], "defaultValue": "", "validationType": "string"}, {"info": "Enter the country code and national destination code", "name": "ccNdc", "size": 6, "type": "ChipInput", "title": "CC/NDC (Ex.,54-435 or 67-43 ect...)", "isInfo": true, "fieldBased": "operator", "validation": true, "isMandatory": true, "defaultValue": [], "validationType": "array", "validationConfig": {"typeError": {"value": "^(\\d{2}-\\d+,)*\\d{2}-\\d+$", "message": "Value of CC/NDC is not proper. Please Check"}, "uniqueError": "Repeated values not allowed", "maxArrayLength": {"value": 10, "message": "Maximum 10 CC/NDC values are allowed"}, "minArrayLength": {"value": 1, "message": "Please enter CC/NDC values"}, "onlyZeroesError": {"value": "^(?!00-0+$)\\d{2,}-\\d+$", "message": "CC/NDC cannot consist only zeroes please enter integer values only"}}}, {"info": "Mobile country  network codes, which uniquely identifies a mobile phone operator", "name": "mccMnc", "size": 6, "type": "ChipInput", "title": "MCC-MNC (Ex.,754-43 or 762-439)", "isInfo": true, "fieldBased": "operator", "validation": true, "isMandatory": true, "defaultValue": [], "validationType": "array", "validationConfig": {"mccValue": {"value": 3, "message": "MCC should contains 3 digits"}, "mncValue": {"value": 3, "message": "MNC should contain 2 or 3 digits"}, "typeError": {"value": "^(\\d{3}-\\d{2,3},)*\\d{3}-\\d{2,3}$", "message": "Value of MCC-MNC is not proper. Please Check"}, "uniqueError": "Repeated values not allowed", "maxArrayLength": {"value": 1, "message": "Only 1 MCC-MNC is allowed"}, "minArrayLength": {"value": 1, "message": "Please enter MCC-MNC value"}, "invalidValueError": {"value": ["000-000", "000-00"], "message": "MCC-MNC cannot consist only zeroes please enter integer values only"}}}], "formName": "General Information"}, {"elements": [{"info": "Local routing number", "name": "lrn", "size": 6, "type": "text", "title": "LRN", "isInfo": true, "isMandatory": false, "validations": [{"type": "matches", "regex": "^(0|[1-9][0-9]*)(\\.[0-9]+)?$", "message": "Only positive integer value is allowed for LRN"}, {"type": "min", "value": 3, "message": "Min length allowed is 3"}, {"type": "max", "value": 5, "message": "Max length allowed is 5"}], "defaultValue": "", "validationType": "string"}, {"info": "Flag to enable/disable MNP check for operator", "name": "mnpCorrection", "size": "6", "type": "radio", "title": "MNP correction", "isInfo": true, "options": [{"label": "Enable", "value": "Enable"}, {"label": "Disable", "value": "Disable"}, {"label": "SRI based", "value": "SRI based"}], "isMandatory": true, "defaultValue": "Disable"}, {"info": "Gateway to be considered for MNP check of operator", "name": "mnpGateway", "size": 6, "type": "select", "title": "MNP gateway", "isInfo": true, "dynamic": {"field": "mnpCorrection", "value": ["Enable", "SRI based"]}, "isMandatory": true, "onClickPath": "mnp-gateway-details", "validations": [{"type": "required", "message": "Please select a MNP gateway"}], "defaultValue": "", "isDropDownApi": true}, {"info": "Cache gateway to be considered for MNP check for operator", "name": "mnpCacheFlag", "size": "6", "type": "radio", "title": "MNP cache", "isInfo": true, "dynamic": {"mnpTypeVisible": true, "dynamicSubModule": "mnpCorrection", "dynamicVisibleField": "mnpGateway", "dynamicVisibleValue": "Enum", "dynamicSubModuleValue": ["Enable", "SRI based"]}, "options": [{"label": "Enable", "value": 0}, {"label": "Disable", "value": 1}], "defaultValue": 1}, {"info": "Bilateral rules is a pre defined agreement between two operators to terminate messages", "name": "bilateralFlag", "size": "6", "type": "radio", "title": "Bilateral ", "isInfo": true, "options": [{"label": "Yes", "value": 0}, {"label": "No", "value": 1}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the bilateral value"}], "defaultValue": 1}, {"info": "Maximum messages that can be sent per hour", "name": "maxMessagePerHour", "size": 6, "type": "text", "title": "Max messages throttle", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the max messages throttle"}, {"type": "typeError", "message": "Only positive numeric value is allowed"}, {"type": "min", "value": 0, "message": "Min value allowed is 0"}, {"type": "max", "value": 99999, "message": "Max value allowed is 99999"}], "defaultValue": "", "validationType": "number"}], "formName": "MNP Information"}, {"elements": [{"name": "tadicCode", "size": 6, "type": "text", "title": "Tadig code", "isMandatory": false, "defaultValue": ""}, {"name": "countryInitials", "size": 6, "type": "text", "title": "Country initials", "isMandatory": false, "defaultValue": ""}, {"name": "organizationName", "size": 6, "type": "text", "title": "Organization name", "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the organization name"}], "defaultValue": ""}, {"name": "networkName", "size": 6, "type": "text", "title": "Network name", "isMandatory": false, "defaultValue": ""}, {"name": "grxProvidersList", "size": 6, "type": "ChipInput", "title": "GRX providers list", "isMandatory": false, "defaultValue": [], "validationType": "array"}, {"name": "asnsList", "size": 6, "type": "ChipInput", "title": "ASNs list", "isMandatory": false, "defaultValue": [], "validationType": "array"}], "formName": "<PERSON><PERSON>"}, {"elements": [{"name": "ccNdcInfo", "size": 12, "type": "fieldArray", "title": "MSIDN number range", "fieldsData": [{"info": "Country Code", "name": "cc", "type": "text", "isInfo": true}, {"info": "Enter the National Destination Code", "name": "ndc", "type": "text", "isInfo": true}, {"name": "snRangeStart", "type": "text"}, {"name": "snRangeStop", "type": "text"}, {"name": "isDelete", "type": "delete"}], "headersData": ["CC", "NDC", "SN range start", "SN range stop", ""], "isMandatory": false, "validations": [{"type": "fieldArray-number<PERSON>nly", "message": "Only numbers allowed", "required": "Required"}], "initialValues": {"ccNdcInfo": [{"cc": "", "ndc": "", "isDelete": true, "snRangeStop": "", "snRangeStart": ""}]}}, {"name": "networkNodeGT", "size": 12, "type": "fieldArray", "title": "Network node GT number range", "fieldsData": [{"info": "Country Code", "name": "cc", "type": "text", "isInfo": true}, {"info": "Enter the National Destination Code", "name": "ndc", "type": "text", "isInfo": true}, {"name": "snRangeStart", "type": "text"}, {"name": "snRangeStop", "type": "text"}, {"name": "isDelete", "type": "delete"}], "headersData": ["CC", "NDC", "SN range start", "SN range stop", ""], "isMandatory": false, "validations": [{"type": "fieldArray-number<PERSON>nly", "message": "Only numbers allowed"}], "initialValues": {"networkNodeGT": [{"cc": "", "ndc": "", "isDelete": true, "snRangeStop": "", "snRangeStart": ""}]}}, {"name": "sscpCarrierInfo", "size": 12, "type": "fieldArray", "title": "SSCP carrier name", "fieldsData": [{"name": "name", "type": "text"}, {"name": "connectivity", "type": "text"}, {"name": "comments", "type": "text"}, {"name": "dpc", "type": "hiddeneye"}, {"name": "isDelete", "type": "delete"}], "headersData": ["SSCP carrier name", "Connectivity", "Comments", "DPC information", ""], "initialValues": {"sscpCarrierInfo": [{"dpc": "", "name": "", "comments": "", "isDelete": true, "connectivity": ""}]}}], "formName": "Table Information"}], "header": "List of operator", "columns": [{"header": "Operator ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "Operator name", "isHovered": true, "accessorKey": "attributes.operatorName"}, {"header": "Country name", "accessorKey": "attributes.countryName"}, {"header": "Region", "accessorKey": "attributes.region"}, {"header": "MCC-MNC", "accessorKey": "attributes.mccMnc"}, {"buttons": [{"type": "image", "title": "ExportXml", "iconUrl": ""}, {"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "formType": "stepper", "buttonName": "+ Add operator", "moduleData": "operator", "moduleName": "Operator", "globalSearch": [{"label": "Operator ID", "value": "id"}, {"label": "Operator name", "value": "operatorName"}, {"label": "Country name", "value": "countryName"}, {"label": "Region", "value": "region"}, {"label": "MCC-MNC", "value": "mccMnc"}], "dropdownOptions": {"field": "countryName", "defaultQuery": "", "defaultValue": "All"}, "stepperDetails": [{"stepTitle": "General Information", "fields": [{"label": "Operator Name", "field": "operatorName"}, {"label": "Country Name", "field": "countryName"}, {"label": "Region", "field": "region"}, {"label": "Circle", "field": "circle"}, {"label": "GT List", "field": "gtList"}, {"label": "CC ", "field": "ccode"}, {"label": "CC NDC", "field": "ccNdc"}, {"label": "MCC MNC", "field": "mccMnc"}]}, {"stepTitle": "MNP Information", "fields": [{"label": "LRN", "field": "lrn"}, {"label": "MNP Correction", "field": "mnpCorrection"}, {"label": "MNP Gateway", "field": "mnpGateway"}, {"label": "MNP <PERSON><PERSON>", "field": "mnpCacheFlag"}, {"label": "Bilateral Flag", "field": "bilateralFlag"}, {"label": "Max Message Per Hour", "field": "maxMessagePerHour"}]}, {"stepTitle": "<PERSON><PERSON>", "fields": [{"label": "TADIG Code", "field": "tadicCode"}, {"label": "Country Initials", "field": "countryInitials"}, {"label": "Organization Name", "field": "organizationName"}, {"label": "Network Name", "field": "networkName"}, {"label": "Creation Date", "field": "creationDate"}, {"label": "Last Modified Date", "field": "lastModifiedDate"}, {"label": "GRX Providers List", "field": "grxProvidersList"}, {"label": "ASNs List", "field": "asnsList"}]}]}