{"header": "List of deal management", "columns": [{"header": "Deal ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "Deal name", "accessorKey": "attributes.name"}, {"header": "Deal rate", "accessorKey": "attributes.rate"}, {"header": "Deal type", "accessorKey": "attributes.type"}, {"header": "Deal Options", "accessorKey": "attributes.options"}, {"buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"info": "Name for the deal", "name": "name", "size": 6, "type": "text", "title": "Deal name", "isInfo": true, "isMandatory": true, "nonEditable": true, "validations": [{"type": "required", "message": "Please enter the Deal name"}, {"type": "matches", "regex": "^(?=.*[0-9])(?=.*[A-Za-z])[A-Za-z][A-Za-z0-9_ ]*$", "message": "Only alphanumeric characters , underscore and space allowed"}], "defaultValue": "", "validationType": "string"}, {"info": "validity period of the deal in months", "name": "validityPrd", "size": 6, "type": "text", "title": "Validity period", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Validity period"}, {"type": "typeError", "message": "Please enter the Validity period with numeric value"}, {"type": "integer", "message": "Please Enter the Validity period with valid Integer value"}, {"type": "positive", "message": "Please Enter the Validity period with positive Integer value"}], "defaultValue": "", "validationType": "number"}, {"info": "Rate applicable for the deal", "name": "rate", "size": 6, "type": "text", "title": "Deal rate", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Deal rate"}, {"type": "matches", "regex": "^(?!0*$)(?!.*\\..*\\..*)[0-9]*\\.[0-9]+$", "message": "Please enter the Deal rate with Positive decimal value"}, {"type": "matches", "regex": "^\\d+(\\.\\d{1,13})?$", "message": "The number can have up to 13 digits after the decimal point."}], "defaultValue": "", "validationType": "string"}, {"info": "Start date of the deal", "name": "startDate", "size": 6, "type": "date", "title": "Start date", "isInfo": true, "minDate": true, "isMandatory": true, "nonEditable": true, "validations": [{"type": "required", "message": "Please enter the Activation date(From date)"}], "defaultValue": "", "validationType": "date"}, {"info": "Type of deal", "name": "type", "size": 6, "type": "select", "title": "Deal type", "isInfo": true, "options": [{"label": "Customer", "value": 1}, {"label": "Supplier", "value": 3}, {"label": "Source country", "value": 5}, {"label": "Source opertaor", "value": 4}, {"label": "Customer & destination country", "value": 6}, {"label": "Customer & destination operator", "value": 7}, {"label": "Supplier & destination country", "value": 8}, {"label": "Supplier & destination operator", "value": 9}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Deal type"}], "defaultValue": "", "validationType": "string"}, {"info": "Entity connected to SMS Hub solution provider with relation as customer", "name": "customer", "size": 6, "type": "select", "title": "Customer", "isInfo": true, "dynamic": {"field": "type", "value": [1, 6, 7]}, "isCustomer": true, "isMandatory": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select the Customer"}], "isDropDownApi": true}, {"info": "Originated operator", "name": "sourceOperator", "size": 6, "type": "select", "title": "Source operator", "isInfo": true, "dynamic": {"field": "type", "value": [4]}, "isMandatory": true, "onClickPath": "operators", "validations": [{"type": "required", "message": "Please select the Source operator"}], "isDropDownApi": true}, {"info": "Country of originated operator", "name": "sourceCountry", "size": 6, "type": "select", "title": "Source country", "isInfo": true, "dynamic": {"field": "type", "value": [5]}, "isCountry": true, "isMandatory": true, "onClickPath": "operators", "validations": [{"type": "required", "message": "Please select the Source country"}], "defaultValue": "", "isDropDownApi": true}, {"info": "Entity connected to SMS Hub solution provider with relation as supplier", "name": "supplier", "size": 6, "type": "select", "title": "Supplier", "isInfo": true, "dynamic": {"field": "type", "value": [3, 8, 9]}, "isMandatory": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select the Supplier"}], "defaultValue": "", "isDropDownApi": true}, {"info": "Country of destiation operator", "name": "destinationCountry", "size": 6, "type": "select", "title": "Destination country", "isInfo": true, "dynamic": {"field": "type", "value": [6, 8]}, "isCountry": true, "isMandatory": true, "onClickPath": "operators", "validations": [{"type": "required", "message": "Please select the Destination country"}], "defaultValue": "", "isDropDownApi": true}, {"info": "The operator of destination", "name": "destinationOperator", "size": 6, "type": "select", "title": "Destination operator", "isInfo": true, "dynamic": {"field": "type", "value": [7, 9]}, "isMandatory": true, "onClickPath": "operators", "validations": [{"type": "required", "message": "Please select the Destination Operator"}], "isDropDownApi": true}, {"info": "select the option on which the deal should be based on:\nRevenue-based (applicable for customers)\nVolume-based\nCost-based (applicable for suppliers)", "name": "options", "size": 6, "type": "select", "title": "Deal options", "isInfo": true, "options": [{"label": "Revenue based", "value": 1}, {"label": "Cost based", "value": 2}, {"label": "Volume based", "value": 3}], "dynamicList": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Deal option"}], "defaultValue": "", "dynamicOptions": [{"options": [{"label": "Revenue based", "value": 1}, {"label": "Volume based", "value": 3}], "dealType": [6, 7, 1]}, {"options": [{"label": "Cost based", "value": 2}, {"label": "Volume based", "value": 3}], "dealType": [8, 9, 3]}, {"options": [{"label": "Revenue based", "value": 1}, {"label": "Volume based", "value": 3}, {"label": "Cost based", "value": 2}], "dealType": [5, 4]}], "validationType": "number"}, {"info": "Value of the deal", "name": "value", "size": 6, "type": "text", "title": "Deal value", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Deal value"}, {"type": "typeError", "message": "Please enter the Deal value with numeric value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 10000, "message": "Max range allowed is 10000"}, {"type": "integer", "message": "Please Enter the Deal value with valid Integer value"}], "defaultValue": "", "validationType": "number"}], "formType": "simple", "buttonName": "+ Add new deal", "moduleData": "deal-management", "moduleName": "Deal", "globalSearch": [{"label": "Deal ID", "value": "id"}, {"label": "Deal name", "value": "name"}, {"label": "Deal rate", "value": "rate"}], "dropdownOptions": {"field": "type", "options": [{"label": "All type of deal", "query": "", "value": "ALL"}, {"label": "Customer", "query": "filters[type][$eq]=1", "value": 1}, {"label": "Supplier", "query": "filters[type][$eq]=3", "value": 3}, {"label": "Source country", "query": "filters[type][$eq]=5", "value": 5}, {"label": "Source opertaor", "query": "filters[type][$eq]=4", "value": 4}, {"label": "Customer & destination country", "query": "filters[type][$eq]=6", "value": 6}, {"label": "Customer & destination operator", "query": "filters[type][$eq]=7", "value": 7}, {"label": "Supplier & destination country", "query": "filters[type][$eq]=8", "value": 8}, {"label": "Supplier & destination operator", "query": "filters[type][$eq]=9", "value": 9}], "defaultQuery": "", "defaultValue": "ALL"}}