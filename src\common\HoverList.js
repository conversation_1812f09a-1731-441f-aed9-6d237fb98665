import { getNestedValue } from "./listPageUtils";
import { CssTooltip } from "../components/FormsUI/StyledComponent";
import { moduleConfiguration } from "./constants";

export const HoverList = ({ row, x, collectionName }) => {
  console.log("collectionName", collectionName);
  const rowData = row.original;
  const columnName = x.accessorKey || "";
  let fieldName = getNestedValue(rowData, columnName);

  if (Array.isArray(fieldName)) {
    fieldName = fieldName.join(", ");
  }

  const displayText =
    fieldName && fieldName.length >= 25
      ? collectionName === moduleConfiguration.mnpGatewayDetails
        ? `${fieldName.substring(0, 13)}...`
        : `${fieldName.substring(0, 8)}.....${fieldName.substring(
            fieldName.length - 8,
            fieldName.length
          )}`
      : fieldName;

  return (
    <CssTooltip
      title={fieldName}
      placement="top"
      arrow
      bgColor="#D9D9D9"
      color="#000000"
    >
      {displayText}{" "}
    </CssTooltip>
  );
};

export default HoverList;
