import React, { useState, useEffect, useContext } from "react";
import { FieldArray } from "formik";
import { PlusCircleIconOutline, DeleteIcon, HiddenEye } from "../../icons";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  InputAdornment,
} from "@mui/material";
import TextFieldWrapper from "../FormsUI/TextField";
import SelectWrapper from "../FormsUI/Select";
import CustomSwitch from "../ToggleSwitch/CustomSwitch";
import DialogBox from "../../components/Dialog/FieldArrayDialog";
import { DottedBox } from "../../components/Dialog/FieldArrayDialog";
import ResultPerPageComponent from "../Pagination/ResultPerPage";
import Pagination from "../Pagination/Pagination";
import theme from "../../tailwind-theme";
import DeleteDialog from "../Dialog/DeleteDialog";
import { MAX_COMMENT_CHAR } from "../../common/config";
import { DataContext } from "../../context/DataContext";
import { moduleConfiguration } from "../../common/constants";

const RenderTableBody = ({
  name,
  values,
  fields,
  hideFieldMatches,
  setFieldValue,
  isDisabled,
  paginatedRows,
  remove,
  push,
  totalRows,
  setCurrentPage,
  limitPerPage,
  currentPage,
  isInfo,
  info,
  moduleName,

  setFieldTouched,
}) => {
  const [open, setOpen] = useState(false);
  const [dialogFormData, setDialogFormData] = useState({});
  const [deleteIndex, setDeleteIndex] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);

  const handleClickOpen = (index) => {
    const rowData = paginatedRows[index] || {};
    setDialogFormData({ ...rowData, index });
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setDialogFormData({});
  };

  const handleSaveDialogData = (newData, index) => {
    const updatedValues = [...values[name]];
    if (updatedValues[index]) {
      if (
        !updatedValues[index].dpc ||
        !Array.isArray(updatedValues[index].dpc)
      ) {
        updatedValues[index].dpc = [];
      }
      if (Array.isArray(newData.dpc)) {
        updatedValues[index].dpc = newData.dpc;
      } else {
        updatedValues[index].dpc.push(newData);
      }

      setFieldValue(name, updatedValues);
    }

    handleClose();
  };

  const totalPages = Math.ceil(totalRows / limitPerPage);

  const isRowFilled = (row) => {
    return fields.some((field) => {
      const fieldValue = row[field.name];
      if (field.type === "select" || field.type === "text") {
        return (
          fieldValue !== undefined && fieldValue !== null && fieldValue !== ""
        );
      }
      if (field.type === "switch") {
        return fieldValue === true;
      }
    });
  };

  const handleDeleteClick = (index) => {
    setDeleteIndex(index);
    setDeleteDialog(true);
  };

  const handleConfirmDelete = () => {
    setDeleteLoading(true);

    if (deleteIndex === 0 && values[name].length === 1) {
      const clearedRow = { ...values[name][0] };
      Object.keys(clearedRow).forEach((key) => {
        clearedRow[key] = Array.isArray(clearedRow[key]) ? [] : "";
      });
      setFieldValue(name, [clearedRow]);
    } else {
      remove(deleteIndex);
      const totalRowsAfterDelete = values[name].length - 1;
      const newTotalPages = Math.ceil(totalRowsAfterDelete / limitPerPage);

      if (currentPage >= newTotalPages && newTotalPages > 0) {
        setCurrentPage(newTotalPages);
      }
    }

    setDeleteLoading(false);
    setDeleteDialog(false);
  };

  const saveFormData = () => {
    localStorage.setItem("formData", JSON.stringify(values));
  };

  useEffect(() => {
    const savedData = localStorage.getItem("formData");
    if (savedData) {
      setFieldValue(name, JSON.parse(savedData)[name]);
    }
  }, []);

  const [showToolTip, setShowTooltip] = useState(true);

  return (
    <FieldArray name={name}>
      {() => (
        <>
          {paginatedRows.map((row, index) => {
            const globalIndex = (currentPage - 1) * limitPerPage + index;
            return (
              <TableRow key={row.id}>
                {fields.map((field, fieldIndex, setFieldValue) => (
                  <TableCell
                    key={fieldIndex}
                    sx={{
                      whiteSpace: "nowrap",
                    }}
                  >
                    {field.type === "select" ? (
                      <SelectWrapper
                        name={`${name}.${values[name].indexOf(row)}.${
                          field.name
                        }`}
                        options={field.options}
                        isDisabled={isDisabled}
                        width="100%"
                        maxWidth="130px"
                        isCustomer={field.isCustomer}
                        isDropDownApi={field.isDropDownApi}
                        collectionName={field.onClickPath}
                        isCountry={field.isCountry}
                      />
                    ) : field.type === "text" &&
                      !(hideFieldMatches && field.name === "targetPrice") ? (
                      <TextFieldWrapper
                        name={`${name}.${values[name].indexOf(row)}.${
                          field.name
                        }`}
                        isDisabled={isDisabled}
                        isPassword={field.isPassword}
                        width={
                          moduleName === moduleConfiguration.operators
                            ? "full"
                            : ""
                        }
                        sx={
                          moduleName === moduleConfiguration.operators
                            ? { minWidth: "100px" }
                            : moduleConfiguration.mnpGatewayDetails
                            ? { minWidth: "150px", maxWidth: "150px" }
                            : { minWidth: "100px", maxWidth: "100%" }
                        }
                        onFocus={() => setShowTooltip(false)}
                        onBlur={(e) => {
                          setShowTooltip(false);
                          setFieldTouched(
                            `${name}.${values[name].indexOf(row)}.${
                              field.name
                            }`,
                            true
                          );
                        }}
                        onMouseEnter={() => setShowTooltip(true)}
                        toolTip={showToolTip}
                        maxLength={
                          field.name === "comments" ? MAX_COMMENT_CHAR : ""
                        }
                      />
                    ) : field.type === "switch" ? (
                      <div className="flex">
                        <CustomSwitch
                          name={`${name}.${values[name].indexOf(row)}.${
                            field.name
                          }`}
                          checked={row.ACTIVE}
                          isDisabled={isDisabled || row.REQ === "Y"}
                        />
                        <span className="ml-2 mt-2">
                          {row.ACTIVE === 1 ? "Active" : "Inactive"}
                        </span>
                      </div>
                    ) : field.type === "hiddeneye" ? (
                      <TextFieldWrapper
                        name={`${name}.${values[name].indexOf(row)}.${
                          field.name
                        }`}
                        className="w-full"
                        // value={[]}
                        isObject={true}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <IconButton
                                onClick={() => handleClickOpen(index)}
                              >
                                <HiddenEye />
                              </IconButton>
                            </InputAdornment>
                          ),
                          readOnly: true,
                        }}
                      />
                    ) : null}
                  </TableCell>
                ))}
                <TableCell>
                  {(globalIndex === 0 && isRowFilled(paginatedRows[0])) ||
                  (moduleName === "channel-partners"
                    ? row.isDelete || totalRows > 1
                    : row.isDelete && totalRows > 1) ? (
                    <IconButton
                      type="button"
                      onClick={() => handleDeleteClick(globalIndex)}
                      disabled={isDisabled}
                    >
                      <DeleteIcon />
                    </IconButton>
                  ) : null}
                </TableCell>
              </TableRow>
            );
          })}
          {currentPage === totalPages && (
            <TableRow className="sticky bottom-0 z-1 bg-white">
              {Array(
                hideFieldMatches &&
                  fields.some((field) => field.name === "targetPrice")
                  ? fields.length - 2
                  : fields.length - 1
              )
                .fill("")
                .map((_, idx) => (
                  <TableCell key={idx}>
                    <DottedBox />
                  </TableCell>
                ))}
              <TableCell colSpan={fields.length + 1} align="right">
                <IconButton
                  type="button"
                  onClick={() => {
                    const newEntry = fields.reduce((acc, field) => {
                      acc.id = Date.now();
                      if (field.name === "COMVIVA_PARAM") {
                        acc[field.name] = "NA";
                        acc.REQ = "N";
                      } else if (field.name === "ACTIVE") {
                        acc[field.name] = 1;
                      } else if (field.name === "isDelete") {
                        acc[field.name] = true;
                      } else if (field.name === "thresholdLevel") {
                        acc[field.name] = fields.length + 2;
                      } else {
                        acc[field.name] = "";
                      }
                      return acc;
                    }, {});
                    push(newEntry);
                    const lastPage = Math.ceil((totalRows + 1) / limitPerPage);
                    setCurrentPage(lastPage);
                  }}
                  disabled={
                    isDisabled ||
                    !isRowFilled(paginatedRows[paginatedRows.length - 1])
                  }
                >
                  <PlusCircleIconOutline
                    style={{
                      color:
                        isDisabled ||
                        !isRowFilled(paginatedRows[paginatedRows.length - 1])
                          ? theme.textColor.mildGray
                          : "black",
                    }}
                  />
                </IconButton>
              </TableCell>
            </TableRow>
          )}

          <DialogBox
            open={open}
            handleClose={handleClose}
            name="dpc"
            setFieldValue={setFieldValue}
            values={dialogFormData}
            onSave={(newData) =>
              handleSaveDialogData(newData, dialogFormData.index)
            }
            isDisabled={isDisabled}
            // onRemoveRow={handleRemoveRow}
          />
          <DeleteDialog
            show={deleteDialog}
            onHide={() => setDeleteDialog(false)}
            onConfirm={handleConfirmDelete}
            title={
              <>
                Are you sure you want to delete?
                <br />
                (Deletion will only happen on click of save)
              </>
            }
            isLoading={deleteLoading}
          />
        </>
      )}
    </FieldArray>
  );
};

const RenderTable = ({
  name,
  values,
  headers,
  fields,
  hideFieldMatches,
  isDisabled,
  setFieldValue,
  isInfo,
  info,
  moduleName,
  setFieldTouched,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [limitPerPage, setLimitPerPage] = useState(10);

  const handleLimitChange = (e) => {
    setLimitPerPage(parseInt(e.target.value, 10));
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const rows = Array.isArray(values[name])
    ? values[name].filter((row) => row !== null && row !== undefined)
    : [];
  const totalRows = rows.length;

  const paginatedRows = rows.slice(
    (currentPage - 1) * limitPerPage,
    currentPage * limitPerPage
  );

  // const filteredHeaders = headers.filter(
  //   (header, index) =>
  //     !(hideFieldMatches && fields[index]?.name === "targetPrice")
  // );
  const filteredHeaders = headers.filter(
    (header, index) =>
      !(hideFieldMatches && fields[index]?.name === "targetPrice")
  );
  // Add an empty string only if `targetPrice` was removed
  if (
    hideFieldMatches &&
    fields.some((field) => field.name === "targetPrice")
  ) {
    filteredHeaders.push("");
  }
  const rowsFilled = Array.isArray(values[name])
    ? values[name].filter((row) => {
        return (
          row &&
          fields.some((field) => {
            const fieldValue = row[field.name];
            if (field.type === "select" || field.type === "text") {
              return (
                fieldValue !== undefined &&
                fieldValue !== null &&
                fieldValue !== ""
              );
            }
          })
        );
      })
    : [];

  const totalFilledRows = rowsFilled.length;

  const startRow = (currentPage - 1) * limitPerPage + 1;
  let endRow = Math.min(totalFilledRows * limitPerPage, totalFilledRows);
  const { sideBarOpen } = useContext(DataContext);

  return (
    <div
      sx={
        moduleName === "channel-partners"
          ? {
              maxWidth: {
                xs: "21vw",
                sm: "40vw",
                md: "72vw",
                lg: "72vw",
                xl: "82vw",
              },
              margin: "0 auto",
            }
          : undefined
      }
    >
      <TableContainer
        component={Paper}
        sx={
          moduleName === moduleConfiguration.operators
            ? {
                boxShadow: "none",
                maxHeight: "400px",
                overflowY: "auto",
                overflowX: "auto",
                maxWidth: "full",
              }
            : moduleName === "mnp-gateway-details"
            ? {
                boxShadow: "none",
                maxHeight: "400px",
                overflowY: "auto",
                overflowX: "auto",
                width: {
                  xs: "41vw",
                  sm: "61vw",
                  md: "64vw",
                  lg: !sideBarOpen ? "65vw" : "52vw",
                  xl: "48vw",
                },
                transition: "width 0.5s ease-in-out, height 0.5s ease-in-out",
              }
            : moduleName !== "channel-partners"
            ? {
                boxShadow: "none",
                maxHeight: "400px",
                overflowY: "auto",
                overflowX: "auto",
                width: {
                  xs: "41vw",
                  sm: "61vw",
                  md: "64vw",
                  lg: "56vw",
                  xl: "48vw",
                },
              }
            : {}
        }
      >
        <Table
          sx={
            moduleName !== "channel-partners"
              ? {
                  minWidth: 550,
                  border: "1.5px solid #BEBEBE",
                }
              : {}
          }
          aria-label="simple table"
        >
          <TableHead
            sx={{
              bgcolor: theme.backgroundColor.bgTable,
              height: 20,
              zIndex: 1,
            }}
            className="sticky top-0"
          >
            <TableRow>
              {filteredHeaders.map((header, index) => (
                <TableCell
                  key={index}
                  sx={{ fontSize: 14, fontWeight: 600, whiteSpace: "nowrap" }}
                >
                  {header}
                </TableCell>
              ))}
              <TableCell />
            </TableRow>
          </TableHead>
          <TableBody>
            <FieldArray name={name}>
              {({ push, remove }) => (
                <RenderTableBody
                  name={name}
                  values={values}
                  fields={fields}
                  hideFieldMatches={hideFieldMatches}
                  isDisabled={isDisabled}
                  paginatedRows={paginatedRows}
                  remove={remove}
                  push={push}
                  totalRows={totalRows}
                  setCurrentPage={setCurrentPage}
                  limitPerPage={limitPerPage}
                  currentPage={currentPage}
                  setFieldValue={setFieldValue}
                  isInfo={isInfo}
                  info={info}
                  moduleName={moduleName}
                  setFieldTouched={setFieldTouched}
                />
              )}
            </FieldArray>
          </TableBody>
        </Table>
      </TableContainer>
      <div className="flex items-center justify-between my-5 ml-5 w-[95%] bottom-0">
        <div className="flex items-center">
          <ResultPerPageComponent
            limit={limitPerPage}
            totalRows={totalRows}
            startRow={startRow}
            endRow={endRow}
            handleLimitChange={handleLimitChange}
          />
          <div className="text-[14px] text-gray-500 ml-5">
            {totalFilledRows === 0
              ? "0 - 0 of 0 rows"
              : `${startRow} - ${endRow} of ${totalFilledRows} rows`}
          </div>
        </div>
        <Pagination
          className="pagination-bar"
          currentPage={currentPage}
          totalCount={totalRows}
          pageSize={limitPerPage}
          onPageChange={handlePageChange}
        />
      </div>
    </div>
  );
};

const FieldArrayTable = ({
  name,
  headers,
  fields,
  values,
  hideFieldMatches,
  isDisabled,
  setFieldValue,
  isInfo,
  info,
  moduleName,
  setFieldTouched,
}) => {
  return (
    <div>
      <RenderTable
        name={name}
        headers={headers}
        fields={fields}
        values={values}
        hideFieldMatches={hideFieldMatches}
        isDisabled={isDisabled}
        setFieldValue={setFieldValue}
        isInfo={isInfo}
        info={info}
        moduleName={moduleName}
        setFieldTouched={setFieldTouched}
      />
    </div>
  );
};

export default FieldArrayTable;
