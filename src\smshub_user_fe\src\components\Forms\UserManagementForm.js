import React, { useState } from "react";
import { Form, Formik } from "formik";
import * as Yup from "yup";
import InputLabel from "../FormsUI/InputLabel";
import Select from "../FormsUI/Select";
import TextFieldWrapper from "../FormsUI/TextField";
import DatePickerWrapper from "../DatePicker/DatePickerFieldWrapper"; // Custom date picker component
import Button from "../Buttons/Button";
import CancelButton from "../Buttons/OutlinedButton";
import FileUpload from "../Upload File/FileUpload";
import CustomRadioGroup from "../FormsUI/RadioButton/CustomRadioGroup";
import { useQuery } from "react-query";
import { getDropDownValues, getAll } from "../../lib/dropdown-api";
import {
  createUser,
  updateUser,
  getDataByIdUsers,
} from "../../lib/user-management-api";
import { useMutation } from "react-query";
import { moduleConfiguration } from "../../common/constants";
import SuccessDialog from "../Dialog/SuccessDialog";
import ErrorDialog from "../Dialog/ErrorDialog";
import { useNavigate, useLocation } from "react-router-dom";
import { getRole } from "../../lib/role-management-api";
import { countryCodeArr } from "../../common/countryCode";
import Dropdown from "../FormsUI/MultiSelect/index";

function UserManagementForm() {
  const { mutate: createUserAPI, isLoading: createRoleLoading } =
    useMutation(createUser);
  const { mutate: updateUserAPI, isLoading: updateRoleLoading } =
    useMutation(updateUser);
  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErroDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [editDetails, setEditDetails] = useState(null);
  const [roleData, setRoleData] = useState(null);
  const [customer, setCustomer] = useState(null);
  const [supplier, setSupplier] = useState(null);
  const [dashboard, setDashboard] = useState([]);

  const navigate = useNavigate();
  const location = useLocation();
  const id = location?.state?.userId;
  const action = location.pathname.split("/").filter(Boolean).pop();

  useQuery(["RoleDetails"], getRole, {
    onSuccess: ({ data }) => {
      setRoleData(data?.data);
    },
  });

  useQuery(["customerDetails", "customer"], getDropDownValues, {
    onSuccess: ({ data }) => {
      setCustomer(data);
    },
  });

  useQuery(["supplierDetails", "supplier"], getDropDownValues, {
    onSuccess: ({ data }) => {
      setSupplier(data);
    },
  });

  useQuery([moduleConfiguration.UserManagement, id], getDataByIdUsers, {
    enabled: id !== null && id !== undefined,
    refetchOnWindowFocus: false,
    onSuccess: ({ data }) => {
      setEditDetails(data);
    },
    onError: (error) => {
      console.error("Error fetching data:", error);
    },
  });

  useQuery(["dashboardList"], getAll, {
    onSuccess: ({ data }) => {
      setDashboard(data?.data);
    },
  });

  const handleSubmit = (values) => {
    const formValues = new FormData();

    Object.keys(values).forEach((key) => {
      if (key === "image") {
        if (values[key]) {
          formValues.append(key, values[key]);
        }
      } else {
        formValues.append(key, values[key]);
      }
    });

    if (action === "add") {
      createUserAPI(
        {
          moduleName: moduleConfiguration.UserManagement,
          reqData: formValues,
          headers: {
            "Content-Type": "multipart/form-data", // Important for file upload
          },
        },
        {
          onSuccess: (resp) => {
            setSuccessDialog(true);
            setMessage(`User management created successfully`);
          },
          onError: ({ response }) => {
            setErroDialog(true);
            setMessage(response?.data?.message);
          },
        }
      );
    } else {
      updateUserAPI(
        {
          moduleName: moduleConfiguration.UserManagement,
          id: id,
          reqData: formValues,
          headers: {
            "Content-Type": "multipart/form-data", // Important for file upload
          },
        },
        {
          onSuccess: (resp) => {
            setSuccessDialog(true);
            setMessage(`User management updated successfully`);
          },
          onError: ({ response }) => {
            setErroDialog(true);
            setMessage(response?.data?.message);
          },
        }
      );
    }
    // handleNextClickStep();
  };

  const initialValues = editDetails
    ? editDetails
    : {
        image: null, // New initial value for the upload field
        accountType: "",
        assignRole: "",
        accountStatus: true,
        activationDate: null,
        expiryDate: null,
        name: "",
        email: "",
        phoneNumber: "",
        countryCode: "+91",
        defaultDashboard: "",
        customerList: [],
        supplierList: [],
        isLdapUser: "",
        typeList: "",
      };

  const validationSchema = Yup.object({
    image: Yup.mixed()
      .nullable()
      .required("Profile picture is required")
      .test(
        "fileSize",
        "File size must be less than 3MB",
        (value) => !value || (value && value.size <= 3 * 1024 * 1024)
      )
      .test(
        "fileType",
        "Unsupported file format. Use jpeg or png",
        (value) =>
          !value || (value && ["image/jpeg", "image/png"].includes(value.type))
      ),
    accountType: Yup.string().required("Please select an account type"),
    assignRole: Yup.string().required("Please select a role"),
    accountStatus: Yup.string().required("Please select account status"),
    activationDate: Yup.date().required("Please select an activation date"),
    expiryDate: Yup.date().required("Please select an expiry date"),
    name: Yup.string().required("Full name is required"),
    email: Yup.string()
      .email("Invalid email format")
      .required("Email is required"),
    phoneNumber: Yup.string()
      .matches(/^\d{10}$/, "Phone number must be 10 digits")
      .required("Phone number is required"),
    defaultDashboard: Yup.string().required(
      "Please select a default dashboard"
    ),
    typeList: Yup.string().required("Please select an option"),
    isLdapUser: Yup.string().required("Please select a type of user"),
    customerList: Yup.array().when("typeList", {
      is: (val) => val === "Customer" || val === "Both",
      then: Yup.array().required("Please select at least one customer"),
    }),
    supplierList: Yup.array().when("typeList", {
      is: (val) => val === "Supplier" || val === "Both",
      then: Yup.array().required("Please select at least one supplier"),
    }),
    accountStatus: Yup.string().required("Please select account status"),
  });

  const accountTypeOptions = [
    { label: "Customized User", value: "customized" },
    { label: "Standard User", value: "standard" },
  ];

  return (
    <Formik
      initialValues={initialValues}
      validateOnMount={true}
      validationSchema={validationSchema}
      enableReinitialize={true}
      onSubmit={(values) => {
        handleSubmit(values);
      }}
    >
      {({
        values,
        setFieldValue,
        errors,
        touched,
        handleChange,
        handleBlur,
      }) => (
        <Form>
          <div className="grid grid-cols-12 gap-10 md:mx-10">
            {/* Profile Picture Upload */}
            <div className="col-span-6">
              <FileUpload
                name="image"
                setFieldValue={setFieldValue}
                profileData={null}
              />
              {/* {errors.image && touched.image && (
                <div className="error-text">{errors.image}</div>
              )} */}
            </div>

            {/* Account Type */}
            <div className="col-span-6">
              <InputLabel label={"Account type"} isMandatory={true} />
              <Select
                name="accountType"
                options={accountTypeOptions}
                placeholder="Select account type"
                values={values}
              />
            </div>
            <div className="col-span-6">
              <CustomRadioGroup
                id="accountStatus"
                name="accountStatus"
                value={values.accountStatus}
                options={[
                  { value: true, label: "Active" },
                  { value: false, label: "Inactive" },
                ]}
                onChange={handleChange}
                onBlur={handleBlur}
                label="Account Status"
                isMandatory={true}
              />
              {errors.accountStatus && touched.accountStatus && (
                <div className="error-text">{errors.accountStatus}</div>
              )}
            </div>
            {/* Assign Role */}
            <div className="col-span-6">
              <InputLabel label={"Assign Role"} isMandatory={true} />
              <Select
                name="assignRole"
                options={roleData?.map((x) => {
                  return { label: x.name, value: x.id };
                })}
                placeholder="Select role"
              />
            </div>

            {/* Activation Date */}
            <div className="col-span-6">
              <InputLabel label={"Activation date"} isMandatory={true} />
              <DatePickerWrapper
                name="activationDate"
                placeholder="Select activation date"
                minDate={true}
              />
            </div>

            {/* Expiry Date */}
            <div className="col-span-6">
              <InputLabel label={"Expiry date"} isMandatory={true} />
              <DatePickerWrapper
                name="expiryDate"
                placeholder="Select expiry date"
                minDate={true}
              />
            </div>

            {/* Full Name */}
            <div className="col-span-6">
              <InputLabel label={"Full Name"} isMandatory={true} />
              <TextFieldWrapper name="name" placeholder="Enter full name" />
            </div>

            {/* Email ID */}
            <div className="col-span-6">
              <InputLabel label={"Email ID"} isMandatory={true} />
              <TextFieldWrapper name="email" placeholder="Enter email ID" />
            </div>

            {/* Phone Number */}
            <div className="col-span-6">
              <InputLabel label={"Phone Number"} isMandatory={true} />
              <div className="flex flex-row gap-2">
                <Select
                  placeholder={""}
                  name="countryCode"
                  value={values.countryCode}
                  //className={"w-1/4"}
                  options={countryCodeArr?.map((x) => {
                    return { label: x, value: x };
                  })}
                  isSearchable={true}
                ></Select>
                <TextFieldWrapper
                  name="phoneNumber"
                  placeholder="Enter phone number"
                />
              </div>
            </div>

            {/* Default Dashboard */}
            <div className="col-span-6">
              <InputLabel label={"Default Dashboard"} isMandatory={true} />
              <Select
                name="defaultDashboard"
                value={values?.defaultDashboard}
                options={dashboard?.map((x) => {
                  return { label: x.name, value: x.id };
                })}
                isSearchable={true}
                //onBlur={handleBlur}
              ></Select>
            </div>

            <div className="col-span-6">
              {
                <>
                  <CustomRadioGroup
                    id="typeList"
                    name="typeList"
                    value={values.typeList}
                    options={[
                      { value: "customerList", label: "Customer List" },
                      { value: "supplierList", label: "Supplier List" },
                      { value: "both", label: "Both" },
                    ]}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    label={"Select any one option"}
                    isMandatory={true}
                  />
                </>
              }
            </div>

            {errors.typeList && touched.typeList && (
              <div
                style={{ top: "27px" }}
                error
                id="typeList"
                className="text-errorColor"
              >
                {errors.typeList}
              </div>
            )}

            {(values.typeList === "customerList" ||
              values.typeList === "both") && (
              <div className="mt-3 col-span-6">
                <InputLabel label={"Customer List"} isMandatory={true} />
                <Dropdown
                  name="customerList"
                  data={customer?.map((x) => {
                    return { label: x.name, value: x.id };
                  })}
                  isMulti={true}
                  placeholder="Add customers"
                  onSelectionChange={(selectedDetail) => {
                    setFieldValue("customerList", selectedDetail);
                  }}
                />
                {/* {errors.customerList && touched.customerList && (
                    <div className="text-errorColor text-xs">
                      {errors.customerList}
                    </div>
                  )} */}
              </div>
            )}
            {errors.customerList && touched.customerList && (
              <div
                style={{ top: "27px" }}
                error
                id="typeList"
                className="text-errorColor"
              >
                {errors.customerList}
              </div>
            )}

            {(values.typeList === "supplierList" ||
              values.typeList === "both") && (
              <div className="mt-3 col-span-6">
                <InputLabel label={"Supplier List"} isMandatory={true} />
                <Dropdown
                  name="supplierList"
                  data={supplier?.map((x) => {
                    return { label: x.name, value: x.id };
                  })}
                  isMulti={true}
                  placeholder="Add suppliers"
                  onSelectionChange={(selectedDetail) => {
                    setFieldValue("customerList", selectedDetail);
                  }}
                />

                {errors.supplierList && touched.supplierList && (
                  <div className="text-errorColor text-xs">
                    {errors.supplierList}
                  </div>
                )}
              </div>
            )}
            {errors.supplierList && touched.supplierList && (
              <div
                style={{ top: "27px" }}
                error
                id="typeList"
                className="text-errorColor"
              >
                {errors.supplierList}
              </div>
            )}
            <div className="col-span-6">
              <>
                <CustomRadioGroup
                  id="isLdapUser"
                  name="isLdapUser"
                  value={values.isLdapUser}
                  options={[
                    { value: true, label: "LDAP user" },
                    { value: false, label: "Non LDAP user" },
                  ]}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  label={"Type of user"}
                  isMandatory={true}
                />
                {errors.isLdapUser && touched.isLdapUser && (
                  <div
                    style={{ top: "27px" }}
                    error
                    id="typeList"
                    className="text-errorColor"
                  >
                    {errors.isLdapUser}
                  </div>
                )}
              </>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex justify-center items-center mt-10">
            <CancelButton
              label="Cancel"
              buttonClassName="w-full md:w-[154px] h-[40px] text-sm mb-3 rounded-[20px]"
              onClick={() => navigate("/app/list/user-managements")}
            />
            <Button
              type="submit"
              label="Save"
              value="submit"
              buttonClassName="w-full md:w-[154px] h-[40px] text-base mb-3 rounded-[20px] ml-5"
              loading={createRoleLoading || updateRoleLoading}
            />
          </div>
          <SuccessDialog
            show={successDialog}
            onHide={() => {
              navigate("/app/list/user-managements");
              setSuccessDialog(false);
            }}
            message={message}
          ></SuccessDialog>
          <ErrorDialog
            show={errorDialog}
            onHide={() => {
              setErroDialog(false);
            }}
            message={message}
          ></ErrorDialog>
        </Form>
      )}
    </Formik>
  );
}

export default UserManagementForm;
