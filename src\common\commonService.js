import React from "react";
import { Grid } from "@mui/material";
import InputLabel from "../components/FormsUI/InputLabel";
import TextFieldWrapper from "../components/FormsUI/TextField";
import Select from "../components/FormsUI/Select";
import CustomSwitch from "../components/ToggleSwitch/CustomSwitch";
import CustomRadioButton from "../components/RadioButton/RadioButton";
import * as Yup from "yup";
import generateValidations from "./generateValidations";
import ShortCodeInput from "../components/Chip";
import OutlinedButton from "../components/Buttons/OutlinedButton";

export const renderFields = (
  fields,
  values,
  setFieldValue,
  navigate,
  formData,
  action
) => {
  return fields.map((field, i) => {
    let {
      name,
      title,
      size,
      type,
      isMandatory,
      isMandatoryy,
      options,
      dynamic,
      placeholder,
      isAddButton,
      onClickPath,
      isDropDownApi,
      navigationLink,
      buttonName,
      generalInfo,
      isInfo,
      info,
      filter,
      notEditable,
      defaultValue,
    } = field;

    let showField = dynamic
      ? typeof dynamic["value"] === "string" ||
        typeof dynamic["value"] === "number"
        ? values[dynamic["field"]] === dynamic["value"]
        : Array.isArray(dynamic["value"]) &&
          dynamic["value"].includes(values[dynamic["field"]])
      : true;

    if (!showField) return <></>;

    return (
      <Grid item xs={12} md={size} key={i}>
        <InputLabel
          label={title}
          isMandatory={isMandatory}
          isMandatoryy={isMandatoryy}
          isAddButton={isAddButton}
          color={"text-bgSecondary"}
          isInfo={isInfo}
          info={info}
          action={action}
        ></InputLabel>
        {(() => {
          switch (type) {
            case "text":
              return (
                <TextFieldWrapper
                  name={name}
                  placeholder={placeholder ? placeholder : "Enter " + title}
                ></TextFieldWrapper>
              );
            case "select":
              return (
                <Select
                  name={name}
                  options={options}
                  placeholder={placeholder ? placeholder : "Select " + title}
                  isDropDownApi={isDropDownApi}
                  collectionName={onClickPath}
                  values={values}
                  filter={filter}
                  isDisabled={notEditable}
                ></Select>
              );

            case "switch":
              return (
                <CustomSwitch
                  name={name}
                  generalInfo={generalInfo}
                  {...field}
                  options={options}
                  defaultValue={defaultValue}
                  action={action}
                ></CustomSwitch>
              );
            case "radio":
              return (
                <CustomRadioButton
                  name={name}
                  {...field}
                  options={options}
                ></CustomRadioButton>
              );
            case "ChipInput":
              return (
                <ShortCodeInput
                  name={name}
                  value={values[name] || []}
                  onChange={(chips) => {
                    setFieldValue(name, chips);
                  }}
                />
              );

            case "button":
              return (
                <OutlinedButton
                  label={buttonName}
                  buttonClassName="w-[150px] h-[40px] text-xs mt-4 rounded-[10px]"
                  onClick={() => {
                    navigate(navigationLink);
                  }}
                />
              );

            case "none":
              return <div></div>;
            default:
              return <div>No element found</div>;
          }
        })()}
      </Grid>
    );
  });
};

export const getDefaultInitialValues = (elements, formData) => {
  let values = {};
  elements.forEach((x) => {
    values[x.name] =
      formData[x.name] !== undefined && formData[x.name] !== null
        ? formData[x.name]
        : x.defaultValue !== undefined && formData[x.name] !== null
        ? x.defaultValue
        : "";
  });

  return values;
};

export const generateValidationSchema = (elements, formData) => {
  let validationFields = {};

  elements?.forEach((x) => {
    if (
      x.type === "ChipInput" &&
      x.validation === true &&
      x.validationType === "array"
    ) {
      const schema = generateValidations(x);

      if (x.dynamic) {
        validationFields[x.name] = Yup.string().when(x.dynamic["field"], {
          is: (val) => {
            if (
              typeof x.dynamic["value"] === "string" ||
              typeof x.dynamic["value"] === "boolean" ||
              typeof x.dynamic["value"] === "number"
            ) {
              return x.dynamic["value"] === val;
            } else if (Array.isArray(x.dynamic["value"])) {
              return x.dynamic["value"].includes(val);
            }
            return false;
          },
          then: () => schema,
          otherwise: () => Yup.mixed(),
        });
      } else {
        validationFields[x.name] = schema;
      }
    }

    if (x.validations) {
      const schema = generateValidations(x);

      if (x.dynamic) {
        validationFields[x.name] = Yup.string().when(x.dynamic["field"], {
          is: (val) => {
            if (
              typeof x.dynamic["value"] === "string" ||
              typeof x.dynamic["value"] === "boolean" ||
              typeof x.dynamic["value"] === "number"
            ) {
              return x.dynamic["value"] === val;
            } else if (Array.isArray(x.dynamic["value"])) {
              return x.dynamic["value"].includes(val);
            }
            return false;
          },
          then: () => schema,
          otherwise: () => Yup.mixed().nullable(),
        });
      } else {
        validationFields[x.name] = schema;
      }
    }
  });

  return validationFields;
};
