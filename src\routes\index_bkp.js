import { lazy } from "react";

const Users = lazy(() => import("../pages/Users"));

const BasicPage = lazy(() => import("../pages/ServiceManagement/Basic_bkp"));
const GTdefinition = lazy(() =>
  import("../pages/ServiceManagement/GTdefinition")
);
const MTindication = lazy(() =>
  import("../pages/ServiceManagement/MTindication")
);

const SRIindication = lazy(() =>
  import("../pages/ServiceManagement/SRIindication")
);
const OperatorList = lazy(() =>
  import("../pages/PreRequisite/Operator/OperatorList")
);

const AddOperator = lazy(() =>
  import("../pages/PreRequisite/Operator/AddOperator")
);

const OperatorCluster = lazy(() =>
  import("../pages/PreRequisite/OperatorCluster/OperatorCluster")
);
const AddOperatorCluster = lazy(() =>
  import("../pages/PreRequisite/OperatorCluster/AddOperatorCluster")
);
const RedirectionalAccount = lazy(() =>
  import("../pages/PreRequisite/RedirectionalAccount/RedirectionalAccount")
);
const AddRedirectionalAccount = lazy(() =>
  import("../pages/PreRequisite/RedirectionalAccount/AddRedirectionalAccount")
);

const OtpVerification = lazy(() => import("../pages/OtpVerification"));
const ESMEAccountList = lazy(() =>
  import("../pages/PreRequisite/ESMEAccount/ESMEAccountList")
);
const AddESMEAccount = lazy(() =>
  import("../pages/PreRequisite/ESMEAccount/AddESMEAccount")
);

const ServiceManagement = lazy(() => import("../pages/ServiceManagement"));
const ListPage = lazy(() => import("../pages/List"));
/**
 * ⚠ These are internal routes!
 * They will be rendered inside the app, using the default `containers/Layout`.
 * If you want to add a route to, let's say, a landing page, you should add
 * it to the `App`'s router, exactly like `Login`, `CreateAccount` and other pages
 * are routed.
 *
 * If you're looking for the links rendered in the SidebarContent, go to
 * `routes/sidebar.js`
 */

const routes = [
  {
    path: "/dashboard",
    // component: Components,
  },
  {
    path: "/admin/user-management",
    component: Users,
  },
  {
    path: "/service",
    component: ServiceManagement,
  },
  {
    path: "/service-management/basic",
    component: BasicPage,
  },
  {
    path: "/service-management/gt",
    component: GTdefinition,
  },
  {
    path: "/service-management/mt",
    component: MTindication,
  },
  {
    path: "/service-management/sri",
    component: SRIindication,
  },
  {
    path: "/operator",
    component: OperatorList,
  },
  {
    path: "/addOperator",
    component: AddOperator,
  },
  {
    path: "/operator-cluster",
    component: OperatorCluster,
  },
  {
    path: "/operator-cluster/add",
    component: AddOperatorCluster,
  },
  {
    path: "/redirectional-account",
    component: RedirectionalAccount,
  },
  {
    path: "/redirectional-account/add",
    component: AddRedirectionalAccount,
  },
  {
    path: "/otp/verification",
    component: OtpVerification,
  },
  {
    path: "/ESMEAccount",
    component: ESMEAccountList,
  },
  {
    path: "/ESMEAccount/:action",
    component: AddESMEAccount,
  },
  {
    path: "/list/:module",
    component: ListPage,
  },
];

export default routes;
