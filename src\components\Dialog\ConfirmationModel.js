import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import Button from "../../components/Buttons/Button";

function ConfirmationModal({
  icon,
  title,
  body,
  show,
  onHide,
  onConfirm,
  cancelHide,
  confirmButonName,
  align,
  isLoading,
  iconImage,
}) {
  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 411,
          maxHeight: 435,
        },
      }}
      maxWidth="xs"
      open={show}
      onClose={onHide}
      className="p-6 font-sans"
    >
      <DialogTitle className="mt-2 flex justify-center">
        {title ? title : ""}
      </DialogTitle>
      <div className="border-b border-panelBorder mx-5"></div>
      <div className="flex justify-center items-center mt-2">
        <div>{icon ? iconImage : ""}</div>
      </div>
      <div className="text-base text-center mt-4 mx-auto">{body}</div>
      <div>
        <div className="text-center mt-10 gap-5 mb-4">
          {cancelHide && cancelHide === "true" ? (
            ""
          ) : (
            <Button
              onClick={onHide}
              buttonClassName={"w-[120px]"}
              label={"No"}
              //style={{ width: "100px", marginRight: "26px" }}
            />
          )}
          <Button
            variant="danger"
            label={confirmButonName ? confirmButonName : "Yes"}
            onClick={onConfirm}
            disabled={isLoading ? isLoading : false}
            buttonClassName={"w-[120px]"}
            //style={{ width: "100px" }}
          />
        </div>
      </div>
    </Dialog>
  );
}

export default ConfirmationModal;
