import React, { createContext, useState } from "react";

export const CustomerSupplierContext = createContext();

export const CustomerSupplierDetails = ({ children }) => {
  const [generalInfoData, setGeneralInfo] = useState([]);
  const [billingInfo, setBillingInfo] = useState([]);
  const [paymentInfo, setPaymentInfo] = useState([]);
  const [requiredInfo, setRequiredInfo] = useState([]);
  const [validationDetail, setValidationDetail] = useState([]);
  return (
    <CustomerSupplierContext.Provider
      value={{
        generalInfoData,
        setGeneralInfo,
        billingInfo,
        setBillingInfo,
        paymentInfo,
        setPaymentInfo,
        requiredInfo,
        setRequiredInfo,
        validationDetail,
        setValidationDetail,
      }}
    >
      {children}
    </CustomerSupplierContext.Provider>
  );
};
