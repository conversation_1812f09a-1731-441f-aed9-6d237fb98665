import { useDropzone } from "react-dropzone";
import { TrashIcon, UploadIcon } from "../../icons";
import { useState } from "react";
import <PERSON><PERSON><PERSON><PERSON>, { DocViewerRenderers } from "@cyntler/react-doc-viewer";
function Dropzone({ file, setFile, isCsv, isPreview = true }) {
  const [isLessThan5MB, setIsLessThan5MB] = useState(true);
  const [isPDFNDocFormat, setIsPDFNDocFormat] = useState(true);
  const [isJSONFormat, setIsJSONFormat] = useState();

  const [myFiles, setMyFiles] = useState(null);
  const { getRootProps, getInputProps, acceptedFiles } = useDropzone({
    accept: isCsv
      ? { "text/csv": [".csv"], "application/vnd.ms-excel": [".xlsx"] }
      : {
          "application/pdf": [".pdf"],
          "application/msword": [".doc"],
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            [".docx"],
        },
    // accept: {
    //   "application/pdf": [".pdf"],

    //   "application/msword": [".doc"],

    //   "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    //     [".docx"],
    //   // "text/csv": [".csv"],
    //   // "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xls"],
    //   // "application/vnd.ms-excel": [".xlsx"],
    // },
    maxSize: 5 * 1024 * 1024,
    maxFiles: 1,
    multiple: false,

    onDropAccepted: (files) => {
      const file = files[0];
      setFile(file);
      setMyFiles(file);
      setIsLessThan5MB(true);
      setIsPDFNDocFormat(true);

      // setIsLessThan5MB(file.size < 5 * 1024 * 1024);
      // setIsPDFNDocFormat(
      //   file.type === "application/pdf" ||
      //     file.type === "application/msword" ||
      //     file.type ===
      //       "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      // );

      // handleFileUpload(formData);
    },
    onDropRejected: (files) => {
      let file = files[0].file;
      setIsLessThan5MB(file.size < 5 * 1024 * 1024);
      setIsPDFNDocFormat(
        file.type === "application/pdf" ||
          file.type === "application/msword" ||
          file.type ===
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      );
      // setIsLessThan5MB(true);
      // setIsPDFNDocFormat(false);
      //setIsJSONFormat(false);
    },
  });

  const files = acceptedFiles.map((file) => (
    <div key={file.path}>
      {file.path} - {file.size} bytes
    </div>
  ));

  const remove = (file) => {
    setMyFiles(null);
    setFile(null);
  };
  // console.log("My files", window.URL.createObjectURL(myFiles));

  return (
    <>
      <div className="container border-2 border-dashed border-uploadBorer rounded-lg py-5 text-center">
        {!myFiles ? (
          <div {...getRootProps({ className: "dropzone" })}>
            <UploadIcon className="mx-auto"></UploadIcon>
            <input {...getInputProps()} />
            <p className="text-xs mt-2 mb-2">
              Drag your file to start uploading
            </p>
            <div className="relative flex items-center text-xs justify-center">
              <div className="w-[80px] border-t border-gray-400"></div>
              <span className="flex-shrink mx-4 text-gray-400">OR</span>
              <div className="w-[80px] border-t border-gray-400"></div>
            </div>
            <div className="w-[150px] mt-2 border-browseFilesBorder text-linkColor p-2 text-xs border rounded-lg mx-auto cursor-pointer">
              Browse files
            </div>
          </div>
        ) : (
          <>
            {isPreview ? (
              <>
                <DocViewer
                  documents={[
                    {
                      uri: window?.URL?.createObjectURL(myFiles),
                      fileName: myFiles.name,
                    },
                  ]}
                  pluginRenderers={DocViewerRenderers}
                />
                <TrashIcon
                  onClick={remove}
                  className="mx-auto cursor-pointer mt-5"
                ></TrashIcon>
              </>
            ) : (
              <div className="py-10 px-20 flex items-center">
                <UploadIcon className="mx-auto"></UploadIcon>
                <div className="flex-grow border-browseFilesNameBorder border rounded-lg ml-5 py-10 flex items-center px-5">
                  <div className="font-semibold">{files}</div>
                  <span
                    className="ml-auto cursor-pointer underline text-sm"
                    onClick={remove}
                  >
                    Remove
                  </span>
                </div>
              </div>
            )}
          </>
        )}

        <div className="text-errorColor text-xs mt-5 flex flex-col">
          <div>
            {!isPDFNDocFormat
              ? "File should be either PDF or doc format"
              : null}
          </div>
          <div>
            {" "}
            {!isLessThan5MB
              ? "Max file size should be less than or equal to 5 MB"
              : null}
          </div>
        </div>
      </div>
      {!myFiles && isPreview ? (
        <div className="text-xs mt-3">Only support pdf and doc files</div>
      ) : null}
      {!isPreview ? (
        <div className="text-xs mt-3">Only support csv and excel files</div>
      ) : null}
    </>
  );
}

export default Dropzone;
