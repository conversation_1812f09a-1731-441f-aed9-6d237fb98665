import React from "react";
import { Backdrop, CircularProgress, Box, Typography } from "@mui/material";
import { seriesNumbersLoading } from "../../common/config";

const FullScreenLoader = ({ open, progress }) => {
  return (
    <Backdrop
      sx={{
        color: "#fff",
        display: "flex", // Enables flex layout
        flexDirection: "column", // Places children in a column
        alignItems: "center", // Centers children horizontally
        justifyContent: "center", // Centers children vertically
        zIndex: (theme) => theme.zIndex.drawer + 1,
      }}
      open={open}
    >
      <Box>
        <CircularProgress color="inherit" />
      </Box>
      <Box mt={2}>
        <Typography variant="body1">
          {seriesNumbersLoading}
          {progress ? progress : ""}
        </Typography>
      </Box>
    </Backdrop>
  );
};

export default FullScreenLoader;
