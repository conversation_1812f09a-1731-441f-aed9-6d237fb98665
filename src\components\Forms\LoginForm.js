import React, { useContext } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { AuthContext } from "../../context/AuthContext";
import { Link } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import { emailValidation } from "../../common/yupValidation";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import LoginButton from "../../components/Buttons/OutlinedButton";
import InputLabel from "../FormsUI/InputLabel";
import TextFieldWrapper from "../FormsUI/TextField";
import { DataContext } from "../../context/DataContext";

function LoginForm() {
  var style = {
    fieldstyle: "mb-4 text-2xl font-sans font-semibold text-gray-900",
  };
  const { login } = useContext(AuthContext);
  const { setEmail, setPassword } = useContext(DataContext);

  const navigate = useNavigate();

  return (
    <div className="font-sans">
      <Formik
        initialValues={{
          email: "",
          password: "",
        }}
        validationSchema={Yup.object().shape({
          email: emailValidation,
          password: Yup.string().required("Password is required"),
        })}
        onSubmit={({ email, password }, { setStatus, setSubmitting }) => {
          setEmail(email);
          setPassword(password);
          setSubmitting(true);
          login(email, password, navigate, (resp) => {
            if (resp?.data?.otpSent === true) {
              navigate("/auth/verify-otp");
            }
          })
            .catch((error) => {
              console.log("error", error);
              toast.error(
                error?.response?.data?.message
                  ? error.response.data.message
                  : error.message
              );
            })
            .finally(() => {
              setSubmitting(false);
            });
        }}
      >
        {({ status, isSubmitting }) => (
          <Form>
            <TextFieldWrapper
              label={"Email ID"}
              isMandatory={true}
              className={style.fieldstyle}
              name="email"
              type="email"
              placeholder="Enter the Email ID"
            />
            <InputLabel
              label={"Password"}
              labelClassName={"mt-3"}
              isMandatory={true}
            />
            <TextFieldWrapper
              name="password"
              isPassword={true}
              placeholder="Enter the password"
            />
            <p className="text-right ">
              <span className="text-[#4977EB] underline underline-offset-1 cursor-pointer text-xs font-semibold">
                <Link to="/auth/forgot-password">{"Forgot password?"}</Link>
              </span>
            </p>
            <LoginButton
              type="submit"
              label={"Log in"}
              buttonClassName="w-full  text-sm mb-3 rounded  mt-6 bg-bgLogin text-white border-errorBorder shadow-xl"
              loading={isSubmitting}
            />
            {status && (
              <p className="text-errorColor text-xs " valid={false}>
                {status}
              </p>
            )}
          </Form>
        )}
      </Formik>
      <ToastContainer position="top-center" autoClose={3000} />
    </div>
  );
}

export default LoginForm;
