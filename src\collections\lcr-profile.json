{"header": "List of LCR", "columns": [{"header": "LCR ID", "accessorKey": "id"}, {"header": "LCR name", "accessorKey": "attributes.lcrName"}, {"header": "LCR type", "accessorKey": "attributes.lcrType"}, {"header": "Cpaas", "accessorKey": "attributes.isReve"}, {"buttons": [{"type": "image", "title": "View", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}, {"type": "image", "title": "Export", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"name": "lcrType", "title": "LCR type", "type": "select", "options": [{"label": "Default LCR", "value": 0}, {"label": "SC_MT", "value": 1}, {"label": "SPEC_LCR", "value": 3}, {"label": "Time based LCR", "value": 4}]}, {"name": "reveCusType", "title": "Cpaas LCR Type", "type": "select", "options": [{"label": "High Quality", "value": 1}, {"label": "Wholesale", "value": 2}, {"label": "Direct", "value": 3}]}, {"name": "reveLcrStatus", "title": "CPASS LCR Status", "type": "radio", "options": [{"label": "Test LCR", "value": 1}, {"label": "Live LCR", "value": 2}]}, {"name": "trafficType", "title": "Traffic type", "type": "radio", "options": [{"label": "A2P", "value": 1}, {"label": "P2P", "value": 2}]}], "formType": "simple", "isLCRList": true, "buttonName": "+ Add LCR", "moduleData": "lcr-profile", "moduleName": "LCR", "globalSearch": [{"label": "LCR ID", "value": "id"}, {"label": "LCR name", "value": "lcrName"}]}