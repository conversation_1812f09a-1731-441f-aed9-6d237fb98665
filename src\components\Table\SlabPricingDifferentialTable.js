import React, { useState, useEffect } from "react";
import { FieldArray, useFormikContext } from "formik";
import Select from "../FormsUI/Select";
import TextFieldWrapper from "../FormsUI/TextField";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
} from "@mui/material";
import { PlusCircleIcon, DeleteIcon, CloseIcon } from "../../icons";
import InputLabel from "../FormsUI/InputLabel";
import AddButton from "../Buttons/OutlinedButton";
import { useQuery } from "react-query";
import { getOperatorList } from "../../lib/customer-supplier-api";
import { getActionFromUrl } from "../../common/urlUtils";
import DeleteDialog from "../Dialog/DeleteDialog";
import theme from "../../tailwind-theme";

const areAllFieldsFilled = (values, fields) =>
  values.every((row) => fields.every((field) => row[field.name] !== ""));

const RenderTableBody = ({
  name,
  values,
  fields,
  headers,
  isView,
  onDeleteClick,
}) => {
  const { setFieldValue } = useFormikContext();

  useEffect(() => {
    values.forEach((row, index) => {
      if (row.sno !== index + 1) {
        setFieldValue(`${name}.${index}.sno`, index + 1);
      }
    });
  }, [values, name, setFieldValue]);

  return (
    <FieldArray name={name}>
      {({ push, remove }) => {
        const allFieldsFilled = areAllFieldsFilled(values, fields);
        const selectedSuppliers = values
          .map((row) => row.supplier)
          .filter(Boolean);

        return (
          <>
            {values?.map((_, index) => (
              <TableRow key={index}>
                {fields?.map((field, fieldIndex) => (
                  <TableCell key={fieldIndex}>
                    {field.name === "sno" ? (
                      <TextFieldWrapper
                        name={`${name}.${index}.${field.name}`}
                        InputProps={{
                          readOnly: true,
                        }}
                      />
                    ) : field.options ? (
                      <Select
                        name={`${name}.${index}.${field.name}`}
                        options={field.options.filter(
                          (option) =>
                            !selectedSuppliers.includes(option.value) ||
                            option.value === values[index].supplier
                        )}
                        className="md:w-[160px] w-full"
                        isDisabled={isView}
                      />
                    ) : (
                      <TextFieldWrapper
                        name={`${name}.${index}.${field.name}`}
                        isDisabled={isView}
                      />
                    )}
                  </TableCell>
                ))}
                <TableCell>
                  {values.length > 1 && (
                    <IconButton
                      type="button"
                      onClick={() => {
                        onDeleteClick(index, remove);
                        setTimeout(() => {
                          values.forEach((row, idx) => {
                            if (row.sno !== idx + 1) {
                              setFieldValue(`${name}.${idx}.sno`, idx + 1);
                            }
                          });
                        }, 0);
                      }}
                      disabled={isView}
                    >
                      <DeleteIcon />
                    </IconButton>
                  )}
                </TableCell>
              </TableRow>
            ))}

            <TableRow>
              <>
                {" "}
                {Array(fields.length)
                  .fill("")
                  .map((_, idx) => (
                    <TableCell key={idx}>
                      <div
                        style={{
                          border: "2px dotted #BEBEBE",
                          padding: "8px",
                          borderRadius: "10px",
                          height: "36px",
                          width: "100%",
                        }}
                      ></div>
                    </TableCell>
                  ))}
              </>
              <TableCell colSpan={headers.length + 1} align="right">
                <IconButton
                  type="button"
                  onClick={() =>
                    allFieldsFilled &&
                    push(
                      fields.reduce(
                        (acc, field) => ({ ...acc, [field.name]: "" }),
                        {}
                      )
                    )
                  }
                  disabled={!allFieldsFilled || isView}
                >
                  <PlusCircleIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          </>
        );
      }}
    </FieldArray>
  );
};

const RenderTable = ({
  name,
  values,
  headers,
  fields,
  isView,
  onDeleteClick,
}) => (
  <TableContainer
    component={Paper}
    sx={{ boxShadow: "none", maxHeight: 400, marginTop: 2 }}
  >
    <Table sx={{ minWidth: 550 }} aria-label="sticky table" stickyHeader>
      <TableHead sx={{ bgcolor: theme.backgroundColor.bgTable }}>
        <TableRow>
          {headers?.map((header, index) => (
            <TableCell
              key={index}
              sx={{
                position: "sticky",
                top: 0,
                zIndex: 1,
                backgroundColor: theme.backgroundColor.bgTable,
                fontWeight: "bold",
              }}
            >
              <div className="ml-0.5 text-sm font-semibold">{header}</div>
            </TableCell>
          ))}
          <TableCell
            sx={{
              position: "sticky",
              top: 0,
              zIndex: 1,
              backgroundColor: theme.backgroundColor.bgTable,
              fontWeight: "bold",
            }}
          />
        </TableRow>
      </TableHead>
      <TableBody>
        <RenderTableBody
          name={name}
          values={values}
          fields={fields}
          headers={headers}
          isView={isView}
          onDeleteClick={onDeleteClick}
        />
      </TableBody>
    </Table>
  </TableContainer>
);

const SlabPricingDifferentialTable = ({
  name,
  values,
  headers,
  fields,
  setFieldValue,
}) => {
  const [pricing, setPricing] = useState(values?.[name] || []);
  const [operatorDetails, setOperatorDetails] = useState([]);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [rowToDelete, setRowToDelete] = useState(null);

  useQuery(
    ["operator-list-details", "operators", { limit: -1 }, "operatorName", "id"],
    getOperatorList,
    {
      onSuccess: ({ data }) => {
        const optionsData = data?.data?.map((list) => ({
          value: list?.id,
          label: list?.attributes?.operatorName,
        }));
        setOperatorDetails(optionsData);
      },
    }
  );

  useEffect(() => {
    if (!values?.[name]) {
      setPricing([]);
    } else {
      setPricing(values?.[name]);
    }
  }, [values]);

  const addNewOperator = () => {
    const newOperator = {
      destOperator: "",
      pricingDetails: [
        {
          sno: "",
          slab: "",
          cost: "",
        },
      ],
      costThereafter: "",
    };

    let newOperatorData = [...pricing, newOperator];

    setPricing(newOperatorData);
    setFieldValue(name, newOperatorData);
  };

  const selectedDestOperators = pricing
    .map((policy) => policy.destOperator)
    .filter(Boolean);

  const removeOperator = (index) => {
    const newOperatorData = pricing.filter((_, i) => i !== index);
    setPricing(newOperatorData);
    setFieldValue(name, newOperatorData);
  };

  const action = getActionFromUrl(window.location.href);
  const onDeleteClick = (index, remove) => {
    setRowToDelete(() => () => remove(index));
    setDeleteDialog(true);
  };

  const handleDeleteConfirm = () => {
    if (rowToDelete) {
      rowToDelete();
      setRowToDelete(null);
      setDeleteDialog(false);
    }
  };

  return (
    <>
      {pricing?.map((policy, policyIndex) => (
        <div
          key={policyIndex}
          className="border border-subNavBorder p-3 bg-bgField mb-5 rounded-[10px] "
        >
          <div className="flex flex-wrap items-center justify-end">
            <div className="flex items-center mb-3">
              {pricing.length > 1 && action !== "view" && (
                <CloseIcon
                  className="w-3 h-3 cursor-pointer"
                  onClick={() => removeOperator(policyIndex)}
                />
              )}
            </div>
          </div>
          <div className="flex flex-wrap items-center justify-end">
            <div className="flex items-center ">
              <div className="text-xs font-extrabold mr-3 ">
                Destination Operator
              </div>
              <Select
                name={`${name}[${policyIndex}].destOperator`}
                options={operatorDetails.filter(
                  (option) =>
                    !selectedDestOperators.includes(option.value) ||
                    option.value === policy.destOperator
                )}
                value={policy.destOperator}
                className="md:w-[200px] w-full"
                bgColor="#FFFFFF"
                isDisabled={action === "view"}
              />
            </div>
          </div>
          <RenderTable
            name={`${name}[${policyIndex}].pricingDetails`}
            values={values?.[name]?.[policyIndex]?.pricingDetails || []}
            headers={headers}
            fields={policy.fieldsData || fields}
            isView={action === "view"}
            onDeleteClick={onDeleteClick}
          />
          <div className="flex justify-end mt-5">
            <div className="flex items-center space-x-4 text-black">
              <InputLabel label={"Cost thereafter"} />
              <TextFieldWrapper
                name={`${name}[${policyIndex}].costThereafter`}
                className="md:w-[350px] w-full"
                isDisabled={action === "view"}
              />
            </div>
          </div>
        </div>
      ))}
      <div className="mt-5 flex justify-end items-end">
        <AddButton
          label="+ Add new destination operator"
          buttonClassName="text-xs font-bold"
          onClick={addNewOperator}
          isDisabled={action === "view"}
        />
      </div>
      <DeleteDialog
        show={deleteDialog}
        onHide={() => setDeleteDialog(false)}
        onConfirm={handleDeleteConfirm}
        title={
          <>
            Are you sure you want to delete?
            <br />
            (Deletion will only happen on click of save)
          </>
        }
      />
    </>
  );
};

export default SlabPricingDifferentialTable;
