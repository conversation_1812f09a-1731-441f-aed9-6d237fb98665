import React, { useState, useEffect, useContext } from "react";
import { Field, FieldArray, useFormikContext } from "formik";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import { CssTextField } from "../FormsUI/StyledComponent";
import { moduleConfiguration } from "../../common/constants";
import { ErrorContext } from "../../context/ErrorContext";

const TextFieldWrapper = ({ field, form, ...props }) => (
  <CssTextField {...field} {...props} />
);

const RenderTableBody = ({
  name,
  values,
  fields,
  headers,
  multiSelectValue,
  fieldArrayError,
  setFieldArrayError,
  moduleName,
  redirectedEntityId,
  action,
}) => {
  const { setFieldValue } = useFormikContext();
  const [oldNameState, setOldNameState] = useState();
  const [oldListType, setOldListType] = useState();
  const [oldSampleValue, setOldSampleValue] = useState();

  const { fieldErrors, setFieldErrors } = useContext(ErrorContext);

  const idToValueMap = multiSelectValue.reduce((map, item) => {
    map[item.id] = item.value;
    return map;
  }, {});

  const validateFields = (distribution) => {
    if (values?.redirectionType === 2) {
      setFieldArrayError("");
    }
    const errors = {};
    let totalPercentage = 0;

    distribution?.forEach((item, index) => {
      if (values?.redirectionType === 3) {
        const percentageValue = item["percentage"];
        if (percentageValue === "") {
          errors[index] = "Percentage of traffic is required";
        }
        totalPercentage += Number(percentageValue || 0);
      }
    });

    if (values?.redirectionType !== 2) {
      if (totalPercentage > 100) {
        setFieldArrayError("Percentage of traffic should not exceed 100");
      } else if (totalPercentage < 100) {
        setFieldArrayError("Percentage of traffic should not be less than 100");
      } else {
        setFieldArrayError("");
      }
    }

    setFieldErrors(errors);
  };

  const calculateTraffic = (percentage) => {
    const sampleValue = values.sampleVal || 0;
    return (
      ((Number(percentage) / 100) * sampleValue).toFixed(2) +
      " out " +
      sampleValue
    );
  };

  const updateTrafficValues = (distribution) => {
    const updatedDistribution = distribution?.map((item, index) => ({
      ...item,
      priority: index + 1,
      ...(values?.redirectionType === 3
        ? {
            Traffic: calculateTraffic(item["percentage"]),
          }
        : {}),
    }));

    if (JSON.stringify(updatedDistribution) !== JSON.stringify(values[name])) {
      setFieldValue(name, updatedDistribution);
      setOldSampleValue(values.sampleVal);
    }
  };

  useEffect(() => {
    if (name !== oldNameState) {
      // handleReset();
      setOldNameState(name);
    } else if (values.sampleVal !== oldSampleValue) {
      setOldSampleValue(values.sampleVal);
    } else if (values?.redirectionListType !== oldListType) {
      setOldListType(values.redirectionListType);
    } else {
      const selectedValues = new Set(
        (values?.redirectionListType === "P" ||
        moduleName === moduleConfiguration.pointCodeListModuleName
          ? values?.point_codes || redirectedEntityId
          : values?.smsc_hosts || redirectedEntityId
        ).map((id) => idToValueMap[id])
      );

      let updatedDistribution = [];

      if (values?.redirectionType === 3) {
        updatedDistribution =
          values[name]?.filter((item) =>
            selectedValues.has(item["SMSC account"])
          ) || [];
      } else if (values?.redirectionType === 2) {
        updatedDistribution =
          values[name]?.filter((item) => selectedValues.has(item["Account"])) ||
          [];
      }

      const itemsToIterate =
        values?.redirectionListType === "P"
          ? values?.point_codes || redirectedEntityId
          : values?.smsc_hosts || redirectedEntityId;

      itemsToIterate?.forEach((itemId) => {
        const value = idToValueMap[itemId];

        if (
          value &&
          !updatedDistribution.some((item) => item["SMSC account"] === value) &&
          values?.redirectionType === 3
        ) {
          updatedDistribution.push({
            redirectedEntityId: itemId,
            "SMSC account": value,
            // percentage: "",
            Traffic: "",
          });
        } else if (
          value &&
          !updatedDistribution.some((item) => item["Account"] === value) &&
          values?.redirectionType === 2
        ) {
          updatedDistribution.push({
            redirectedEntityId: itemId,
            Account: value,
            // priority: "",
          });
        }
      });

      updatedDistribution = updatedDistribution.map((item) => {
        const priorityMatch = values.priorityTable?.find(
          (priorityItem) =>
            priorityItem.redirectedEntityId === item.redirectedEntityId
        );
        if (values?.redirectionType === 2 && priorityMatch) {
          return {
            ...item,
            priority: priorityMatch.priority || item.priority || "",
          };
        }

        if (values?.redirectionType === 3 && priorityMatch) {
          return {
            ...item,
            percentage:
              priorityMatch.percentage !== undefined
                ? priorityMatch.percentage
                : item.percentage !== undefined
                ? item.percentage
                : "",
          };
        }

        return item;
      });

      updateTrafficValues(updatedDistribution);
    }
  }, [
    values.smsc_hosts,
    values.point_codes,
    multiSelectValue,
    setFieldValue,
    values,
    name,
    idToValueMap,
  ]);

  useEffect(() => {
    updateTrafficValues(values[name]);
    validateFields(values[name]);
  }, [values.sampleVal, values[name]]);

  return (
    <>
      <FieldArray name={name}>
        {() => (
          <div style={{ minWidth: "100%", marginLeft: "20px" }}>
            <TableRow sx={{ height: "10px" }}>
              {headers.map((header, index) => (
                <TableCell
                  key={index}
                  sx={{
                    fontSize: "12px",
                    fontWeight: 400,
                    borderBottom: "none",
                    whiteSpace: "nowrap",
                    paddingBottom: "0px",
                  }}
                >
                  {header}
                </TableCell>
              ))}
              <TableCell />
            </TableRow>
            {values[name]?.map((item, index) => {
              return (
                <TableRow key={index}>
                  {fields.map((field, fieldIndex) => (
                    <TableCell key={fieldIndex}>
                      {field.name === "SMSC account" ||
                      field.name === "Account" ? (
                        <Field
                          name={`${name}.${index}.${field.name}`}
                          component={TextFieldWrapper}
                          value={
                            field.name === "SMSC account"
                              ? item["SMSC account"]
                              : item["Account"]
                          }
                          InputProps={{ readOnly: true }}
                        />
                      ) : field.name === "Traffic" ? (
                        <Field
                          name={`${name}.${index}.${field.name}`}
                          component={TextFieldWrapper}
                          value={item[field.name]}
                          InputProps={{ readOnly: true }}
                        />
                      ) : field.name === "percentage" ? (
                        <Field
                          name={`${name}.${index}.${field.name}`}
                          component={TextFieldWrapper}
                          value={item[field.name]}
                          onChange={(e) => {
                            const updatedValue = [...values[name]];
                            updatedValue[index][field.name] = e.target.value;
                            setFieldValue(name, updatedValue);
                            //  validateTotalPercentage(updatedValue);
                            updateTrafficValues(updatedValue);
                            validateFields(updatedValue);
                          }}
                          InputProps={{
                            readOnly: action === "viewData",
                          }}
                        />
                      ) : (
                        <Field
                          name={`${name}.${index}.${field.name}`}
                          component={TextFieldWrapper}
                          value={item[field.name]}
                          InputProps={{ readOnly: true }}
                        />
                      )}
                      {field.name === "priority" && fieldErrors[index] && (
                        <div
                          style={{
                            color: "#d32f2f",
                            fontSize: "11px",
                            //marginLeft: "20px",
                          }}
                        >
                          {fieldErrors[index]}
                        </div>
                      )}
                      {field.name === "percentage" && fieldErrors[index] && (
                        <div
                          style={{
                            color: "#d32f2f",
                            fontSize: "11px",
                            //marginLeft: "20px",
                          }}
                        >
                          {fieldErrors[index]}
                        </div>
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              );
            })}
          </div>
        )}
      </FieldArray>
      {fieldArrayError && (
        <div style={{ color: "#d32f2f", fontSize: "11px", marginLeft: "20px" }}>
          {fieldArrayError}
        </div>
      )}
    </>
  );
};

const RenderTable = ({
  name,
  values,
  headers,
  tableTitle,
  fields,
  multiSelectValue,
  setFieldArrayError,
  fieldArrayError,
  moduleName,
  redirectedEntityId,
  action,
}) => (
  <TableContainer component={Paper} sx={{ boxShadow: "none" }}>
    <Table
      sx={{
        minWidth: 550,
        border: "1.5px solid #BEBEBE",
      }}
      aria-label="simple table"
    >
      <TableHead
        sx={{
          backgroundColor: "#37415033",
          "& th": { padding: "10px" },
        }}
      >
        <TableRow>
          <TableCell>{tableTitle}</TableCell>
          <TableCell></TableCell>
          <TableCell></TableCell>
        </TableRow>
      </TableHead>
      <TableBody>
        <RenderTableBody
          headers={headers}
          name={name}
          values={values}
          fields={fields}
          multiSelectValue={multiSelectValue}
          fieldArrayError={fieldArrayError}
          setFieldArrayError={setFieldArrayError}
          moduleName={moduleName}
          redirectedEntityId={redirectedEntityId}
          action={action}
        />
      </TableBody>
    </Table>
  </TableContainer>
);
const SimpleFieldTable = ({
  name,
  values,
  headers,
  fields,
  multiSelectValue,
  tableTitle,
  setFieldArrayError,
  fieldArrayError,
  moduleName,
  action,
}) => {
  return (
    <>
      {(values.smsc_hosts && values.smsc_hosts?.length) ||
      (values?.point_codes && values?.point_codes?.length) > 0 ? (
        <RenderTable
          name={name}
          values={values}
          headers={headers}
          fields={fields}
          multiSelectValue={multiSelectValue}
          tableTitle={tableTitle}
          setFieldArrayError={setFieldArrayError}
          fieldArrayError={fieldArrayError}
          moduleName={moduleName}
          action={action}
        />
      ) : null}
    </>
  );
};

export default SimpleFieldTable;
