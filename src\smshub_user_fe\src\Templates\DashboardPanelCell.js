import { CssTooltip } from "../components/FormsUI/StyledComponent";
import { InfoIcon } from "../icons";
import React, { useState } from "react";

const DashboardPanelComponent = (element, maxLimit) => {
  const [counts, setCounts] = useState({ panelCount: 0, dashboardCount: 0 });

  console.log("counts", counts);

  const handleCountChange = (type, action, maxLimit) => {
    setCounts((prevCounts) => {
      const newValue =
        action === "increase"
          ? Math.min(prevCounts[type] + 1, maxLimit)
          : Math.max(prevCounts[type] - 1, 0);
      return { ...prevCounts, [type]: newValue };
    });
  };

  console.log("element", element);

  const countType =
    element === "dashboardCount" ? "dashboardCount" : "panelCount";
  return (
    <div className="flex justify-between items-center w-full">
      <div className="flex items-center">
        <span>
          No. of {element.element === "dashboardCount" ? "Dashboard" : " Panel"}
        </span>
      </div>
      <CssTooltip
        title={`Max ${element.maxLimit} ${
          element.element === "dashboardCount"
            ? "Dashboards allowed"
            : "Panels allowed"
        }`}
        placement="top"
        arrow
      >
        <InfoIcon className="w-4 h-3.5" />
      </CssTooltip>
      <div className="flex items-center justify-between border rounded ml-2 w-32">
        <button
          className="px-2"
          onClick={() =>
            handleCountChange(countType, "decrease", element.maxLimit)
          }
        >
          -
        </button>
        <input
          type="number"
          className="w-12 text-center border-none outline-none"
          min={0}
          max={element.maxLimit}
          value={counts[countType]}
          readOnly
        />
        <button
          className="px-2"
          onClick={() =>
            handleCountChange(countType, "increase", element.maxLimit)
          }
        >
          +
        </button>
      </div>
    </div>
  );
};

export default DashboardPanelComponent;
