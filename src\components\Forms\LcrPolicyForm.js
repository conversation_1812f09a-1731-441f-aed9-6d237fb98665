import React, { useEffect, useState } from "react";
import { Form, Formik } from "formik";
import { useMutation, useQuery } from "react-query";
import {
  createLcr,
  createRecord,
  updateParentDetail,
  getParentDetailsById,
} from "../../lib/lcr-profile-api";
import InputLabel from "../FormsUI/InputLabel";
import Select from "../FormsUI/Select";
import TextFieldWrapper from "../FormsUI/TextField";
import ImportFiles from "../Files/ImportFiles";
import SuccessDialog from "../Dialog/SuccessDialog";
import ErrorDialog from "../Dialog/ErrorDialog";
import Button from "../Buttons/Button";
import CancelButton from "../Buttons/OutlinedButton";
import { useNavigate } from "react-router-dom";
import CustomSwitch from "../ToggleSwitch/CustomSwitchHttp";
import LCRProfileFieldArrayTable from "../Table/LCRProfileFieldArrayTable";
import DatePickerFieldWrapper from "../FormsUI/DatePicker";
import TimeBasedPolicy from "./TimeBasedPolicy";
import RadioButtonGroup from "../RadioButton/RadioButtonGroup";
import { moduleConfiguration } from "../../common/constants";
import dayjs from "dayjs";
import { useSupplierData } from "../Dropdown";
import {
  getInitialValues,
  getValidationSchema,
} from "../HelperFunction/LcrProfile";
import LCRErrorTable from "../Dialog/LCRErrorTable";

function LcrPolicyForm({
  isEdit,
  id,
  parentId,
  dropdownDetails,
  isViewLCR,
  moduleData,
  lcrTypeValue,
}) {
  const navigate = useNavigate();
  const [file, setFile] = useState(null);
  const [lcrTypee, setLcrType] = useState(0);
  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [formData, setFormData] = useState(null);
  const [errorMessage, setErrorMessage] = useState("");

  const { mutate: createLCRAPI, isLoading: createLCRLoading } =
    useMutation(createLcr);
  const { mutate: createRecordAPI, isLoading: createLoading } =
    useMutation(createRecord);
  const {
    mutate: updateParentDetailAPI,
    isLoading: updateParentDetailLoading,
  } = useMutation(updateParentDetail);

  const supplierData = useSupplierData();

  const isDropdownEnabled = formData?.lcrType === 0 || formData?.lcrType === 3;
  const filteredOptions = isDropdownEnabled
    ? dropdownDetails[0].options.filter(
        (option) => option.value === 0 || option.value === 3
      )
    : dropdownDetails[0].options;

  useQuery(
    [
      moduleConfiguration.lcrConfiguration,
      moduleConfiguration.parentDetails,
      id,
      parentId,
      lcrTypeValue,
    ],
    getParentDetailsById,
    {
      enabled: parentId > 0,
      refetchOnWindowFocus: false,
      onSuccess: ({
        data: {
          data: { attributes },
        },
      }) => {
        setFormData(attributes);
      },
    }
  );

  const handleSubmit = (values) => {
    const detailsData = {
      lcrName: values.lcrName,
      lcrType: values.lcrType,

      parentDetails: values.parentDetails.map((policy) => {
        const commonFields = {
          region: policy.region,
          destOpId: policy.destOpId,
          mccMnc: policy.mccMnc,
        };

        if (values.lcrType === 4) {
          commonFields.percentage = policy.percentage;
        }

        return {
          ...commonFields,
          policyDetails: policy.policyDetails.map((item) => {
            if (values.lcrType !== 4) {
              return {
                supplier: item.supplier,
                cost: item?.cost,
                percentage: item?.percentage,
                position: item?.position,
                quality: item?.quality,
              };
            } else {
              return {
                supplier: item?.supplier,
                cost: item?.cost,
                fromTime: dayjs(item.fromTime, "HH:mm").format("HH:mm"),
                toTime: dayjs(item.toTime, "HH:mm").format("HH:mm"),
              };
            }
          }),
        };
      }),
    };

    if (values.lcrType === 0 && values.isReve === 1) {
      detailsData.isReve = values.isReve;
      detailsData.reveCusType = values.reveCusType;
      detailsData.trafType = values.trafType;
      detailsData.reveLcrStatus = Number(values.reveLcrStatus);
    } else if (values.lcrType === 0) {
      detailsData.isReve = values.isReve;
    } else if (values.lcrType === 4) {
      detailsData.dateReq = values.dateReq;
      if (values.dateReq === 1) {
        detailsData.startDate = dayjs(values.startDate).format("YYYY-MM-DD");
      }
    }

    const reqData = { data: detailsData, ...(file && { fileUpload: file }) };

    const apiConfig = {
      moduleData: moduleConfiguration.lcrImportConfiguration,
      reqData,
    };

    const apiCallbacks = {
      onSuccess: () => {
        setSuccessDialog(true);
        setMessage(`LCR ${isEdit ? "updated" : "created"} successfully`);
      },
      onError: ({ response }) => {
        const errorMessage = response?.data?.error?.message;
        const firstErrorMessage = Array.isArray(errorMessage)
          ? errorMessage[0]
          : errorMessage;
        if (
          file &&
          typeof firstErrorMessage === "string" &&
          firstErrorMessage.startsWith("Validation Errors")
        ) {
          setErrorMessage(firstErrorMessage);
        } else {
          setErrorDialog(true);
          setMessage(firstErrorMessage);
        }
      },
    };

    const commonUpdateAPI = (apiMethod, additionalConfig = {}) =>
      apiMethod({ ...apiConfig, ...additionalConfig }, apiCallbacks);

    if (isEdit) {
      commonUpdateAPI(updateParentDetailAPI, {
        moduleName: moduleConfiguration.lcrConfiguration,
        parentDetail: moduleConfiguration.parentDetails,
        lcrId: id,
        parentId: parentId,
      });
    } else {
      commonUpdateAPI(file ? createLCRAPI : createRecordAPI, {
        moduleName: moduleConfiguration.lcrConfiguration,
      });
    }
  };

  useEffect(() => {
    if (!file) {
      setErrorMessage("");
    }
  }, [file]);

  return (
    <Formik
      initialValues={getInitialValues(formData)}
      validateOnMount={true}
      validationSchema={getValidationSchema(file, lcrTypee)}
      enableReinitialize={true}
      onSubmit={(values) => {
        handleSubmit(values);
      }}
    >
      {({ values, setFieldValue, errors }) => (
        <Form>
          {setLcrType(values?.lcrType)}
          <div className="grid grid-cols-12 gap-10 p-10 md:mx-10 ">
            <div className="col-span-6 ">
              <InputLabel label={"LCR profile name"} isMandatory={true} />
              <TextFieldWrapper
                name="lcrName"
                placeholder={"Enter LCR profile name"}
                isDisabled={isEdit ? true : false}
              />
            </div>
            <div className="col-span-6 ">
              <InputLabel label={"LCR type"} isMandatory={true} />
              <Select
                name="lcrType"
                options={filteredOptions}
                isDisabled={
                  (isEdit && formData?.lcrType === 1) || formData?.isReve === 1
                    ? true
                    : false
                }
                onChange={(e) => {
                  setFile(null);
                }}
              />
            </div>
            {values.lcrType === 0 && (
              <>
                <div className="col-span-3 ">
                  <InputLabel label={"CPAAS value status"} isMandatory={true} />
                  <CustomSwitch
                    name="isReve"
                    checked={values.isReve}
                    disabled={isEdit ? true : false}
                  />
                </div>
                {values.isReve === 1 && (
                  <>
                    <div className="col-span-3 ">
                      <InputLabel
                        label={"CPAAS LCR status"}
                        isMandatory={false}
                      />
                      <RadioButtonGroup
                        name="reveLcrStatus"
                        value={values.reveLcrStatus}
                        onChange={(e) =>
                          setFieldValue("reveLcrStatus", e.target.value)
                        }
                        options={dropdownDetails[2].options}
                        isDisabled={isEdit ? true : false}
                      />
                    </div>
                    <div className="col-span-3 ">
                      <InputLabel label={"Traffic type"} isMandatory={false} />
                      <RadioButtonGroup
                        name="trafType"
                        value={values.trafType}
                        onChange={(e) =>
                          setFieldValue("trafType", e.target.value)
                        }
                        options={dropdownDetails[3].options}
                        isDisabled={isEdit ? true : false}
                      />
                    </div>
                    <div className="col-span-3 ">
                      <InputLabel
                        label={"CPAAS LCR type"}
                        isMandatory={false}
                      />
                      <Select
                        name="reveCusType"
                        options={dropdownDetails[1].options}
                        isDisabled={isEdit ? true : false}
                      />
                    </div>
                  </>
                )}
              </>
            )}
            {values.lcrType === 4 && (
              <>
                <div className="col-span-6 ">
                  <InputLabel
                    label={"Special date required"}
                    isMandatory={false}
                  />
                  <CustomSwitch
                    name="dateReq"
                    checked={values.dateReq}
                    disabled={isEdit ? true : false}
                  />
                </div>

                {values.dateReq === 1 && (
                  <>
                    <div className="col-span-6 ">
                      <InputLabel label={"Start date"} isMandatory={true} />
                      <DatePickerFieldWrapper name="startDate" />
                    </div>
                  </>
                )}
              </>
            )}
          </div>
          {values.lcrType === 4 ||
            (!isViewLCR && !isEdit && (
              <div className="md:mx-14">
                <ImportFiles setFile={setFile} file={file} />
              </div>
            ))}
          <div className="mt-10">
            {!file ? (
              <>
                {values.lcrType !== 4 ? (
                  <>
                    {" "}
                    <LCRProfileFieldArrayTable
                      name="parentDetails"
                      values={values}
                      headers={[
                        "Supplier",
                        "Cost",
                        "Percentage",
                        "Position",
                        "Quality",
                      ]}
                      fields={[
                        {
                          name: "supplier",
                          options: [supplierData],
                        },
                        { name: "cost" },
                        { name: "percentage" },
                        { name: "position" },
                        { name: "quality" },
                      ]}
                      setFieldValue={setFieldValue}
                      isEdit={isEdit}
                    />
                  </>
                ) : (
                  <>
                    {" "}
                    <TimeBasedPolicy
                      name="parentDetails"
                      values={values}
                      headers={["Supplier", "Cost", "Start time", "End time"]}
                      fields={[
                        {
                          name: "supplier",
                          options: [supplierData],
                        },
                        { name: "cost" },
                        { name: "fromTime" },
                        { name: "toTime" },
                      ]}
                      setFieldValue={setFieldValue}
                      isEdit={isEdit}
                    />
                  </>
                )}
              </>
            ) : null}
            {errorMessage && (
              <div className="mt-10">
                <LCRErrorTable errorMessages={errorMessage} />
              </div>
            )}
          </div>
          <div className="flex justify-center items-center mt-10">
            <CancelButton
              label="Cancel"
              buttonClassName="w-full md:w-[154px] h-[40px] text-sm mb-3 rounded-[20px]"
              onClick={() => {
                navigate(`/app/list/${moduleConfiguration.lcrConfiguration}`);
              }}
            />
            {!errorMessage && (
              <Button
                type="submit"
                label="Save"
                value="submit"
                buttonClassName="w-full md:w-[154px] h-[40px] text-base mb-3 rounded-[20px] ml-5"
                loading={
                  createLCRLoading || createLoading || updateParentDetailLoading
                }
              />
            )}
          </div>
          <SuccessDialog
            show={successDialog}
            onHide={() => {
              setSuccessDialog(false);
              navigate(`/app/list/${moduleConfiguration.lcrConfiguration}`);
            }}
            message={message}
          />
          <ErrorDialog
            show={errorDialog}
            onHide={() => setErrorDialog(false)}
            message={message}
          />
        </Form>
      )}
    </Formik>
  );
}

export default LcrPolicyForm;
