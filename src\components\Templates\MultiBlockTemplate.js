import { Formik, Form } from "formik";
import Grid from "@mui/material/Grid";
import Button from "../Buttons/OutlinedButton";
import ConfirmNNextButton from "../Buttons/Button";
import * as Yup from "yup";
import { useLocation, useNavigate } from "react-router-dom";
import { useMutation, useQuery } from "react-query";
import { createRecord, updateRecord, getDataById } from "../../lib/list-api";
import SuccessDialog from "../Dialog/SuccessDialog";
import { useContext, useEffect, useState } from "react";
import {
  getDefaultInitialValues,
  renderFields,
  generateValidationSchema,
} from "../../common/common";
import ErrorDialog from "../Dialog/ErrorDialog";
import MultiBlocks from "../Blocks/MultiBlocks";
import {
  deleteIndexedDB,
  getIndexedDBDataById,
  initializeIndexedDB,
  updateIndexedDBDataById,
} from "../HelperFunction/localStorage";
import { multiStepFormContext } from "../../context/MultiStepFormContext";
import { moduleConfiguration, operatorType } from "../../common/constants";
import { findCustomerSupplier } from "../../common/urlUtils";

const MultiBlockTemplate = (props) => {
  let {
    blocks,
    handleCancel,
    moduleName,
    action,
    id,
    moduleNameValue,
    navigationPath,
    header,
  } = props;

  const { mutate: createRecordAPI, isLoading: createLoading } =
    useMutation(createRecord);
  const { mutate: updateRecordAPI, isLoading: updateLoading } =
    useMutation(updateRecord);
  const location = useLocation();
  const navigate = useNavigate();

  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErroDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [formData, setFormData] = useState({});
  const [multiSelectValue, setMultiSelectValue] = useState([]);
  const [singleSelectValue, setSingleSelectValue] = useState([]);
  const [fieldArrayError, setFieldArrayError] = useState("");
  const [previousFormData, setPreviousFormData] = useState({});
  const [existingData, setExistingData] = useState(null);
  const searchParams = new URLSearchParams(location.search);
  const currentId = searchParams.get("currentId");

  const [deleteLastEntry, setDeleteLastEntry] = useState(false);
  const dataIndex =
    existingData?.data?.[0]?.moduleNameValue ===
    moduleConfiguration.customerSupplier
      ? 0
      : 1;

  const interfaceType =
    existingData?.data?.[dataIndex]?.formData?.generalInfoData?.interfaceType;
  const subInterfaceEsme =
    existingData?.data[dataIndex]?.formData?.generalInfoData?.subInterfaceEsme;
  const ocComplaince =
    existingData?.data[dataIndex]?.formData?.generalInfoData?.ocCompliance;
  const { setCurrentStep } = useContext(multiStepFormContext);

  const fetchData = async (isPrevious) => {
    if (currentId) {
      try {
        const data = await getIndexedDBDataById(
          "navigationDetails",
          "FormDB",
          parseInt(currentId)
        );

        if (isPrevious && data?.data?.length) {
          const lastRecord = data.data.find(
            (ele) => ele.moduleNameValue === moduleName
          );

          if (lastRecord) {
            setPreviousFormData(lastRecord);
            setDeleteLastEntry(true);
          }
        }
        setExistingData(data);
      } catch (error) {
        console.error("Error fetching data from IndexedDB:", error);
      }
    }
  };

  useEffect(() => {
    fetchData(action === "add" ? true : false);
  }, [currentId]);

  const doBeforeNavigate = async (path) => {
    if (deleteLastEntry) {
      const currentData = existingData.data.find(
        (entry) => entry.moduleNameValue === moduleName
      );
      existingData.data = existingData.data.filter(
        (entry) => entry.moduleNameValue !== moduleName
      );
      await updateIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        Number(currentId),
        existingData
      );
      if (currentData?.previousPath) {
        navigate(currentData.previousPath);
        return;
      }
    }
    navigate(path);
  };

  const handleButtonClick = async () => {
    if (existingData && existingData.data && existingData.data.length > 0) {
      const lastRecord = existingData.data[existingData.data.length - 1];
      if (
        !lastRecord?.previousPath &&
        lastRecord?.currentPath === `${location.pathname}${location.search}`
      ) {
        try {
          doBeforeNavigate(`/app/list/${moduleName}`);
          await deleteIndexedDB("navigationDetails");
        } catch (error) {
          console.error("Error deleting database:", error);
        }
      } else if (lastRecord?.currentPath) {
        setCurrentStep(3);

        doBeforeNavigate(lastRecord.currentPath);
      }
    } else {
      doBeforeNavigate(`/app/list/${moduleName}`);
    }
  };

  useEffect(() => {
    if (navigationPath) {
      const dbName = "navigationDetails";
      const storeName = "FormDB";

      initializeIndexedDB(dbName, storeName)
        .then((result) => {
          const currentUrl = new URL(window.location.href);

          if (!currentUrl.searchParams.has("currentId")) {
            currentUrl.searchParams.set(
              "currentId",
              result?.id ? result.id : result?.data?.[0]?.id || 1
            );
            navigate(currentUrl.pathname + currentUrl.search, {
              replace: true,
            });
          }
        })
        .catch((error) => {
          console.error("Error initializing IndexedDB:", error);
        });
    }
  }, [navigationPath, navigate]);

  const handleSubmit = (values) => {
    const updatedBlocks = blocks.map((block) => ({
      ...block,
      elements: block?.elements ? [...block.elements] : [],
    }));
    const elementsFromDefaultBlocks = updatedBlocks
      ?.filter((block) => block.blockType === "defaultBlock")
      .flatMap((block) => block.elements || []);

    const allowedValues = {};

    elementsFromDefaultBlocks.forEach((field) => {
      const dynamic = field.dynamic;
      const visibility = field.visibilityConditions;

      let isDynamicValid = true;

      if (dynamic) {
        const fieldValue = String(values[dynamic.field]).toLowerCase();
        if (Array.isArray(dynamic.value)) {
          isDynamicValid = dynamic.value
            .map((val) => String(val).toLowerCase())
            .includes(fieldValue);
        } else {
          isDynamicValid = fieldValue === String(dynamic.value).toLowerCase();
        }
      }

      let isVisible = true;
      if (visibility) {
        isVisible = Object.entries(visibility).every(([key, validValues]) => {
          const fieldValue = String(values[key]).toLowerCase();
          return validValues.some(
            (validValue) => String(validValue).toLowerCase() === fieldValue
          );
        });
      }

      if (isDynamicValid && isVisible) {
        allowedValues[field.name] = values[field.name];
      }
    });

    let sampleData = {};

    Object.keys(allowedValues)?.forEach((x) => {
      if (allowedValues[x] !== undefined && allowedValues[x] !== "") {
        sampleData[x] = allowedValues[x];
      }
    });

    const cleanedSs7PathExtended = values.ss7PathExtended?.map((entry) => {
      return Object.entries(entry)
        .filter(([_, value]) => value !== undefined && value !== "")
        .reduce((acc, [key, value]) => {
          acc[key] = value;
          return acc;
        }, {});
    });

    sampleData.ss7PathExtended = cleanedSs7PathExtended;
    const formData = existingData?.data[dataIndex]?.formData;

    const outgoingAccounts = sampleData?.supplierListId;
    const generalInfo = formData?.generalInfoData;
    const billingInformation = formData?.billingInfo;
    const paymentInfo = formData?.paymentInfo;
    const esmeAccount = existingData?.data[dataIndex]?.esmeAccounts;
    const requiredInfoS7 = existingData?.data[dataIndex]?.requiredInfoDetails;
    const requiredInfo =
      formData?.generalInfoData?.interfaceType === "SMPP_ES"
        ? [esmeAccount] || []
        : formData?.generalInfoData?.interfaceType === "SMPP"
        ? formData?.requiredInfo?.map((item) => item.id) || []
        : null;

    if (typeof sampleData.supplierId !== "number") {
      delete sampleData.supplierId;
    }
    if (existingData?.data[dataIndex]?.actionData === "edit") {
      sampleData.id = Number(id);
    }
    let customerData = {
      data: {
        path: {
          data: sampleData,
        },
        ...generalInfo,
        billingInformation: billingInformation,
        ...paymentInfo,
      },
    };
    if (
      formData?.generalInfoData?.interfaceType === "SMPP_ES" &&
      formData?.generalInfoData?.interfaceType === "SMPP"
    ) {
      customerData.data = {
        ...customerData.data,
        outgoingAccounts: String(outgoingAccounts),
      };
    }
    if (formData?.generalInfoData?.interfaceType === "SS7") {
      customerData.data = {
        ...customerData.data,
        gtType: requiredInfoS7?.gtType,
        calledAddrType: requiredInfoS7?.calledAddrType,
        operator_clusters: requiredInfoS7?.operator_clusters ?? [],
        gtValue: requiredInfoS7?.gtValue,
        ...(requiredInfoS7?.calledAddrType === 3 && {
          calledGtPrefix: requiredInfoS7?.calledGtPrefix,
        }),
      };
    }
    if (
      generalInfo?.operatorType === operatorType.Both ||
      generalInfo?.operatorType === operatorType.Customer
    ) {
      customerData.data.esme_accounts = requiredInfo;
    }
    if (interfaceType) {
      if (existingData?.data[dataIndex]?.actionData === "edit") {
        updateRecordAPI(
          {
            moduleName: moduleConfiguration.customerSupplier,
            id: existingData?.data[dataIndex]?.customerId,
            reqData: customerData,
          },
          {
            onSuccess: ({ data }) => {
              setSuccessDialog(true);
              setMessage(
                `${findCustomerSupplier(
                  existingData?.data[dataIndex]?.formData?.generalInfoData
                    .operatorType
                )} ${
                  existingData?.data[dataIndex]?.actionData === "edit"
                    ? "updated"
                    : "created"
                }  successfully`
              );
            },
            onError: ({ response }) => {
              setErroDialog(true);
              setMessage(response?.data?.error?.message);
            },
          }
        );
      } else {
        createRecordAPI(
          {
            moduleName: moduleConfiguration.customerSupplier,
            reqData: customerData,
          },
          {
            onSuccess: ({ data }) => {
              setSuccessDialog(true);
              setMessage(
                `${findCustomerSupplier(
                  existingData?.data[dataIndex]?.formData?.generalInfoData
                    .operatorType
                )} ${
                  existingData?.data[dataIndex]?.actionData === "edit"
                    ? "updated"
                    : "created"
                }  successfully`
              );
            },
            onError: ({ response }) => {
              setErroDialog(true);
              setMessage(response?.data?.error?.message);
            },
          }
        );
      }
    } else {
      if (action === "add" && !interfaceType) {
        let reqData = { data: sampleData };
        createRecordAPI(
          { moduleName, reqData },
          {
            onSuccess: ({ data }) => {
              setSuccessDialog(true);
              setMessage(`${moduleNameValue} created successfully`);
            },

            onError: ({ response }) => {
              setErroDialog(true);
              setMessage(response?.data?.error?.message);
            },
          }
        );
      } else {
        let reqData = { data: sampleData };
        updateRecordAPI(
          { moduleName, id, reqData },
          {
            onSuccess: ({ data }) => {
              setSuccessDialog(true);
              setMessage(`${moduleNameValue} updated successfully`);
            },
            onError: ({ response }) => {
              setErroDialog(true);
              setMessage(response?.data?.error?.message);
            },
          }
        );
      }
    }
  };

  function filterEmptyValues(obj) {
    if (obj === null || obj === undefined) {
      return false;
    }
    if (Array.isArray(obj)) {
      if (obj.length === 0) {
        return false;
      }
      return obj.map((item) => filterEmptyValues(item)).some(Boolean);
    }
    if (typeof obj === "object") {
      if (Object.keys(obj).length === 0) {
        return false;
      }
      return Object.entries(obj)
        .map(([_, value]) => filterEmptyValues(value))
        .some(Boolean);
    }
    return true;
  }

  const { isLoading: isFormDataLoading } = useQuery(
    [moduleName, id, "", "getAll"],
    getDataById,
    {
      enabled: id > 0 ? true : false,
      refetchOnWindowFocus: false,
      onSuccess: (resp) => {
        const filteredAttributes = Object.fromEntries(
          Object.entries(resp?.data?.data?.attributes || {}).filter(
            ([_, value]) => filterEmptyValues(value)
          )
        );

        setFormData(filteredAttributes);
      },
    }
  );

  const updatedBlocks = blocks.map((block) => {
    const updatedElements = block?.elements?.map((element) => {
      if (element.name === "interfaceType" && interfaceType) {
        const interfaceMapping = {
          SS7: 1,
          SMPP_ES: 3,
          SMPP:
            Number(subInterfaceEsme) === 1
              ? 2
              : Number(subInterfaceEsme) === 2
              ? 4
              : 1,
        };

        const value = interfaceMapping[interfaceType] ?? 1;
        return {
          ...element,
          addEditable: true,
          replaceValue: value,
        };
      }
      if (element.name === "supplierId" && interfaceType && action === "add") {
        return {
          ...element,
          type: "text",
          defaultValue:
            existingData?.data[dataIndex]?.formData?.generalInfoData?.name,
          addEditable: true,
        };
      }
      if (element.name === "ocComplianceFlag" && ocComplaince) {
        return {
          ...element,
          replaceValue: ocComplaince === "Y" ? 1 : 0,
          addEditable: true,
        };
      }
      return element;
    });
    return { ...block, elements: updatedElements };
  });

  const elementsFromDefaultBlocks = updatedBlocks
    ?.filter((block) => block.blockType === "defaultBlock")
    .flatMap((block) => block.elements || []);

  const initialValuesFromCustomizedBlocks = updatedBlocks
    ?.filter((block) => block.blockType === "customizedBlocks")
    .flatMap((block) => block || []);

  const combinedResults = [
    ...elementsFromDefaultBlocks,
    ...initialValuesFromCustomizedBlocks,
  ];
  const combinedInitialValues = [
    ...elementsFromDefaultBlocks,
    initialValuesFromCustomizedBlocks[0],
  ];
  return (
    <div>
      {!isFormDataLoading ? (
        <Formik
          initialValues={
            action === "add" && !previousFormData.formData
              ? getDefaultInitialValues(
                  elementsFromDefaultBlocks,
                  formData,
                  moduleName
                )
              : previousFormData.formData
              ? getDefaultInitialValues(
                  combinedInitialValues,
                  previousFormData.formData,
                  moduleName
                )
              : getDefaultInitialValues(
                  combinedInitialValues,
                  formData,
                  moduleName
                )
          }
          validationSchema={Yup.object().shape(
            generateValidationSchema(combinedResults)
          )}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, submitForm, setFieldValue, errors }) => (
            <Form>
              {updatedBlocks.map((block, blockIndex) => {
                if (
                  block.blockOf &&
                  block.blockOf.field &&
                  block.blockOf.value &&
                  values[block.blockOf.field] &&
                  block.blockOf.value.includes(values[block.blockOf.field])
                ) {
                  return (
                    <fieldset
                      key={blockIndex}
                      className="border border-gray-300 p-1 m-0 md:p-10 md:m-8 rounded-md"
                    >
                      <legend className="text-sm">{block.blockName}</legend>
                      <Grid container spacing={4}>
                        {renderFields(
                          block.elements,
                          values,
                          setFieldValue,
                          navigate,
                          formData,
                          action,
                          setMultiSelectValue,
                          multiSelectValue,
                          fieldArrayError,
                          setFieldArrayError,
                          moduleName,
                          singleSelectValue,
                          setSingleSelectValue,
                          previousFormData?.formData,
                          header
                        )}
                      </Grid>
                    </fieldset>
                  );
                } else {
                  return null;
                }
              })}
              {values?.isCustomPath === 1 && (
                <MultiBlocks
                  blockName={blocks[4].blockName}
                  name="ss7PathExtended"
                  key={blocks[4].blockName}
                  blockIndex={0}
                  block={blocks[4]}
                  values={values}
                  moduleName={moduleName}
                  action={action}
                />
              )}
              <div className="flex justify-center gap-5 pt-5 pb-10">
                {action === "viewData" ? (
                  <Button
                    label={"Close"}
                    buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
                    onClick={() => handleCancel()}
                  />
                ) : (
                  <Button
                    label={"Cancel"}
                    buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
                    onClick={() => {
                      handleButtonClick();
                    }}
                  />
                )}

                {action === "viewData" ? null : (
                  <ConfirmNNextButton
                    label={"Save"}
                    buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
                    onClick={() => submitForm()}
                    loading={createLoading || updateLoading}
                  />
                )}
              </div>

              <SuccessDialog
                show={successDialog}
                onHide={() => {
                  navigate(`/app/list/${moduleConfiguration.customerSupplier}`);
                  setSuccessDialog(false);
                }}
                message={message}
              />

              <ErrorDialog
                show={errorDialog}
                onHide={() => {
                  setErroDialog(false);
                  if (
                    !message.includes("path") &&
                    !message.includes("paths") &&
                    !message.includes("Path") &&
                    !message.includes("Paths")
                  ) {
                    if (
                      existingData?.data[dataIndex]?.moduleNameValue ===
                      moduleConfiguration.customerSupplier
                    ) {
                      navigate(existingData?.data[dataIndex]?.currentPath);
                    }
                  }
                }}
                message={message}
              ></ErrorDialog>
            </Form>
          )}
        </Formik>
      ) : null}
    </div>
  );
};

export default MultiBlockTemplate;
