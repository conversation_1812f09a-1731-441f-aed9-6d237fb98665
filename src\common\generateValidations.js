import * as Yup from "yup";
import { IPV4_regex, IPV6_regex } from "./constants.js";

const applyNumericValidations = (schema, rule) => {
  switch (rule.type) {
    case "min":
      return schema.min(rule.value, rule.message);
    case "max":
      return schema.max(rule.value, rule.message);
    case "integer":
      return schema
        .integer(rule.message)
        .test(
          "no-decimal",
          rule.message || "Decimal values are not allowed",
          (value, context) => {
            const originalValue = context.originalValue;
            if (value === undefined || value === null) return true;
            const regex = /^-?\d+$/;
            return regex.test(originalValue);
          }
        );
    case "positive":
      return schema.positive(rule.message);
    case "numRange":
      return schema.test("range-validation", rule.message, (value) => {
        if (Array.isArray(value)) {
          return value.every((v) => {
            const [start, end] = v
              .split("-")
              .map((num) => parseInt(num.trim(), 10));
            return start < end;
          });
        }
        const [start, end] = value
          .split("-")
          .map((num) => parseInt(num.trim(), 10));
        return start < end;
      });
    case "greaterThan":
      return schema.test("greaterThan", rule.message, function (value) {
        const relatedField = this.parent[rule.field];
        return value >= relatedField;
      });
      break;

    default:
      return schema;
  }
};

const applyStringValidations = (schema, rule) => {
  switch (rule.type) {
    case "min":
      return schema.min(rule.value, rule.message);
    case "max":
      return schema.max(rule.value, rule.message);
    case "required":
      return schema.required(rule.message);
    case "typeError":
      return schema.typeError(rule.message);
    case "matches":
      const dynamicRegex = new RegExp(rule.regex);
      return schema?.matches(dynamicRegex, rule.message);
    case "nullable":
      return schema.nullable();
    case "emptyArray":
      return schema.test("empty-array", rule.message, (value) => {
        if (Array.isArray(value) && value.length === 0) {
          return false;
        }
        return true;
      });
    case "ip-validation-single": //single field validation,not array
      return schema.test("ip-validation-single", rule.message, (value) => {
        return IPV4_regex.test(value) || IPV6_regex.test(value);
      });
    case "ip-validation": //array validation
      return schema.test("ip-validation", rule.message, (value) => {
        if (Array.isArray(value)) {
          return value.every(
            (ip) => IPV4_regex.test(ip) || IPV6_regex.test(ip)
          );
        }
        return false;
      });

    case "numRange": //array validation
      return schema.test("range-validation", rule.message, (value) => {
        if (Array.isArray(value)) {
          return value.every((v) => {
            const [start, end] = v
              .split("-")
              .map((num) => parseInt(num.trim(), 10));
            return start < end;
          });
        }
        const [start, end] = value
          .split("-")
          .map((num) => parseInt(num.trim(), 10));
        return start < end;
      });
    case "unique-error-message":
      return schema.test(
        "unique-error-message",
        rule.message,
        function (value) {
          if (!value || value.length === 0) return true;
          const errors = value.map((v) =>
            IPV4_regex.test(v) || IPV6_regex.test(v)
              ? null
              : "Please enter a valid IPv4 or IPv6 address."
          );
          const errorMessage = errors.find((error) => error !== null);
          return errorMessage
            ? this.createError({ message: errorMessage })
            : true;
        }
      );
    case "alphanumeric-array": //array validation
      return schema.test("alphanumeric-validation", rule.message, (value) => {
        if (!Array.isArray(value)) {
          return false;
        }
        const alphaNumWithOptionalSymbolsRegex =
          /^[A-Za-z0-9]*[A-Za-z]+[A-Za-z0-9]*[0-9]+[A-Za-z0-9]*([!@#$%^&*()_+={}\[\]:;"'<>?,./]*)$/;
        return value.every((item) => {
          if (!item.includes(",")) return false;
          const [alphanumericPart, lengthPart] = item.split(",");
          const isValidAlphaNum =
            alphaNumWithOptionalSymbolsRegex.test(alphanumericPart);
          const isLengthMatching =
            alphanumericPart.length === Number(lengthPart);
          return isValidAlphaNum && isLengthMatching;
        });
      });
    case "no-duplicates":
      return schema.test("no-duplicates", rule.message, (value) => {
        if (value && value.length) {
          const uniqueValues = new Set(value);
          return uniqueValues.size === value.length;
        }
        return true;
      });
    case "hypenRangeMin":
      return schema.test("hypenRangeMin", rule.message, (value) => {
        const ranges = value.split(",");
        return ranges.length >= rule.value;
      });
    case "hypenRangeMax":
      return schema.test("hypenRangeMax", rule.message, (value) => {
        const ranges = value.split(",");
        return ranges.length <= rule.value;
      });
    case "noDuplicates":
      return schema.test("noDuplicates", rule.message, (value) => {
        if (typeof value === "string") {
          const ranges = value.split(",").map((range) => range.trim());
          const uniqueRanges = new Set(ranges);
          return uniqueRanges.size === ranges.length;
        }
        return true;
      });
    default:
      return schema;
  }
};

const applyValidations = (schema, validations) => {
  if (Array.isArray(validations)) {
    validations.forEach((rule) => {
      if (
        [
          "min",
          "max",
          "integer",
          "numeric",
          "numRange",
          "greaterThan",
        ].includes(rule.type)
      ) {
        schema = applyNumericValidations(schema, rule);
      } else {
        schema = applyStringValidations(schema, rule);
      }
    });
  }
  return schema;
};

const applyfieldBasedValidations = (schema, validations) => {
  return schema.test("is-valid", "Each valid number", function (value) {
    const { parent } = this;
    if (!parent) return true;
    if (validations.some((v) => v.type === "required")) {
      const requiredValidation = validations.find((v) => v.type === "required");
      if (!value || value.length === 0) {
        // return this.createError({ message: requiredValidation.message });

        if (parent.grpType === "100") {
          return this.createError({
            message: requiredValidation.alphaNumericMessage,
          });
        } else if (parent.grpType !== "100") {
          return this.createError({
            message: requiredValidation.numbersMessage,
          });
        }
      }
    }
    if (validations.some((v) => v.type === "no-duplicates")) {
      const noDuplicatesValidation = validations.find(
        (v) => v.type === "no-duplicates"
      );
      if (value.length !== new Set(value).size) {
        return this.createError({
          message: noDuplicatesValidation.message,
        });
      }
    }

    if (
      validations.some((v) => v.type === "min" || v.type === "minAlphaNumeric")
    ) {
      const minValue = validations.find((v) =>
        parent.grpType !== "100"
          ? v.type === "min"
          : v.type === "minAlphaNumeric"
      );

      const singleDigitPositions = [];
      value.forEach((item, index) => {
        if (item.length === 1) {
          singleDigitPositions.push(index + 1);
        }
      });

      const minErrors = [];
      if (value.some((item) => item.length < minValue.value)) {
        minErrors.push(minValue.message);
      }

      // if (singleDigitPositions.length > 0) {
      //   minErrors.unshift(
      //     `Single digit number is not allowed. Single digit number is encountered at position ${singleDigitPositions.join(
      //       ", "
      //     )}.`
      //   );
      // }

      if (minErrors.length > 0) {
        return this.createError({
          message: minErrors.join("\n"),
        });
      }
    }

    if (
      validations.some((v) => v.type === "max" || v.type === "maxAlphaNumeric")
    ) {
      const maxValue = validations.find((v) =>
        parent.grpType !== "100"
          ? v.type === "max"
          : v.type === "maxAlphaNumeric"
      );

      const maxExceedingPositions = [];
      value.forEach((item, index) => {
        if (item.length > maxValue.value) {
          maxExceedingPositions.push(index + 1);
        }
      });

      const maxErrors = [];
      if (maxExceedingPositions.length > 0) {
        maxErrors.push(`Please enter a valid maximum length of 15 digits.`);
      }

      if (maxErrors.length > 0) {
        return this.createError({
          message: maxErrors.join("\n"),
        });
      }
    }

    const matchedValidation = validations.find(
      (v) => v.options && v.options.includes(parseInt(parent.grpType))
    );
    if (matchedValidation) {
      if (
        matchedValidation.type === "numeric-array" ||
        matchedValidation.type === "alpha-numeric-array" ||
        parseInt(parent?.grpType)
      ) {
        let convertedRegex = new RegExp(matchedValidation.regex);
        if (convertedRegex && typeof convertedRegex.test === "function") {
          if (!value.every((val) => convertedRegex.test(val))) {
            return this.createError({ message: matchedValidation.message });
          }
        } else {
          return this.createError({ message: "Invalid value" });
        }
      }
    } else {
      const numericValidation = validations.find(
        (v) => v.type === "numeric-array"
      );
      if (numericValidation && !value.every((val) => !isNaN(Number(val)))) {
        return this.createError({ message: numericValidation.message });
      }
    }
    return true;
  });
};

const generateValidationSchema = (field) => {
  let schema;
  if (field.validationType === "array" && field.type === "fieldArray") {
    schema = Yup.array().of(
      Yup.object().shape(
        field.fieldsData.reduce((acc, subField) => {
          acc[subField.name] = applyValidations(
            Yup[subField.validationType ? subField.validationType : "string"](),
            subField.validations || []
          );
          return acc;
        }, {})
      )
    );
  } else if (
    field.validationType === "array" &&
    field.blockType === "customizedBlocks"
  ) {
    schema = Yup.array().of(
      Yup.object().shape(
        field.fields.reduce((acc, subField) => {
          const baseSchema =
            Yup[subField.validationType ? subField.validationType : "string"]();
          if (subField.dynamic) {
            acc[subField.name] = Yup.string().when(subField.dynamic.field, {
              is: (val) => {
                if (
                  typeof subField.dynamic.value === "string" ||
                  typeof subField.dynamic.value === "boolean" ||
                  typeof subField.dynamic.value === "number"
                ) {
                  const convertedVal = Number(val);
                  return subField.dynamic.value === convertedVal;
                } else if (Array.isArray(subField.dynamic.value)) {
                  return subField.dynamic.value.includes(val);
                }
                return false;
              },
              then: () =>
                applyValidations(baseSchema, subField.validations || []),
              otherwise: () => Yup.string(),
            });
          } else {
            acc[subField.name] = applyValidations(
              baseSchema,
              subField.validations || []
            );
          }
          return acc;
        }, {})
      )
    );
  } else if (
    field.validationType === "array" &&
    field.type === "MultiChipInput" &&
    field.fieldBased === "grpType"
  ) {
    schema = Yup.array();
    schema = applyfieldBasedValidations(schema, field.validations || []);
  } else if (
    field.validationType === "array" &&
    field.fieldBased === "gtValue"
  ) {
    schema = Yup.array()
      .of(
        Yup.string()
          .test(
            "is-positive-integer",
            field.validationConfig.integerError,
            (value) => /^\d+$/.test(value)
          )
          .min(
            field.validationConfig.minValue.value,
            field.validationConfig.minValue.message
          )
          .max(
            field.validationConfig.maxValue.value,
            field.validationConfig.maxValue.message
          )
      )
      .min(
        field.validationConfig.minArrayLength.value,
        field.validationConfig.minArrayLength.message
      )
      .test("no-duplicates", field.validationConfig.uniqueError, (value) =>
        Array.isArray(value) ? new Set(value).size === value.length : true
      );
  } else if (
    field.validationType === "array" &&
    field.fieldBased === "senderPrefix"
  ) {
    schema = Yup.array()
      .of(
        Yup.string()
          .test(
            "is-positive-integer",
            field.validationConfig.integerError,
            (value) => /^\d+$/.test(value)
          )
          .min(
            field.validationConfig.minValue.value,
            field.validationConfig.minValue.message
          )
          .max(
            field.validationConfig.maxValue.value,
            field.validationConfig.maxValue.message
          )
      )
      .min(
        field.validationConfig.minArrayLength.value,
        field.validationConfig.minArrayLength.message
      )
      .max(
        field.validationConfig.maxArrayLength.value,
        field.validationConfig.maxArrayLength.message
      )
      .test("no-duplicates", field.validationConfig.uniqueError, (value) =>
        Array.isArray(value) ? new Set(value).size === value.length : true
      );
  } else if (
    field.validationType === "array" &&
    field.fieldBased === "operator"
  ) {
    schema = Yup.array().of(
      Yup.string()
        .test(
          "is-positive-integer",
          field.validationConfig?.integerError || "Invalid integer",
          (value) => {
            if (!field.validationConfig?.integerError) return true;
            return /^\d+$/.test(value);
          }
        )
        .max(
          field.validationConfig?.maxValue?.value || Infinity,
          field.validationConfig?.maxValue?.message || "Value exceeds maximum"
        )
        .test(
          "invalid-values",
          field.validationConfig?.invalidValueError?.message ||
            "Value is not accepted",
          (value) => {
            const unacceptableValues =
              field.validationConfig?.invalidValueError?.value || [];
            return !unacceptableValues.includes(value);
          }
        )
        .min(
          field.validationConfig?.minValue?.value || 0,
          field.validationConfig?.minValue?.message || "Value is below minimum"
        )

        .test(
          "no-zeroes",
          field.validationConfig?.onlyZeroesError?.message ||
            "Input cannot consist only of zeroes",
          (value) => {
            if (!field.validationConfig?.onlyZeroesError) return true;
            const regex = new RegExp(
              field.validationConfig.onlyZeroesError.value
            );
            return regex.test(value);
          }
        )
        .matches(
          field.validationConfig?.typeError?.value || /.*/,
          field.validationConfig?.typeError?.message || "Invalid type"
        )
    );
    if (field.validationConfig?.minArrayLength?.value) {
      schema = schema.min(
        field.validationConfig.minArrayLength.value,
        field.validationConfig.minArrayLength.message || "Array is too short"
      );
    }

    if (field.validationConfig?.maxArrayLength?.value) {
      schema = schema.max(
        field.validationConfig.maxArrayLength.value,
        field.validationConfig.maxArrayLength.message || "Array is too long"
      );
    }
    if (field.validationConfig?.uniqueError) {
      schema = schema.test(
        "no-duplicates",
        field.validationConfig?.uniqueError || "Repeated values not allowed",
        (value) =>
          Array.isArray(value) ? new Set(value).size === value.length : true
      );
    }
  } else if (field.name === "port" && field.module === "port") {
    schema = Yup.mixed().test("port-validation", function (value) {
      const requiredValidation = field.validations.find(
        (v) => v.type === "required"
      );
      if (!value || value.length === 0 || value === undefined || value === "") {
        if (this.parent.protocol !== "H") {
          return this.createError({
            message: requiredValidation.message,
          });
        } else {
          return this.createError({
            message: requiredValidation.selectMessage,
          });
        }
      }
      const { options } = field;

      const numericValue = Number(value);
      if (isNaN(numericValue)) {
        return this.createError({
          message: "Please enter a port number with a positive numeric value",
        });
      }

      if (numericValue < 1024) {
        return this.createError({
          message: "Minimum port value is 1024",
        });
      }

      if (numericValue > 65535) {
        return this.createError({
          message: "Maximum port value is 65535",
        });
      }

      if (this.parent.protocol !== "H") {
        const optionValues = options.map((option) => Number(option.value));
        if (optionValues.includes(numericValue)) {
          return this.createError({
            message:
              "Port value is already used for HTTP protocol. Please use a different port",
          });
        }
      }

      return true;
    });
  } else if (
    field.validationType === "array" &&
    field.type === "FieldTableMultiple"
  ) {
    schema = Yup.array().of(
      Yup.object().shape(
        field.fieldsData.reduce((acc, subField) => {
          const baseSchema = Yup[subField.validationType]();

          acc[subField.name] = applyValidations(
            baseSchema,
            subField.validations || []
          );
          return acc;
        }, {})
      )
    );
  } else {
    schema = Yup[field.validationType ? field.validationType : "string"]();
    schema = applyValidations(schema, field.validations || []);
  }
  return schema;
};

export default generateValidationSchema;
