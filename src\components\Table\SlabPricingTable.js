import React, { useState, useEffect } from "react";
import { FieldArray, useFormikContext } from "formik";
import Select from "../FormsUI/Select";
import TextFieldWrapper from "../FormsUI/TextField";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
} from "@mui/material";
import { PlusCircleIcon, DeleteIcon } from "../../icons";
import InputLabel from "../FormsUI/InputLabel";
import { getActionFromUrl } from "../../common/urlUtils";
import theme from "../../tailwind-theme";
import DeleteDialog from "../Dialog/DeleteDialog";

const areAllFieldsFilled = (values, fields) =>
  values.every((row) => fields.every((field) => row[field.name] !== ""));

const RenderTableBody = ({
  name,
  values,
  fields,
  headers,
  isView,
  onDeleteClick,
}) => {
  const { setFieldValue } = useFormikContext();

  useEffect(() => {
    values.forEach((row, index) => {
      if (row.sno !== index + 1) {
        setFieldValue(`${name}.${index}.sno`, index + 1);
      }
    });
  }, [values, name, setFieldValue]);

  return (
    <FieldArray name={name}>
      {({ push, remove }) => {
        const allFieldsFilled = areAllFieldsFilled(values, fields);
        const selectedSuppliers = values
          .map((row) => row.supplier)
          .filter(Boolean);

        return (
          <>
            {values?.map((_, index) => (
              <TableRow key={index}>
                {fields?.map((field, fieldIndex) => (
                  <TableCell key={fieldIndex}>
                    {field.name === "sno" ? (
                      <TextFieldWrapper
                        name={`${name}.${index}.${field.name}`}
                        InputProps={{
                          readOnly: true,
                        }}
                      />
                    ) : field.options ? (
                      <Select
                        name={`${name}.${index}.${field.name}`}
                        options={field.options.filter(
                          (option) =>
                            !selectedSuppliers.includes(option.value) ||
                            option.value === values[index].supplier
                        )}
                        className="md:w-[160px] w-full"
                        isDisabled={isView}
                      />
                    ) : (
                      <TextFieldWrapper
                        name={`${name}.${index}.${field.name}`}
                        isDisabled={isView}
                      />
                    )}
                  </TableCell>
                ))}
                <TableCell>
                  {values.length > 1 && (
                    <IconButton
                      type="button"
                      onClick={() => {
                        onDeleteClick(index, remove);
                        setTimeout(() => {
                          values.forEach((row, idx) => {
                            if (row.sno !== idx + 1) {
                              setFieldValue(`${name}.${idx}.sno`, idx + 1);
                            }
                          });
                        }, 0);
                      }}
                      disabled={isView}
                    >
                      <DeleteIcon />
                    </IconButton>
                  )}
                </TableCell>
              </TableRow>
            ))}

            <TableRow>
              <>
                {" "}
                {Array(fields.length)
                  .fill("")
                  .map((_, idx) => (
                    <TableCell key={idx}>
                      <div
                        style={{
                          border: "2px dotted #BEBEBE",
                          padding: "8px",
                          borderRadius: "10px",
                          height: "36px",
                          width: "100%",
                        }}
                      ></div>
                    </TableCell>
                  ))}
              </>
              <TableCell colSpan={headers.length + 1} align="right">
                <IconButton
                  type="button"
                  onClick={() =>
                    allFieldsFilled &&
                    push(
                      fields.reduce(
                        (acc, field) => ({ ...acc, [field.name]: "" }),
                        {}
                      )
                    )
                  }
                  disabled={!allFieldsFilled || isView}
                >
                  <PlusCircleIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          </>
        );
      }}
    </FieldArray>
  );
};

const RenderTable = ({
  name,
  values,
  headers,
  fields,
  isView,
  onDeleteClick,
}) => (
  <TableContainer component={Paper} sx={{ boxShadow: "none", maxHeight: 400 }}>
    <Table sx={{ minWidth: 550 }} aria-label="sticky table" stickyHeader>
      <TableHead sx={{ bgcolor: theme.backgroundColor.bgTable }}>
        <TableRow>
          {headers?.map((header, index) => (
            <TableCell
              key={index}
              sx={{
                position: "sticky",
                top: 0,
                zIndex: 1,
                backgroundColor: theme.backgroundColor.bgTable,
                fontWeight: "bold",
              }}
            >
              <div className="ml-0.5 text-sm font-semibold">{header}</div>
            </TableCell>
          ))}
          <TableCell
            sx={{
              position: "sticky",
              top: 0,
              zIndex: 1,
              backgroundColor: theme.backgroundColor.bgTable,
              fontWeight: "bold",
            }}
          />
        </TableRow>
      </TableHead>
      <TableBody>
        <RenderTableBody
          name={name}
          values={values}
          fields={fields}
          headers={headers}
          isView={isView}
          onDeleteClick={onDeleteClick}
        />
      </TableBody>
    </Table>
  </TableContainer>
);

const SlabPricingTable = ({ name, values, headers, fields }) => {
  const [pricing, setPricing] = useState(values?.[name] || []);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [rowToDelete, setRowToDelete] = useState(null);

  useEffect(() => {
    setPricing(values?.[name] || []);
  }, [values]);

  const action = getActionFromUrl(window.location.href);
  const onDeleteClick = (index, remove) => {
    setRowToDelete(() => () => remove(index));
    setDeleteDialog(true);
  };

  const handleDeleteConfirm = () => {
    if (rowToDelete) {
      rowToDelete();
      setRowToDelete(null);
      setDeleteDialog(false);
    }
  };

  return (
    <>
      {pricing?.map((policy, policyIndex) => (
        <div
          key={policyIndex}
          className="border border-subNavBorder p-3 bg-bgField mb-5 rounded-[10px] "
        >
          <RenderTable
            name={`${name}[${policyIndex}].pricingDetails`}
            values={values?.[name]?.[policyIndex]?.pricingDetails || []}
            headers={headers}
            fields={policy.fieldsData || fields}
            isView={action === "view" ? true : false}
            onDeleteClick={onDeleteClick}
          />
          <div className="flex justify-end mt-5">
            <div className="flex items-center space-x-4 text-black">
              <InputLabel label={"Cost thereafter"} />
              <TextFieldWrapper
                name={`${name}[${policyIndex}].costThereafter`}
                className="md:w-[350px] w-full"
                isDisabled={action === "view"}
              />
            </div>
          </div>
        </div>
      ))}
      <DeleteDialog
        show={deleteDialog}
        onHide={() => setDeleteDialog(false)}
        onConfirm={handleDeleteConfirm}
        title={
          <>
            Are you sure you want to delete?
            <br />
            (Deletion will only happen on click of save)
          </>
        }
      />
    </>
  );
};

export default SlabPricingTable;
