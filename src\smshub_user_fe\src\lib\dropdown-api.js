import { axiosPrivate } from "../common/axiosPrivate";
import getAPIMap from "../routes/ApiUrls";

/**
 * This method is used to get users
 * @param {*} options
 * @returns
 */
export async function getDropDownValues({ queryKey }) {
  let url = getAPIMap("getReporting");

  if (queryKey[0] !== undefined && queryKey[0] === "customerDetails") {
    url += `/${queryKey[1]}`;
  }
  if (queryKey[0] !== undefined && queryKey[0] === "supplierDetails") {
    url += `/${queryKey[1]}`;
  }

  let response = axiosPrivate.get(url);
  return response;
}

export async function getAll(options) {
  //console.log(options);
  let url = getAPIMap("getDashboard");
  if (
    options?.queryKey[1] &&
    options?.queryKey[1] !== "" &&
    options?.queryKey[1] !== undefined
  ) {
    url += `?page=${options.queryKey[1]}`;
  }
  if (options?.queryKey[2] !== "" && options?.queryKey[2] !== undefined) {
    url += `&limit=${options.queryKey[2]}`;
  }
  if (options?.queryKey[3] !== "" && options?.queryKey[3] !== undefined) {
    url += `&search=${options.queryKey[3]}`;
  }
  if (options.queryKey[4] !== "" && options.queryKey[4] !== undefined) {
    url += `?createdBy=${options.queryKey[4]}`;
  }

  let response = axiosPrivate.get(url);
  return response;
}
