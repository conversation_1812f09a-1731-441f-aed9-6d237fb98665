{"header": "List of series", "columns": [{"header": "Group Id", "accessorKey": "id", "filterVariant": "range"}, {"header": "Group Name", "accessorKey": "attributes.grpName"}, {"header": "Group Numbers", "accessorKey": "attributes.msisdn"}, {"header": "Group Type", "accessorKey": "attributes.grpType"}, {"buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"info": "Type of group", "name": "grpType", "size": 6, "type": "select", "title": "Group type", "isInfo": true, "nonEditable": true, "options": [{"label": "Source IMSI", "value": 52}, {"label": "Source VMSC", "value": 53}, {"label": "Source MSISDN", "value": 32}, {"label": "Source short code", "value": 30}, {"label": "Source CELL ID", "value": 66}, {"label": "Destination IMSI", "value": 26}, {"label": "Destination VMSC", "value": 27}, {"label": "Destination MSISDN", "value": 33}, {"label": "Destination short code", "value": 65}, {"label": "Destination CELL ID", "value": 67}, {"label": "Destination CHARSET", "value": 50}, {"label": "Sender Address", "value": 40}, {"label": "SC Address", "value": 70}, {"label": "SCLP Address", "value": 71}, {"label": "SCDP Address", "value": 72}, {"label": "SRS GT", "value": 133}, {"label": "Alpha numeric", "value": 100}], "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Group type"}], "defaultValue": "", "validationType": "string"}, {"info": "Name of the group", "name": "grpName", "size": 6, "type": "text", "title": "Group name", "isInfo": true, "isMandatory": true, "validations": [{"type": "matches", "regex": "^(?=.*[0-9])(?=.*[A-Za-z])[A-Za-z][A-Za-z0-9_]*$", "message": "Only alphanumeric characters and underscore are allowed.Begin with alphabet"}, {"type": "required", "message": "Please enter the Group name"}, {"type": "min", "value": 3, "message": "Min length is 3"}, {"type": "max", "value": 20, "message": "Max length is 20"}], "defaultValue": "", "validationType": "string"}, {"info": "Numeric or Alphanumeric values for grouping", "name": "numbers", "size": 6, "type": "ChipInput", "title": "Numbers", "isInfo": true, "dynamic": {"field": "grpType", "value": [52, 53, 32, 30, 66, 26, 27, 33, 65, 67, 100, 40, 70, 71, 72, 133], "dynamicTitle": "Alpha numeric", "dynamicValue": 100}, "fieldBased": "grpType", "validation": true, "isMandatory": true, "validations": [{"type": "required", "numbersMessage": "Please enter numbers", "alphaNumericMessage": "Please enter alpha numeric"}, {"type": "numeric-array", "regex": "^\\d+$", "message": "Please enter the numbers field with integer values", "options": [52, 53, 32, 30, 66, 26, 27, 33, 65, 67, 50, 40, 70, 71, 72, 133]}, {"type": "no-duplicates", "message": "Duplicate values not allowed"}, {"type": "min", "value": 2, "message": "Minimum 2 digits required"}, {"type": "minAlphaNumeric", "value": 2, "message": "Minimum 2 digits required"}, {"type": "max", "value": 15, "message": "Maximum 15 digits only allowed"}, {"type": "maxAlphaNumeric", "value": 15, "message": "Maximum 15 digits only allowed"}, {"type": "alpha-numeric-array", "regex": "^(?!^[a-zA-Z]+$)(?!^[0-9]+$)[a-zA-Z0-9_]+$", "message": "Please enter the alpha numberic field with alpha numeric values", "options": [100]}], "defaultValue": "", "validationType": "array"}, {"info": "Special characters to be considered for groupping", "name": "charset<PERSON><PERSON>ber", "size": 6, "type": "select", "title": "CHARSET number", "isInfo": true, "dynamic": {"field": "grpType", "value": 50}, "options": [{"label": "*", "value": "*"}, {"label": "#", "value": "#"}, {"label": "*#", "value": "*#"}], "isMandatory": true, "validations": [{"type": "required", "message": "Please Select Character Set"}], "defaultValue": "", "validationType": "string"}], "formType": "simple", "buttonName": "+ Add series", "moduleData": "series", "moduleName": "Series network", "globalSearch": [{"label": "Group id", "value": "id"}, {"label": "Group name", "value": "grpName"}]}