import React, { useContext, useEffect, useRef, useState } from "react";
import InputLabel from "../../FormsUI/InputLabel";
import { Form, Formik } from "formik";
import { CustomerSupplierContext } from "../../../context/CustomerSupplierContext";
import ConfirmNNextButton from "../../Buttons/Button";
import Button from "../../Buttons/OutlinedButton";
import Dropdown from "../../FormsUI/MultiSelect/MultiSelect";
import { useMutation, useQuery } from "react-query";
import { createRecord, updateRecord } from "../../../lib/list-api";
import {
  getGTDefinitionData,
  getOperatorListData,
} from "../../../lib/customer-supplier-api";
import SuccessDialog from "../../Dialog/SuccessDialog";
import ErrorDialog from "../../Dialog/ErrorDialog";
import { useLocation, useNavigate } from "react-router-dom";
import RadioButtonGroup from "../../RadioButton/RadioButtonGroup";
import Select from "../../FormsUI/Select";
import {
  calledAddrType,
  gtType,
  moduleConfiguration,
  ocCompliance,
  operatorType,
} from "../../../common/constants";
import {
  findCustomerSupplier,
  getActionFromUrl,
  getIdAfterEdit,
  getTrimmedUrl,
} from "../../../common/urlUtils";
import { CssTooltip } from "../../FormsUI/StyledComponent";
import { EditIcon } from "../../../icons";
import { requiredSS7validationSchema } from "../../../payloadDetails/RequiredInfoPayload";
import {
  getIndexedDBDataById,
  initializeIndexedDB,
  updateIndexedDBDataById,
} from "../../HelperFunction/localStorage";
import { multiStepFormContext } from "../../../context/MultiStepFormContext";
import { deleteIndexedDB } from "../../HelperFunction/localStorage";

function RequiredInformationFormSS7({ handleBack, editDetails, isView }) {
  const {
    setRequiredInfo,
    generalInfoData,
    billingInfo,
    paymentInfo,
    requiredInfo,
  } = useContext(CustomerSupplierContext);
  const { setCurrentStep } = useContext(multiStepFormContext);

  const { mutate: createRecordAPI, isLoading: createLoading } =
    useMutation(createRecord);
  const { mutate: updateRecordAPI, isLoading: updateLoading } =
    useMutation(updateRecord);

  const navigate = useNavigate();
  const location = useLocation();
  const url = getTrimmedUrl();
  const [pathIdd, setPathId] = useState("");
  const [gtDetails, setGTDetails] = useState({});
  const [deleteLastEntry, setDeleteLastEntry] = useState(false);
  const [existingData, setExistingData] = useState(null);

  const header = "List of Customer/Supplier";
  const action = getActionFromUrl(window.location.href);
  const searchParams = new URLSearchParams(location.search);
  const currentId = searchParams.get("currentId");
  const editId = getIdAfterEdit(window.location.href);
  const formikRef = useRef(null);
  const moduleName = moduleConfiguration.customerSupplier;

  const fetchData = async (isPrevious) => {
    if (currentId) {
      try {
        const data = await getIndexedDBDataById(
          "navigationDetails",
          "FormDB",
          parseInt(currentId)
        );
        const dataIndex =
          data?.data?.[0]?.moduleNameValue ===
          moduleConfiguration.customerSupplier
            ? 0
            : 1;
        if (data?.data?.[dataIndex]?.pathId) {
          setPathId(data?.data?.[dataIndex]?.pathId);
        }
        if (data?.data?.[dataIndex]?.requiredInfoDetails) {
          setGTDetails(data?.data?.[dataIndex]?.requiredInfoDetails);
        }
        if (isPrevious && data?.data?.length) {
          const lastRecord = data.data.find(
            (ele) => ele.moduleNameValue === moduleName
          );

          if (lastRecord) {
            setDeleteLastEntry(true);
          }
        }
        setExistingData(data);
      } catch (error) {
        console.error("Error fetching data from IndexedDB:", error);
      }
    }
  };

  useEffect(() => {
    fetchData(action === "add" ? true : false);
  }, [currentId]);

  useEffect(() => {
    const dbName = "navigationDetails";
    const storeName = "FormDB";

    initializeIndexedDB(dbName, storeName)
      .then((result) => {
        const currentUrl = new URL(window.location.href);

        if (!currentUrl.searchParams.has("currentId")) {
          currentUrl.searchParams.set(
            "currentId",
            result?.id ? result.id : result?.data?.[0]?.id || 1
          );
          navigate(currentUrl.pathname + currentUrl.search, {
            replace: true,
          });
        }
      })
      .catch((error) => {
        console.error("Error initializing IndexedDB:", error);
      });
  }, []);

  const doBeforeNavigate = async (path, forceDelete = false) => {
    if (deleteLastEntry || forceDelete) {
      const currentData = existingData.data.find(
        (entry) => entry.moduleNameValue === moduleName
      );
      existingData.data = existingData.data.filter(
        (entry) => entry.moduleNameValue !== moduleName
      );
      await updateIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        Number(currentId),
        existingData
      );
      if (currentData?.previousPath) {
        navigate(currentData.previousPath);
        return;
      }
    }
    navigate(path);
  };

  const handleButtonClick = async () => {
    if (existingData && existingData.data && existingData.data.length > 0) {
      const lastRecord = existingData.data[existingData.data.length - 1];
      if (lastRecord.moduleNameValue === moduleConfiguration.customerSupplier) {
        setCurrentStep(3);
      } else {
        setCurrentStep(0);
      }
      if (
        !lastRecord?.previousPath &&
        lastRecord?.currentPath === `${location.pathname}${location.search}`
      ) {
        try {
          doBeforeNavigate(`/app/list/${moduleName}`);
          await deleteIndexedDB("navigationDetails");
        } catch (error) {
          console.error("Error deleting database:", error);
        }
      } else if (lastRecord?.currentPath) {
        const fullUrl = window.location.href;
        const appUrl = fullUrl.substring(fullUrl.indexOf("/app"));

        if (lastRecord.currentPath === appUrl) {
          doBeforeNavigate(lastRecord.previousPath, true);
        } else {
          doBeforeNavigate(lastRecord.currentPath);
        }
      }
    } else {
      doBeforeNavigate(`/app/list/${moduleName}`);
    }
  };

  const [dialogState, setDialogState] = useState({
    successDialog: false,
    errorDialog: false,
    message: "",
  });
  const [gtValues, setGtValues] = useState({
    accountDetails: [],
    a2pGTValue: [],
    p2pGTValue: [],
    dummyGtValue: [],
    specialPrefix: [],
  });

  const defaultOperatorAccount = editDetails?.operator_clusters?.data?.map(
    (list) => ({
      id: list?.id,
      value: list?.clusterName,
      label: list?.clusterName,
    })
  );

  const { refetch } = useQuery(
    [
      "operator-cluster",
      moduleConfiguration.operatorCluster,
      { limit: -1 },
      "customerRelation",
      formikRef?.current?.values?.calledAddrType,
      "clusterName",
      formikRef?.current?.values?.calledGtPrefix,
      editId,
    ],
    getOperatorListData,
    {
      onSuccess: ({ data }) => {
        const optionsData = data?.data?.map((list) => ({
          id: list?.id,
          value: list?.attributes?.clusterName,
          label: list?.attributes?.clusterName,
        }));
        setGtValues((prev) => ({
          ...prev,
          accountDetails: optionsData,
        }));
      },
      enabled: !!formikRef?.current?.values?.calledAddrType,
    }
  );

  useQuery(
    ["service-management", moduleConfiguration.serviceManagement],
    getGTDefinitionData,
    {
      onSuccess: ({ data }) => {
        const mapToDropdownOptions = (list) =>
          list?.map((item) => ({ id: item, value: item, label: item }));

        setGtValues((prev) => ({
          ...prev,
          a2pGTValue: mapToDropdownOptions(data?.data?.a2p_gt),
          p2pGTValue: mapToDropdownOptions(data?.data?.p2p_gt),
          dummyGtValue: mapToDropdownOptions(data?.data?.dummy_gt),
          specialPrefix: mapToDropdownOptions(data?.data?.special_prefix),
        }));
      },
    }
  );

  const handleAddButtonClick = async (navigation) => {
    const nextPath = navigation;
    const currentPath = url;
    const formData = {
      generalInfoData,
      billingInfo,
      paymentInfo,
      requiredInfo,
    };
    const id = parseInt(currentId, 10);
    const moduleNameValue = moduleConfiguration.customerSupplier;
    const pathId =
      editPathId?.[0] || pathIdd.id
        ? {
            id: editPathId[0]?.id || pathIdd.id,
            name: editPathId[0]?.name || pathIdd.name,
          }
        : {};
    const previousModuleName = `${
      action === "add" ? "Add" : "Edit"
    } customer/supplier`;
    const listHeader = header;
    const requiredInfoDetails = JSON.parse(
      JSON.stringify(formikRef?.current?.values)
    );

    setCurrentStep(0);
    try {
      const existingData = await getIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        id
      );
      const updatedData = existingData || { data: [] };

      const existingIndex = updatedData.data.findIndex(
        (entry) => entry.moduleNameValue === moduleNameValue
      );

      if (existingIndex !== -1) {
        updatedData.data.splice(existingIndex, 1);
      }

      const newEntry = {
        currentPath,
        formData,
        previousPath:
          updatedData.data[updatedData.data.length - 1]?.currentPath || null,
        requiredInfoDetails,
        moduleNameValue,
        previousModuleName,
        pathId,
        actionData: action,
        customerId: editId,
      };

      if (updatedData.data.length === 0) {
        newEntry.listHeader = listHeader;
      }

      updatedData.data.push(newEntry);

      await updateIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        id,
        updatedData
      );

      navigate(`${nextPath}?currentId=${id}`);
    } catch (error) {
      console.error("Error updating IndexedDB:", error);
    }
  };

  const handleSubmit = (values) => {
    const {
      gtType,
      calledAddrType,
      operator_clusters,
      calledGtPrefix,
      gtValue,
      paths,
    } = values;

    let reqData = {
      data: {
        ...generalInfoData,
        billingInformation: billingInfo,
        ...paymentInfo,
      },
    };

    const operatorClusterIds =
      Array.isArray(operator_clusters) &&
      operator_clusters.every((cluster) => typeof cluster === "number")
        ? operator_clusters
        : operator_clusters?.map((cluster) => cluster.id) || [];

    if (generalInfoData?.operatorType === operatorType.Both) {
      reqData.data = {
        ...reqData.data,
        gtType,
        calledAddrType,
        gtValue,
        ...(generalInfoData?.hubFlag === ocCompliance.hubFlagNo && {
          operator_clusters: operatorClusterIds,
        }),
        ...(calledAddrType === 3 && { calledGtPrefix }),
      };
    } else if (generalInfoData?.operatorType === operatorType.Customer) {
      reqData.data = {
        ...reqData.data,
        gtType,
        calledAddrType,
        gtValue,
        ...(generalInfoData?.hubFlag === ocCompliance.hubFlagNo && {
          operator_clusters: operatorClusterIds,
        }),
        ...(calledAddrType === 3 && { calledGtPrefix }),
      };
    } else if (generalInfoData?.operatorType === operatorType.Supplier) {
      // reqData.data.paths = paths;
    }

    const onSuccess = () => {
      setDialogState({
        successDialog: true,
        errorDialog: false,
        message: `${findCustomerSupplier(generalInfoData?.operatorType)}  ${
          action !== "add" ? "updated" : "created"
        } successfully`,
      });
    };

    const onError = ({ response }) => {
      setDialogState({
        successDialog: false,
        errorDialog: true,
        message: response?.data?.error?.message,
      });
    };

    if (action !== "add") {
      updateRecordAPI(
        {
          moduleName: moduleConfiguration.customerSupplier,
          id: editId,
          reqData,
        },
        { onSuccess, onError }
      );
    } else {
      createRecordAPI(
        { moduleName: moduleConfiguration.customerSupplier, reqData },
        { onSuccess, onError }
      );
    }
  };

  const editPathId =
    editDetails?.paths?.data?.map((list) => ({
      id: list?.id,
      name: list?.pathName,
    })) || [];

  const initialPathId =
    pathIdd?.id || (editPathId.length > 0 ? editPathId[0]?.id : "");

  return (
    <>
      <Formik
        innerRef={formikRef}
        initialValues={{
          gtType:
            gtDetails?.gtType ||
            editDetails?.gtType ||
            gtType.find((item) => item.label === "Dummy GT")?.value,
          calledAddrType:
            gtDetails?.calledAddrType ||
            editDetails?.calledAddrType ||
            calledAddrType.find((item) => item.label === "A2P GT")?.value,
          operator_clusters:
            gtDetails?.operator_clusters || defaultOperatorAccount || [],
          gtValue: gtDetails?.gtValue || editDetails?.gtValue,
          calledGtPrefix:
            gtDetails?.calledGtPrefix || editDetails?.calledGtPrefix || "",
          paths: initialPathId,
        }}
        validationSchema={requiredSS7validationSchema(
          generalInfoData,
          formikRef
        )}
        validateOnMount={true}
        enableReinitialize={true}
        onSubmit={handleSubmit}
      >
        {({ values, setFieldValue, errors, touched, setFieldError }) => (
          <Form className="flex flex-col mx-10">
            <div
              className={`mt-10 ${
                generalInfoData?.operatorType === operatorType.Both
                  ? "grid grid-cols-1 gap-5 items-start w-full"
                  : ""
              }`}
            >
              {[operatorType.Customer, operatorType.Both].includes(
                generalInfoData?.operatorType
              ) ? (
                <div className="grid grid-cols-2 gap-5">
                  <div className="col-span-1">
                    <InputLabel
                      label="Calling party address"
                      isMandatory={true}
                    />
                    <RadioButtonGroup
                      name="gtType"
                      value={values.gtType}
                      onChange={(e) => {
                        setFieldValue("gtType", parseInt(e.target.value));
                        setFieldValue("gtValue", "");
                      }}
                      options={gtType}
                      isDisabled={action === "view"}
                    />
                  </div>

                  {values?.gtType ===
                    gtType.find((item) => item.label === "Dummy GT")?.value && (
                    <div className="col-span-1 mt-5">
                      <InputLabel label="Dummy GT" isMandatory={true} />

                      <Select
                        name="gtValue"
                        options={gtValues.dummyGtValue}
                        isDisabled={action === "view"}
                      />
                    </div>
                  )}

                  {values?.gtType ===
                    gtType.find((item) => item.label === "A2P GT")?.value && (
                    <div className="col-span-1 mt-5">
                      <InputLabel label="A2P GT values" isMandatory={true} />
                      <Select
                        name="gtValue"
                        options={gtValues.a2pGTValue}
                        isDisabled={action === "view"}
                      />
                    </div>
                  )}

                  {values?.gtType ===
                    gtType.find((item) => item.label === "P2P GT")?.value && (
                    <div className="col-span-1 mt-5">
                      <InputLabel label="P2P GT values" isMandatory={true} />
                      <Select
                        name="gtValue"
                        options={gtValues.p2pGTValue}
                        isDisabled={action === "view"}
                      />
                    </div>
                  )}

                  <div className="col-span-1 mt-5">
                    <InputLabel
                      label="Called party address"
                      isMandatory={true}
                    />
                    <Select
                      name="calledAddrType"
                      options={calledAddrType}
                      isDisabled={action === "view"}
                      onChange={() => {
                        refetch();
                      }}
                    />
                  </div>
                  {generalInfoData?.hubFlag === ocCompliance.hubFlagNo ? (
                    <div className="col-span-1 mt-5">
                      <div className="flex">
                        <InputLabel
                          label="Operator cluster"
                          isMandatory={true}
                          isAddButton={true}
                          onClick={() => {
                            handleAddButtonClick(
                              `/app/list/${moduleConfiguration.operatorCluster}/add`
                            );
                          }}
                          action={action}
                        />
                      </div>

                      <Dropdown
                        btnWidth="w-1/2"
                        data={gtValues.accountDetails}
                        btnName="Select Operator cluster"
                        onSelectionChange={(selectedDetail) => {
                          const updatedSelectedDetail = selectedDetail.map(
                            (item) => {
                              return {
                                ...item,
                                label: item.label || item.value,
                              };
                            }
                          );
                          const selectedIds = updatedSelectedDetail.map(
                            (item) => item.id
                          );
                          setFieldValue("operator_clusters", selectedIds);
                          setRequiredInfo(selectedDetail);
                        }}
                        defaultSelectedData={
                          gtDetails?.operator_clusters
                            ? gtDetails?.operator_clusters
                            : defaultOperatorAccount
                        }
                        apiOptions={false}
                        disabled={action === "view"}
                        moduleName={moduleConfiguration.customerSupplier}
                        setCurrentStep={setCurrentStep}
                        collectionName={moduleConfiguration.operatorCluster}
                        values={
                          (generalInfoData,
                          billingInfo,
                          paymentInfo,
                          requiredInfo)
                        }
                        header={header}
                        action={action}
                        requiredInfoDetails={
                          formikRef?.current?.values
                            ? JSON.parse(
                                JSON.stringify(formikRef.current.values)
                              )
                            : {}
                        }
                        pathDetail={
                          editPathId?.[0] || pathIdd.id
                            ? {
                                id: editPathId[0]?.id || pathIdd.id,
                                name: editPathId[0]?.name || pathIdd.name,
                              }
                            : {}
                        }
                      />

                      {errors.operator_clusters &&
                        touched.operator_clusters && (
                          <div className="text-errorColor text-xs">
                            {errors.operator_clusters}
                          </div>
                        )}
                    </div>
                  ) : null}

                  {values?.calledAddrType ===
                    calledAddrType.find(
                      (item) => item.label === "Special prefix"
                    )?.value && (
                    <div className="col-span-1 mt-5">
                      <InputLabel label="Special prefix" isMandatory={true} />
                      <Select
                        name="calledGtPrefix"
                        options={gtValues.specialPrefix}
                        isDisabled={action === "view"}
                        onChange={() => {
                          refetch();
                        }}
                      />
                    </div>
                  )}
                </div>
              ) : null}
              {[operatorType.Supplier, operatorType.Both].includes(
                generalInfoData?.operatorType
              ) ? (
                <div
                  className={`mt-5 ${
                    generalInfoData?.operatorType === operatorType.Supplier
                      ? "flex justify-center w-full"
                      : "w-1/2"
                  }`}
                >
                  {pathIdd?.id ||
                  (editPathId.length > 0 ? editPathId[0]?.id : "") ? (
                    <div className="flex gap-5">
                      <Button
                        label={
                          pathIdd?.name ||
                          (editPathId.length > 0 ? editPathId[0]?.name : "") ||
                          ""
                        }
                        buttonClassName="w-full text-sm rounded-[10px] bg-[#D9D9D980]"
                      />
                      {action !== "view" ? (
                        <>
                          <CssTooltip title={"Edit Path"} placement="top" arrow>
                            <EditIcon
                              className="w-4 h-4 cursor-pointer mt-3"
                              onClick={() => {
                                handleAddButtonClick(
                                  `/app/list/${
                                    moduleConfiguration.paths
                                  }/edit/${
                                    pathIdd?.id
                                      ? pathIdd.id
                                      : editPathId.length > 0
                                      ? editPathId[0]?.id
                                      : ""
                                  }`
                                );
                              }}
                            />
                          </CssTooltip>
                        </>
                      ) : null}
                    </div>
                  ) : (
                    <CssTooltip
                      title={"Click to Add Path"}
                      placement="top"
                      arrow
                    >
                      <div
                        className={` ${
                          generalInfoData.operatorType === operatorType.Both
                            ? "w-full mt-5"
                            : "ml-44 w-1/2"
                        }`}
                      >
                        <Button
                          label={"Add Path"}
                          buttonClassName={`${
                            generalInfoData.operatorType === operatorType.Both
                              ? "w-full mr-10"
                              : "w-1/2"
                          } text-sm rounded-[10px]`}
                          onClick={() => {
                            const { paths, ...otherErrors } = errors;

                            if (
                              generalInfoData.operatorType ===
                                operatorType.Both &&
                              Object.keys(otherErrors).length > 0
                            ) {
                              return;
                            } else {
                              handleAddButtonClick(
                                `/app/list/${moduleConfiguration.paths}/add`
                              );
                            }
                          }}
                        />
                        {errors.paths && touched.paths && (
                          <div className="text-errorColor text-xs">
                            {errors.paths}
                          </div>
                        )}
                      </div>
                    </CssTooltip>
                  )}
                </div>
              ) : null}
            </div>

            <div className="flex justify-center gap-5 mt-20">
              <Button
                label={"Back"}
                buttonClassName="w-full md:w-[150px] h-[40px] text-sm"
                onClick={handleBack}
              />
              {action !== "view" ? (
                <ConfirmNNextButton
                  label={"Save"}
                  buttonClassName="w-full md:w-[150px] h-[40px] text-sm"
                  type="submit"
                  loading={createLoading || updateLoading}
                />
              ) : (
                <ConfirmNNextButton
                  label={"Close"}
                  buttonClassName="w-full md:w-[150px] h-[40px] text-sm"
                  onClick={() => {
                    navigate(
                      `/app/list/${moduleConfiguration.customerSupplier}`
                    );
                  }}
                />
              )}
            </div>
          </Form>
        )}
      </Formik>
      <SuccessDialog
        show={dialogState.successDialog}
        onHide={() => {
          handleButtonClick();
          setDialogState((prev) => ({ ...prev, successDialog: false }));
        }}
        message={dialogState.message}
      />
      <ErrorDialog
        show={dialogState.errorDialog}
        onHide={() =>
          setDialogState((prev) => ({ ...prev, errorDialog: false }))
        }
        message={dialogState.message}
      />
    </>
  );
}

export default RequiredInformationFormSS7;
