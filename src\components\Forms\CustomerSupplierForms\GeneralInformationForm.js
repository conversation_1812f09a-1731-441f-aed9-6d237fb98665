import React, { useContext, useEffect, useState } from "react";
import InputLabel from "../../FormsUI/InputLabel";
import TextFieldWrapper from "../../FormsUI/TextField";
import Select from "../../FormsUI/Select";
import { Form, Formik } from "formik";
import RadioButtonGroup from "../../RadioButton/RadioButtonGroup";
import CustomSwitch from "../../ToggleSwitch/CustomSwitchHttp";
import ShortCodeInput from "../../Chip";
import Grid from "@mui/material/Grid";
import ConfirmNNextButton from "../../Buttons/Button";
import Button from "../../Buttons/OutlinedButton";
import { CustomerSupplierContext } from "../../../context/CustomerSupplierContext";
import customerSupplier from "../../../collections/customer-supplier-managements.json";
import {
  getCommonPayload,
  getCustomerPayload,
  getSupplierPayload,
  getFraudManagementPayload,
  generalInfoInitialValue,
} from "../../../payloadDetails/GeneralInfoPayload";
import {
  interfaceType,
  moduleConfiguration,
  ocCompliance,
  operatorType,
  protocolType,
  subInterface,
  subInterfaceEsme,
} from "../../../common/constants";
import { useNavigate } from "react-router-dom";
import { customerSupplierValidationSchema } from "../../../payloadDetails/GeneralInfoPayload";
import { getActionFromUrl } from "../../../common/urlUtils";
import { useQuery } from "react-query";
import { getGTDefinitionData } from "../../../lib/customer-supplier-api";

function GeneralInformationForm({
  handleNext,
  editDetails,
  isView,
  handleButtonClick,
}) {
  const navigate = useNavigate();
  const {
    generalInfoData,
    setGeneralInfo,
    setBillingInfo,
    setPaymentInfo,
    setRequiredInfo,
  } = useContext(CustomerSupplierContext);
  const [formValues, setFormValues] = useState(generalInfoInitialValue || {});
  const actionData = getActionFromUrl(window.location.href);

  useEffect(() => {
    if (generalInfoData) {
      setFormValues({
        ...formValues,
        ...generalInfoData,
      });
    }
  }, [generalInfoData]);

  useEffect(() => {
    if (editDetails && generalInfoData.length === 0) {
      setFormValues({
        ...formValues,
        ...editDetails,
      });
    }
  }, [editDetails]);

  const dropdownValues = customerSupplier?.elements || [];

  const getDropdownOptions = (key) => {
    const fieldConfig = dropdownValues.find((item) => item.name === key);
    if (!fieldConfig || !fieldConfig.options) {
      return [];
    }
    return fieldConfig.options;
  };

  const getOptions = (field, values) => {
    const fieldConfig = dropdownValues.find((item) => item.name === field);
    const conditions = fieldConfig.options.conditions || [];
    const matchedCondition = conditions.find(({ condition }) => {
      return Object.keys(condition).every((key) =>
        condition[key].includes(values[key])
      );
    });
    return matchedCondition
      ? matchedCondition.options
      : fieldConfig.options.default || [];
  };

  const getLabelByName = (name) => {
    const fieldConfig = dropdownValues.find((item) => item.name === name);
    return fieldConfig?.label || "";
  };

  const handleSubmit = (values) => {
    let payload = getCommonPayload(values, actionData);

    if (values.interfaceType === interfaceType.ESME) {
      if (
        [operatorType.Customer, operatorType.Both].includes(values.operatorType)
      ) {
        payload = {
          ...payload,
          ...getCustomerPayload(values),
        };
      }
      if (
        [operatorType.Supplier, operatorType.Both].includes(values.operatorType)
      ) {
        payload = {
          ...payload,
          ...getSupplierPayload(values),
        };
      }
      if (values.operatorType === operatorType.Both) {
        payload.twowaySms = values.twowaySms;
      }
    } else if (values.interfaceType === interfaceType["SMPP ES"]) {
      if (
        [operatorType.Supplier, operatorType.Both].includes(values.operatorType)
      ) {
        payload = {
          ...payload,
          ...getSupplierPayload(values),
        };
      }
      if (values.operatorType === operatorType.Both) {
        payload = {
          ...payload,
          ...getCustomerPayload(values),
          twowaySms: values.twowaySms,
        };
      }
    } else if (values.interfaceType === interfaceType.SS7) {
      if (
        [operatorType.Supplier, operatorType.Both].includes(values.operatorType)
      ) {
        payload = {
          ...payload,
          ...getSupplierPayload(values),
          hlrSupplierFlag: values.hlrSupplierFlag,
        };
      }
      if (
        [operatorType.Customer, operatorType.Both].includes(values.operatorType)
      ) {
        payload = {
          ...payload,
          ...getCustomerPayload(values),
          avoidCustRevalidation: values.avoidCustRevalidation,
          ...(values.avoidCustRevalidation === 0 &&
            getFraudManagementPayload(values)),
        };
      }
      if (values.operatorType === operatorType.Both) {
        payload.twowaySms = values.twowaySms;
      }
    }

    setGeneralInfo(payload);

    handleNext();
  };

  const { data: serviceParameterData } = useQuery(
    ["service-management", moduleConfiguration.serviceManagement],
    getGTDefinitionData,
    {
      onSuccess: ({ data }) => {},
    }
  );
  return (
    <Formik
      initialValues={formValues}
      validationSchema={customerSupplierValidationSchema(serviceParameterData)}
      validateOnMount={true}
      enableReinitialize={true}
      onSubmit={(values) => {
        handleSubmit(values);
      }}
    >
      {({ values, setFieldValue, errors }) => (
        <Form>
          <div className="mx-4">
            <Grid container spacing={4} sx={{ p: 2 }}>
              {/* Name */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Customer/Supplier Name"}
                  isMandatory={true}
                  isInfo={true}
                  info={"Parameter to define the type of entity."}
                />
                <TextFieldWrapper
                  name="name"
                  placeholder={"Enter the name"}
                  isDisabled={isView}
                />
              </Grid>
              {/* Relation Type */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={getLabelByName("operatorType")}
                  isMandatory={true}
                  isInfo={true}
                  info={
                    "Customer: Messages from customer will be delivered by SMS Hub. Supplier: Hub delivers messages via supplier."
                  }
                />
                <RadioButtonGroup
                  name="operatorType"
                  value={values.operatorType}
                  onChange={(e) => {
                    if (
                      values.protocolType === protocolType["A2P / P2P"] &&
                      (values.interfaceType === interfaceType.SS7 ||
                        e.target.value === operatorType.Supplier)
                    ) {
                      setFieldValue("protocolType", protocolType.A2P);
                    }
                    if (
                      e.target.value === operatorType.Customer &&
                      values.interfaceType === interfaceType["SMPP ES"]
                    ) {
                      setFieldValue("interfaceType", interfaceType.ESME);
                    }
                    setFieldValue("operatorType", e.target.value);
                  }}
                  options={getDropdownOptions("operatorType")}
                  isDisabled={isView}
                />
              </Grid>
              {/* Interface Type */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={getLabelByName("interfaceType")}
                  isMandatory={true}
                  isInfo={true}
                  info={"Type of interface for SMS transmission"}
                />
                <RadioButtonGroup
                  name="interfaceType"
                  value={values.interfaceType}
                  onChange={(e) => {
                    if (
                      values.protocolType === protocolType["A2P / P2P"] &&
                      (values.operatorType === operatorType.Supplier ||
                        e.target.value === interfaceType.SS7)
                    ) {
                      setFieldValue("protocolType", protocolType.A2P);
                    }
                    setFieldValue("interfaceType", e.target.value);
                  }}
                  options={getOptions("interfaceType", values)}
                  isDisabled={isView}
                />
              </Grid>
              {/* Sub Interface Type */}
              <Grid item xs={12} md={6}>
                {values.interfaceType === interfaceType.ESME ? (
                  <>
                    <InputLabel
                      label={getLabelByName("subInterfaceEsme")}
                      isMandatory={true}
                      isInfo={true}
                      info={"Interface SubType"}
                    />
                    <RadioButtonGroup
                      name="subInterfaceEsme"
                      value={values.subInterfaceEsme}
                      onChange={(e) => {
                        setFieldValue("subInterfaceEsme", e.target.value);
                        setRequiredInfo([]);
                      }}
                      options={getDropdownOptions("subInterfaceEsme")}
                      isDisabled={isView}
                    />
                  </>
                ) : values.interfaceType === interfaceType.SS7 ? (
                  <>
                    <InputLabel
                      label={getLabelByName("subInterface")}
                      isMandatory={true}
                      isInfo={true}
                      info={"Interface SubType"}
                    />
                    <RadioButtonGroup
                      name="subInterface"
                      value={values.subInterface}
                      onChange={(e) =>
                        setFieldValue("subInterface", e.target.value)
                      }
                      options={getDropdownOptions("subInterface")}
                      isDisabled={isView}
                    />
                  </>
                ) : null}
              </Grid>
              {/* Protocol Type */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={getLabelByName("protocolType")}
                  isMandatory={true}
                  isInfo={true}
                  info={"Type of protocol"}
                />
                <RadioButtonGroup
                  name="protocolType"
                  value={values.protocolType}
                  onChange={(e) => {
                    if (
                      values.protocolType === protocolType["A2P / P2P"] &&
                      (e.target.value === operatorType.Supplier ||
                        values.interfaceType === interfaceType.SS7)
                    ) {
                      setFieldValue("protocolType", protocolType.A2P);
                    } else {
                      setFieldValue("protocolType", e.target.value);
                    }
                  }}
                  options={getOptions("protocolType", values)}
                  isDisabled={isView}
                />
              </Grid>
              {values.operatorType === operatorType.Supplier &&
              values.interfaceType === interfaceType.SS7 ? (
                <Grid item xs={12} md={6} />
              ) : null}
              {values?.protocolType === protocolType.P2P &&
              values.interfaceType === interfaceType.SS7 &&
              values.operatorType === operatorType.Customer ? (
                <Grid item xs={12} md={6}>
                  <InputLabel
                    label={"Reverse SRI flag"}
                    info={
                      "Flag to enable  to validate the Originating MSISDN.In events of error received as SRI response, the MT_FSM will be rejected.Reverse SRI will be done only for SS7 P2P customer.The operator to which the customer belongs. This needs to be configured using Operator Profile and Operator Cluster options as explained earlier."
                    }
                    isInfo={true}
                  />
                  <CustomSwitch
                    name="reverseSriFlag"
                    checked={values.reverseSriFlag}
                    isDisabled={isView}
                  />
                </Grid>
              ) : null}
              {[operatorType.Customer, operatorType.Both].includes(
                values.operatorType
              ) && (
                <>
                  {values.operatorType === operatorType.Both &&
                  Number(values?.subInterfaceEsme) === subInterfaceEsme.HTTP ? (
                    <Grid item xs={12} md={6}>
                      <InputLabel label={"Two way SMS"} />
                      <CustomSwitch
                        name="twowaySms"
                        checked={values.twowaySms}
                        isDisabled={isView}
                      />
                    </Grid>
                  ) : values?.protocolType === protocolType.P2P &&
                    values.interfaceType === interfaceType.SS7 &&
                    values.operatorType === operatorType.Customer ? null : (
                    <Grid item xs={12} md={6}></Grid>
                  )}
                  <Grid item xs={12} md={6}>
                    <InputLabel
                      label={getLabelByName("customerType")}
                      isMandatory={true}
                      isInfo={true}
                      info={"Type of customer on boarded on the platform"}
                    />
                    <Select
                      name="customerType"
                      options={getDropdownOptions("customerType")}
                      isDisabled={isView}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <InputLabel
                      label={getLabelByName("customerStatus")}
                      isMandatory={true}
                      isInfo={true}
                      info={"Status of the on boarded customer"}
                    />
                    <Select
                      name="customerStatus"
                      options={getDropdownOptions("customerStatus")}
                      isDisabled={isView}
                    />
                  </Grid>
                </>
              )}
              {/* Supplier Type and Status */}
              {[operatorType.Supplier, operatorType.Both].includes(
                values.operatorType
              ) && (
                <>
                  {!(
                    values.operatorType === operatorType.Both &&
                    values.interfaceType === interfaceType["SMPP ES"]
                  ) && values.operatorType !== operatorType.Supplier ? (
                    <Grid item xs={12} md={6}></Grid>
                  ) : null}
                  {!(
                    values.operatorType === operatorType.Both &&
                    values.interfaceType === interfaceType["SMPP ES"]
                  ) &&
                  !(
                    values.interfaceType === interfaceType.SS7 &&
                    values.operatorType === operatorType.Supplier
                  ) ? (
                    <Grid item xs={12} md={6}></Grid>
                  ) : null}
                  <Grid item xs={12} md={6}>
                    <InputLabel
                      label={getLabelByName("supplierType")}
                      isMandatory={true}
                      isInfo={true}
                      info={"Type of supplier on boarded on the platform"}
                    />
                    <Select
                      name="supplierType"
                      options={getDropdownOptions("supplierType")}
                      isDisabled={isView}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <InputLabel
                      label={getLabelByName("supplierStatus")}
                      isMandatory={true}
                      isInfo={true}
                      info={"Status of the on boarded supplier"}
                    />
                    <Select
                      name="supplierStatus"
                      options={getDropdownOptions("supplierStatus")}
                      isDisabled={isView}
                    />
                  </Grid>
                </>
              )}
              {/* Deliver Timestamp */} {/* MNP Status */} {/* Short Code */}
              {(values.operatorType === operatorType.Customer ||
                values.operatorType === operatorType.Both) &&
                (values.interfaceType === interfaceType.ESME ||
                  values.interfaceType === interfaceType["SMPP ES"] ||
                  values.interfaceType === interfaceType.SS7) &&
                (values.subInterfaceEsme === subInterfaceEsme.SMPP ||
                  values.subInterfaceEsme === subInterfaceEsme.HTTP ||
                  values.subInterface === subInterface.SIGTRAN ||
                  values.subInterface === subInterface.SS7) && (
                  <>
                    <Grid item xs={12} md={4}>
                      <InputLabel
                        label={"Deliver timestamp upto seconds"}
                        labelClassName="whitespace-nowrap"
                        isInfo={true}
                        info={
                          "Flag to enable or disable the delivery time stamp to have seconds field"
                        }
                      />
                      <CustomSwitch
                        name="deliverTimestamp"
                        checked={values.deliverTimestamp}
                        generalInfo={true}
                        isDisabled={isView}
                      />
                    </Grid>
                    <Grid item xs={12} md={2}>
                      <InputLabel
                        label={"MNP status"}
                        labelClassName="whitespace-nowrap "
                        isInfo={true}
                        info={
                          "Flag to enable or disable the MNP feature for customer"
                        }
                      />
                      <CustomSwitch
                        name="mnp"
                        checked={values.mnp}
                        generalInfo={true}
                        isDisabled={isView}
                      />
                    </Grid>
                    {values.protocolType === protocolType.P2P ? (
                      <Grid item xs={12} md={6} />
                    ) : (
                      <Grid item xs={12} md={6}>
                        <InputLabel
                          label={"Short code (Press enter to add short code)"}
                          isInfo={true}
                          info={"Short code to be associated with the customer"}
                        />
                        <ShortCodeInput
                          name="shortCodes"
                          value={values.shortCodes}
                          onChange={(chips) => {
                            setFieldValue("shortCodes", chips);
                          }}
                          isDisabled={isView}
                        />
                      </Grid>
                    )}
                    {/* Configured Interval */}
                    <Grid item xs={12} md={6}>
                      <InputLabel
                        label={getLabelByName("allowSpamTimeInterval")}
                        isInfo={true}
                        info={
                          "Field to configure the maximum number of allowed fraud messages.If the count of fraud messages crosses the configured threshold the alert will be generated"
                        }
                      />
                      <RadioButtonGroup
                        name="allowSpamTimeInterval"
                        value={values.allowSpamTimeInterval}
                        onChange={(e) => {
                          setFieldValue(
                            "allowSpamTimeInterval",
                            e.target.value
                          );
                        }}
                        isDisabled={isView}
                        options={getDropdownOptions("allowSpamTimeInterval")}
                      />
                    </Grid>
                    {/* DND Status */}
                    <Grid item xs={12} md={6}>
                      <InputLabel
                        label={"DND status"}
                        isInfo={true}
                        info={"Flag to enable or disable the DnD check feature"}
                      />
                      <CustomSwitch
                        name="dnd"
                        checked={values.dnd}
                        isDisabled={isView}
                        generalInfo={true}
                      />
                    </Grid>
                    {/* Message Count */}
                    <Grid item xs={12} md={6}>
                      <InputLabel
                        label={"Alert on spam message count exceeds"}
                        isInfo={true}
                        info={
                          "The Spam message count to be considered to raise the alert"
                        }
                      />
                      <TextFieldWrapper
                        name="allowSpamCount"
                        isDisabled={isView}
                      />
                    </Grid>
                    {/* Variation in Traffic */}
                    <Grid item xs={12} md={6}>
                      <InputLabel
                        label={"Alert on percentage variation in traffic"}
                        isInfo={true}
                        info={
                          "The percentage value to be considered to raise the alert when the calculated variation in traffic is equal to the configured value"
                        }
                      />
                      <TextFieldWrapper
                        name="trafficVariation"
                        isDisabled={isView}
                      />
                    </Grid>
                    {/* Deliver Room Flag */}
                    <Grid item xs={12} md={6}>
                      <InputLabel
                        label={"Deliver roam flag"}
                        isInfo={true}
                        info={
                          "If enabled for the customer then the message from that customer will be delivered to the destination which is currently in roaming state"
                        }
                      />
                      <CustomSwitch
                        name="roamDeliveryFlag"
                        checked={values.roamDeliveryFlag}
                        generalInfo={true}
                        isDisabled={isView}
                      />
                    </Grid>
                  </>
                )}
              {/* Time */}
              <Grid item xs={12} md={2}>
                <InputLabel
                  label={getLabelByName("time")}
                  isInfo={true}
                  info={
                    "Time zone in which the customer or supplier is located"
                  }
                />
                <Select
                  name="timezone"
                  options={getDropdownOptions("time")}
                  isDisabled={isView}
                />
              </Grid>
              {/* Hour */}
              <Grid item xs={12} md={2}>
                <InputLabel label={getLabelByName("hour")} />
                <Select
                  name="hour"
                  options={getDropdownOptions("hour")}
                  isDisabled={isView}
                />
              </Grid>
              {/* Minutes */}
              <Grid item xs={12} md={2}>
                <InputLabel label={getLabelByName("min")} />
                <Select
                  name="min"
                  options={getDropdownOptions("min")}
                  isDisabled={isView}
                />
              </Grid>
              {values?.operatorType === operatorType.Supplier &&
              values?.interfaceType === interfaceType.SS7 ? (
                <Grid item xs={12} md={6}>
                  <InputLabel label={"HLR supplier status"} />
                  <CustomSwitch
                    name="hlrSupplierFlag"
                    checked={values.hlrSupplierFlag}
                    isInfo={true}
                    info={
                      "Flag to Enable/Disable the Supplier for HLR DIP. Enable to use supplier for HLR dip"
                    }
                    isDisabled={isView}
                  />
                </Grid>
              ) : (values?.interfaceType === interfaceType["SMPP ES"] ||
                  values.interfaceType === interfaceType.ESME) &&
                values.operatorType === operatorType.Supplier ? (
                <Grid item xs={12} md={6} />
              ) : null}
              {/* Hub Status */}
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"HUB status"}
                  isInfo={true}
                  info={
                    "Parameter to show whether the customer/supplier is a Hub"
                  }
                />
                <CustomSwitch
                  name="hubFlag"
                  checked={values.hubFlag}
                  generalInfo={true}
                  isDisabled={isView}
                />
              </Grid>
              {/* Hub Name and GT */}
              {values.hubFlag === ocCompliance.hubFlag && (
                <>
                  <Grid item xs={12} md={6}>
                    <InputLabel
                      label={"HUB Name"}
                      isInfo={true}
                      info={"Name of the HUB"}
                      isMandatory={true}
                    />
                    <TextFieldWrapper
                      name="hubName"
                      placeholder={"Enter HUB name"}
                      isDisabled={isView}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <InputLabel
                      label={"HUB GT (Press enter to add HUB GT)"}
                      isInfo={true}
                      info={"HUB gateway details used to identify HUB customer"}
                      isMandatory={true}
                    />
                    <ShortCodeInput
                      name="hubGT"
                      value={values.hubGT}
                      onChange={(chips) => {
                        setFieldValue("hubGT", chips);
                      }}
                      isDisabled={isView}
                    />
                  </Grid>
                </>
              )}
              <Grid item xs={12} md={6} />
              {/* OC Compliance */}
              {values.hubFlag === ocCompliance.hubFlag && (
                <Grid item xs={12} md={6}>
                  <InputLabel
                    label={"OC compliance"}
                    isInfo={true}
                    info={
                      "Flag to enable or disable the open connectivity compliance, if enabled the source address will be revealed to the receiver"
                    }
                  />
                  <CustomSwitch
                    name="ocCompliance"
                    checked={values.ocCompliance}
                    generalInfo={true}
                    isDisabled={isView}
                  />
                </Grid>
              )}
              {/* Dummy MCC-MNC */}
              {values.ocCompliance === ocCompliance.ocCompliance &&
                values.hubFlag === ocCompliance.hubFlag && (
                  <Grid item xs={12} md={6}>
                    <InputLabel
                      label={"Dummy MCC-MNC (Press enter to add dummy MCC-MNC)"}
                      isInfo={true}
                      info={
                        "The MCC MNC value to be used if OC Compliance is enabled."
                      }
                    />
                    <ShortCodeInput
                      name="dummyMccMnc"
                      value={values.dummyMccMnc}
                      onChange={(chips) => {
                        setFieldValue("dummyMccMnc", chips);
                      }}
                      isDisabled={isView}
                    />
                  </Grid>
                )}
              {[operatorType.Customer, operatorType.Both].includes(
                values.operatorType
              ) &&
              values.interfaceType === interfaceType.SS7 &&
              values.interfaceType !== interfaceType["SMPP ES"] ? (
                <>
                  <Grid item xs={12} md={6}>
                    <InputLabel
                      label={"Avoid revalidation at MT"}
                      isInfo={true}
                      info={
                        "A flag to enable or disable the check of the source details at MT"
                      }
                    />
                    <CustomSwitch
                      name="avoidCustRevalidation"
                      checked={values.avoidCustRevalidation}
                      isDisabled={isView}
                    />
                  </Grid>
                  {!values.avoidCustRevalidation &&
                    values.hubFlag !== ocCompliance.hubFlag && (
                      <>
                        <Grid item xs={12} md={6}>
                          <InputLabel
                            label={"Fraud management"}
                            isInfo={true}
                            info={
                              "Flag to enable fraud message detection for respective SS7 Customer"
                            }
                          />
                          <CustomSwitch
                            name="enableFraudManagement"
                            checked={values.enableFraudManagement}
                            isDisabled={isView}
                          />
                        </Grid>
                        {values.enableFraudManagement === 1 && (
                          <>
                            <Grid item xs={12} md={6}>
                              <InputLabel
                                label={getLabelByName("handleFraudMessage")}
                                isInfo={true}
                                info={
                                  "Field to decide how to handle the detected fraud message"
                                }
                              />
                              <RadioButtonGroup
                                name="handleFraudMessage"
                                value={values.handleFraudMessage}
                                onChange={(e) =>
                                  setFieldValue(
                                    "handleFraudMessage",
                                    parseInt(e.target.value)
                                  )
                                }
                                options={getDropdownOptions(
                                  "handleFraudMessage"
                                )}
                                isDisabled={isView}
                              />
                            </Grid>
                            <Grid item xs={12} md={6} />
                            <Grid item xs={12} md={6}>
                              <InputLabel
                                label={"Periodic time interval"}
                                isMandatory={true}
                                isInfo={true}
                                info={
                                  "Field to configure the periodic time interval for fraud message detection.The count of detected fraud messages will be reset to 0 after the configured time"
                                }
                              />
                              <TextFieldWrapper
                                name="timeInterval"
                                isDisabled={isView}
                              />
                            </Grid>
                            <Grid item xs={12} md={6}>
                              <InputLabel
                                label={"Allowed fraud messages threshold"}
                                isMandatory={true}
                                isInfo={true}
                                info={
                                  "Field to configure the maximum number of allowed fraud messages. If the count of fraud messages crosses the configured threshold the alert will be generated"
                                }
                              />
                              <TextFieldWrapper
                                name="maxThreshold"
                                isDisabled={isView}
                              />
                            </Grid>
                          </>
                        )}
                      </>
                    )}
                </>
              ) : null}
              {values.customerStatus === ocCompliance.customerStatus ? (
                <>
                  {" "}
                  <Grid item xs={12} md={6}>
                    <InputLabel
                      label={"Test quota"}
                      isMandatory={true}
                      isInfo={true}
                      info={"Enter the total number of quota message value"}
                    />
                    <TextFieldWrapper name="testQuota" isDisabled={isView} />
                  </Grid>
                </>
              ) : null}
              {actionData !== "add" &&
              values.interfaceType === interfaceType.ESME ? (
                <>
                  <Grid item xs={12} md={6}>
                    <InputLabel
                      label={"CPAAS Enabled"}
                      isInfo={true}
                      info={
                        "Yes: SMPP Customer or Supplier has onboarded using an external platform.No: SMPP Customer or Supplier has onboarded using Comviva's Hub platform."
                      }
                    />
                    <CustomSwitch
                      name="ECPaaS"
                      checked={values.ECPaaS}
                      isDisabled
                      cpassStatus={true}
                    />
                  </Grid>
                  {values.ECPaaS === "1" &&
                    actionData !== "add" &&
                    values.interfaceType === interfaceType.ESME && (
                      <>
                        <Grid item xs={12} md={6}>
                          <InputLabel label={"Is India terminate"} />
                          <CustomSwitch
                            name="isIndiaTerminate"
                            checked={values.isIndiaTerminate}
                            cpassStatus={true}
                            isDisabled={actionData === "view"}
                          />
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <InputLabel label={"CPAAS contract closure"} />
                          <Select
                            name="contractFlag"
                            options={[
                              {
                                label: "Yes",
                                value: 1,
                              },
                              {
                                label: "No",
                                value: 0,
                              },
                            ]}
                            isDisabled
                          />
                        </Grid>
                      </>
                    )}
                </>
              ) : null}
            </Grid>
          </div>
          <div className="flex justify-center gap-5 pt-5 pb-10">
            <Button
              label={"Cancel"}
              buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
              onClick={() => {
                handleButtonClick();
                setGeneralInfo([]);
                setBillingInfo([]);
                setPaymentInfo([]);
                setRequiredInfo([]);
              }}
            />
            <ConfirmNNextButton
              label={"Next"}
              buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
              type="submit"
            />
          </div>
        </Form>
      )}
    </Formik>
  );
}

export default GeneralInformationForm;
