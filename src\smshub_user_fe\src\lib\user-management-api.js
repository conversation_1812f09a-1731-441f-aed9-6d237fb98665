import { axiosPrivate } from "../common/axiosPrivate";
import getAPIMap from "../routes/ApiUrls";
import { moduleConfiguration } from "../../../common/constants";

/**
 * This method is used to get users
 * @param {*} options
 * @returns
 */
export async function getUserDetail(options) {
  let url = getAPIMap("getUser");
  if (options.queryKey[1] && options.queryKey[1] !== "") {
    url = url + "?page=" + options.queryKey[1];
  }
  if (options.queryKey[2] && options.queryKey[2] !== "") {
    url = url + "&limit=" + options.queryKey[2];
  }

  let response = axiosPrivate.get(url);
  return response;
}

/**
 * This method is used to create the user
 * @param {*} param0
 * @returns
 */
export async function createUser(options) {
  let url = getAPIMap("getUser");

  return await axiosPrivate.post(url, options.reqData);
}

/**
 * This method is used to update user
 * @param {*} param0
 * @returns
 */
export async function updateUser(options) {
  let url = getAPIMap("getUser");
  const moduleName = options?.moduleName;

  url = `${url}/${options.id}`;
  return await axiosPrivate.put(url, options.reqData);
}

/**
 * This method is used to get data by Id
 * @param {*} param0
 * @returns
 */
export async function getDataByIdUsers({ queryKey }) {
  let url = `${getAPIMap("getUser")}/${queryKey[1]}`;
  const queryParams = [];
  queryParams.push("populate=*");
  if (queryParams.length) {
    url += `?${queryParams.join("&")}`;
  }
  const response = await axiosPrivate.get(url);
  return response;
}

/**
 * This method is used to get the global search list data
 * @param {*} param0
 * @returns {Promise}
 */
export async function getGlobalSearchList({ queryKey }) {
  const [searchText, limitPerPage, currentPage] = queryKey;

  const url = new URL(getAPIMap("getUser"));
  const params = new URLSearchParams();
  params.append("sortBy", "createdAt:DESC");
  params.append("search", searchText);
  // params.append("pagination[pageSize]", limitPerPage);
  // params.append("pagination[page]", currentPage);

  url.search = params.toString();

  return axiosPrivate.get(url.toString());
}

/**
 * This method is used to delete the data from table
 * @param {*} param0
 * @returns
 */
export async function deleteList(options) {
  let url = getAPIMap("getUser");

  url = `${url}/${options.rowId}`;

  let response = axiosPrivate.delete(url);
  return response;
}
