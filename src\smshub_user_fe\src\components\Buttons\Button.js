import LoadingButton from "@mui/lab/LoadingButton";
import React from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
const ConfirmNNextButton = ({ buttonClassName, disabled, ...rest }) => {
  const { t } = useTranslation();
  return (
    <LoadingButton
      {...rest}
      style={{
        textTransform: "none",
        fontFamily: "OpenSanHebrew",
      }}
      className={twMerge(
        `bg-bgSecondary text-white text-medium font-normal h-10 rounded-[40px] disabled:bg-opacity-60 disabled:text-white text-lg ${buttonClassName}`
      )}
      variant="contained"
      disabled={disabled}
    >
      <span>{t(`${rest.label}`)}</span>
    </LoadingButton>
  );
};

export default ConfirmNNextButton;
