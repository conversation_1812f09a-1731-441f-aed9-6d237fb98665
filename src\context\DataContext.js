import React, { createContext, useState } from "react";
// import { useParams } from "react-router-dom";

export const DataContext = createContext();

export const CommonDataContext = ({ children }) => {
  const [idList, setIdList] = useState([]);
  const [showfieldNames, setShowFieldNames] = useState([]);
  const [navigationPaths, setNavigationPaths] = useState([]);
  const [navigateState, setNavigateState] = useState(false);
  const [rowDetails, setRowDetails] = useState([]);
  const [connType, setConnType] = useState("");
  const [sideBarOpen, setSideBarOpen] = useState(true);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const updateNavigationPath = (location) => {
    if (location?.state?.url) {
      setNavigationPaths((prevPaths) => {
        if (!prevPaths.includes(location.state.url)) {
          return [...prevPaths, location.state.url];
        }
        return prevPaths;
      });
    }
  };

  const updateShowFieldState = (moduleName, name, val) => {
    const containsName = Object.values(showfieldNames).some((array) =>
      array.includes(name)
    );
    if (containsName) {
      return;
    } else {
      setShowFieldNames((prevName) => {
        const newNames = { [moduleName]: [] };
        newNames[moduleName] = [...(prevName[moduleName] || []), name];
        newNames[moduleName] = [...new Set(newNames[moduleName])];
        return newNames;
      });
    }
  };

  return (
    <DataContext.Provider
      value={{
        idList,
        setIdList,
        showfieldNames,
        setShowFieldNames,
        updateShowFieldState,
        updateNavigationPath,
        navigationPaths,
        setNavigationPaths,
        navigateState,
        setNavigateState,
        rowDetails,
        setRowDetails,
        connType,
        setConnType,
        email,
        setEmail,
        password,
        setPassword,
        //  updateFieldState,
        sideBarOpen,
        setSideBarOpen,
      }}
    >
      {children}
    </DataContext.Provider>
  );
};
