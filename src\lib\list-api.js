import { axiosPrivate } from "../common/axiosPrivate";
import { lcrType, moduleConfiguration, populate } from "../common/constants";
import { dynamicJsonConfig } from "../common/config";
import getAPIMap from "../routes/ApiUrls";
import axios from "axios";
/**
 * This method is used to get the page schema
 * @param {*} options
 * @returns
 */
export async function getPageSchema({ queryKey }) {
  const modelData = dynamicJsonConfig.find((ele) => {
    return ele.model === queryKey[0];
  });
  const urlKey = modelData ? "dynamicSchemaUrl" : "pageSchemaUrl";
  let url = getAPIMap(urlKey);

  if (modelData) {
    const { ruleType, model } = modelData;
    url += `?ruleType=${ruleType}&model=${model}`;
  }
  //  else if (queryKey[0] === moduleConfiguration.esmeSessions) {
  //   url = url + moduleConfiguration.sessionManagement;
  // }
  else {
    url = url + queryKey[0];
  }

  let response = axiosPrivate.get(url);
  return response;
}

/**
 *This method is used to get the list data
 * @param {*} param0
 * @returns
 */
export async function getListData({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${queryKey[1]}`;

  if (queryKey[1] === moduleConfiguration.customerSupplier) {
    // url += populate.customerSupplier;
  }

  const queryParams = [];
  queryParams.push("sort[id]=desc");
  if (queryKey[1] === moduleConfiguration.pointCodeListModuleName) {
    queryParams.push("filters[redirectionListType][$eq]=P");
    queryParams.push("populate=*");
  } else if (queryKey[1] === moduleConfiguration.redirectionalListModuleName) {
    queryParams.push("filters[redirectionListType][$eq]=E");
    queryParams.push("populate=*");
    queryParams.push("pathRelation=true");
    if (queryKey[0]) queryParams.push(`pathId=${queryKey[0]}`);
  } else if (queryKey[1] === moduleConfiguration.esmeAccounts) {
    queryParams.push("pathRelation=true");
    queryParams.push("customerRelation=true");
    queryParams.push("filters[protocol][$eq]=5");
    if (queryKey[0]) queryParams.push(`pathId=${queryKey[0]}`);
  } else {
    queryParams.push("populate=*");
  }

  if (queryKey[1] === moduleConfiguration.serviceManagement) {
    queryParams.push("pagination[limit]=-1");
  }

  if (queryKey[2]?.limit !== undefined) {
    queryParams.push(`pagination[limit]=${queryKey[2].limit}`);
  }
  if (queryKey[3] !== undefined) {
    queryParams.push(`pagination[pageSize]=${queryKey[3]}`);
  }
  if (queryKey[4] !== undefined) {
    queryParams.push(`pagination[page]=${queryKey[4]}`);
  }
  if (queryKey[7] !== undefined && queryKey[7] !== "") {
    queryParams.push(`filters[accId][$eq]=${queryKey[7]}`);
  }
  if (queryKey[8] === moduleConfiguration.hubRuleConfiguration) {
    queryParams.push("filters[editInfoMaster][ruleType][$eq]=9");
  }
  if (queryKey[8] === moduleConfiguration.ruleConfiguration) {
    queryParams.push("filters[editInfoMaster][ruleType][$eq]=6");
  }
  if (queryKey[9] !== "" && queryKey[9] !== undefined) {
    //console.log("queryKey[9]", queryKey[9]);
    queryParams.push(`${queryKey[9]}`);
  }
  if (queryKey[10] !== "" && queryKey[10] !== undefined) {
    if (queryKey[10] !== "all")
      queryParams.push(`filters[customerId]=${queryKey[10]}`);
  }
  if (queryParams.length) {
    url += `${url.includes("?") ? "&" : "?"}${queryParams.join("&")}`;
  }

  const response = await axiosPrivate.get(url);
  return response;
}

export async function getListTemplateData({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${queryKey[1]}`;

  if (queryKey[1] === moduleConfiguration.customerSupplier) {
    //  url += populate.customerSupplier;
  }
  const queryParams = [];
  queryParams.push("sort[id]=desc");
  if (queryKey[1] === moduleConfiguration.pointCodeListModuleName) {
    queryParams.push("filters[redirectionListType][$eq]=P");
  } else if (queryKey[1] === moduleConfiguration.series) {
    queryParams.push("numbersLimit=3");
    queryParams.push("populate=*");
  } else if (queryKey[1] === moduleConfiguration.redirectionalListModuleName) {
    queryParams.push("filters[redirectionListType][$eq]=E");
    queryParams.push("pathRelation=true");
    if (queryKey[0]) queryParams.push(`pathId=${queryKey[0]}`);
  } else if (queryKey[1] === moduleConfiguration.smscSessions) {
    queryParams.push("filters[accType][$eq]=R");
  } else {
    if (
      queryKey[1] === moduleConfiguration.retryPolicy ||
      queryKey[1] === moduleConfiguration.customerCreditProfile ||
      queryKey[1] === moduleConfiguration.series ||
      queryKey[1] === moduleConfiguration.customerSupplier
    ) {
      queryParams.push("populate=*");
    }
  }

  if (queryKey[1] === moduleConfiguration.serviceManagement) {
    queryParams.push("pagination[limit]=-1");
  }
  if (queryKey[2] !== undefined) {
    queryParams.push(`pagination[pageSize]=${queryKey[2]}`);
  }
  if (queryKey[3] !== undefined) {
    queryParams.push(`pagination[page]=${queryKey[3]}`);
  }
  if (queryKey[4] === moduleConfiguration.hubRuleConfiguration) {
    queryParams.push("filters[editInfoMaster][ruleType][$eq]=9");
    queryParams.push("populate[editInfoMaster]=*");
  }
  if (queryKey[4] === moduleConfiguration.ruleConfiguration) {
    queryParams.push("filters[editInfoMaster][ruleType][$eq]=6");
    queryParams.push("populate[editInfoMaster]=*");
  }
  if (queryKey[5] !== "" && queryKey[5] !== undefined) {
    if (queryKey[5] !== "all")
      queryParams.push(`filters[customerId]=${queryKey[5]}`);
  }
  if (queryKey[6] && queryKey[6].length > 0) {
    const fieldsParams = queryKey[6]
      .map((field) => `fields[]=${field}`)
      .join("&");
    queryParams.push(fieldsParams);
    //queryParams.push(`select=${queryKey[6]}`);
  }
  if (
    queryKey[1] === moduleConfiguration.redirectionalAccModuleName &&
    (queryKey[7] === "" || queryKey[7] === undefined)
  ) {
    queryParams.push("filters[connType]=S");
  }
  if (queryKey[7] !== "" && queryKey[7] !== undefined) {
    //console.log("queryKey[7]", queryKey[7]);
    queryParams.push(`${queryKey[7]}`);
  }
  if (queryParams.length) {
    url += `${url.includes("?") ? "&" : "?"}${queryParams.join("&")}`;
  }

  const response = await axiosPrivate.get(url);
  return response;
}

export async function getMultiSelectListData({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${queryKey[1]}`;
  if (queryKey[1] === moduleConfiguration.customerSupplier) {
    url += populate.customerSupplier;
  }
  const queryParams = [];
  queryParams.push("sort[id]=desc");
  if (queryKey[1] === moduleConfiguration.pointCodeListModuleName) {
    queryParams.push("filters[redirectionListType][$eq]=P");
    queryParams.push("populate=*");
  } else if (queryKey[1] === moduleConfiguration.redirectionalListModuleName) {
    queryParams.push("filters[redirectionListType][$eq]=E");
    queryParams.push("populate=*");
  } else {
    queryParams.push("populate=*");
  }
  if (queryKey[1] === moduleConfiguration.operators && queryKey[4] !== "add") {
    queryParams.push(`clusterId=${queryKey[4]}`);
  }

  if (queryKey[2]?.limit) {
    queryParams.push(`pagination[limit]=${queryKey[2].limit}`);
  }

  if (queryKey[3]) {
    queryParams.push(`clusterType=${queryKey[3]}`);
  }
  if (
    queryKey[5] &&
    queryKey[1] === moduleConfiguration.redirectionalAccModuleName
  ) {
    queryParams.push(
      `filters[connType]=${Number(queryKey[5]) === 1 ? "H" : "S"}`
    );
  }

  if (queryParams.length) {
    url += `${url.includes("?") ? "&" : "?"}${queryParams.join("&")}`;
  }

  const response = await axiosPrivate.get(url);
  return response;
}

/**
 *This method is used to get the data by Id
 * @param {*} param0
 * @returns
 */
export async function getDataById({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${queryKey[0]}/${queryKey[1]}`;
  const queryParams = [];
  if (
    queryKey[0] === "redirectional-lists" ||
    queryKey[0] === "point-code-lists" ||
    queryKey[0] === "serieses" ||
    queryKey[0] === "rules" ||
    queryKey[0] === "group-customer-suppliers" ||
    queryKey[0] === "operator-clusters" ||
    queryKey[0] === "deal-managements" ||
    queryKey[0] === "retry-policies" ||
    queryKey[0] === "customer-credit-profiles" ||
    queryKey[0] === "ports" ||
    queryKey[0] === "esme-accounts" ||
    queryKey[0] === "channel-partners"
  ) {
    queryParams.push("populate=*");
  }

  if (queryKey[0] === "rules") {
    // https://smshub.openturf.dev/hub-config/api/rules/48?populate[editInfoMaster]=*&filters[editInfoMaster][ruleType][$eq]=6
    //queryParams.push("populate[editInfoMaster]=*");
    queryParams.push("filters[editInfoMaster][ruleType][$eq]=6");
  }

  if (queryKey[2] === true) {
    queryParams.push("populate[dlrParameters][populate]=*");
    queryParams.push("populate[pushParameters][populate]=*");
  }

  if (
    queryKey[3] === "getAll" &&
    queryKey[4] !== moduleConfiguration.scATLCRImportName &&
    queryKey[0] !== "redirectional-lists"
  ) {
    queryParams.push("populate=*");
  }

  if (
    queryKey[0] === "redirectional-accounts" &&
    (queryKey[3] === "SMPP" ||
      queryKey[3] === "HTTP" ||
      queryKey[3] === "SS7" ||
      queryKey[3] === "S" ||
      queryKey[3] === "H" ||
      queryKey[3] === "M")
  ) {
    queryParams.push(
      `filters[connType]=${
        queryKey[3] === "SMPP" || queryKey[3] === "S"
          ? "S"
          : queryKey[3] === "HTTP" || queryKey[3] === "H"
          ? "H"
          : "M"
      }`
    );
  }

  if (queryKey[4] !== undefined) {
    queryParams.push(`pagination[pageSize]=${queryKey[4]}`);
  }

  if (queryKey[5] !== undefined) {
    queryParams.push(`pagination[page]=${queryKey[5]}`);
  }

  if (queryKey[6] !== undefined) {
    queryParams.push(`_q=${queryKey[6]}`);
  }

  if (queryParams.length) {
    url += `?${queryParams.join("&")}`;
  }

  const response = await axiosPrivate.get(url);
  return response;
}

/**
/**
 * This method is used to create the list
 * @param {*} param0
 * @returns
 */
export async function createRecord(options) {
  let url = getAPIMap("listUrl");
  const moduleName =
    options?.moduleName === moduleConfiguration.hubRuleConfiguration
      ? moduleConfiguration.rules
      : options?.moduleName;

  url = `${url}/${moduleName}`;
  return await axiosPrivate.post(url, options.reqData);
}

/**
 * This method is used to create the add LCR
 * @param {*} options - Contains the module data and request data
 * @returns {Promise} - Axios response promise
 */
export async function createLcr(options) {
  //console.log("options", options);
  let url = getAPIMap("lcrCreate");
  url = url + `?model=${options.moduleData}`;
  let formData = new FormData();
  //console.log("formData", options.reqData.data.fileUpload);
  formData.append("file", options.reqData.fileUpload);
  formData.append("profileName", options.reqData.data.profileName);
  if (options.reqData.data.customerType !== undefined) {
    formData.append("customerType", options.reqData.data.customerType);
  }
  if (options.reqData.data.trafficType !== undefined) {
    formData.append("trafficType", options.reqData.data.trafficType);
  }
  if (options.reqData.data.cpassStatus !== undefined) {
    formData.append("cpassStatus", options.reqData.data.cpassStatus);
  }
  if (options.reqData.data.cpassLcrType !== undefined) {
    formData.append("cpassLcrType", options.reqData.data.cpassLcrType);
  }
  if (options.reqData.data.cpassLcrStatus !== undefined) {
    formData.append("cpassLcrStatus", options.reqData.data.cpassLcrType);
  }
  if (options.reqData.data.speacialDateRequired !== undefined) {
    formData.append(
      "speacialDateRequired",
      options.reqData.data.speacialDateRequired
    );
  }

  formData.append("lcrType", options.reqData.data.lcrType);
  let response = axiosPrivate.post(url, formData, options.reqData);

  return response;
}

/**
 * This method is used to create the add LCR
 * @param {*} options - Contains the module data and request data
 * @returns {Promise} - Axios response promise
 */
export async function updateLcr(options) {
  let url = getAPIMap("lcrCreate");
  url = url + `/${options.id}`;
  url = url + `?model=${options.moduleData}`;
  let formData = new FormData();

  formData.append("file", options.reqData.fileUpload);
  formData.append("lcrName", options.reqData.data.lcrName);
  if (options.reqData.data.customerType !== undefined) {
    formData.append("custType", options.reqData.data.customerType);
  }
  if (options.reqData.data.trafficType !== undefined) {
    formData.append("trafficType", options.reqData.data.trafficType);
  }
  if (options.reqData.data.cpassStatus !== undefined) {
    formData.append("cpassStatus", options.reqData.data.cpassStatus);
  }
  if (options.reqData.data.cpassLcrType !== undefined) {
    formData.append("cpassLcrType", options.reqData.data.cpassLcrType);
  }
  if (options.reqData.data.cpassLcrStatus !== undefined) {
    formData.append("cpassLcrStatus", options.reqData.data.cpassLcrType);
  }
  if (options.reqData.data.speacialDateRequired !== undefined) {
    formData.append(
      "speacialDateRequired",
      options.reqData.data.speacialDateRequired
    );
  }
  formData.append("lcrType", options.reqData.data.lcrType);
  let response = axiosPrivate.put(url, formData, options.reqData);

  return response;
}

/**
 * This method is used to create the list
 * @param {*} param0
 * @returns
 */
export async function updateRecord(options) {
  let url = getAPIMap("listUrl");
  const moduleName =
    options?.moduleName === moduleConfiguration.hubRuleConfiguration
      ? moduleConfiguration.rules
      : options?.moduleName;

  url = `${url}/${moduleName}/${options.id}`;
  return await axiosPrivate.put(url, options.reqData);
}

// To update ESME account Status
export async function updateESMEStatus(options) {
  let url = getAPIMap("updateStatus");
  url = `${url}/${options.id}`;
  let response = axiosPrivate.put(url, options.reqData);
  return response;
}

// This method is used to update the Service management values
export async function updateServiceManagementRecord(options) {
  let url = getAPIMap("saveServiceManagement");
  let response = axiosPrivate.put(url, options.reqData);
  return response;
}

/**
 * This method is used to delete the data from table
 * @param {*} param0
 * @returns
 */
export async function deleteList(options) {
  let url = getAPIMap("listUrl");

  url =
    options.collectionName === moduleConfiguration.hubRuleConfiguration
      ? `${url}/${moduleConfiguration.rules}/${options.rowId}`
      : `${url}/${options.collectionName}/${options.rowId}`;

  if (
    options.collectionName === moduleConfiguration.lcrConfiguration &&
    options.filterValue
  ) {
    url = url + `?filters[lcrType]=${options.filterValue}`;
  }

  if (
    options?.connType &&
    options.collectionName === moduleConfiguration.redirectionalAccModuleName
  ) {
    url += url.includes("?")
      ? `&filters[connType]=${options.connType}`
      : `?filters[connType]=${options.connType}`;
  }

  let response = axiosPrivate.delete(url);
  return response;
}

/**
 * Reusable method to fetch data from a given API endpoint.
 * @param {string} endpointKey - The key for the endpoint URL in the API map.
 * @returns {Promise} - The response from the API.
 */
async function fetchData(endpointKey) {
  let url = getAPIMap(endpointKey);
  let response = await axiosPrivate.get(url);
  return response;
}

/**
 * This method is used to get the About Us schema
 * @param {*} options
 * @returns
 */
export function getAboutUsData() {
  return fetchData("aboutusUrl");
}

/**
 * This method is used to get the Contact Us schema
 * @param {*} options
 * @returns
 */
export function getContactUsData() {
  return fetchData("contactUsUrl");
}

/**
 * This method is used to get the country name for operator list page schema
 * @param {*} options
 * @returns
 */
export async function getCountryNameData({ queryKey }) {
  let url = getAPIMap("countryName");
  url += "?populate=*&sort[id]=desc";
  url += `&pagination[limit]=${queryKey[2].limit}`;
  url += "&select=[countryName]";
  let response = await axiosPrivate.get(url);
  return response;
}

/**
 * This method is used for Export All details download
 * @param {*} options
 * @returns
 */
export async function exportAll({ queryKey }) {
  const url = new URL(getAPIMap("exportData"));
  const params = new URLSearchParams();
  const [_, model, fileType, query, ids, viewId, viewModel] = queryKey;

  params.append("model", model);
  if (fileType) params.append("fileType", fileType);
  if (query) params.append("_q", query);

  if (Array.isArray(ids)) {
    params.append("ids", ids.length === 0 ? "*" : `[${ids}]`);
  }

  if (viewId) params.append("viewId", viewId);
  if (viewModel) params.append("viewModel", viewModel);

  url.search = params.toString();

  return axiosPrivate.get(url.toString(), { responseType: "blob" });
}

/**
 * This method is used to delete the data all from table
 * @param {*} param0
 * @returns
 */
export async function deleteAllApi(options) {
  // console.log("options", options);
  const filters = options?.dropdownFilter;
  const connTypeValue = filters.split("=")[1];
  const modulePaths =
    options.modulePath === moduleConfiguration.hubRuleConfigurationImport ||
    options.modulePath === moduleConfiguration.ruleConfigurationImport
      ? `${"rule"}`
      : `${options.modulePath}`;
  let url = getAPIMap("deleteAll");
  if (!options.ids.length) {
    url = url + `?model=${modulePaths}&ids=*`;
  } else {
    url = url + `?model=${modulePaths}&ids=${options.ids}`;
  }
  if (
    options?.dropdownFilter &&
    options.modulePath === moduleConfiguration.redirectionalAccImportName
  ) {
    url = url + `&filters[connType]=${connTypeValue}`;
  }
  if (
    options?.dropdownFilter &&
    options.modulePath === moduleConfiguration.lcrImportConfiguration
  ) {
    url = url + `&lcrType=${connTypeValue}`;
  }
  if (options.modulePath === moduleConfiguration.scATLCRImportName) {
    url = url + `&lcrType=${2}`;
  }
  let response = axiosPrivate.delete(url);
  return response;
}
/**
 *This method is used to get the data by Id
 * @param {*} param0
 * @returns
 */

export async function getPointCodeData({ queryKey }) {
  let url = getAPIMap("getPointCode");
  url = url.replace("{id}", queryKey[1]);
  url = url + `?model=${queryKey[2]}`;
  url += "&sort[id]=desc";
  url += `&pagination[pageSize]=${queryKey[3]}`;
  url += `&pagination[page]=${queryKey[4]}`;
  let response = axiosPrivate.get(url);
  return response;
}
/**
 * This method is used to handle XML dialog
 * @param {*} options
 * @returns
 */
export async function extractXmlDialog(options) {
  let url = getAPIMap("xmlDialog") + "?model=operator";
  let formData = new FormData();
  formData.append("file", options.file);

  let response = await axiosPrivate.post(url, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response;
}

/**
 * This API call is to get error list based on error category selected in the retry-policy module
 */
export async function getErrorDetails({ queryKey }) {
  let url = getAPIMap("errorListUrl");

  if (queryKey[1] !== undefined) {
    url += `?filters[failureResult][$eq]=${queryKey[1]}&filters[guiEnable][$eq]=1`;
  } else {
    url += `?filters[guiEnable][$eq]=1&pagination[limit]=-1`;
  }

  let response = await axiosPrivate.get(url);
  return response;
}

export async function getSMSConfig() {
  let url = getAPIMap("smsConfig");
  let response = await axiosPrivate.get(url);
  return response;
}

/**
 *This method is used to get the point code list (View List)
 * @param {*} param0
 * @returns
 */

export async function getPointCodeListData({ queryKey }) {
  let url = getAPIMap("getPointCodeList");
  url = url.replace("{id}", queryKey[1]);
  url = url + `?model=${queryKey[2]}`;
  url += "&sort[id]=desc";
  url += `&pagination[pageSize]=${queryKey[3]}`;
  url += `&pagination[page]=${queryKey[4]}`;

  if (queryKey[5]) {
    url += `&filters[connType]=${
      queryKey[5] === "SMPP" ? "S" : queryKey[5] === "HTTP" ? "H" : "M"
    }`;
  }
  let response = axiosPrivate.get(url);
  return response;
}

/**
 * This method is used to get the License Agreement data.
 * @param {*} options
 * @returns
 */
export async function fetchEndUserLicense() {
  try {
    const url = getAPIMap("endUserLicenseAgreement");
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error("There has been a problem with your axios request:", error);
    return {
      data: null,
      message: "An error occurred while fetching the license agreement.",
    }; // Return an error message and null data
  }
}

/**
 * This method is used to get the global search list data
 * @param {*} param0
 * @returns {Promise}
 */
export async function getGlobalSearchList({ queryKey }) {
  const [
    _,
    model,
    fieldName,
    value,
    exportFlag,
    idsValue,
    extensionType,
    dropdownFilter,
    collectionName,
    rowId,
    idSearch,
    limitPerPage,
    currentPage,
    viewAccount,
    moduleFilter,
    accId,
    exportLcr,
  ] = queryKey;

  const url = new URL(getAPIMap("globalSearch"));
  const params = new URLSearchParams();
  params.append("model", model);
  params.append("sort[id]", "desc");
  if (fieldName) params.append("fieldName", fieldName);
  if (value) params.append("value", value);
  if (collectionName) params.append("viewModel", collectionName);
  if (
    rowId &&
    model !== moduleConfiguration.lcrImportConfiguration &&
    model !== moduleConfiguration.scATLCRImportName
  ) {
    params.append("viewId", rowId);
  }
  if (viewAccount !== undefined) params.append("viewAccount", true);
  if (
    fieldName === "custId" ||
    fieldName === "supId" ||
    fieldName === "mnpOpId" ||
    fieldName === "mnpClsId" ||
    fieldName === "supplierName" ||
    fieldName === "accId" ||
    (fieldName === "systemId" && model === moduleConfiguration.esmeModel)
  )
    params.append("idSearch", "true");

  if (moduleFilter) {
    params.append(moduleFilter, true);
  }

  if (exportFlag) {
    params.append("exportFlag", exportFlag);
    if (extensionType) params.append("fileType", extensionType);
    params.append("ids", idsValue);

    if (queryKey[8] === "E") {
      //For ESME Session
      params.append("filters[accType][$eq]", "E");
    }
    if (queryKey[1] === "smsc-session" && queryKey[8] !== "E") {
      //For SMSC Session
      params.append("filters[accType][$eq]", "R");
    }
    if (moduleFilter) {
      params.append(moduleFilter, true);
    }
    if (accId) {
      params.append("filters[accId][$eq]", accId);
    }
    if (queryKey[9]) {
      params.append("exportLcr", queryKey[9]);
    }
  }
  if (queryKey[10] && Object.keys(queryKey[10]).length > 0) {
    params.append("select", queryKey[10]);
  }
  if (
    queryKey[13] &&
    queryKey[13] !== "all" &&
    model === moduleConfiguration.customerCreditProfile
  ) {
    params.append("filters[customerId][$eq]", queryKey[13]);
  }
  if (dropdownFilter || queryKey[0] === moduleConfiguration.scAtLCRModuleName) {
    const dropdownFilterData =
      queryKey[0] === moduleConfiguration.scAtLCRModuleName
        ? "filters[lcrType]=2"
        : dropdownFilter;

    // Split the string by '&' to handle multiple filters
    const filtersArray = dropdownFilterData.includes("&")
      ? dropdownFilterData.split("&")
      : [dropdownFilterData];

    filtersArray.forEach((filter) => {
      const [filterKey, filterValue] = filter.split("=");
      if (filterKey && filterValue) {
        params.append(filterKey, filterValue);
      }
    });
  }

  if (queryKey[11]) {
    params.append("pagination[pageSize]", queryKey[11]);
  }
  if (queryKey[12]) {
    params.append("pagination[page]", queryKey[12]);
  }

  url.search = params.toString();

  if (exportFlag) {
    return axiosPrivate.get(url.toString(), { responseType: "blob" });
  } else {
    return axiosPrivate.get(url.toString());
  }
}

/**
 * This method is used to get the global search list data
 * @param {*} param0
 * @returns {Promise}
 */
export async function getLCRGlobalSearch({ queryKey }) {
  const [_, model, viewId, fieldName, value, lcrTypeValue] = queryKey;

  const url = new URL(getAPIMap("globalSearch"));
  const params = new URLSearchParams();

  params.append("model", model);
  params.append("viewAccount", true);
  if (viewId) params.append("viewId", viewId);
  if (fieldName) params.append("fieldName", fieldName);
  if (value) params.append("value", value);

  if (
    fieldName === "sourceOpId" ||
    fieldName === "supplier" ||
    fieldName === "destOpId"
  )
    params.append("idSearch", "true");
  if (lcrTypeValue != null) {
    params.set("filters[lcrType]", lcrTypeValue);
  }

  // if (limitPerPage) {
  //   params.append("pagination[pageSize]", limitPerPage);
  // }
  // if (currentPage) {
  //   params.append("pagination[page]", currentPage);
  // }

  url.search = params.toString();

  return axiosPrivate.get(url.toString());
}

/**
 * This method is used to get the XML upload
 * @param {*} param0
 * @returns {Promise}
 */
export async function getXmlUpload({ queryKey }) {
  const url = getAPIMap("exportXml");

  const response = url + `/${queryKey[0]}`;

  return axiosPrivate.get(response);
}

/**
 * This method is used to filter the credit transaction report customer wise
 * @param {*} param0
 * @returns {Promise}
 */
export async function getCreditTransactionCustomer({ queryKey }) {
  const url = getAPIMap("creditTransactionCustomers");
  const urlCustomer =
    url +
    `?linkedModel=${moduleConfiguration.customerSupplierImport}&model=${moduleConfiguration.customerCreditProfile}&select=id, name`;
  let response = await axiosPrivate.get(urlCustomer);
  return response;
}
export async function getDataByIdSeries({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${moduleConfiguration.series}/${
    queryKey[1]
  }`;
  const queryParams = [];
  queryParams.push("populate=*");
  if (queryParams.length) {
    url += `?${queryParams.join("&")}`;
  }
  const response = await axiosPrivate.get(url);
  return response;
}
export async function getSeriesStatus({ queryKey }) {
  let url = `${getAPIMap("seriesStatus")}/${queryKey[1]}`;
  const response = await axiosPrivate.get(url);
  return response;
}

export async function getGlobalConfig({ queryKey }) {
  let url = `${getAPIMap("globalConfig")}`;
  const response = await axiosPrivate.get(url);
  return response;
}
