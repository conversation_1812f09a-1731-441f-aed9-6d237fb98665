import React from "react";
import { useTranslation } from "react-i18next";

const InputLabel = ({ label, color, labelClassName, isMandatory }) => {
  const { t } = useTranslation();

  return (
    <p
      className={`${
        color ? color : "text-headingColor"
      } text-xs font-normal mb-1 ${labelClassName}`}
    >
      {t(label)}
      {isMandatory ? <span className="text-errorColor text-xs"> *</span> : null}
    </p>
  );
};

export default InputLabel;
