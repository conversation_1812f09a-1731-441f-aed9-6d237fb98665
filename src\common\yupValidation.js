import * as Yup from "yup";
import { onlyNumSpaceNotAllowed, websiteRegex } from "./constants";

const mobileNumberValidation = Yup.string()
  .test((value, ctx) => {
    if (
      ctx.parent?.mobileNumber?.replace(ctx?.parent?.dialCode, "")[0] === "0"
    ) {
      return ctx.createError({
        message: "Mobile number cannot start with 0",
      });
    }

    if (
      ctx.parent?.mobileNumber?.replace(ctx?.parent?.dialCode, "") === "" ||
      value === ""
    ) {
      return ctx.createError({ message: "Mobile number is required" });
    }
    const formattedValueWithoutFormat = ctx.parent?.formattedValue?.replace(
      /\D/g,
      ""
    );
    const mobileNumberWithoutFormat = ctx.parent?.mobileNumber?.replace(
      /\D/g,
      ""
    );

    if (formattedValueWithoutFormat === mobileNumberWithoutFormat) {
      if (ctx.parent?.format?.length !== ctx.parent?.formattedValue?.length) {
        const length = (
          ctx.parent?.format
            ?.substring(ctx.parent?.format.indexOf(" ") + 1)
            .match(/\./g) || []
        ).length;
        return ctx.createError({
          message: "Mobile number must be of length" + " " + length,
        });
      }
    }

    let numberData = ctx.parent?.mobileNumber?.replace(
      ctx?.parent?.dialCode,
      ""
    );
    if (numberData < 1) {
      return ctx.createError({ message: "Enter valid mobile number" });
    }
    return true;
  })
  .required("Mobile number is required");

const emailValidation = Yup.string()
  .min(2, "Must be 2 characters or more")
  .max(256, "Must be 256 characters or less")
  .matches(/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/, {
    message: "Must be valid Email ID",
  })
  .required("Email ID is required");

const passwordValidation = Yup.string()
  .min(8, "Min allowed characters are 8")
  .max(10, "Max length allowed is 10")
  .required("Password is required")
  .matches(/[A-Z]/, "Must contain at least one uppercase letter")
  .matches(/[a-z]/, "Must contain at least one lowercase letter")
  .matches(/[0-9]/, "Must contain at least one number")
  .matches(
    /[!@#\$%^*()_+{}\[\]:;<>,.?~|]/,
    "Must contain at least one special character"
  )
  .matches(/^\S*$/, "Space is not allowed");

const firstNameValidation = Yup.string()
  .min(1, "Min length allowed is 1 characters")
  .max(50, "Max length allowed is 50 characters")
  .matches(onlyNumSpaceNotAllowed, "Must contain atleast one Alphabet")
  .required("First Name is required");

const lastNameValidation = Yup.string()
  .min(1, "Min length allowed is 1 characters")
  .max(50, "Max length allowed is 50 characters")
  .matches(onlyNumSpaceNotAllowed, "Must contain atleast one Alphabet")
  .required("Last Name is required");

const companyNameValidation = Yup.string()
  .max(256, "Max length allowed is 256 characters")
  .min(2, "Min length allowed is 2 characters")
  .matches(onlyNumSpaceNotAllowed, "Must contain atleast one Alphabet");

const WebsiteValidation = Yup.string()
  .required("Website Link is required")
  .min(2, "Must be atleast 2 characters")
  .max(256, "Max length allowed is 256 characters")
  .test("is-valid-website", "Invalid company website", (value) => {
    return websiteRegex.test(value);
    //&&
    //(value.startsWith("http://") || value.startsWith("https://")) //to ensure if website url must contains http:// or https://
  });

const descriptionValidation = Yup.string()
  .min(2, "Must be atleast 2 characters")
  .matches(onlyNumSpaceNotAllowed, "Must contain atleast one Alphabet")
  .required("Description is required");

const accountNumberValidation = Yup.string()
  .matches(/^[a-zA-Z0-9]+$/, "Only alphanumeric characters are allowed")
  .max(18, "Maximum length allowed is 18 characters")
  .min(9, "Minimum length allowed is 9 characters")
  .required("Account Number is required");

const accountHolderName = Yup.string()
  .required("Account Holder Name is required")
  .max(256, "Max length allowed is 256 characters")
  .min(2, "Min length allowed is 2 characters")
  .matches(onlyNumSpaceNotAllowed, "Must contain atleast one Alphabet");

export {
  mobileNumberValidation,
  emailValidation,
  passwordValidation,
  firstNameValidation,
  lastNameValidation,
  companyNameValidation,
  WebsiteValidation,
  descriptionValidation,
  accountNumberValidation,
  accountHolderName,
};
