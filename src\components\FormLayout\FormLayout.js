import React, { useContext, useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  getIndexedDBDataById,
  updateIndexedDBDataById,
} from "../HelperFunction/localStorage";
import {
  getActionAndModuleNameFromURL,
  getModuleNameFromURL,
} from "../../common/urlUtils";
import { moduleConfiguration } from "../../common/constants";
import { multiStepFormContext } from "../../context/MultiStepFormContext";

function FormLayout({ title, children, respData, header, stepper, action }) {
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const currentId = searchParams.get("currentId");
  const [existingData, setExistingData] = useState(null);
  const [breadcrumbs, setBreadcrumbs] = useState([]);
  const { setCurrentStep } = useContext(multiStepFormContext);

  const loactionData = getActionAndModuleNameFromURL(window.location.href);
  const currentModuleName = getModuleNameFromURL(window.location.href);

  const fetchData = async () => {
    if (!parseInt(currentId)) return;
    try {
      const data = await getIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        parseInt(currentId)
      );
      setExistingData(data);
    } catch (error) {
      console.error("Error fetching data from IndexedDB:", error);
    }
  };

  const generateBreadcrumbs = () => {
    const breadcrumbsList = [
      {
        name: header,
        path: `/app/list/${currentModuleName}`,
      },
    ];

    if (existingData?.data?.length) {
      const { data = [] } = existingData;
      breadcrumbsList[0].name = data[0].listHeader;

      data.forEach((entry, index) => {
        if (currentModuleName === entry.moduleNameValue) {
          breadcrumbsList[0].path = `/app/list/${currentModuleName}`;
          return;
        }
        breadcrumbsList.push({
          name: entry.previousModuleName || loactionData,
          path: entry.currentPath,
        });
      });

      breadcrumbsList.forEach((entry, index) => {
        if (breadcrumbsList.length > 1 && index < breadcrumbsList.length - 1)
          entry.path = null;
      });

      breadcrumbsList.push({ name: title, path: null });
    } else {
      breadcrumbsList.push({
        name: loactionData,
        path: null,
      });
    }

    setBreadcrumbs(breadcrumbsList);
  };

  const handleBreadcrumbClick = (path, index) => {
    if (path) {
      const moduleName = path.split("/")[3];
      if (moduleName === moduleConfiguration.customerSupplier) {
        setCurrentStep(3);
      }
      navigate(path);
      const updatedData = { data: existingData.data.slice(0, index) };
      updateIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        parseInt(currentId),
        updatedData
      );
      setExistingData(updatedData);
      generateBreadcrumbs();
    }
  };

  useEffect(() => {
    fetchData();
  }, [currentId]);

  useEffect(() => {
    if (existingData) {
      generateBreadcrumbs();
    }
  }, [existingData]);

  return (
    <div className="w-full h-full">
      <div className="sticky top-0 bg-bgBody h-[40px] z-10 content-center mt-0 mb-0">
        <div className="flex gap-2 font-medium">
          {Array.isArray(breadcrumbs) && breadcrumbs.length > 0 ? (
            <>
              {action !== "viewData" ? (
                <>
                  {" "}
                  {breadcrumbs.map((crumb, index) => (
                    <React.Fragment key={index}>
                      <span
                        className={`text-base ${
                          crumb.path
                            ? "text-textNAColor cursor-pointer hover:underline"
                            : "text-black font-bold"
                        }`}
                        onClick={() =>
                          crumb.path && handleBreadcrumbClick(crumb.path, index)
                        }
                      >
                        {crumb?.name
                          ?.split(" ")
                          .map((word) =>
                            ["http", "mnp", "esme"].includes(word.toLowerCase())
                              ? word.toUpperCase()
                              : word
                          )
                          .join(" ")}
                      </span>
                      {index < breadcrumbs.length - 1 && <span>{">"}</span>}
                    </React.Fragment>
                  ))}
                </>
              ) : (
                <>
                  {" "}
                  <div
                    className="text-textNAColor cursor-pointer hover:underline"
                    onClick={() => {
                      navigate(`/app/list/${currentModuleName}`);
                    }}
                  >{`${header} > `}</div>
                  <div className="text-black font-bold">
                    {" "}
                    {title &&
                      title
                        ?.split(" ")
                        .map((word) =>
                          ["http", "mnp", "esme"].includes(word.toLowerCase())
                            ? word.toUpperCase()
                            : word
                        )
                        .join(" ")}
                  </div>
                </>
              )}
            </>
          ) : (
            <>
              <div
                className="text-textNAColor cursor-pointer hover:underline"
                onClick={() => {
                  navigate(`/app/list/${currentModuleName}`);
                }}
              >{`${header} > `}</div>
              <div className="text-black font-bold">
                <div className="text-black font-bold">
                  {title &&
                    title
                      ?.split(" ")
                      .map((word) =>
                        ["http", "mnp", "esme"].includes(word.toLowerCase())
                          ? word.toUpperCase()
                          : word
                      )
                      .join(" ")}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
      {!stepper && (
        <div className="mt-5 bg-bgHeader text-headerColor font-bold rounded-t-lg align-middle flex justify-center py-5 text-lg">
          {title}
        </div>
      )}

      {!stepper ? (
        <div className="bg-white p-2 md:p-10">{children}</div>
      ) : (
        <div>{children}</div>
      )}
    </div>
  );
}

export default FormLayout;
