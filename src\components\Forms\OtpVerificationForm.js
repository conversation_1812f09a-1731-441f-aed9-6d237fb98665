import React, { useContext } from "react";
import { Formik, Form } from "formik";
import { useTranslation } from "react-i18next";
import { AuthContext } from "../../context/AuthContext";
import { DataContext } from "../../context/DataContext";
import { useTimer } from "react-timer-hook";
import TextFieldWrapper from "../FormsUI/TextField";
import LoginButton from "../../components/Buttons/OutlinedButton";
import * as Yup from "yup";
import { useNavigate } from "react-router-dom";

const OTP_EXPIRY_IN_MINUTES = 1;

const OtpVerificationForm = () => {
  const { email, password } = useContext(DataContext);
  const { otpVerificationCall, login } = useContext(AuthContext);
  const { t } = useTranslation();
  const navigate = useNavigate();

  const getExpiryTimestamp = () => {
    const time = new Date();
    time.setMinutes(time.getMinutes() + OTP_EXPIRY_IN_MINUTES);
    return time;
  };

  const {
    seconds,
    minutes,
    isRunning,
    restart: restartTimer,
  } = useTimer({
    expiryTimestamp: getExpiryTimestamp(),
    onExpire: () => console.log("Timer expired"),
  });

  const handleResendClick = () => {
    if (!isRunning) {
      login(email, password)
        .then(() => {
          restartTimer(getExpiryTimestamp());
        })
        .catch((error) => {});
    }
  };

  const validationSchema = Yup.object().shape({
    otp: Yup.string()
      .matches(/^[0-9]+$/, "Only numbers are allowed")
      .required("OTP is required"),
  });

  return (
    <Formik
      initialValues={{ otp: "" }}
      validationSchema={validationSchema}
      onSubmit={({ otp }, { setStatus, setSubmitting }) => {
        setSubmitting(true);
        setStatus();
        otpVerificationCall(email, otp, password)
          .then(() => {
            navigate("/app/home");
          })
          .catch((error) => {
            if (error.response) {
              setStatus(error.response.data.message);
            } else {
              setStatus("Some error occurred. Please try again.");
            }
            setSubmitting(false);
          });
      }}
    >
      {({ status, isSubmitting }) => (
        <Form>
          <TextFieldWrapper
            label="OTP"
            name="otp"
            placeholder="Enter the OTP you received in your registered mail ID"
            isMandatory={true}
          />

          {status && (
            <p className="text-errorColor text-xs" valid={false}>
              {status}
            </p>
          )}

          <div className="flex justify-between font-normal text-xs mt-5 w-full mb-3">
            <div>
              <span className="font-normal text-xs">Time Remaining: </span>
              <span className="text-[#4B93FF] text-sm font-semibold">
                {minutes < 10 ? `0${minutes}` : minutes}:
                {seconds < 10 ? `0${seconds}` : seconds}
              </span>
            </div>
            <div>
              <div
                onClick={handleResendClick}
                className={`text-sm underline font-semibold ${
                  isRunning
                    ? "text-[#D9D9D9] cursor-not-allowed"
                    : "text-[#4B93FF] cursor-pointer"
                }`}
              >
                {t("Resend OTP")}
              </div>
            </div>
          </div>

          <LoginButton
            type="submit"
            label="Verify"
            buttonClassName="w-full text-sm mb-3 rounded mt-8 bg-bgLogin text-white border-errorBorder shadow-xl"
            loading={isSubmitting}
          />
        </Form>
      )}
    </Formik>
  );
};

export default OtpVerificationForm;
