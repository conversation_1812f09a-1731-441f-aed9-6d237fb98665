import {
  interfaceType,
  ocCompliance,
  operatorType,
  protocolType,
  subInterface,
  subInterfaceEsme,
} from "../common/constants";
import * as Yup from "yup";

export const getCommonPayload = (values, actionData) => ({
  name: values.name,
  operatorType: values.operatorType,
  interfaceType: values.interfaceType,
  protocolType: values.protocolType,
  timezone: values.timezone,
  hour: values.hour,
  min: values.min,
  hubFlag: values.hubFlag,
  subInterfaceEsme: values.subInterfaceEsme,
  subInterface: values.subInterface,
  ocCompliance: values.ocCompliance,
  ...(values.ocCompliance === ocCompliance.ocCompliance && {
    dummyMccMnc: values.dummyMccMnc,
  }),
  ...(values.hubFlag === ocCompliance.hubFlag && {
    hubName: values.hubName,
    hubGT: values.hubGT,
  }),
  ...(values.protocolType === 2 &&
    values.operatorType === operatorType.Customer &&
    values.interfaceType === interfaceType.SS7 && {
      reverseSriFlag: values.reverseSriFlag,
    }),
  ...(actionData === "edit" &&
    values.interfaceType === interfaceType.ESME && {
      ECPaaS: values.ECPaaS,
      contractFlag: values.contractFlag,
      isIndiaTerminate: values.isIndiaTerminate,
    }),
});

export const getCustomerPayload = (values) => ({
  customerType: values.customerType,
  customerStatus: values.customerStatus,
  ...(values.customerStatus === ocCompliance.customerStatus && {
    testQuota: values.testQuota,
  }),
  deliverTimestamp: values.deliverTimestamp,
  ...(values.protocolType !== protocolType.P2P && {
    shortCodes: values.shortCodes,
  }),
  allowSpamTimeInterval: values.allowSpamTimeInterval,
  dnd: values.dnd,
  mnp: values.mnp,
  allowSpamCount: values.allowSpamCount,
  trafficVariation: values.trafficVariation,
  roamDeliveryFlag: values.roamDeliveryFlag,
});

export const getSupplierPayload = (values) => ({
  supplierStatus: values.supplierStatus,
  supplierType: values.supplierType,
});

export const getFraudManagementPayload = (values) => ({
  enableFraudManagement: values.enableFraudManagement,
  ...(values.enableFraudManagement === ocCompliance.enableFraudManagement && {
    handleFraudMessage: values.handleFraudMessage,
    timeInterval: values.timeInterval,
    maxThreshold: values.maxThreshold,
  }),
});

export const generalInfoInitialValue = {
  name: "",
  operatorType: operatorType.Customer,
  interfaceType: interfaceType.ESME,
  subInterface: subInterface.SIGTRAN,
  subInterfaceEsme: subInterfaceEsme.SMPP,
  protocolType: protocolType.A2P,
  customerType: null,
  customerStatus: null,
  supplierType: null,
  supplierStatus: null,
  deliverTimestamp: "Y",
  shortCodes: [],
  mnp: "Y",
  allowSpamTimeInterval: 1,
  dnd: "Y",
  allowSpamCount: null,
  trafficVariation: null,
  roamDeliveryFlag: "Y",
  timezone: "(GMT +)",
  hour: "5",
  min: "30",
  ocCompliance: ocCompliance.ocComplianceNo,
  dummyMccMnc: [],
  hubFlag: ocCompliance.hubFlag,
  hubName: "",
  hubGT: [],
  avoidCustRevalidation: ocCompliance.avoidCustRevalidation,
  enableFraudManagement: 0,
  handleFraudMessage: 1,
  timeInterval: null,
  maxThreshold: null,
  testQuota: null,
  twowaySms: 1,
  hlrSupplierFlag: 1,
  reverseSriFlag: 1,
  ECPaaS: "0",
  contractFlag: 0,
  isIndiaTerminate: "0",
};

export const customerSupplierValidationSchema = (serviceParameterData) => {
  const shortCodemaxLength = Number(
    serviceParameterData?.data?.data?.max_len_at_addr
  );
  return Yup.object().shape({
    name: Yup.string()
      .matches(
        /^[a-zA-Z0-9 _!@#$%^&*().-]+$/,
        "Only alphanumeric characters, underscore, space, and special characters (-!@#$%^&*().) are allowed for Name"
      )
      .required("Please enter Customer/Supplier Name"),

    hubGT: Yup.array()
      .nullable()
      .of(
        Yup.number()
          .typeError("Only Integer value is allowed for HUB GT")
          .min(1, "HUB GT value must be at least 1")
          .test("max-length", "HUB GT value cannot exceed 20 digits", (value) =>
            value ? value.toString().length <= 20 : true
          )
      )
      .max(10, "HUB GT can have at most 10 items")
      .test("hub-gt-required", "HUB GT is required", function (value) {
        const { hubFlag } = this.parent;
        return hubFlag === "Y"
          ? Array.isArray(value) && value.length >= 1
          : true;
      })
      .test(
        "unique",
        "HUB GT values must be unique.",
        (values) => !values || new Set(values).size === values.length
      ),

    hubName: Yup.string()
      .nullable()
      .test("hub-name-required", "HUB name is required", function (value) {
        const { hubFlag } = this.parent;
        return hubFlag === "Y" ? !!value : true;
      })
      .test(
        "hub-name-valid",
        "HUB name must start with an alphabet and contain only alphanumeric characters, underscores, and spaces",
        function (value) {
          const { hubFlag } = this.parent;
          return hubFlag === "Y"
            ? /^[A-Za-z][A-Za-z0-9 _]*$/.test(value)
            : true;
        }
      ),

    shortCodes: Yup.array()
      .of(
        Yup.string()
          .matches(/^\d+$/, "Short code must contain only numeric values.")
          .test(
            "positive",
            "Short code value must be greater than 0.",
            (value) => parseInt(value, 10) > 0
          )
          .test(
            "no-leading-zero",
            "Short code should not start with 0.",
            (value) => value[0] !== "0"
          )
          .test(
            "length",
            `Short code length must be ${shortCodemaxLength} digits or less.`,
            (value) => value.length <= shortCodemaxLength
          )
      )
      .test(
        "unique",
        "Short codes must be unique.",
        (values) => new Set(values).size === values.length
      ),

    testQuota: Yup.number()
      .nullable()
      .typeError("Please enter a valid number")
      .test("test-quota-required", "Please enter test quota", function (value) {
        const { customerStatus } = this.parent;

        return Number(customerStatus) === ocCompliance.customerStatus
          ? !!Number.isInteger(value)
          : true;
      })
      .test(
        "test-quota-not-zero",
        "Please enter a valid test quota greater than 0",
        function (value) {
          const { customerStatus } = this.parent;

          return Number(customerStatus) === ocCompliance.customerStatus
            ? value !== 0
            : true;
        }
      )
      .test(
        "test-quota-seconds",
        "Please enter a valid test quota between 1 and 9998",
        function (value) {
          const { customerStatus } = this.parent;

          return Number(customerStatus) === ocCompliance.customerStatus
            ? Number.isInteger(value) && value > 0 && value < 9999
            : true;
        }
      ),
    dummyMccMnc: Yup.array()
      .of(
        Yup.string().matches(
          /^[0-9]{3,6}$/,
          "Each Dummy MCC-MNC must be an integer with 3 to 6 digits"
        )
      )
      .test(
        "unique",
        "Each Dummy MCC-MNC value must be unique.",
        (values) => !values || new Set(values).size === values.length
      )
      .nullable(),

    customerType: Yup.string()
      .nullable()
      .test(
        "customer-type-required",
        "Please Select Customer type",
        function (value) {
          return this.parent.operatorType !== operatorType.Customer &&
            this.parent.operatorType !== operatorType.Both
            ? true
            : !!value;
        }
      ),

    maxThreshold: Yup.number()
      .nullable()
      .typeError("Please enter a valid number")
      .test(
        "max-threshold-required",
        "Please enter Allowed Fraud Messages Threshold Value",
        function (value) {
          return (this.parent.operatorType === "C" ||
            this.parent.operatorType === "C,S") &&
            this.parent.enableFraudManagement ===
              ocCompliance.enableFraudManagement &&
            this.parent.avoidCustRevalidation ===
              ocCompliance.avoidCustRevalidation &&
            this.parent.interfaceType === "SS7"
            ? !!value
            : true;
        }
      )
      .test(
        "max-threshold-seconds",
        "Please enter Allowed Fraud Messages Threshold Value less than or equal to 50000",
        function (value) {
          return (this.parent.operatorType === "C" ||
            this.parent.operatorType === "C,S") &&
            this.parent.enableFraudManagement ===
              ocCompliance.enableFraudManagement &&
            this.parent.avoidCustRevalidation ===
              ocCompliance.avoidCustRevalidation &&
            this.parent.interfaceType === "SS7"
            ? Number.isInteger(value) && value > 0 && value <= 50000
            : true;
        }
      ),

    supplierType: Yup.string()
      .nullable()
      .test(
        "supplier-type-required",
        "Please Select Supplier type",
        function (value) {
          return this.parent.operatorType === operatorType.Supplier ||
            this.parent.operatorType === operatorType.Both
            ? !!value
            : true;
        }
      ),

    timeInterval: Yup.number()
      .nullable()
      .typeError("Please enter a valid number")
      .test(
        "time-interval-required",
        "Please enter Periodic Time Interval in seconds",
        function (value) {
          return (this.parent.operatorType === "C" ||
            this.parent.operatorType === "C,S") &&
            this.parent.enableFraudManagement ===
              ocCompliance.enableFraudManagement &&
            this.parent.avoidCustRevalidation ===
              ocCompliance.avoidCustRevalidation &&
            this.parent.interfaceType === "SS7"
            ? !!value
            : true;
        }
      )
      .test(
        "time-interval-seconds",
        "Please enter Periodic Time Interval less than or equal to 86400 seconds",
        function (value) {
          return (this.parent.operatorType === "C" ||
            this.parent.operatorType === "C,S") &&
            this.parent.enableFraudManagement ===
              ocCompliance.enableFraudManagement &&
            this.parent.avoidCustRevalidation ===
              ocCompliance.avoidCustRevalidation &&
            this.parent.interfaceType === "SS7"
            ? Number.isInteger(value) && value > 0 && value <= 86400
            : true;
        }
      ),

    allowSpamCount: Yup.number()
      .nullable()
      .typeError("Please enter a valid number")
      .max(50000, "Values must be less than or equal to 50000")
      .min(
        1,
        "Spam Messages limit to raise the alert should be greater than zero"
      ),

    customerStatus: Yup.string()
      .nullable()
      .test(
        "customer-status-required",
        "Please Select Customer status",
        function (value) {
          return this.parent.operatorType !== operatorType.Customer &&
            this.parent.operatorType !== operatorType.Both
            ? true
            : !!value;
        }
      ),

    supplierStatus: Yup.string()
      .nullable()
      .test(
        "supplier-status-required",
        "Please Select Supplier status",
        function (value) {
          return this.parent.operatorType === operatorType.Supplier ||
            this.parent.operatorType === operatorType.Both
            ? !!value
            : true;
        }
      ),

    trafficVariation: Yup.number()
      .nullable()
      .typeError("Please enter number")
      .max(100, "Values must be less than or equal to 100.")
      .min(
        1,
        "Percentage variation in traffic to raise the alert should be greater than zero."
      ),
  });
};
