import { useEffect, useState } from "react";
import { Tabs, Tab, Box, IconButton, withTheme } from "@mui/material";
import theme from "../../../tailwind-theme";

function CustomTab({
  tabs,
  defaultTab,
  onChange = false,
  backgroundColor,
  color,
  enableCloseIcon,
  onClose,
  indicatorWidth,
  selectedColor,
  isCreatePopup,
}) {
  const [selectedTab, setSelectedTab] = useState(defaultTab);

  const handleTabChange = (event, newValue) => {
    if (onChange) {
      onChange(event, newValue);
    }
    setSelectedTab(isCreatePopup ? 0 : newValue);
  };

  useEffect(() => setSelectedTab(defaultTab), [defaultTab]);

  const getTabColor = (index) => {
    // if (index < selectedTab) return `${theme.borderColor.tabColor}`;
    if (index === selectedTab) return `${theme.textColor.headerColor}`;
    return `${color && color ? color : theme.textColor.tabTextColor}`;
  };

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        flexDirection: "row",
        // borderRadius: "2px",
      }}
    >
      <Tabs
        value={selectedTab}
        onChange={handleTabChange}
        variant="scrollable"
        scrollButtons="auto"
        sx={{
          width: enableCloseIcon ? "94%" : "100%",
          backgroundColor: `${
            backgroundColor && backgroundColor ? backgroundColor : "white"
          }`,

          borderTopLeftRadius: "4px",
          borderTopRightRadius: "4px",
          height: "52px",
          color: `${color && color ? color : "#808080"}`,
          "& .MuiTabs-indicator": {
            backgroundColor: "#F04E24",
            height: "2px",
          },

          "& .MuiTabScrollButton-root": {
            width: "20px",
          },
          "& .MuiTab-root": {
            minWidth: "0",
            width: `${
              indicatorWidth && indicatorWidth ? indicatorWidth : "fit-content"
            }`,
          },
          "& .MuiButtonBase-root.MuiTab-root.Mui-selected": {
            color: `${
              selectedColor && selectedColor
                ? selectedColor
                : theme.textColor.headerColor
            }`,
          },
          justifyContent: "space-between",
        }}
      >
        {tabs.map((tab, index) => (
          <Tab
            key={index}
            label={tab.label ? tab?.label : tab}
            sx={{
              textTransform: "none",
              fontSize: "14px",
              fontFamily: `"Open Sans", sans-serif`,
              color: getTabColor(index),
              fontWeight: "700",
              paddingBottom: 0,
            }}
          />
        ))}
      </Tabs>
      <Box style={{ border: "", borderRadius: "4px" }}>
        {tabs.map((tab, index) => (
          <div key={index}></div>
        ))}
      </Box>
      {enableCloseIcon && <IconButton sx={{ width: "6%" }}></IconButton>}
    </Box>
  );
}

export default CustomTab;
