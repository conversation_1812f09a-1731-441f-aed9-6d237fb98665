{"moduleName": "SMPP Account", "header": "List of SMPP account", "buttonName": "+ Add SMPP account ", "formType": "stepper", "moduleData": "smpp-account", "forms": [{"formName": "Account information", "elements": [{"name": "protocol", "title": "Protocol", "size": 6, "type": "select", "defaultValue": "", "options": [{"label": "SMPP", "value": "SMPP"}]}, {"name": "systemType", "title": "System type", "size": 6, "type": "text", "defaultValue": ""}, {"name": "addPort", "title": "Port", "size": 6, "type": "multiselect", "isMandatory": true, "isAddButton": true, "onClickPath": "port", "apiOptions": true, "defaultValue": []}, {"name": "systemId", "title": "System ID", "size": 6, "type": "text", "defaultValue": ""}, {"name": "accountType", "title": "Account type", "size": 6, "type": "switch", "defaultValue": true}, {"name": "maximumTransmit", "title": "Maximum transmit (MSLA)", "size": 6, "type": "text", "defaultValue": ""}, {"name": "password", "title": "Password", "size": 6, "type": "text", "defaultValue": ""}, {"name": "ConfirmPassword", "title": "Confirm password", "size": 6, "type": "text", "defaultValue": ""}, {"name": "dateOfActivation", "title": "Date of activation", "size": 6, "type": "date", "defaultValue": ""}, {"name": "dateOfExpiry", "title": "Date of expiry", "size": 6, "type": "date", "defaultValue": ""}, {"name": "defaultAddress", "title": "Default address", "size": 6, "type": "text", "defaultValue": ""}, {"name": "", "title": "", "size": 6, "type": "none"}, {"name": "maximumPriority", "title": "Maximum priority", "size": 6, "type": "text", "defaultValue": ""}, {"name": "", "title": "", "size": 6, "type": "none"}, {"name": "retryFlagStatus", "title": "Retry flag status", "size": 6, "type": "switch", "defaultValue": true}, {"name": "binaryNumberStatus", "title": "Binary number status", "size": 6, "type": "switch", "defaultValue": true}, {"name": "allService", "title": "All service no allowed status ", "size": 6, "type": "switch", "defaultValue": true}, {"name": "alphaNumericAllowed", "title": "Alpha numeric allowed", "size": 6, "type": "switch", "defaultValue": true}, {"name": "operatorType", "title": "Operator type", "size": 6, "type": "select", "defaultValue": "", "options": [{"label": "", "value": ""}]}]}, {"formName": "Message information", "elements": [{"name": "msgAcceptanceCriteria", "title": "Message acceptance criteria", "size": 6, "type": "radio", "defaultValue": "Truncate", "labelList": ["Truncate", "Reject"]}, {"name": "gsmConversionRequestAt", "title": "GSM conversion request(AT)", "size": 6, "type": "switch", "defaultValue": true}, {"name": "gsmConversionRequestAo", "title": "GSM conversion request (AO)", "size": 6, "type": "switch", "defaultValue": true}, {"name": "atValidity", "title": "AT validity period (secs)", "size": 6, "type": "text", "defaultValue": ""}, {"name": "moATValidity", "title": "MO-AT validity period(secs)", "size": 6, "type": "text", "defaultValue": ""}, {"name": "deliveryReport", "title": "Delivery report (AT-MO) status", "size": 6, "type": "switch", "defaultValue": true}, {"name": "deliveryReportMoAt", "title": "Delivery report (MO-AT) status", "size": 6, "type": "switch", "defaultValue": true}, {"name": "storeAndForwardEnable", "title": "Store and forward enable", "size": 6, "type": "radio", "defaultValue": "Send", "labelList": ["Send", "Send and receive"]}, {"name": "outBindStatus", "title": "Out bind status", "size": 6, "type": "switch", "defaultValue": true}, {"name": "previlageFeatureStatus", "title": "Previlage feature status", "size": 6, "type": "switch", "defaultValue": true}, {"name": "supportDataStatus", "title": "Support data_sm status", "size": 6, "type": "switch", "defaultValue": true}, {"name": "oneMobileStatus", "title": "One mobile status", "size": 6, "type": "switch", "defaultValue": true}, {"name": "sarFeatureStatus", "title": "SAR feature status", "size": 6, "type": "switch", "defaultValue": true}, {"name": "senderPrefix", "title": "Sender prefix", "size": 6, "type": "text", "defaultValue": ""}, {"name": "senderPrefixStatus", "title": "Sender prefix status", "size": 6, "type": "switch", "defaultValue": true}, {"name": "msgTypeId", "title": "Message type ID", "size": 6, "type": "select", "defaultValue": "", "options": [{"label": "", "value": ""}]}, {"name": "nickNameStatus", "title": "Nick name status", "size": 6, "type": "switch", "defaultValue": true}, {"name": "cellBroadcastStatus", "title": "Cell broadcast status", "size": 6, "type": "switch", "defaultValue": true}]}, {"formName": "Capacity information", "elements": [{"name": "messagesPerSecond", "title": "Messages per second", "size": 6, "type": "text", "defaultValue": ""}, {"name": "messagesPerMinute", "title": "Messages per minute", "size": 6, "type": "text", "defaultValue": ""}, {"name": "messagesPerHour", "title": "Messages per hour", "size": 6, "type": "text", "defaultValue": ""}, {"name": "messagesPerDay", "title": "Messages per day", "size": 6, "type": "text", "defaultValue": ""}, {"name": "messageLength", "title": "Message length", "size": 6, "type": "text", "defaultValue": ""}, {"name": "atWindowSize", "title": "AT window size", "size": 6, "type": "text", "defaultValue": ""}, {"name": "esmePrepaidStatus", "title": "ESME prepaid status", "size": 3, "type": "switch", "defaultValue": true}, {"name": "rejectAsThrottlePerSec", "title": "Reject as throttle per sec", "size": 3, "type": "switch", "defaultValue": true}]}]}