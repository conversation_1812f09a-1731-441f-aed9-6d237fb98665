export const trimName = (name) => {
  const start = name.slice(0, Math.floor(15 / 2));
  const end = name.slice(-Math.floor(15 / 2));
  const trimedName = `${start}...${end}`;
  return trimedName;
};

export const trimReason = (entry) => {
  let reasonParts = [];
  const mappedLines = entry
    .split("\n")
    .filter((line) => line.includes("Mapped with"));

  mappedLines.forEach((line) => {
    const nameMatch = line.match(/'([^']+)'/);
    if (nameMatch && nameMatch[1].length > 15) {
      const fullName = nameMatch[1];
      const start = fullName.slice(0, Math.floor(15 / 2));
      const end = fullName.slice(-Math.floor(15 / 2));
      const trimmedName = `${start}...${end}`;
      reasonParts.push(line.replace(fullName, trimmedName));
    } else {
      reasonParts.push(line.trim());
    }
  });
  return reasonParts;
};
