import "react-phone-input-2/lib/high-res.css";
import PhoneInput from "react-phone-input-2";
import { useField, useFormikContext, ErrorMessage, Field } from "formik";
import { useTranslation } from "react-i18next";
const PhoneNumberInput = (props) => {
  const { name, label, ...rest } = props;
  const { t } = useTranslation();
  const [field, meta] = useField(name);
  const { setFieldTouched, setFieldValue } = useFormikContext();
  const styles = {
    mobileField:
      "w-full h-[40px] rounded-xl pl-20 border-0 font-sans text-sm text-headingColor opacity-80",
  };

  return (
    <Field name={name} id={name} type="text">
      {({ field: { value } }) => (
        <div>
          {props.label && (
            <div className="text-headingColor font-medium text-sm mb-1 opacity-80">
              {t(props.label)}
              {rest.isMandatory ? (
                <span className="text-asteriskColor text-xs"> *</span>
              ) : null}
            </div>
          )}
          <PhoneInput
            country="in"
            name={name}
            inputProps={{ name: name }}
            preferredCountries={["in", "us"]}
            //placeholder="Enter mobile number"
            label={label}
            value={value}
            inputClass={styles.mobileField}
            onChange={async (value, country, e, formattedValue, name) => {
              const { format, dialCode } = country;

              if (props.countryName && props.countryName !== country?.name) {
                setFieldValue(props.name, dialCode);
                setFieldValue("format", format);
                setFieldValue("dialCode", dialCode);
                setFieldValue("formattedValue", formattedValue);
                setFieldValue("countryName", country.name);
              } else {
                setFieldValue(props.name, value);
                setFieldValue("format", format);
                setFieldValue("dialCode", dialCode);
                setFieldValue("formattedValue", formattedValue);
                setFieldValue("countryName", country.name);
              }
            }}
            // onChange={props.onChange}
            onBlur={() => setFieldTouched(name, true)}
            containerClass={`${
              meta?.error && meta?.touched
                ? `border-[#d32f2f]`
                : `border-[#666666]`
            } border border-solid  hover:border-black hover:border-2 hover:-m-[1px] focus:border-borderPrimary rounded-md text-base`}
            buttonClass="border-0 border-r bg-white border-r-[#666666] hover:border-red rounded-l-xl px-3 scale-2 font-medium text-base"
            disableSearchIcon={true}
            countryCodeEditable={false}
            autoFormat={true}
            enableSearch={true}
          />
          <ErrorMessage
            component="div"
            className="text-[#D32F2F] absolute text-[11px] mt-0.5"
            name={name}
          />
        </div>
      )}
    </Field>
  );
};

export default PhoneNumberInput;
