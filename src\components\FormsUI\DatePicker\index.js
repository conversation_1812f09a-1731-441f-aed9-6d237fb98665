import { useField, useFormikContext } from "formik";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { useEffect, useState } from "react";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import theme from "../../../tailwind-theme";

dayjs.extend(utc);
dayjs.extend(timezone);

const DatePickerFieldWrapper = ({
  name,
  label,
  maxDate,
  labelClassName,
  labelColor,
  isMandatory,
  isDisabled,
  defaultValue,
  action,
  isCurrentDate,
  minDate,
  fromTmrwDate,
  showMinDate,
  ...rest
}) => {
  const [field, meta] = useField(name);
  const { setFieldValue, setFieldTouched } = useFormikContext();
  const [selectedValue, setSelectedValue] = useState(null);

  useEffect(() => {
    if (action === "add" && defaultValue) {
      setSelectedValue(dayjs());
    } else if (isCurrentDate && action === "edit") {
      setSelectedValue(dayjs());
    } else if (field.value) {
      if (field.value.endsWith("Z")) {
        setSelectedValue(dayjs(field.value).utc().local());
      } else {
        setSelectedValue(dayjs(field.value));
      }
    }
  }, [field.value, defaultValue, isCurrentDate, action]);

  const handleDateChange = (val) => {
    setSelectedValue(val);
    setFieldValue(name, val ? val.format("YYYY-MM-DD") : "");
  };

  const configDateField = {
    ...field,
    ...rest,
    fullWidth: true,
    InputLabelProps: { shrink: true },
  };

  if (meta && meta.touched && meta.error) {
    configDateField.error = true;
    configDateField.helperText = meta.error;
  }

  return (
    <div>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          className="w-full"
          maxDate={maxDate}
          format={"DD-MM-YYYY"}
          minDate={
            minDate === true
              ? dayjs()
              : fromTmrwDate === true
              ? dayjs().add(2, "day")
              : showMinDate
              ? dayjs(showMinDate).add(1, "day")
              : rest.minDate
          }
          value={selectedValue ? dayjs(selectedValue) : null}
          disabled={isDisabled}
          onChange={handleDateChange}
          sx={{
            "& label.Mui-focused": {
              color: "#2D2D2D",
            },
            "& .MuiOutlinedInput-root": {
              height: "40px",
              fontSize: "12px",
              fontWeight: "500",
              backgroundColor: "#F3F3F3",
              flexDirection: "row-reverse",
              "& .MuiOutlinedInput-notchedOutline": {
                border:
                  meta.error && meta.touched
                    ? `1px solid ${theme.borderColor.errorBorder} !important`
                    : "1px solid #808080 !important",
                borderRadius: "10px",
              },
              "&:hover fieldset": {
                border:
                  meta.error && meta.touched
                    ? `1px solid ${theme.borderColor.errorBorder} !important`
                    : "1px solid #808080 !important",
              },
              "&.Mui-focused fieldset": {
                border:
                  meta.error && meta.touched
                    ? `1px solid ${theme.borderColor.errorBorder} !important`
                    : "1px solid #808080 !important",
              },
            },
          }}
        />
      </LocalizationProvider>

      {meta.error && meta.touched && (
        <p className="text-[11px] text-[#D32F2F] mt-0.5">{meta.error}</p>
      )}
    </div>
  );
};

export default DatePickerFieldWrapper;
