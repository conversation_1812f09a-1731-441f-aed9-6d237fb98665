import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import Logo from "../icons/logo.svg";
import ForgotPasswordForm from "../components/Forms/ForgotPasswordForm";
import CustomCard from "../components/Card/CustomCard";
import { CloseIcon } from "../icons";
import { useNavigate } from "react-router-dom";

function ForgotPassword() {
  const [showSuccess, setShowSuccess] = useState(false);
  const navigate = useNavigate();

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900 font-sans">
      <Link
        to="/"
        className="pl-8 pr-8 text-xl font-bold text-gray-800 dark:text-gray-200 bg-white  dark:bg-white-900"
      >
        <img
          width={"105px"}
          aria-hidden="true"
          src={Logo}
          alt="Airtel SMS Hub Reporting"
        />
      </Link>
      <div className="flex flex-1 h-full items-center lg:mt-0">
        <div className="flex-1 h-full max-w-xl mx-auto overflow-hidden bg-white rounded-lg shadow-xl dark:bg-gray-800">
          <main className="flex items-center justify-center p-6 sm:p-12 md:flex-row">
            <div className="w-full">
              <CloseIcon
                className="w-3 h-3 cursor-pointer float-right"
                onClick={() => {
                  navigate("/auth/login");
                }}
              />
              <h1
                className="mb-2 text-xl font-semibold text-gray-700 dark:text-gray-200"
                style={{ textAlign: "center" }}
              >
                {showSuccess ? "Email Sent" : "Forgot password"}
                <hr />
              </h1>

              <br />

              {showSuccess ? (
                <CustomCard />
              ) : (
                <ForgotPasswordForm setShowSuccess={setShowSuccess} />
              )}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}

export default ForgotPassword;
