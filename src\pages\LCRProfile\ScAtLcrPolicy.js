import React from "react";
import ScAtLcrpolicyForm from "../../components/Forms/ScAtLcrpolicyForm";
import FormLayout from "../../components/FormLayout/FormLayout";
import { useLocation } from "react-router-dom";
import { moduleConfiguration } from "../../common/constants";

function ScAtLcrPolicy() {
  const {
    state: {
      isEdit,
      id,
      parentId,
      dropdownDetails,
      isViewLCR,
      lcrTypeValue,
    } = {},
  } = useLocation();
  return (
    <div className="mx-5 my-8">
      <FormLayout
        lcrDetails={moduleConfiguration.scAtLCRModuleName}
        title={isEdit ? "Edit SC AT LCR" : "Add SC AT LCR"}
        header="List of SC AT LCR"
      >
        <ScAtLcrpolicyForm
          isEdit={isEdit}
          id={id}
          parentId={parentId}
          dropdownDetails={dropdownDetails}
          isViewLCR={isViewLCR}
          lcrTypeValue={lcrTypeValue}
        />
      </FormLayout>
    </div>
  );
}

export default ScAtLcrPolicy;
