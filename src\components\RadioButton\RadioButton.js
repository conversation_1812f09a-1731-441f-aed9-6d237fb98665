import React, { useEffect } from "react";
import RadioGroup, { useRadioGroup } from "@mui/material/RadioGroup";
import FormControlLabel from "@mui/material/FormControlLabel";
import FormControl from "@mui/material/FormControl";
import { Box, Stack } from "@mui/material";
import Radio from "@mui/material/Radio";
import { styled } from "@mui/system";
import PropTypes from "prop-types";
import { useField, useFormikContext } from "formik";

const StyledFormControlLabel = styled((props) => (
  <FormControlLabel {...props} />
))(({ theme, checked }) => ({
  "& .MuiFormControlLabel-label": {
    color: "black",

    fontWeight: 400,
    opacity: 1,
  },

  [theme.breakpoints.up("sm")]: {
    "& .MuiFormControlLabel-label": {
      fontWeight: 400,
      fontSize: "14px",
    },
  },
}));

function MyFormControlLabel(props) {
  const radioGroup = useRadioGroup();
  let checked = false;
  if (radioGroup) {
    // Using String conversion to handle both string and numeric comparisons
    checked = String(radioGroup.value) === String(props.value);
  }
  return <StyledFormControlLabel checked={checked} {...props} />;
}
MyFormControlLabel.propTypes = {
  value: PropTypes.any,
};

function CustomRadioButton(props) {
  const [field, meta] = useField(props.name);

  const { setFieldValue, setFieldTouched } = useFormikContext();
  const {
    radioInputLabel,
    labelList,
    mandantory,
    inputName,
    labelWidth,
    disabled,
    titleWeight,
    options,
    isDisabled,
    defaultValue,
    moduleName,
  } = props;
  const Styles = {
    radioStyle: {
      "&, &.Mui-checked": {
        color: "#3576EB",
        borderColor: "black",
      },
      "& .MuiSvgIcon-root": {
        fontSize: "15px",
      },
    },
    radioLabelStyle: {
      //  textTransform: "capitalize",
      fontSize: "14px !important",
      width: labelWidth ? labelWidth : "auto",
      // marginLeft: marginLeft ? marginLeft : "0px",
    },
    titleStyle: {
      color: "black",
      //  fontFamily: "Space Grotesk",
      fontSize: "14px",
      fontWeight: titleWeight ? titleWeight : "500",
      paddingBottom: "8px",
    },
    mandatoryStyle: { color: "red", fontSize: "14px" },
  };

  useEffect(() => {
    if (!moduleName === "operators") {
      if (field.value === undefined || field.value === "") {
        setFieldValue(props.name, defaultValue);
      }
    }
  }, [defaultValue, field.value, props.name, setFieldValue]);

  return (
    <Box>
      {radioInputLabel ? (
        <Stack style={{ ...Styles.titleStyle }}>
          {radioInputLabel ? radioInputLabel : ""}
          {mandantory && <span style={{ ...Styles.mandatoryStyle }}>*</span>}
        </Stack>
      ) : (
        ""
      )}
      <FormControl>
        <RadioGroup
          row
          name={inputName}
          onChange={(e) => {
            if (!isDisabled) {
              const selectedOption = options.find(
                (option) => String(option.value) === e.target.value
              );
              setFieldValue(props.name, selectedOption?.value);
            }
          }}
          /* defaultValue={defaultValue ? defaultValue : ""} */
        >
          {options &&
            options.length > 0 &&
            options.map((itemLabel, i) => {
              return (
                <MyFormControlLabel
                  sx={{ ...Styles.radioLabelStyle }}
                  label={itemLabel.label}
                  key={i}
                  control={
                    <Radio
                      name={inputName}
                      sx={{
                        "& .MuiSvgIcon-root:not(.MuiSvgIcon-root ~ .MuiSvgIcon-root)":
                          {
                            color: "#000000",
                          },
                        "& .MuiSvgIcon-root + .MuiSvgIcon-root": {
                          color: "#3576EB",
                        },
                        "& .MuiSvgIcon-root": {
                          fontSize: "15px",
                          width: "20px",
                          height: "20px",
                          borderRadius: "1px",
                        },
                      }}
                      checked={String(field.value) === String(itemLabel.value)}
                      value={String(itemLabel.value)}
                      onChange={(e) => {
                        if (!isDisabled) {
                          const selectedOption = options.find(
                            (option) => String(option.value) === e.target.value
                          );
                          setFieldValue(props.name, selectedOption?.value);
                        }
                      }}
                      disabled={disabled}
                    />
                  }
                />
              );
            })}
        </RadioGroup>
      </FormControl>
    </Box>
  );
}
CustomRadioButton.propTypes = {
  name: PropTypes.string.isRequired,
  radioInputLabel: PropTypes.string,
  labelList: PropTypes.array,
  mandantory: PropTypes.bool,
  inputName: PropTypes.string.isRequired,
  labelWidth: PropTypes.string,
  disabled: PropTypes.bool,
  titleWeight: PropTypes.number,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
        .isRequired,
    })
  ).isRequired,
  defaultValue: PropTypes.any,
};
export default CustomRadioButton;
