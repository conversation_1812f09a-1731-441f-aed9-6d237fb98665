import React, { useContext, useState } from "react";
import BreadcrumbNavigation from "../components/BreadCrumbNavigation";
import { useQuery } from "react-query";
import { getPageSchema } from "../lib/lcr-profile-api";
import { AuthContext } from "../context/AuthContext";
import ConfirmationModal from "../components/Dialog/ConfirmationModel";

function HomePage() {
  const [details, setDetails] = useState([]);
  const [file, setFile] = useState("");
  const urlBase = process.env.REACT_APP_API_URL;
  let { reset, logout, setReset } = useContext(AuthContext);

  useQuery(["home-page"], getPageSchema, {
    refetchOnWindowFocus: false,
    onSuccess: ({ data }) => {
      setDetails(data?.data?.[0]?.attributes?.schemadetails);
      setFile(data?.data?.[0]?.attributes?.file?.data?.attributes?.url);
    },
  });

  return (
    <div className="h-[84vh] flex flex-col">
      <BreadcrumbNavigation title={"Home page"} />
      <div className="relative w-full h-full my-2 rounded-2xl border-[30px] border-homePageBorder flex items-center justify-center text-center font-bold">
        <div className="flex flex-col">
          <div className="text-xm mb-5">{details.title}</div>
          <div className="text-2xl mt-5 text-textNAColor">
            {details.subtitle}
          </div>
          <img
            src={`${urlBase}/${file}`}
            alt={"Home Page"}
            className="absolute bottom-0 right-0 m-2 w-[150px] h-auto"
          />
        </div>
      </div>
      {reset && (
        <ConfirmationModal
          show={true}
          onHide={() => {
            reset = false;
          }}
          cancelHide="true"
          align="center"
          confirmButonName="Okay"
          onConfirm={() => {
            setReset(false);
            logout();
          }}
          body={
            "We have sent an email to reset your password in your registered mailid. Kindly change your password."
          }
          title={"Reset Your Password"}
        />
      )}
    </div>
  );
}

export default HomePage;
