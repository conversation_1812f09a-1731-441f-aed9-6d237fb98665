import React from "react";
import { Link } from "react-router-dom";
import LoginForm from "../components/Forms/LoginForm";

function Login() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <Link
        to="/"
        className="pl-8 pr-8 text-xl font-bold text-gray-800 bg-white"
      >
        <div className="bg-logo h-[70px] w-[150px] bg-contain bg-no-repeat" />
      </Link>

      <div className="flex flex-1 items-center justify-center">
        <main className="max-w-xl w-full bg-white rounded-lg shadow-xl p-6 sm:p-12">
          <h1 className="mb-4 text-2xl font-semibold text-gray-700 text-center">
            Welcome User!
          </h1>
          <hr className="mb-6" />

          <LoginForm />

          <hr className="mt-6" />
        </main>
      </div>
    </div>
  );
}

export default Login;
