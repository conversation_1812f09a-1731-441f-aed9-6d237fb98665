import React, { useState } from "react";
import { Field, FieldArray, ErrorMessage } from "formik";
import ResultPerPageComponent from "../Pagination/ResultPerPage";
import Pagination from "../Pagination/Pagination";
import { PlusCircleIcon, DeleteIcon } from "../../icons";
import DeleteDialog from "../Dialog/DeleteDialog";
import SuccessDialog from "../Dialog/SuccessDialog";

const MAX_ROWS = 98;

const FieldArrayWithPagination = ({
  values = { retryPolicies: [] },
  errors,
  touched,
  headers,
  isDisabled,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [limitPerPage, setLimitPerPage] = useState(10);
  const [resultPerPage, setResultPerPage] = useState(
    Array.from({ length: 5 }, (_, i) => ({
      name: (i + 1).toString(),
      values: (i + 1).toString(),
    }))
  );
  const [hovered, setHovered] = useState(false);
  const [tooltipMessage, setTooltipMessage] = useState("");
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [rowToDelete, setRowToDelete] = useState(null);

  const allFieldsFilled =
    Array.isArray(values?.retryPolicies) &&
    values.retryPolicies.every(
      (policy) => policy.retryInterval1 !== "" && policy.retryInterval2 !== ""
    );

  const totalCount = values?.retryPolicies?.length || 1;
  const startIndex = (currentPage - 1) * limitPerPage;
  const endIndex = Math.min(startIndex + limitPerPage, totalCount);

  const isAddDisabled =
    !allFieldsFilled || values.retryPolicies?.length * 2 >= MAX_ROWS;
  const isLastPage = currentPage === Math.ceil(totalCount / limitPerPage);

  const onDeleteClick = (startIndex, index, remove) => {
    setRowToDelete(() => () => remove(startIndex + index));
    setDeleteDialog(true);
  };
  const handleDeleteConfirm = () => {
    if (rowToDelete) {
      rowToDelete();
      setRowToDelete(null);
      setDeleteDialog(false);
      setSuccessDialog(true);
      setMessage("Retry intervals deleted successfully");
    }
    if (startIndex === endIndex - 1) {
      setCurrentPage(currentPage - 1);
    }
  };
  const handleLimitChange = (e) => {
    setLimitPerPage(parseInt(e.target.value));
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleMouseEnter = () => {
    if (values.retryPolicies?.length * 2 >= MAX_ROWS) {
      setTooltipMessage("Max rows allowed is 98");
    } else if (!allFieldsFilled) {
      setTooltipMessage("Fill the empty fields to add new row");
    } else {
      setTooltipMessage("");
    }
    setHovered(true);
  };

  const handleMouseLeave = () => {
    setHovered(false);
  };
  const rows = Array.isArray(values?.retryPolicies)
    ? values?.retryPolicies.filter((row) => row !== null && row !== undefined)
    : [];
  const totalRows = rows.length;
  return (
    <>
      <FieldArray name="retryPolicies">
        {({ insert, remove, push }) => (
          <div className="space-y-4">
            <table className="min-w-full border border-grey-300 relative">
              <thead className="bg-[#37415033]">
                <tr>
                  {headers.map((header, index) => (
                    <th className="p-2" key={index}>
                      {header}
                    </th>
                  ))}
                  <th className=""></th>
                </tr>
              </thead>
              <tbody>
                {Array.isArray(values?.retryPolicies) &&
                  values.retryPolicies
                    .slice(startIndex, endIndex)
                    .map((policy, index) => (
                      <React.Fragment key={startIndex + index}>
                        <tr>
                          <td className="pl-8">
                            {currentPage === 1
                              ? startIndex + index * 2 + 1
                              : startIndex * 2 + index * 2 + 1}
                          </td>
                          <td className="pl-14">
                            <Field
                              name={`retryPolicies.${
                                startIndex + index
                              }.retryInterval1`}
                              className="w-3/4 p-2 border border-gray-300 rounded"
                              disabled={isDisabled}
                            />
                            <ErrorMessage
                              name={`retryPolicies.${
                                startIndex + index
                              }.retryInterval1`}
                              component="div"
                              className="text-[#DC3833] text-sm mt-1"
                            />
                          </td>
                          <td className="pl-8 bg-[#EFEFEF]">
                            {currentPage === 1
                              ? startIndex + index * 2 + 2
                              : startIndex * 2 + index * 2 + 2}
                          </td>
                          <td className="pl-14 bg-[#EFEFEF]">
                            <Field
                              name={`retryPolicies.${
                                startIndex + index
                              }.retryInterval2`}
                              className="w-3/4 p-2 border border-gray-300 rounded"
                              disabled={isDisabled}
                            />
                            <ErrorMessage
                              name={`retryPolicies.${
                                startIndex + index
                              }.retryInterval2`}
                              component="div"
                              className="text-[#DC3833] text-sm mt-1"
                            />
                          </td>
                          <td className="p-2 bg-[#EFEFEF]">
                            <DeleteIcon
                              onClick={
                                isDisabled || totalCount === 1
                                  ? null
                                  : () =>
                                      onDeleteClick(startIndex, index, remove)
                              }
                              style={{
                                cursor:
                                  isDisabled || totalCount === 1
                                    ? "not-allowed"
                                    : "pointer",
                              }}
                            />
                          </td>
                        </tr>
                      </React.Fragment>
                    ))}

                {isLastPage && (
                  <tr>
                    <td className="pl-8">{totalCount * 2 - 1 + 2}</td>
                    <td className="pl-14">
                      <input
                        type="text"
                        disabled
                        className="w-3/4 p-2 border border-dashed border-gray-400 rounded bg-gray-100"
                      />
                    </td>
                    <td className="pl-8 bg-[#EFEFEF]">{totalCount * 2 + 2}</td>
                    <td className="pl-14 bg-[#EFEFEF]">
                      <input
                        type="text"
                        disabled
                        className="w-3/4 p-2 border border-dashed border-gray-400 rounded bg-gray-100"
                      />
                    </td>
                    <td className="p-2 bg-[#EFEFEF] relative">
                      <PlusCircleIcon
                        className={`${
                          !isAddDisabled && !isDisabled
                            ? "pointer"
                            : "cursor-not-allowed"
                        }`}
                        onClick={() => {
                          if (!isAddDisabled && !isDisabled) {
                            push({ retryInterval1: "", retryInterval2: "" });
                            const lastPage = Math.ceil(
                              (totalRows + 1) / limitPerPage
                            );
                            setCurrentPage(lastPage);
                          }
                        }}
                        onMouseEnter={handleMouseEnter}
                        onMouseLeave={handleMouseLeave}
                      />
                    </td>
                  </tr>
                )}
              </tbody>
            </table>

            <div className="flex justify-end  w-full h-10 mt-4">
              {hovered && (
                <div className="p-2 text-sm text-[#DC3833] ml-auto">
                  {!isDisabled ? tooltipMessage : null}
                </div>
              )}
            </div>
          </div>
        )}
      </FieldArray>

      <div className="flex justify-between items-center mt-4 w-full px-4">
        <div className="flex items-center">
          <ResultPerPageComponent
            countPerPage={resultPerPage}
            limit={limitPerPage}
            handleLimitChange={handleLimitChange}
          />
          <span className="ml-4 text-gray-600 text-sm">
            {startIndex + 1} - {endIndex} of {totalCount} rows
          </span>
        </div>
        <Pagination
          className="pagination-bar"
          currentPage={currentPage}
          totalCount={totalCount}
          pageSize={limitPerPage}
          onPageChange={handlePageChange}
        />
      </div>
      <DeleteDialog
        show={deleteDialog}
        onHide={() => {
          setDeleteDialog(false);
        }}
        onConfirm={handleDeleteConfirm}
        title={
          <>
            Are you sure you want to delete?
            <br />
            (Deletion will only happen on click of save)
          </>
        }
      />
      <SuccessDialog
        show={successDialog}
        onHide={() => {
          setSuccessDialog(false);
        }}
        message={message}
      />
    </>
  );
};

export default FieldArrayWithPagination;
