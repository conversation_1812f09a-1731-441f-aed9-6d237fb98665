import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import { CheckMarkIcon, CloseIcon, ErrorIcon, SuccessIcon } from "../../icons";
import Button from "../Buttons/Button";

function SuccessDialog({ show, onHide, message, buttonName }) {
  const IconComponent = CheckMarkIcon;

  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 411,
          maxHeight: 435,
          fontFamily: "OpenSansHebrew",
          borderRadius: "10px",
        },
      }}
      maxWidth="xs"
      open={show}
      onClose={onHide}
      className="p-6 font-sans"
    >
      <DialogTitle className="mt-2 flex justify-end">
        <CloseIcon className="w-2.5 h-2.5 cursor-pointer" onClick={onHide} />
      </DialogTitle>

      <div className="flex justify-center items-center mt-2">
        <SuccessIcon />
      </div>

      <div className="text-base mt-4 mx-auto text-center px-4">{message}</div>
      <div>
        <div className="text-center mt-10 gap-5 mb-4">
          <Button
            style={{ fontFamily: "Open Sans Hebrew" }}
            label={buttonName || "Okay"}
            buttonClassName="w-full md:w-[111px] h-[30px] text-sm rounded-[20px]"
            onClick={() => {
              onHide();
            }}
          />
        </div>
      </div>
    </Dialog>
  );
}

export default SuccessDialog;
