import { useState } from "react";
import dayjs from "dayjs";
import { DayPicker } from "react-day-picker";
import { CloseIcon } from "../icons";
import "react-day-picker/dist/style.css";
const CalendarComponent = (props) => {
  const [range, setRange] = useState(null);
  const [isApplyClicked, setIsApplyClicked] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  return (
    <div className=" flex justify-center w-full md:w-[218px] h-10 bg-bgTableFilter border border-tableFilter rounded-lg">
      <div className="relative">
        <div
          className="flex gap-2 items-center mt-2 text-sm font-bold"
          onClick={() => setOpenDialog(true)}
        >
          {!isApplyClicked || !range ? (
            <>Select Date</>
          ) : (
            <>
              {dayjs(range?.from).format("DD MMM YY") +
                " - " +
                dayjs(range?.to).format("DD MMM YY")}
            </>
          )}
        </div>

        {openDialog && (
          <>
            <div className="absolute top-6 right-0 px-1 py-1 border border-outerBorder rounded-sm bg-white min-w-[200px] min-h-[218px]  z-10">
              <div className="w-full flex justify-end">
                <CloseIcon
                  className="w-2.5 h-2.5 cursor-pointer"
                  onClick={() => setOpenDialog(false)}
                />
              </div>
              <div className="flex flex-row">
                <DayPicker
                  id="test"
                  mode="range"
                  selected={range}
                  max={new Date()}
                  onSelect={(dates) => {
                    setRange(dates);
                  }}
                />
              </div>

              <div className=" flex flex-row">
                <button
                  className={`mt-2 w-full h-9 text-sm text-blue-600 rounded-[3px] mb-2 cursor-pointer disabled:bg-opacity-40`}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setIsApplyClicked(true);
                    setOpenDialog(false);
                    console.log(range);
                    props?.setDateFilter(range);
                    props.setPageNumber(1);
                  }}
                >
                  Apply
                </button>
                <button
                  className={`mt-2 w-full h-9 text-sm text-red-500 rounded-[3px] mb-2 cursor-pointer disabled:bg-opacity-40`}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setRange(null);
                    props.setPageNumber(1);
                    props?.setDateFilter(null);
                  }}
                >
                  Clear
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
export default CalendarComponent;
