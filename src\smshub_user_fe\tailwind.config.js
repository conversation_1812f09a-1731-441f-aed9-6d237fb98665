const tailwindConfig = require("./src/tailwind.config");

module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  important: "#root",
  theme: {
    extend: {
      fontSize: {
        small: 10,
        xs: 12,
        medium: 15,
        large: 32,
        title: 24,
      },
      backgroundColor: {
        bgTable: "#DDE9FD",
        bgSecondary: "#e31937",
        bgHeader: "#DDE9FD",
        bgField: "#FFFFFF",
        bgSelected: "#D9D9D9",
        bgStepper: "#F28F8F80",
        bgStepperActive: "#EBECED",
        bgChecked: "#3576EB",
        bgSelectedOption: "#c4c4c4",
      },
      textColor: {
        errorColor: "#DC3833",
        headingColor: "#666666",
        headerColor: "#000000",
        textNAColor: "#808080",
      },
      fontWeight: {
        sideBar: 600,
        fields: 500,
      },
      borderColor: {
        tableBorder: "#808080",
        errorBorder: "#DC3833",
      },
      borderRadius: {
        fieldRadius: "10px",
      },
    },
  },
  plugins: [],
};
