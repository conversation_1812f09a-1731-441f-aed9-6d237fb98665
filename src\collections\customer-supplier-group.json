{"header": "List of customer/supplier group", "columns": [{"header": "Group Id", "accessorKey": "id", "filterVariant": "range"}, {"header": "Group name", "accessorKey": "attributes.groupName"}, {"buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"info": "Name for the group", "name": "groupName", "size": 6, "type": "text", "title": "Group name", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the group name"}, {"type": "matches", "regex": "^(?=.*[0-9])(?=.*[A-Za-z])[A-Za-z][A-Za-z0-9_ ]*$", "message": "Only alphanumeric characters,underscore and space are allowed.It should start with an alphabet"}, {"type": "min", "value": 2, "message": "Min length allowed is 2"}, {"type": "max", "value": 20, "message": "Max length allowed is 20"}], "defaultValue": "", "validationType": "string"}, {"info": "Brief description", "name": "groupDesc", "size": 6, "type": "text", "title": "Group description", "isInfo": true, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Min length allowed is 0"}, {"type": "max", "value": 50, "message": "Max length allowed is 50"}], "defaultValue": "", "validationType": "string"}, {"name": "customer_supplier_managements", "size": 6, "type": "multiselect", "title": "Customer/supplier name", "apiOptions": true, "isAddButton": true, "isMandatory": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select the customer/supplier name"}], "defaultValue": [], "validationType": "array"}], "formType": "simple", "buttonName": "+ Add new group", "moduleData": "group-customer-supplier", "moduleName": "Customer/supplier group", "globalSearch": [{"label": "Group id", "value": "id"}, {"label": "Group name", "value": "groupName"}]}