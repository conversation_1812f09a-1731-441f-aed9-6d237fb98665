import React, { useContext, useState, useEffect } from "react";
import { Formik, Form } from "formik";
import Grid from "@mui/material/Grid";
import Button from "../Buttons/OutlinedButton";
import ConfirmNNextButton from "../Buttons/Button";
import * as Yup from "yup";
import { useLocation, useNavigate } from "react-router-dom";
import { useMutation, useQuery } from "react-query";
import { createRecord, updateRecord, getDataById } from "../../lib/list-api";
import SuccessDialog from "../Dialog/SuccessDialog";
import ErrorDialog from "../Dialog/ErrorDialog";
import { multiStepFormContext } from "../../context/MultiStepFormContext";
import {
  renderFields,
  generateValidationSchema,
  getStepperDefaultInitialValues,
} from "../../common/common";
import { moduleConfiguration } from "../../common/constants";
import Toast from "../../components/Toast";
import {
  deleteIndexedDB,
  getIndexedDBDataById,
  initializeIndexedDB,
  updateIndexedDBDataById,
} from "../HelperFunction/localStorage";
import { convertToUTC } from "../../common/utcConversion";
import InputLabel from "../FormsUI/InputLabel";
import { CloseIcon } from "../../icons";

const StepFormTemplate = (props) => {
  const {
    formTitle,
    elements,
    moduleName,
    action,
    id,
    currentStep,
    steps,
    moduleNameValue,
    navigationPath,
    header,
    setStepperData,
  } = props;

  const { handleNextClick, handlePrevClick, setCurrentStep } =
    useContext(multiStepFormContext);
  const { mutate: createRecordAPI, isLoading: createLoading } =
    useMutation(createRecord);
  const { mutate: updateRecordAPI, isLoading: updateLoading } =
    useMutation(updateRecord);
  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErroDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [formData, setFormData] = useState({});
  const [showToast, setShowToaster] = useState(false);
  const [previousFormData, setPreviousFormData] = useState({});
  const [existingData, setExistingData] = useState(null);

  const [allowedValues, setAllowedValues] = useState({});

  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const currentId = searchParams.get("currentId");
  const [deleteLastEntry, setDeleteLastEntry] = useState(false);

  const { extractedData } = location.state || {};

  const fetchData = async (isPrevious) => {
    if (currentId) {
      try {
        const data = await getIndexedDBDataById(
          "navigationDetails",
          "FormDB",
          parseInt(currentId)
        );
        if (isPrevious && data?.data?.length) {
          const lastRecord = data.data.find(
            (ele) => ele.moduleNameValue === moduleName
          );

          if (lastRecord) {
            setPreviousFormData(lastRecord);
            setDeleteLastEntry(true);
          }
        }
        setExistingData(data);
      } catch (error) {
        console.error("Error fetching data from IndexedDB:", error);
      }
    }
  };

  useEffect(() => {
    fetchData(action === "add" ? true : false);
  }, [currentId]);

  const doBeforeNavigate = async (path, forceDelete = false) => {
    if (deleteLastEntry || forceDelete) {
      const currentData = existingData.data.find(
        (entry) => entry.moduleNameValue === moduleName
      );
      existingData.data = existingData.data.filter(
        (entry) => entry.moduleNameValue !== moduleName
      );
      await updateIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        Number(currentId),
        existingData
      );
      if (currentData?.previousPath) {
        navigate(currentData.previousPath);
        return;
      }
    }
    navigate(path);
  };

  const handleButtonClick = async () => {
    if (existingData && existingData.data && existingData.data.length > 0) {
      const lastRecord = existingData.data[existingData.data.length - 1];
      if (lastRecord.moduleNameValue === moduleConfiguration.customerSupplier) {
        setCurrentStep(3);
      } else {
        setCurrentStep(0);
      }
      if (
        !lastRecord?.previousPath &&
        lastRecord?.currentPath === `${location.pathname}${location.search}`
      ) {
        try {
          doBeforeNavigate(`/app/list/${moduleName}`);
          await deleteIndexedDB("navigationDetails");
        } catch (error) {
          console.error("Error deleting database:", error);
        }
      } else if (lastRecord?.currentPath) {
        const fullUrl = window.location.href;
        const appUrl = fullUrl.substring(fullUrl.indexOf("/app"));

        if (lastRecord.currentPath === appUrl) {
          doBeforeNavigate(lastRecord.previousPath, true);
        } else {
          doBeforeNavigate(lastRecord.currentPath);
        }
      }
    } else {
      doBeforeNavigate(`/app/list/${moduleName}`);
    }
  };

  useEffect(() => {
    if (navigationPath) {
      const dbName = "navigationDetails";
      const storeName = "FormDB";

      initializeIndexedDB(dbName, storeName)
        .then((result) => {
          const currentUrl = new URL(window.location.href);

          if (!currentUrl.searchParams.has("currentId")) {
            currentUrl.searchParams.set(
              "currentId",
              result?.id ? result.id : result?.data?.[0]?.id || 1
            );
            navigate(currentUrl.pathname + currentUrl.search, {
              replace: true,
            });
          }
        })
        .catch((error) => {
          console.error("Error initializing IndexedDB:", error);
        });
    }
  }, [navigationPath, navigate]);

  const getHubRuleInput = (newData) => {
    const ruleInput = elements
      .filter(
        (element) =>
          newData[element.name] !== undefined &&
          element.name !== "rule_status" &&
          element.name !== "rule_comment"
      )
      .map((element) => ({
        label: element.name,
        inputValue: newData[element.name].toString(),
        inputType: element.inputType,
        guiInputType: element.guiInputType,
      }));

    return ruleInput;
  };

  const handleSubmit = (values) => {
    setStepperData && setStepperData(values);
    let filteredValues = {};
    Object.keys(values).forEach((key) => {
      if (
        values[key] !== undefined &&
        values[key] !== null &&
        values[key] !== ""
      ) {
        filteredValues[key] = values[key];
      }
    });
    let newData = { ...formData, ...filteredValues };

    delete newData.creationDate;
    delete newData.lastModifiedDate;
    delete newData.createdAt;
    delete newData.publishedAt;
    delete newData.updatedAt;
    delete newData.hostPort;

    const newAllowedValues = {};

    elements.forEach((field) => {
      const visibility = field.visibilityConditions;

      let isVisible = true;
      if (visibility) {
        isVisible = Object.entries(visibility).every(([key, validValues]) =>
          validValues.includes(newData[key])
        );
      }
      if (isVisible) {
        newAllowedValues[field.name] = values[field.name];
      }
    });
    setAllowedValues((prevAllowedValues) => ({
      ...prevAllowedValues,
      ...newAllowedValues,
    }));

    if (
      moduleName === moduleConfiguration.hubRuleConfiguration ||
      moduleName === moduleConfiguration.ruleConfiguration
    ) {
      newData.ruleInput = [...getHubRuleInput(newData)];
    }

    if (newData.DLR && newData.PUSH) {
      newData.templateParams = {
        DLR: newData.DLR || [],
        PUSH: newData.PUSH || [],
      };
      delete newData.DLR;
      delete newData.PUSH;
    }

    setFormData(newData);
    if (currentStep === steps?.length - 1) {
      let reqData = {
        ...(moduleName === moduleConfiguration.operators &&
          extractedData && {
            uploadedFileId: extractedData.uploadedFileId,
          }),
        data:
          moduleName === "esme-accounts"
            ? { ...allowedValues, ...newAllowedValues }
            : newData,
      };
      if (
        moduleName === moduleConfiguration.operators &&
        reqData.data?.uploadedFileId
      ) {
        delete reqData.data.uploadedFileId;
      }
      if (reqData.data.activationDate) {
        reqData.data.activationDate = convertToUTC(
          reqData.data.activationDate,
          "activationDate"
        );
      }

      if (reqData.data.terminationDate) {
        reqData.data.terminationDate = convertToUTC(
          reqData.data.terminationDate,
          "terminationDate"
        );
      }
      if (action === "add") {
        createRecordAPI(
          {
            moduleName:
              moduleName === moduleConfiguration.hubRuleConfiguration ||
              moduleName === moduleConfiguration.ruleConfiguration
                ? moduleConfiguration.rules
                : moduleName,
            reqData,
          },
          {
            onSuccess: (resp) => {
              setSuccessDialog(true);
              setMessage(`${moduleNameValue} created successfully`);
            },
            onError: ({ response }) => {
              setErroDialog(true);
              setMessage(response?.data?.error?.message);
            },
          }
        );
      } else {
        updateRecordAPI(
          {
            moduleName:
              moduleName === moduleConfiguration.hubRuleConfiguration ||
              moduleName === moduleConfiguration.ruleConfiguration
                ? moduleConfiguration.rules
                : moduleName,
            id,
            reqData,
          },
          {
            onSuccess: (resp) => {
              setSuccessDialog(true);
              setMessage(`${moduleNameValue} updated successfully`);
            },
            onError: ({ response }) => {
              setErroDialog(true);
              setMessage(response?.data?.error?.message);
            },
          }
        );
      }
    } else {
      handleNextClick();
    }
  };

  const { isLoading: isFormDataLoading } = useQuery(
    [
      moduleName === moduleConfiguration.hubRuleConfiguration ||
      moduleName === moduleConfiguration.ruleConfiguration
        ? moduleConfiguration.rules
        : moduleName,
      id,
      "getAll",
    ],
    getDataById,
    {
      enabled: id > 0 ? true : false,
      refetchOnWindowFocus: false,
      onSuccess: (resp) => {
        setCurrentStep(0);

        let filteredAttributes = Object.fromEntries(
          Object.entries(resp?.data?.data?.attributes || {}).filter(
            ([_, value]) => value !== null
          )
        );

        setFormData(filteredAttributes);
      },
    }
  );

  useEffect(() => {
    if (extractedData) {
      setFormData((prevData) => ({
        ...prevData,
        ...extractedData.data,
      }));
      if (action === "add") {
        setShowToaster(true);
      }
    }
    if (action !== "add") {
      setShowToaster(false);
    }
  }, [extractedData, action]);

  useEffect(() => {
    if (formData && formData.templateParams) {
      const { DLR = [], PUSH = [] } = formData.templateParams;

      const updatedFormData = {
        ...formData,
        DLR,
        PUSH,
      };

      delete updatedFormData.templateParams;

      setFormData(updatedFormData);
    } else if (formData && formData.jsonString) {
      const {
        ccNdcInfo = [],
        networkNodeGT = [],
        sscpCarrierInfo = [],
        tadicCode = "",
        countryInitials = "",
        organizationName = "",
        networkName = "",
        grxProvidersList = "",
        asnsList = "",
      } = formData.jsonString;

      const updatedFormData = {
        ...formData,
        ccNdcInfo,
        networkNodeGT,
        sscpCarrierInfo,
        tadicCode,
        countryInitials,
        organizationName,
        networkName,
        grxProvidersList,
        asnsList,
      };
      delete updatedFormData.jsonString;
      setFormData(updatedFormData);
    }
  }, [formData]);

  const useDefaultValues =
    (action === "add" && !previousFormData?.formData) ||
    moduleName === "operators" ||
    moduleName === moduleConfiguration.hubRuleConfiguration;

  const flattenConfig = (data) => {
    if (data?.config) {
      const { config, ...rest } = data;
      return { ...rest, ...config };
    }
    return data;
  };
  const initialValues = useDefaultValues
    ? getStepperDefaultInitialValues(
        elements,
        flattenConfig(formData),
        moduleName
      )
    : previousFormData?.formData
    ? getStepperDefaultInitialValues(
        elements,
        previousFormData?.formData,
        moduleName
      )
    : getStepperDefaultInitialValues(elements, formData, moduleName);

  return (
    <div>
      {!isFormDataLoading ? (
        <Formik
          initialValues={initialValues}
          validationSchema={
            action !== "viewData"
              ? Yup.object().shape(generateValidationSchema(elements, formData))
              : null
          }
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({
            values,
            submitForm,
            setFieldValue,
            errors,
            setFieldTouched,
            ...formikProps
          }) => (
            <Form>
              <div className="bg-white h-[50px] z-10 content-center">
                <div className="flex mx-3 my-3">
                  <div className="flex justify-between items-center w-full">
                    <InputLabel
                      label={steps[currentStep]}
                      labelClassName="font-semibold"
                    />
                    <CloseIcon
                      className="w-3 h-4 cursor-pointer ml-auto"
                      onClick={() => handleButtonClick()}
                    />
                  </div>
                </div>
                <div className="mt-3 mx-3 mb-3 border-b-2 border-listBorder " />
              </div>
              <fieldset className="md:m-4 rounded-md">
                <legend className="text-sm">{formTitle}</legend>
                <Grid container spacing={4}>
                  {renderFields(
                    elements,
                    values,
                    setFieldValue,
                    navigate,
                    formData,
                    action,
                    "",
                    "",
                    "",
                    "",
                    moduleName,
                    "",
                    "",
                    previousFormData.formData,
                    header,
                    setFieldTouched,
                    formikProps
                  )}
                </Grid>
              </fieldset>

              <div className="flex justify-center gap-5 pt-5 pb-10">
                {currentStep !== 0 && (
                  <Button
                    label="Back"
                    buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
                    onClick={() => handlePrevClick()}
                  />
                )}

                {currentStep === 0 && (
                  <Button
                    label="Cancel"
                    buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
                    onClick={() => handleButtonClick()}
                  />
                )}

                <ConfirmNNextButton
                  label={
                    action === "viewData" && currentStep === steps?.length - 1
                      ? "Close"
                      : currentStep === steps?.length - 1
                      ? "Save"
                      : "Next"
                  }
                  buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
                  onClick={(e) => {
                    e.preventDefault();
                    if (
                      action === "viewData" &&
                      currentStep === steps?.length - 1
                    ) {
                      navigate(`/app/list/${moduleName}`);
                    } else {
                      // handleSubmitWithFocus(
                      //   validateForm,
                      //   submitForm,
                      //   setTouched
                      // );
                      submitForm();
                    }
                  }}
                  type="submit"
                  loading={createLoading || updateLoading}
                />
              </div>

              <SuccessDialog
                show={successDialog}
                onHide={() => {
                  handleButtonClick();
                  setSuccessDialog(false);
                }}
                message={message}
              ></SuccessDialog>
              <ErrorDialog
                show={errorDialog}
                onHide={() => {
                  setErroDialog(false);
                }}
                message={message}
              ></ErrorDialog>
              {showToast && (
                <Toast message={"XML file uploaded successfully"} />
              )}
            </Form>
          )}
        </Formik>
      ) : null}
    </div>
  );
};

export default StepFormTemplate;
