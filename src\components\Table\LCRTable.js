import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import SearchComponent from "../SearchComponent";
import CustomTable from "../Table";
import SuccessDialog from "../Dialog/SuccessDialog";
import ResultPerPageComponent from "../Pagination/ResultPerPage";
import Pagination from "../Pagination/Pagination";
import useDebounce from "../../common/useDebounce";
import ErrorDialog from "../Dialog/ErrorDialog";
import { useLocation } from "react-router-dom";
import { useMutation, useQuery } from "react-query";
import UploadLCRFile from "../Files/UploadLCRFile";
import {
  LCRGlobalSearch,
  lcrType,
  moduleConfiguration,
  scATLCRGlobalSearch,
} from "../../common/constants";
import {
  deleteParentDetail,
  updateLcr,
  getParentDetails,
} from "../../lib/lcr-profile-api";
import TransformLCRData from "../TransformData/TransformLCRData";
import { getLCRGlobalSearch } from "../../lib/list-api";
import { DEFAULT_PAGE_SIZE } from "../../common/config";
import {
  getLastNumberFromURL,
  getLastUrlSegment,
  getModuleNameFromURL,
} from "../../common/urlUtils";
import { getColumnsConfig } from "../../common/listPageUtils";
import DeleteDialog from "../Dialog/DeleteDialog";
import LCRErrorTableDialog from "../Dialog/LCRErrorTableDialog";

const LCRTable = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { selectedRow, moduleData, dropdownDetails } = location.state || {};

  const [currentPage, setCurrentPage] = useState(1);
  const [limitPerPage, setLimitPerPage] = useState(DEFAULT_PAGE_SIZE);
  const [searchText, setSearchText] = useState("");
  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErroDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [message, setMessage] = useState("");
  const debouncedValue = useDebounce(searchText, 500);
  const [listData, setListData] = useState([]);
  const [file, setFile] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [globalSearchSelect, setGlobalSearchSelect] = useState("");
  const [pagination, setPagination] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [filteredRow, setFilteredRow] = useState("");
  const [selectedIds, setSelectedIds] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [idToDelete, setIdToDelete] = useState(null);
  const [errorData, setErrorData] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const currentModuleName = getModuleNameFromURL(window.location.href);
  const id = getLastUrlSegment(window.location.href);

  const handleLimitChange = (e) => {
    setLimitPerPage(e.target.value);
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const { mutate: updateLCRAPI, isLoading: createLCRLoading } =
    useMutation(updateLcr);

  const { mutate: deleteParentDetailAPI } = useMutation(deleteParentDetail);
  const lcrTypeValue = lcrType.find(
    (item) => selectedRow.attributes.lcrType === item.label
  )?.value;

  const { refetch } = useQuery(
    [
      moduleData === moduleConfiguration.scATLCRImportName
        ? moduleConfiguration.scAtLCRModuleName
        : moduleConfiguration.lcrConfiguration,
      id,
      moduleConfiguration?.parentDetails,
      lcrTypeValue,
      limitPerPage,
      currentPage,
    ],
    getParentDetails,
    {
      enabled: id > 0 && (!globalSearchSelect || !debouncedValue),
      refetchOnWindowFocus: false,
      onSuccess: ({ data }) => {
        setListData(data?.data?.attributes?.parentDetails);
        setOpenDialog(false);
        setPagination(data?.data || []);
      },
    }
  );
  useQuery(
    [
      "collectionnName",
      moduleData === moduleConfiguration.scATLCRImportName
        ? moduleConfiguration.scATLCRImportName
        : moduleConfiguration.lcrImportConfiguration,
      selectedRow.id,
      globalSearchSelect,
      debouncedValue,
      lcrTypeValue,
    ],
    getLCRGlobalSearch,
    {
      enabled: !!globalSearchSelect && !!debouncedValue,
      refetchOnWindowFocus: false,
      onSuccess: ({ data }) => {
        setListData(data?.data?.attributes?.parentDetails);
        setOpenDialog(false);
        setPagination(data?.data || []);
      },
    }
  );

  const handleDelete = (rowId) => {
    deleteParentDetailAPI(
      {
        moduleName:
          moduleData === moduleConfiguration.scATLCRImportName
            ? moduleConfiguration.scAtLCRModuleName
            : moduleConfiguration.lcrConfiguration,
        parentDetail: moduleConfiguration.parentDetails,
        id: id,
        parentId: rowId,
        lcrTypeValue,
      },
      {
        onSuccess: (resp) => {
          setSuccessDialog(true);
          setDeleteDialog(false);
          setMessage(`Record(s) deleted successfully`);
          setCurrentPage(1);
          refetch();
          setIdToDelete(null);
        },
        onError: ({ response }) => {
          setErrorData(true);
          setMessage(response?.data?.error?.message);
          refetch();
          setDeleteDialog(false);
          setIdToDelete(null);
        },
      }
    );
  };
  const handleUploadClick = () => {
    if (file) {
      const updatedValues = {
        lcrName: selectedRow?.attributes?.lcrName,
        lcrType: lcrTypeValue,
        custType: selectedRow?.attributes?.custType === "Operator" ? "S" : "H",
      };

      let reqData = { data: updatedValues, fileUpload: file };
      updateLCRAPI(
        {
          moduleData:
            moduleData === moduleConfiguration.scATLCRImportName
              ? moduleConfiguration.scATLCRImportName
              : moduleConfiguration.lcrImportConfiguration,
          id: id,
          reqData,
        },
        {
          onSuccess: (resp) => {
            refetch();
            setSuccessDialog(true);
            setOpenDialog(false);
            setMessage(
              `${
                moduleData === moduleConfiguration.scATLCRImportName
                  ? "SC AT LCR"
                  : "LCR"
              }  updated successfully`
            );
          },
          onError: ({ response }) => {
            setOpenDialog(false);
            const errorMessage = response?.data?.error?.message;
            if (errorMessage?.startsWith("Validation Errors")) {
              setErroDialog(true);
              setErrorMessage(errorMessage);
            } else {
              setErroDialog(true);
              setMessage(errorMessage);
            }
          },
        }
      );
    }
  };

  const transformedData = TransformLCRData(listData, currentPage, limitPerPage);
  const lcrId = getLastNumberFromURL(window.location.href);
  const total = pagination?.attributes?.meta?.pagination?.total;
  const columnsData = getColumnsConfig({
    navigate,
    moduleData,
    moduleConfiguration,
    dropdownDetails,
    setDeleteDialog,
    setIdToDelete,
    lcrId,
    lcrTypeValue,
    total,
  });

  useEffect(() => {
    if (!file) {
      setMessage("");
    }
  }, [file]);
  return (
    <>
      <div className="flex flex-col">
        <>
          <div className="flex gap-2 font-medium my-7">
            <div
              className="text-textNAColor text-base cursor-pointer hover:underline"
              onClick={() => navigate(`/app/list/${currentModuleName}`)}
            >{`${
              moduleData === moduleConfiguration.scATLCRImportName
                ? "List of SC AT LCR > "
                : "List of LCR > "
            }`}</div>
            <div className="text-base">
              {`LCR name - ${selectedRow.attributes.lcrName} | LCR Type -
              ${selectedRow.attributes.lcrType}`}
            </div>
          </div>

          <div className="flex my-6 items-center justify-between mt-8">
            <div>
              <SearchComponent
                onChange={(e) => {
                  setSearchText(e.target.value);
                }}
                globalSearch={
                  moduleData === moduleConfiguration.scATLCRImportName
                    ? scATLCRGlobalSearch
                    : LCRGlobalSearch
                }
                setGlobalSearchSelect={setGlobalSearchSelect}
              />
            </div>
            {selectedRow?.attributes?.lcrType !== "Time based LCR" && (
              <div className="flex items-center space-x-10">
                <div
                  className="underline cursor-pointer"
                  onClick={() => {
                    setOpenDialog(true);
                  }}
                >
                  {`Upload  ${
                    moduleData === moduleConfiguration.scATLCRImportName
                      ? "SC AT"
                      : ""
                  } LCR file`}
                </div>
              </div>
            )}
          </div>
        </>
        <div className="my-4">
          <CustomTable
            data={transformedData || []}
            columns={columnsData}
            onRowClick={() => {}}
            isLcrDetails={true}
            setIsSearching={setIsSearching}
            setFilteredRow={setFilteredRow}
            setSelectedIds={setSelectedIds}
            setFilteredData={setFilteredData}
          />
        </div>

        {pagination &&
          pagination?.attributes?.meta?.pagination?.total !== undefined && (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                margin: "20px 0px 20px 20px",
                width: "95%",
              }}
            >
              <div className="flex">
                <ResultPerPageComponent
                  limit={limitPerPage}
                  handleLimitChange={handleLimitChange}
                />
                <div
                  style={{
                    display: "flex",
                    fontSize: "14px",
                    padding: "10px 0px 0px 10px",
                    color: "#808080",
                  }}
                >
                  {!isSearching ? (
                    <>
                      {(pagination?.attributes?.meta?.pagination?.total === 0
                        ? 0
                        : (currentPage - 1) * limitPerPage + 1) +
                        " - " +
                        Math.min(
                          limitPerPage * currentPage,
                          pagination?.attributes?.meta?.pagination?.total
                        )}{" "}
                      of {pagination?.attributes?.meta?.pagination?.total} rows
                    </>
                  ) : (
                    <>
                      <>
                        {`${
                          filteredRow === 0
                            ? 0
                            : (currentPage - 1) * limitPerPage + 1
                        }  - ${
                          (currentPage - 1) * limitPerPage + filteredRow
                        } of ${
                          pagination?.attributes?.meta?.pagination?.total
                        } rows (Matched for this page)`}
                      </>
                    </>
                  )}
                </div>
              </div>
              <Pagination
                className="pagination-bar"
                currentPage={currentPage}
                totalCount={
                  pagination?.attributes?.meta?.pagination?.total || 0
                }
                pageSize={limitPerPage}
                onPageChange={handlePageChange}
                isSearching={isSearching}
              />
            </div>
          )}

        <SuccessDialog
          show={successDialog}
          onHide={() => {
            setSuccessDialog(false);
          }}
          message={message}
        />
        <LCRErrorTableDialog
          open={errorDialog}
          onClose={() => {
            setErroDialog(false);
          }}
          errorMessages={errorMessage}
        />

        <ErrorDialog
          show={errorData}
          onHide={() => {
            setErrorData(false);
          }}
          message={message}
        />

        <UploadLCRFile
          show={openDialog}
          onClose={() => {
            setOpenDialog(false);
            setFile(null);
          }}
          selectFile={file}
          setSelectFile={setFile}
          onUploadClick={handleUploadClick}
          createLoading={createLCRLoading}
        />

        <DeleteDialog
          show={deleteDialog}
          onHide={() => setDeleteDialog(false)}
          onConfirm={() => {
            handleDelete(idToDelete);
          }}
          title={"Are you sure you want to delete?"}
        />
      </div>
    </>
  );
};

export default LCRTable;
