import React, { useState } from "react";
import { useQuery } from "react-query";
import Dialog from "@mui/material/Dialog";
import { CancelIcon } from "../../icons";
import { fetchEndUserLicense } from "../../lib/list-api";

const EndUserLicenseAgreement = ({ open, handleClose }) => {
  const [details, setDetails] = useState("");
  useQuery("licenseAgreement", fetchEndUserLicense, {
    onSuccess: ({ data }) => {
      setDetails(data);
    },
    onError: () => {
      setDetails("Not Found");
    },
  });

  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 565,
          maxHeight: 571,
          fontFamily: "OpenSanHebrew",
          borderRadius: "10px",
        },
      }}
      maxWidth="xs"
      open={open}
      onClose={handleClose}
      className="p-6 font-sans"
    >
      <div className="bg-bgBox w-full h-14 justify-center flex items-center">
        <div className="text-white">License Agreement</div>
      </div>
      <div
        className="flex justify-end absolute right-0 top-0 cursor-pointer"
        onClick={handleClose}
      >
        <CancelIcon className="flex items-center mx-3 my-3 " />
      </div>
      <div className="flex justify-center my-7">
        <div className="px-10 py-5">
          {/* {LicenseData ? LicenseData.message : "Loading..."} */}
          {details && <div>{details}</div>}
        </div>
      </div>
    </Dialog>
  );
};

export default EndUserLicenseAgreement;
