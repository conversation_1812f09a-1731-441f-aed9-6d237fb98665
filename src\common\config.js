import { moduleConfiguration } from "../common/constants";

export const INFO_ICON = true;
export const MAX_PRIORITY = 20;
export const resultPerPage = [
  { name: "10", values: "10" },
  { name: "20", values: "20" },
  { name: "50", values: "50" },
  { name: "100", values: "100" },
  { name: "200", values: "200" },
  { name: "500", values: "500" },
  { name: "999", values: "999" },
];
export const DEFAULT_PAGE_SIZE = "200";
export const MAX_HTTP_TEMPLATE = 10;
export const MAX_SUPPLIERS = 10;
export const allowedStartMinutes = ["00", "15", "30", "45"];
export const allowedEndMinutes = ["14", "29", "44", "59"];
export const MAX_THRESHOLDLEVEL = 10;
export const seriesNumbersLoading = "Please wait, data is loading...";
export const operatorNameMaxLength = 25;
export const MAX_COMMENT_CHAR = 250;
export const SERIES_LIMITATION = 30000;
// LCR configuration
export const validationLcrConfig = {
  lcrName: {
    regex: /^[A-Za-z][A-Za-z0-9_]*$/,
    regexMessage:
      "LCR Name must start with a letter and can only contain alphanumeric characters and underscores",
  },
  cost: {
    min: 1,
    max: 100,
    errorMessage: "Cost should be between 1 and 100",
  },
};
export const dynamicJsonConfig = [
  {
    model: moduleConfiguration.hubRuleConfiguration,
    ruleType: 9,
  },
  {
    model: moduleConfiguration.ruleConfiguration,
    ruleType: 6,
  },
];

const Joi = require("joi");
const apiUrl = process.env.REACT_APP_API_URL;

const envVarsSchema = Joi.object()
  .keys({
    NODE_ENV: Joi.string()
      .valid("production", "development", "demo", "test")
      .required(),
    REACT_APP_API_URL: apiUrl,
    REACT_APP_USERS_PER_PAGE: Joi.number()
      .default(10)
      .description("number of users per page in users table"),
  })
  .unknown();

const { value: envVars, error } = envVarsSchema
  .prefs({ errors: { label: "key" } })
  .validate(process.env);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

export const config = {
  api: {
    url: envVars.NODE_ENV === "production" ? apiUrl : apiUrl,
  },

  users: {
    resultsPerPage: envVars.REACT_APP_USERS_PER_PAGE,
  },
};
