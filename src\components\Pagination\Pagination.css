.pagination-container {
  display: flex;
  list-style-type: none;

  .pagination-item {
    color: #707070;
    text-align: center;
    font-family: "OpenSanHebrew";
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 1; /* Adjust the line height as needed */
    padding: 0 6px; /* Adjust padding as needed */
    height: 24px;
    margin: 0; /* Remove the extra 'px' */
    display: flex;
    box-sizing: border-box;
    align-items: center;
    border-radius: 14px;
    min-width: 20px;

    &.dots:hover {
      background-color: transparent;
      cursor: default;
    }
    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
      cursor: pointer;
    }

    &.selected {
      background-color: rgba(0, 0, 0, 0.08);
    }

    .arrow {
      &::before {
        position: relative;
        /* top: 3pt; Uncomment this to lower the icons as requested in comments*/
        content: "";
        /* By using an em scale, the arrows will size with the font */
        display: inline-block;
        width: 0.4em;
        height: 0.4em;
        border-right: 0.12em solid rgba(0, 0, 0, 0.87);
        border-top: 0.12em solid rgba(0, 0, 0, 0.87);
      }

      &.left {
        transform: rotate(-135deg) translate(-50%);
      }

      &.right {
        transform: rotate(45deg);
      }
    }

    &.disabled {
      pointer-events: none;

      .arrow::before {
        border-right: 0.12em solid rgba(0, 0, 0, 0.43);
        border-top: 0.12em solid rgba(0, 0, 0, 0.43);
      }

      &:hover {
        background-color: transparent;
        cursor: default;
      }
    }
  }
}
