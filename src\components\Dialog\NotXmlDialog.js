import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import { CloseIcon, CrossMarkIcon } from "../../icons";
import Button from "../Buttons/Button";
import { useState } from "react";

function NotXmlDialog({ show, onClose, message }) {
  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 411,
          maxHeight: 435,
          fontFamily: "OpenSansHebrew",
          borderRadius: "10px",
        },
      }}
      maxWidth="xs"
      open={show}
      onClose={onClose}
      className="p-6 font-sans"
    >
      <DialogTitle className="mt-2 flex justify-end">
        <CloseIcon className="w-2.5 h-2.5 cursor-pointer" onClick={onClose} />
      </DialogTitle>

      <div className="flex justify-center items-center mt-2">
        <CrossMarkIcon />
      </div>
      <div className="text-base mt-4 mx-auto">{message}</div>
      <div>
        <div className="text-center mt-10 gap-5 mb-4">
          <Button
            style={{ fontFamily: "Open Sans Hebrew" }}
            label={"Okay"}
            buttonClassName="w-full md:w-[111px] h-[30px] text-sm rounded-[20px]"
            onClick={onClose}
          />
        </div>
      </div>
    </Dialog>
  );
}

export default NotXmlDialog;
