import React, { useState, useContext } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@mui/material";
import { CloseIcon, ImportFileIcon, DeleteIcon } from "../../icons";
import { useDropzone } from "react-dropzone";
import CloudUploadImg from "../../assets/img/cloud.png";
import { useNavigate } from "react-router-dom";
import NotXmlDialog from "./NotXmlDialog";
import { extractXmlDialog } from "../../lib/list-api";
import { useMutation } from "react-query";
import ConfirmNNextButton from "../Buttons/Button";
import { multiStepFormContext } from "../../context/MultiStepFormContext";

function XmlUploadDialog({ show, onClose, setSelectFile, createLoading }) {
  const [selectedFileName, setSelectedFileName] = useState("");
  const [selectedFile, setSelectedFile] = useState(null);
  const navigate = useNavigate();
  const [showNotXmlDialog, setShowNotXmlDialog] = useState(false);
  const { setCurrentStep } = useContext(multiStepFormContext);
  const [message, setMessage] = useState("");

  const { mutate: extractXml, isLoading } = useMutation(extractXmlDialog, {
    onSuccess: (data) => {
      setCurrentStep(0);
      navigate("/app/list/operators/add", {
        state: {
          extractedData: data?.data,
          fileName: selectedFileName,
          collectionName: "operators",
          uploadedFileId: data?.uploadedFileId,
        },
      });
    },
    onError: (data) => {
      setShowNotXmlDialog(true);
    },
  });

  const onDrop = (acceptedFiles) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      const maxFileSize = 5 * 1024 * 1024;
      if (file.type !== "text/xml") {
        setShowNotXmlDialog(true);
        setMessage("Only xml file format allowed");
      } else if (file.size > maxFileSize) {
        setMessage("Maximum file size allowed is 5MB");
      } else {
        setSelectFile(file);
        setSelectedFileName(file.name);
        setSelectedFile(file);
      }
    }
  };

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: ".xml",
    multiple: false,
    maxSize: 5 * 1024 * 1024,
  });

  const clearFile = (e) => {
    e.stopPropagation();
    setSelectedFileName("");
    setSelectedFile(null);
    setSelectFile(null);
  };

  const handleAddOperator = () => {
    if (!selectedFile) {
      setCurrentStep(0);
      navigate("/app/list/operators/add", {
        state: { collectionName: "operators" },
      });
    } else {
      extractXml({ file: selectedFile });
    }
  };

  const handleClose = (e) => {
    e.stopPropagation();
    setSelectedFileName("");
    setSelectedFile(null);
    setSelectFile(null);
    onClose();
  };

  return (
    <>
      <Dialog
        sx={{
          "& .MuiDialog-paper": {
            width: "100%",
            maxWidth: 500,
            maxHeight: 500,
            fontFamily: "OpenSanHebrew",
            borderRadius: "10px",
          },
        }}
        maxWidth="xs"
        open={show}
        onClose={handleClose}
        className="p-6 font-sans"
      >
        <DialogTitle className="pb-0 flex justify-end">
          <CloseIcon className="w-4 h-4 cursor-pointer" onClick={handleClose} />
        </DialogTitle>
        <div className="flex justify-center font-bold text-[14px]">
          {selectedFile
            ? "Selected xml file ready for upload"
            : "Choose a XML operator profile file to upload"}
        </div>
        <div className="flex justify-center text-[10px] text-[#808080]">
          {selectedFile ? "" : "only XML file allowed"}
        </div>
        <DialogContent className="flex flex-col items-center p-6 font-open-sans-hebrew text-medium py-1">
          <div
            {...getRootProps()}
            className="border-2 border-dashed border-[#808080] rounded-lg w-[377px] h-[200px] flex flex-col justify-center items-center p-2 mt-2 relative"
          >
            <input {...getInputProps()} />
            {selectedFile ? (
              <div className="flex justify-center items-center w-full p-2 align-center">
                <span className="truncate">{selectedFileName}</span>
                <button
                  className="text-red-500 hover:text-red-700 ml-2"
                  onClick={clearFile}
                >
                  <DeleteIcon className="w-4 h-4" />
                </button>
              </div>
            ) : (
              <>
                <img
                  src={CloudUploadImg}
                  className="text-[#808080] w-[133px] h-[95px] cursor-pointer"
                  alt="Upload"
                />
                <p className="text-[#808080] mt-0 mb-0 text-[12px]">
                  {selectedFile ? "" : "Drag & drop XML file here"}
                </p>
                <p className="text-[#808080] mt-0 text-small mb-2">or</p>
                <div className="bg-gray-200 font-bold font-open-sans-hebrew p-2 rounded-lg flex justify-center text-center w-[303px] h-[33px] items-center cursor-pointer">
                  <ImportFileIcon className="w-4 h-4" /> Import operator profile
                </div>
              </>
            )}
          </div>
          <p className="text-gray-500 text-[10px] mt-2 mb-2">
            {selectedFile ? "" : "or you can also"}
          </p>
          <ConfirmNNextButton
            buttonClassName={`w-[303px] h-[33px] mb-6 cursor-pointer`}
            onClick={handleAddOperator}
            label={selectedFile ? "Upload" : "+ Add operator profile manually"}
            loading={createLoading}
          />
        </DialogContent>
      </Dialog>
      {showNotXmlDialog && (
        <NotXmlDialog
          show={showNotXmlDialog}
          onClose={() => setShowNotXmlDialog(false)}
          message={message}
        />
      )}
    </>
  );
}

export default XmlUploadDialog;
