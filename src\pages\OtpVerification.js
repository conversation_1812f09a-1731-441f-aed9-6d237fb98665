import React from "react";
import { Link, useNavigate } from "react-router-dom";
import Logo from "../icons/logo.svg";
import OTPVerificationForm from "../components/Forms/OtpVerificationForm";
import { CloseIcon } from "../icons";

function OtpVerification() {
  const navigate = useNavigate();
  return (
    <div className="flex flex-col min-h-screen bg-gray-50 font-sans">
      <Link
        to="/"
        className="pl-4 pr-4 text-xl font-bold text-gray-800 dark:text-gray-200 bg-white dark:bg-white-900"
      >
        <img
          width={"100px"}
          aria-hidden="true"
          src={Logo}
          alt="Airtel SMS Hub Reporting"
        />
      </Link>
      <div className="flex flex-1 h-full items-center lg:mt-0">
        <div className="flex-1 h-full max-w-xl mx-auto overflow-hidden bg-white rounded-lg shadow-xl ">
          <main className="flex items-center justify-center p-6 sm:p-12 md:flex-row">
            <div className="w-full">
              <CloseIcon
                className="w-3 h-3 cursor-pointer float-right"
                onClick={() => {
                  navigate("/auth/login");
                }}
              />
              <h1
                className="mb-2 text-xl font-semibold text-gray-700 "
                style={{ textAlign: "center" }}
              >
                Enter OTP
                <hr />
              </h1>
              <br />

              <OTPVerificationForm />
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}

export default OtpVerification;
