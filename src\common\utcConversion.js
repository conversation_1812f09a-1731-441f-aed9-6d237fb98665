import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

export const convertToUTC = (dateString, type) => {
  if (!dateString || !type) return null;
  if (dateString.endsWith("Z") || dateString.includes("+00:00")) {
    return dateString;
  }
  const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const time = type === "activationDate" ? "00:00:00" : "23:59:59";
  return dayjs.tz(`${dateString} ${time}`, userTimeZone).utc().format();
};
