import { Formik, Form } from "formik";
import React, { useState } from "react";
import { useQuery } from "react-query";
import Select from "../FormsUI/Select";
import { getCreditTransactionCustomer } from "../../lib/list-api";

function CreditTransactionFilter({
  setCreditCustomer,
  setIsSearching,
  // setSelectedIds,
}) {
  // const filteredCustomerType = [
  //   { label: "All", value: null },
  //   ...dropdownDetails,
  // ];
  const [optionsData, setOptionsData] = useState([]);
  useQuery(
    ["credit-transaction", "Customer name", { limit: -1 }],
    getCreditTransactionCustomer,
    {
      onSuccess: (response) => {
        const customOptionsData = response?.data?.data.map((item) => ({
          label: item.attributes.name,
          value: item.id,
        }));
        const allOption = { value: "all", label: "All customers" };
        setOptionsData([allOption, ...customOptionsData]);
      },
    }
  );
  return (
    <div>
      {" "}
      <Formik
        initialValues={{
          type: "all",
        }}
      >
        {({ values, errors, status }) => (
          <Form>
            <div className="w-[450px] h-[40px] ">
              <Select
                name="type"
                options={optionsData}
                placeholder={"Select a customer"}
                bgColor="#f3f3f3"
                borderRadius="24px"
                paddingX="4px"
                onChange={(selectedOption) => {
                  setCreditCustomer(selectedOption.value);
                  setIsSearching(
                    selectedOption.value && selectedOption.value !== "all"
                      ? true
                      : false
                  );
                }}
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
}

export default CreditTransactionFilter;
