import React, { useContext, useEffect, useState } from "react";
import InputLabel from "../../FormsUI/InputLabel";
import { Form, Formik } from "formik";
import { CustomerSupplierContext } from "../../../context/CustomerSupplierContext";
import ConfirmNNextButton from "../../Buttons/Button";
import Button from "../../Buttons/OutlinedButton";
import Dropdown from "../../FormsUI/MultiSelect/MultiSelect";
import { useMutation, useQuery } from "react-query";
import { createRecord, updateRecord } from "../../../lib/list-api";
import SuccessDialog from "../../Dialog/SuccessDialog";
import ErrorDialog from "../../Dialog/ErrorDialog";
import { useNavigate } from "react-router-dom";
import { getSmppListData } from "../../../lib/customer-supplier-api";
import { EditIcon } from "../../../icons";
import {
  moduleConfiguration,
  operatorType,
  subInterfaceEsme,
} from "../../../common/constants";
import {
  findCustomerSupplier,
  getActionFromUrl,
  getIdAfterEdit,
  getTrimmedUrl,
} from "../../../common/urlUtils";
import { useLocation } from "react-router-dom";
import { CssTooltip } from "../../FormsUI/StyledComponent";
import { requiredvalidationSchema } from "../../../payloadDetails/RequiredInfoPayload";
import {
  getIndexedDBDataById,
  initializeIndexedDB,
  updateIndexedDBDataById,
} from "../../HelperFunction/localStorage";
import { multiStepFormContext } from "../../../context/MultiStepFormContext";
import { deleteIndexedDB } from "../../HelperFunction/localStorage";

function RequiredInformationForm({ handleBack, editDetails, isView }) {
  const {
    requiredInfo,
    setRequiredInfo,
    generalInfoData,
    billingInfo,
    paymentInfo,
    setGeneralInfo,
    setBillingInfo,
    setPaymentInfo,
  } = useContext(CustomerSupplierContext);

  const { mutate: createRecordAPI, isLoading: createLoading } =
    useMutation(createRecord);
  const { mutate: updateRecordAPI, isLoading: updateLoading } =
    useMutation(updateRecord);

  const navigate = useNavigate();
  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [pathIdd, setPathId] = useState("");
  const [accountDetails, setAccountDetails] = useState([]);
  const [deleteLastEntry, setDeleteLastEntry] = useState(false);
  const [existingData, setExistingData] = useState(null);

  const location = useLocation();
  const header = "List of customers & suppliers";
  const action = getActionFromUrl(window.location.href);
  const searchParams = new URLSearchParams(location.search);
  const currentId = searchParams.get("currentId");
  const editId = getIdAfterEdit(window.location.href);

  const defaultEsmeAccount = (editDetails?.esme_accounts?.data || [])
    .filter((list) => Number(list?.protocol) === 5)
    .map((list) => ({
      id: list?.id,
      value: list?.id,
      label: list?.systemId,
      protocol: list?.protocol,
    }));

  const editPathId =
    editDetails?.paths?.data?.map((list) => ({
      id: list?.id,
      name: list?.pathName,
    })) || [];

  const moduleName = moduleConfiguration.customerSupplier;

  const fetchData = async (isPrevious) => {
    if (currentId) {
      try {
        const data = await getIndexedDBDataById(
          "navigationDetails",
          "FormDB",
          parseInt(currentId)
        );
        const dataIndex =
          data?.data?.[0]?.moduleNameValue ===
          moduleConfiguration.customerSupplier
            ? 0
            : 1;
        if (data?.data?.[dataIndex]?.pathId) {
          setPathId(data?.data?.[dataIndex]?.pathId);
        }
        if (isPrevious && data?.data?.length) {
          const lastRecord = data.data.find(
            (ele) => ele.moduleNameValue === moduleName
          );

          if (lastRecord) {
            setDeleteLastEntry(true);
          }
        }
        setExistingData(data);
      } catch (error) {
        console.error("Error fetching data from IndexedDB:", error);
      }
    }
  };

  useEffect(() => {
    fetchData(action === "add" ? true : false);
  }, [editId]);

  const doBeforeNavigate = async (path, forceDelete = false) => {
    if (deleteLastEntry || forceDelete) {
      const currentData = existingData.data.find(
        (entry) => entry.moduleNameValue === moduleName
      );
      existingData.data = existingData.data.filter(
        (entry) => entry.moduleNameValue !== moduleName
      );
      await updateIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        Number(currentId),
        existingData
      );
      if (currentData?.previousPath) {
        navigate(currentData.previousPath);
        return;
      }
    }
    navigate(path);
  };

  const handleButtonClick = async () => {
    if (existingData && existingData.data && existingData.data.length > 0) {
      const lastRecord = existingData.data[existingData.data.length - 1];
      if (lastRecord.moduleNameValue === moduleConfiguration.customerSupplier) {
        setCurrentStep(3);
      } else {
        setCurrentStep(0);
      }
      if (
        !lastRecord?.previousPath &&
        lastRecord?.currentPath === `${location.pathname}${location.search}`
      ) {
        try {
          doBeforeNavigate(`/app/list/${moduleName}`);
          await deleteIndexedDB("navigationDetails");
        } catch (error) {
          console.error("Error deleting database:", error);
        }
      } else if (lastRecord?.currentPath) {
        const fullUrl = window.location.href;
        const appUrl = fullUrl.substring(fullUrl.indexOf("/app"));

        if (lastRecord.currentPath === appUrl) {
          doBeforeNavigate(lastRecord.previousPath, true);
        } else {
          doBeforeNavigate(lastRecord.currentPath);
        }
      }
    } else {
      doBeforeNavigate(`/app/list/${moduleName}`);
    }
  };

  useQuery(
    [
      "esme-account-smpp",
      "esme-accounts",
      { limit: -1 },
      "customerRelation",
      subInterfaceEsme.SMPP,
      editId,
      "systemId",
      "protocol",
    ],
    getSmppListData,
    {
      refetchOnWindowFocus: false,
      onSuccess: ({ data }) => {
        const optionsData = data?.data?.map((list) => ({
          id: list?.id,
          value: list?.id,
          label: `${list?.attributes?.systemId} (Acc ID: ${list?.id})`,
        }));

        setAccountDetails(optionsData);
      },
    }
  );

  useEffect(() => {
    const dbName = "navigationDetails";
    const storeName = "FormDB";

    initializeIndexedDB(dbName, storeName)
      .then((result) => {
        const currentUrl = new URL(window.location.href);

        if (!currentUrl.searchParams.has("currentId")) {
          currentUrl.searchParams.set(
            "currentId",
            result?.id ? result.id : result?.data?.[0]?.id || 1
          );
          navigate(currentUrl.pathname + currentUrl.search, {
            replace: true,
          });
        }
      })
      .catch((error) => {
        console.error("Error initializing IndexedDB:", error);
      });
  }, []);

  const url = getTrimmedUrl();

  const { setCurrentStep } = useContext(multiStepFormContext);

  const handleAddButtonClick = async (navigation) => {
    const nextPath = navigation;
    const currentPath = url;
    const formData = {
      generalInfoData,
      billingInfo,
      paymentInfo,
      requiredInfo,
    };
    const id = parseInt(currentId, 10);
    const moduleNameValue = "customer-supplier-managements";
    const pathId =
      editPathId?.[0] || pathIdd.id
        ? {
            id: editPathId?.[0]?.id || pathIdd.id,
            name: editPathId?.[0]?.name || pathIdd.name,
          }
        : {};
    const previousModuleName = `${
      action === "add" ? "Add" : "Edit"
    } customer/supplier`;
    const listHeader = header;
    setCurrentStep(0);
    try {
      const existingData = await getIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        id
      );
      const updatedData = existingData || { data: [] };

      const existingIndex = updatedData.data.findIndex(
        (entry) => entry.moduleNameValue === moduleNameValue
      );

      if (existingIndex !== -1) {
        updatedData.data.splice(existingIndex, 1);
      }

      const newEntry = {
        currentPath,
        formData,
        previousPath:
          updatedData.data[updatedData.data.length - 1]?.currentPath || null,
        moduleNameValue,
        previousModuleName,
        pathId,
        actionData: action,
        customerId: editId,
      };

      if (updatedData.data.length === 0) {
        newEntry.listHeader = listHeader;
      }

      updatedData.data.push(newEntry);

      await updateIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        id,
        updatedData
      );

      navigate(`${nextPath}?currentId=${id}`);
    } catch (error) {
      console.error("Error updating IndexedDB:", error);
    }
  };

  const handleSubmit = (values) => {
    const { esme_accounts, paths } = values;
    const cleanedEsmeAccounts = esme_accounts.filter(
      (account) => account !== ""
    );
    let reqData = {
      data: {
        ...generalInfoData,
        billingInformation: billingInfo,
        ...paymentInfo,
      },
    };

    if (generalInfoData?.operatorType === operatorType.Both) {
      reqData.data.esme_accounts = cleanedEsmeAccounts;
      // reqData.data.paths = paths;
    } else if (generalInfoData?.operatorType === operatorType.Customer) {
      reqData.data.esme_accounts = cleanedEsmeAccounts;
    }
    // else if (generalInfoData?.operatorType === operatorType.Supplier) {
    //   reqData.data.paths = paths;
    // }
    if (action !== "add") {
      updateRecordAPI(
        {
          moduleName: moduleConfiguration.customerSupplier,
          id: editId,
          reqData,
        },
        {
          onSuccess: (resp) => {
            setSuccessDialog(true);
            setMessage(
              `${findCustomerSupplier(
                generalInfoData?.operatorType
              )} updated successfully`
            );
          },
          onError: ({ response }) => {
            setErrorDialog(true);
            setMessage(response?.data?.error?.message);
          },
        }
      );
    } else {
      createRecordAPI(
        { moduleName: moduleConfiguration.customerSupplier, reqData },
        {
          onSuccess: (resp) => {
            setSuccessDialog(true);
            setMessage(
              `${findCustomerSupplier(
                generalInfoData?.operatorType
              )} created successfully`
            );
          },
          onError: ({ response }) => {
            setErrorDialog(true);
            setMessage(response?.data?.error?.message);
          },
        }
      );
    }
  };

  const initialPathId =
    pathIdd?.id || (editPathId.length > 0 ? editPathId[0]?.id : "");

  return (
    <>
      <Formik
        initialValues={{
          esme_accounts: [],
          paths: initialPathId,
        }}
        validationSchema={requiredvalidationSchema(generalInfoData)}
        validateOnMount={true}
        enableReinitialize={true}
        onSubmit={handleSubmit}
      >
        {({ values, setFieldValue, errors, touched }) => (
          <Form className="flex flex-col items-center h-[300px]  mx-10">
            <div
              className={`mx-5 mt-10 ${
                generalInfoData?.operatorType === operatorType.Both
                  ? "grid grid-cols-2 gap-10 items-start w-full"
                  : "flex flex-col items-center w-1/2"
              }`}
            >
              {[operatorType.Customer, operatorType.Both].includes(
                generalInfoData?.operatorType
              ) ? (
                <div className="w-full">
                  <InputLabel
                    label={`Add SMPP account`}
                    isMandatory={true}
                    isAddButton={true}
                    onClick={() => {
                      handleAddButtonClick(
                        `/app/list/${moduleConfiguration.esmeAccounts}/add`
                      );
                    }}
                    action={action}
                  />
                  <Dropdown
                    btnWidth="w-full"
                    data={accountDetails}
                    btnName={`Select SMPP account`}
                    onSelectionChange={(selectedDetail) => {
                      const updatedSelectedDetail = selectedDetail.map(
                        (item) => {
                          return {
                            ...item,
                            label: item.label || item.value,
                          };
                        }
                      );

                      const selectedIds = updatedSelectedDetail.map(
                        (item) => item.id
                      );

                      setFieldValue("esme_accounts", selectedIds);
                      setRequiredInfo(selectedDetail);
                    }}
                    defaultData={
                      requiredInfo.length > 0
                        ? requiredInfo
                        : defaultEsmeAccount.length > 0
                        ? defaultEsmeAccount
                        : []
                    }
                    apiOptions={false}
                    disabled={action === "view"}
                    moduleName={moduleConfiguration.customerSupplier}
                    setCurrentStep={setCurrentStep}
                    collectionName={moduleConfiguration.esmeAccounts}
                    values={
                      (generalInfoData, billingInfo, paymentInfo, requiredInfo)
                    }
                    header={header}
                    action={action}
                    pathDetail={
                      editPathId?.[0] || pathIdd.id
                        ? {
                            id: editPathId?.[0]?.id || pathIdd.id,
                            name: editPathId?.[0]?.name || pathIdd.name,
                          }
                        : {}
                    }
                  />
                  {errors.esme_accounts && touched.esme_accounts && (
                    <div className="text-errorColor text-xs">
                      {errors.esme_accounts}
                    </div>
                  )}
                </div>
              ) : null}
              {[operatorType.Supplier, operatorType.Both].includes(
                generalInfoData?.operatorType
              ) ? (
                <div className="w-full flex justify-center mt-5">
                  {pathIdd?.id ||
                  (editPathId.length > 0 ? editPathId[0]?.id : "") ? (
                    <div className="flex gap-5">
                      <Button
                        label={
                          pathIdd?.name ||
                          (editPathId.length > 0 ? editPathId[0]?.name : "") ||
                          ""
                        }
                        buttonClassName="w-full text-sm rounded-[10px] bg-[#D9D9D980]"
                      />
                      {action !== "view" ? (
                        <>
                          <CssTooltip title={"Edit Path"} placement="top" arrow>
                            <EditIcon
                              className="w-4 h-4 mt-3 cursor-pointer"
                              onClick={() => {
                                if (
                                  generalInfoData.operatorType ===
                                    operatorType.Supplier ||
                                  ((values.esme_accounts.length > 0 ||
                                    requiredInfo.length > 0) &&
                                    [
                                      operatorType.Customer,
                                      operatorType.Both,
                                    ].includes(generalInfoData.operatorType))
                                ) {
                                  handleAddButtonClick(
                                    `/app/list/${
                                      moduleConfiguration.paths
                                    }/edit/${
                                      pathIdd?.id
                                        ? pathIdd.id
                                        : editPathId.length > 0
                                        ? editPathId[0]?.id
                                        : ""
                                    }`
                                  );
                                }
                              }}
                            />
                          </CssTooltip>
                        </>
                      ) : null}
                    </div>
                  ) : (
                    <CssTooltip
                      title={"Click to Add Path"}
                      placement="top"
                      arrow
                    >
                      <div className="w-full">
                        <Button
                          label={"Add Path"}
                          buttonClassName="w-full text-sm rounded-[10px]"
                          onClick={() => {
                            if (
                              (values.esme_accounts.length > 0 &&
                                (generalInfoData.operatorType ===
                                  operatorType.Customer ||
                                  generalInfoData.operatorType ===
                                    operatorType.Both)) ||
                              generalInfoData.operatorType ===
                                operatorType.Supplier
                            )
                              handleAddButtonClick(
                                `/app/list/${moduleConfiguration.paths}/add`
                              );
                          }}
                        />
                        {errors.paths && touched.paths && (
                          <div className="text-errorColor text-xs">
                            {errors.paths}
                          </div>
                        )}
                      </div>
                    </CssTooltip>
                  )}
                </div>
              ) : null}
            </div>

            <div className="flex justify-center gap-5 mt-20">
              <Button
                label={"Back"}
                buttonClassName="w-full md:w-[150px] h-[40px] text-sm"
                onClick={() => {
                  handleBack();
                }}
              />
              {action !== "view" ? (
                <ConfirmNNextButton
                  label={"Save"}
                  buttonClassName="w-full md:w-[150px] h-[40px] text-sm"
                  type="submit"
                  loading={createLoading || updateLoading}
                />
              ) : (
                <ConfirmNNextButton
                  label={"Close"}
                  buttonClassName="w-full md:w-[150px] h-[40px] text-sm"
                  onClick={() => {
                    navigate(
                      `/app/list/${moduleConfiguration.customerSupplier}`
                    );
                  }}
                />
              )}
            </div>
          </Form>
        )}
      </Formik>
      <SuccessDialog
        show={successDialog}
        onHide={() => {
          handleButtonClick();
          setSuccessDialog(false);
          setGeneralInfo([]);
          setBillingInfo([]);
          setPaymentInfo([]);
          setRequiredInfo([]);
        }}
        message={message}
      />
      <ErrorDialog
        show={errorDialog}
        onHide={() => setErrorDialog(false)}
        message={message}
      />
    </>
  );
}

export default RequiredInformationForm;
