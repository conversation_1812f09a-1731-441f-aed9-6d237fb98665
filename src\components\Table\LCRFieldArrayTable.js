import React, { useState, useEffect } from "react";
import { FieldArray, useFormikContext } from "formik";
import Select from "../FormsUI/Select";
import TextFieldWrapper from "../FormsUI/TextField";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
} from "@mui/material";
import { PlusCircleIcon, DeleteIcon } from "../../icons";
import AddButton from "../Buttons/OutlinedButton";
import { useOperatorData, useShortCodeData } from "../Dropdown";
import { MAX_SUPPLIERS } from "../../common/config";
import theme from "../../tailwind-theme";
import ErrorDialog from "../Dialog/ErrorDialog";
import DeleteDialog from "../Dialog/DeleteDialog";

const RenderTableBody = ({
  name,
  values,
  fields,
  headers,
  setErroDialog,
  setMessage,
  setDeleteDialog,
  onDeleteClick,
}) => (
  <FieldArray name={name}>
    {({ push, remove }) => {
      const selectedSuppliers = values
        .map((row) => row.supplier)
        .filter(Boolean);

      return (
        <>
          {values?.map((row, index) => (
            <TableRow key={index}>
              {fields?.map((field, fieldIndex) => (
                <TableCell key={fieldIndex}>
                  {field.options ? (
                    <Select
                      name={`${name}.${index}.${field.name}`}
                      options={field.options[0].filter((option) => {
                        return (
                          !selectedSuppliers.includes(option.value) ||
                          option.value === row.supplier
                        );
                      })}
                      className="md:w-[200px] w-full"
                    />
                  ) : (
                    <TextFieldWrapper
                      name={`${name}.${index}.${field.name}`}
                      value={
                        row.supplier === -1
                          ? { cost: 0, percentage: 0, position: 0 }[field.name]
                          : row[field.name]
                      }
                      isDisabled={row.supplier === -1}
                    />
                  )}
                </TableCell>
              ))}
              <TableCell>
                {values.length > 1 && (
                  <IconButton
                    type="button"
                    onClick={() => onDeleteClick(index, remove)}
                  >
                    <DeleteIcon />
                  </IconButton>
                )}
              </TableCell>
            </TableRow>
          ))}
          {values.length < MAX_SUPPLIERS && (
            <TableRow>
              <>
                {" "}
                {Array(fields.length)
                  .fill("")
                  .map((_, idx) => (
                    <TableCell key={idx}>
                      <div
                        style={{
                          border: "2px dotted #BEBEBE",
                          padding: "8px",
                          borderRadius: "10px",
                          height: "36px",
                          width: "100%",
                        }}
                      ></div>
                    </TableCell>
                  ))}
              </>
              <TableCell colSpan={headers.length + 1} align="right">
                <IconButton
                  type="button"
                  onClick={() => {
                    const supplierExist = values.some(
                      (row) => row.supplier === -1
                    );
                    if (supplierExist) {
                      setErroDialog(true);
                      setMessage(
                        "Multiple suppliers cannot be added with no routes"
                      );
                    } else {
                      push(
                        fields.reduce(
                          (acc, field) => ({ ...acc, [field.name]: "" }),
                          {}
                        )
                      );
                    }
                  }}
                >
                  <PlusCircleIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          )}
        </>
      );
    }}
  </FieldArray>
);

const RenderTable = ({
  name,
  values,
  headers,
  fields,
  setErroDialog,
  setMessage,
  setDeleteDialog,
  onDeleteClick,
}) => (
  <TableContainer component={Paper} sx={{ boxShadow: "none", maxHeight: 400 }}>
    <Table sx={{ minWidth: 550 }} aria-label="sticky table" stickyHeader>
      <TableHead sx={{ bgcolor: theme.backgroundColor.bgTable }}>
        <TableRow>
          {headers?.map((header, index) => (
            <TableCell
              key={index}
              sx={{
                position: "sticky",
                top: 0,
                zIndex: 1,
                backgroundColor: theme.backgroundColor.bgTable,
                fontWeight: "bold",
              }}
            >
              <div className="ml-0.5 text-xs font-semibold">{header}</div>
            </TableCell>
          ))}
          <TableCell
            sx={{
              position: "sticky",
              top: 0,
              zIndex: 1,
              backgroundColor: theme.backgroundColor.bgTable,
              fontWeight: "bold",
            }}
          />
        </TableRow>
      </TableHead>
      <TableBody>
        <RenderTableBody
          name={name}
          values={values}
          fields={fields}
          headers={headers}
          setErroDialog={setErroDialog}
          setMessage={setMessage}
          setDeleteDialog={setDeleteDialog}
          onDeleteClick={onDeleteClick}
        />
      </TableBody>
    </Table>
  </TableContainer>
);

const LCRFieldArrayTable = ({
  name,
  values,
  headers,
  fields,
  setFieldValue,
  isEdit,
}) => {
  const [policies, setPolicies] = useState(values?.parentDetails || []);
  const { setFieldValue: formikSetFieldValue } = useFormikContext();
  const [errorDialog, setErrorDialog] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [rowToDelete, setRowToDelete] = useState(null);

  const operatorDetails = useOperatorData();
  const shortCodeDetails = useShortCodeData();

  useEffect(() => {
    policies.forEach((policy, policyIndex) => {
      const selectedOperator = operatorDetails.find(
        (operator) => operator.value === policy.sourceOpId
      );
      if (selectedOperator) {
        formikSetFieldValue(
          `parentDetails[${policyIndex}].region`,
          selectedOperator.region
        );
        formikSetFieldValue(
          `parentDetails[${policyIndex}].mccMnc`,
          selectedOperator.mccMnc
        );
      }
    });
  }, [policies, operatorDetails, formikSetFieldValue]);

  useEffect(() => {
    if (!values?.parentDetails) {
      setPolicies([]);
    } else {
      setPolicies(values.parentDetails);
    }
  }, [values]);

  const addNewLCRPolicy = () => {
    const newPolicy = {
      shortCode: "",
      sourceOpId: "",
      region: "",
      mccMnc: "",
      policyDetails: [
        {
          supplier: "",
          cost: null,
          percentage: null,
          position: null,
        },
      ],
      isNew: true,
    };

    if (values.custType !== "S") {
      newPolicy.shortCode = "";
    }
    let newPolicyData = [...policies, newPolicy];

    setPolicies(newPolicyData);
    setFieldValue(name, newPolicyData);
  };

  const onDeleteClick = (index, remove) => {
    setRowToDelete(() => () => remove(index));
    setDeleteDialog(true);
  };

  const handleDeleteConfirm = () => {
    if (rowToDelete) {
      rowToDelete();
      setRowToDelete(null);
      setDeleteDialog(false);
    }
  };

  return (
    <>
      <div>
        {policies?.map((policy, policyIndex) => (
          <div
            key={policyIndex}
            className="border border-subNavBorder p-3 bg-bgField mb-5 rounded-[10px]"
          >
            <div className="flex justify-between mb-3 flex-wrap">
              <div className="flex items-center">
                <div className="text-sm font-bold ml-1 mt-2">
                  {`LCR policy ${policyIndex + 1}`}
                </div>
              </div>
              <div className="flex flex-wrap items-center">
                {values.custType === "S" ? (
                  <div className="flex items-center mr-5">
                    <div className="text-xs mr-5 font-normal">Short code</div>
                    <Select
                      name={`parentDetails[${policyIndex}].shortCode`}
                      options={shortCodeDetails}
                      value={policy.shortCode}
                      className="md:w-[180px] w-full"
                      minHeight={theme.minHeight.lcrPolicyheight}
                      bgColor={theme.backgroundColor.bgPrimary}
                      isDisabled={isEdit && !policy.isNew}
                    />
                  </div>
                ) : (
                  <>
                    <div className="flex items-center mr-5">
                      <div className="text-xs mr-5 font-normal">Short code</div>
                      <Select
                        name={`parentDetails[${policyIndex}].shortCode`}
                        options={shortCodeDetails}
                        value={policy.shortCode}
                        className="md:w-[180px] w-full"
                        minHeight={theme.minHeight.lcrPolicyheight}
                        bgColor={theme.backgroundColor.bgPrimary}
                        isDisabled={isEdit && !policy.isNew}
                      />
                    </div>
                    <div className="flex items-center mr-3">
                      <div className="text-xs mr-3 font-normal">
                        Source operator
                      </div>
                      <Select
                        name={`parentDetails[${policyIndex}].sourceOpId`}
                        options={[
                          { label: "All Operators", value: -99999 },
                          ...operatorDetails,
                        ]}
                        value={policy.sourceOpId}
                        onChange={(value) => {
                          formikSetFieldValue(
                            `parentDetails[${policyIndex}].sourceOpId`,
                            value
                          );
                        }}
                        className="md:w-[180px] w-full"
                        bgColor={theme.backgroundColor.bgPrimary}
                        minHeight={theme.minHeight.lcrPolicyheight}
                        isDisabled={isEdit && !policy.isNew}
                      />
                    </div>
                    <div className="flex items-center ">
                      <div className="text-xs mr-5 font-normal">Region</div>
                      <TextFieldWrapper
                        name={`parentDetails[${policyIndex}].region`}
                        className="md:w-[180px] md:h-[34px] w-full"
                        bgColor={theme.backgroundColor.bgPrimary}
                        minHeight={theme.minHeight.lcrPolicyheight}
                        isDisabled={true}
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
            <RenderTable
              name={`parentDetails[${policyIndex}].policyDetails`}
              values={values?.parentDetails?.[policyIndex]?.policyDetails || []}
              headers={headers}
              fields={policy.fieldsData || fields}
              setErroDialog={setErrorDialog}
              setMessage={setMessage}
              setDeleteDialog={setDeleteDialog}
              onDeleteClick={onDeleteClick}
            />
          </div>
        ))}

        <ErrorDialog
          show={errorDialog}
          onHide={() => setErrorDialog(false)}
          message={message}
        />
        <DeleteDialog
          show={deleteDialog}
          onHide={() => setDeleteDialog(false)}
          message={message}
          onConfirm={handleDeleteConfirm}
          title={
            <>
              Are you sure you want to delete?
              <br />
              (Deletion will only happen on click of save)
            </>
          }
        />
      </div>
      <div className="mt-5 flex justify-end items-end">
        <AddButton
          label="+ Add new short code"
          buttonClassName="text-xs font-bold"
          onClick={addNewLCRPolicy}
        />
      </div>
    </>
  );
};

export default LCRFieldArrayTable;
