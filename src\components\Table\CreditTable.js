import React from "react";
import { Formik, Field, Form, FieldArray } from "formik";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";
import TextField from "../FormsUI/TextField";
import CustomSwitch from "../ToggleSwitch/CustomSwitch";
import { PlusCircleIcon, DeleteIcon } from "../../icons";

const CreditTable = ({ parameters, headers }) => {
  const parametersArray = Array.isArray(parameters) ? parameters : [];

  const initialValues = {
    parameters: parametersArray.map((row) => ({
      type1: row.type1 || "",
      type2: row.type2 || "",
      type3: row.type3 || "",
    })),
  };

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={(values) => console.log(values)}
    >
      {({ values }) => (
        <Form>
          <TableContainer component={Paper} sx={{ boxShadow: "none" }}>
            <Table
              sx={{ minWidth: 550, border: "1.5px solid #BEBEBE" }}
              aria-label="simple table"
            >
              <TableHead sx={{ backgroundColor: "#37415033" }}>
                <TableRow>
                  {headers.map((header, index) => (
                    <TableCell key={index}>{header}</TableCell>
                  ))}
                  <TableCell></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <FieldArray name="parameters">
                  {({ push, remove }) => (
                    <>
                      {values.parameters.map((row, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            {row.type1 ? (
                              <span>{row.type1}</span>
                            ) : (
                              <Field
                                name={`parameters.${index}.type1`}
                                as={TextField}
                              />
                            )}
                          </TableCell>
                          <TableCell>
                            <Field
                              name={`parameters.${index}.type2`}
                              as={TextField}
                            />
                          </TableCell>
                          <TableCell>
                            <Field
                              name={`parameters.${index}.type3`}
                              as={TextField}
                            />
                          </TableCell>
                          <TableCell>
                            <DeleteIcon onClick={() => remove(index)} />
                          </TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell></TableCell>
                        <TableCell></TableCell>
                        <TableCell></TableCell>
                        <TableCell colSpan={headers.length}>
                          <PlusCircleIcon
                            onClick={() => {
                              const nextTypeNumber =
                                values.parameters.length + 1;
                              push({
                                type1: `Threshold Type ${nextTypeNumber}`,
                                type2: "",
                                type3: "",
                              });
                            }}
                          />
                        </TableCell>
                      </TableRow>
                    </>
                  )}
                </FieldArray>
              </TableBody>
            </Table>
          </TableContainer>
        </Form>
      )}
    </Formik>
  );
};

export default CreditTable;
