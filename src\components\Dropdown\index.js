import { useState } from "react";
import { useQuery } from "react-query";
import {
  getShortCode,
  getSupplierListData,
  getPageSchema,
} from "../../lib/lcr-profile-api";
import { getOperatorList } from "../../lib/customer-supplier-api";
import { moduleConfiguration, operatorType } from "../../common/constants";

// Hook to fetch and manage supplier data
const useSupplierData = () => {
  const [supplierData, setSupplierData] = useState([]);

  useQuery(
    [
      "customer-supplier",
      moduleConfiguration.customerSupplier,
      { limit: -1 },
      operatorType.Supplier,
      operatorType.Both,
      //"name",
    ],
    getSupplierListData,
    {
      onSuccess: ({ data }) => {
        const optionsData = data?.data?.map((list) => ({
          label: list?.attributes?.name,
          value: list?.id,
        }));
        setSupplierData(optionsData);
      },
    }
  );

  return supplierData;
};

// Hook to fetch and manage customer data
const useCustomerData = () => {
  const [customerData, setCustomerData] = useState([]);

  useQuery(
    [
      "customer-supplier",
      moduleConfiguration.customerSupplier,
      { limit: -1 },

      //"name",
    ],
    getSupplierListData,
    {
      onSuccess: ({ data }) => {
        const optionsData = data?.data?.map((list) => ({
          label: list?.attributes?.name,
          value: list?.id,
        }));
        setCustomerData(optionsData);
      },
    }
  );

  return customerData;
};

// Hook to fetch and manage operator data
const useOperatorData = () => {
  const [operatorDetails, setOperatorDetails] = useState([]);

  useQuery(
    [
      "operator-list-details",
      moduleConfiguration.operators,
      { limit: -1 },
      "operatorName",
      "id",
      "region",
      "mccMnc",
    ],
    getOperatorList,
    {
      onSuccess: ({ data }) => {
        const optionsData = data?.data?.map((list) => ({
          value: list?.id,
          label: list?.attributes?.operatorName,
          region: list?.attributes?.region,
          mccMnc: list?.attributes?.mccMnc,
        }));
        setOperatorDetails(optionsData);
      },
    }
  );

  return operatorDetails;
};

// Hook to fetch and manage operator data
const useOperatorClusterData = () => {
  const [operatorCluster, setOperatorClusterDetails] = useState([]);

  useQuery(
    [
      "operator-list-details",
      moduleConfiguration.operatorCluster,
      { limit: -1 },
    ],
    getOperatorList,
    {
      onSuccess: ({ data }) => {
        const optionsData = data?.data?.map((list) => ({
          value: list?.id,
          label: list?.attributes?.clusterName,
        }));
        setOperatorClusterDetails(optionsData);
      },
    }
  );

  return operatorCluster;
};

// Hook to fetch and manage mccMnc data
const useMccMncData = () => {
  const [mccMncDetails, setMccMncDetails] = useState([]);

  useQuery(
    [
      "operator-list-details",
      "operators",
      { limit: -1 },
      "operatorName",
      "id",
      "region",
      "mccMnc",
    ],
    getOperatorList,
    {
      onSuccess: ({ data }) => {
        const optionsData = data?.data?.map((list) => ({
          value: list?.attributes?.mccMnc,
          label: list?.attributes?.mccMnc,
        }));
        setMccMncDetails(optionsData);
      },
    }
  );

  return mccMncDetails;
};

// Hook to fetch and short code data

const useShortCodeData = () => {
  const [shortCodeDetails, setShortCodeDetails] = useState([]);

  useQuery(["get-short-code"], getShortCode, {
    onSuccess: ({ data }) => {
      const optionsData = data?.data?.map((list) => ({
        value: list?.attributes?.short_code,
        label: list?.attributes?.short_code,
      }));
      setShortCodeDetails(optionsData);
    },
  });

  return shortCodeDetails;
};

const useLcrData = (collectionName) => {
  const [lcrData, setLcrData] = useState([]);

  useQuery([collectionName], getPageSchema, {
    onSuccess: ({ data }) => {
      setLcrData(data?.data?.[0]?.attributes?.schemadetails?.dropdowns);
    },
  });

  return lcrData;
};

export {
  useSupplierData,
  useOperatorData,
  useMccMncData,
  useShortCodeData,
  useLcrData,
  useCustomerData,
  useOperatorClusterData,
};
