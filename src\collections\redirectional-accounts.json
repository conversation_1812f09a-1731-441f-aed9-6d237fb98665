{"header": "List of redirectional accounts", "columns": [{"header": "SMSC ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "SMSC account name", "accessorKey": "attributes.smscName"}, {"header": "System ID", "accessorKey": "attributes.smscUserName", "renderFunction": "row.row.original.attributes.connType === `M` ?row.row.original.attributes.smscUserName:row.row.original.attributes.smscUserName "}, {"header": "Connection type", "accessorKey": "attributes.connType"}, {"header": "Message redirection/sec", "accessorKey": "attributes.msgCapacityPerSec", "filterVariant": "range"}, {"buttons": [{"type": "button", "title": "View List"}, {"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"info": "Name of the SMPP account to connect to SMSC", "name": "smscName", "size": 6, "type": "text", "title": "SMSC account name", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the SMSC Account Name"}, {"type": "matches", "regex": "^[a-zA-Z](?=.*\\d)[a-zA-Z0-9_]*$", "message": "Only alphanumeric characters and underscore allowed.It should start with alphabet."}, {"type": "min", "value": 2, "message": "Min length is 2"}, {"type": "max", "value": 15, "message": "Max length is 15"}], "defaultValue": "", "validationType": "string"}, {"info": "Type of connection between SMS Hub and SMSC.", "name": "connType", "size": 6, "type": "select", "title": "Connection type", "isInfo": true, "options": [{"label": "SMPP", "value": "S"}, {"label": "HTTP", "value": "H"}, {"label": "SS7", "value": "M"}], "isMandatory": true, "nonEditable": true, "validations": [{"type": "required", "message": "Please select the Connection type."}], "defaultValue": ""}, {"info": "Number of messages redirected to the Fallback SMSC every second", "name": "msgCapacityPerSec", "size": 6, "type": "text", "title": "Message redirection (per sec)", "isInfo": true, "dynamic": {"field": "connType", "value": ["S", "H"]}, "isMandatory": true, "validations": [{"type": "typeError", "message": "Please enter the Message Redirection per sec with numeric value"}, {"type": "positive", "message": "Please enter the Message Redirection per Sec with Positive Integer value"}, {"type": "integer", "message": "Please enter the Message Redirection per sec with Integer value"}, {"type": "required", "message": "Please enter the Message Redirection per sec"}], "defaultValue": "", "validationType": "number"}, {"info": "Number of messages redirected to the Fallback SMSC after every minute\nThe value is automatically populated when Message Redirection (Per Sec) is entered.", "name": "msgCapacityPerMin", "size": 6, "type": "text", "title": "Message redirection (per min)", "isInfo": true, "dynamic": {"field": "connType", "value": ["S", "H"]}, "isMandatory": true, "validations": [{"type": "typeError", "message": "Please enter the Message Redirection per min with numeric value"}, {"type": "min", "value": 0, "message": "Please enter the Message Redirection per min with Positive Integer value"}, {"type": "integer", "message": "Please enter the Message Redirection per min with Integer value"}, {"type": "required", "message": "Please enter the Message Redirection per min"}], "defaultValue": "", "validationType": "number"}, {"info": "IP address of the SMPP server", "name": "smscHost", "size": 6, "type": "text", "title": "SMPP host", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the SMPP host value"}, {"type": "matches", "regex": "^((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([A-Za-z0-9\\-]{1,63})(\\.[A-Za-z]{2,})+)$", "message": "Please enter valid SMPP host(IPV4/Domain name)"}], "defaultValue": "", "validationType": "string"}, {"info": "Number of messages redirected to the Fallback SMSC after every hour\nThe value is automatically populated when Message Redirection (Per Sec) is entered.", "name": "msgCapacityPerHour", "size": 6, "type": "text", "title": "Message redirection (per hour)", "isInfo": true, "dynamic": {"field": "connType", "value": ["S", "H"]}, "isMandatory": true, "validations": [{"type": "typeError", "message": "Please enter the Message Redirection per hour with numeric value"}, {"type": "min", "value": 0, "message": "Please enter the Message Redirection per hour with Positive Integer value"}, {"type": "integer", "message": "Please enter the Message Redirection per hour with Integer value"}, {"type": "required", "message": "Please enter the Message Redirection per hour"}], "defaultValue": "", "validationType": "number"}, {"info": "Port to connect to the SMPP server", "name": "smscPort", "size": 6, "type": "text", "title": "SMPP port", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter SMPP port value"}, {"type": "min", "value": 1024, "message": "Please enter SMPP Port greater than 1023"}, {"type": "max", "value": 65534, "message": "Please enter SMPP Port less than 65535"}, {"type": "typeError", "message": "Please enter SMPP port with numeric value"}, {"type": "integer", "message": "Please enter SMPP port with valid integer value"}, {"type": "positive", "message": "Please enter SMPP port with positive integer value"}], "defaultValue": "", "validationType": "number"}, {"info": "System ID name to log on to the SMPP server", "name": "smscUserName", "size": 6, "type": "text", "title": "SMPP system ID", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the System id"}, {"type": "matches", "regex": "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z][a-zA-Z0-9_]*$", "message": "Only alphanumeric characters and underscore are allowed(Should start with alphabet)"}, {"type": "max", "value": 20, "message": "System id cannot be of more than 20 characters!"}], "defaultValue": "", "validationType": "string", "sharedFieldName": true}, {"info": "Alternative SMSC Host IP/Domain", "name": "altHost", "size": 6, "type": "text", "title": "Alternate host", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "isMandatory": false, "validations": [{"type": "matches", "regex": "^((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])(\\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([A-Za-z0-9\\-]{1,63})(\\.[A-Za-z]{2,})+)$", "message": "Please enter valid alternate host(IPV4/Domain name)"}], "defaultValue": ""}, {"info": "Alternative SMSC Host Port", "name": "altPort", "size": 6, "type": "text", "title": "Alternate port", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "isMandatory": false, "validations": [{"type": "min", "value": 1024, "message": "Please enter Alternate port greater than 1023"}, {"type": "max", "value": 65534, "message": "Please enter Alternate port less than 65535"}, {"type": "typeError", "message": "Please enter Alternate port with numeric value"}, {"type": "integer", "message": "Please enter Alternate port with valid integer value"}, {"type": "positive", "message": "Please enter Alternate port with positive integer value"}], "defaultValue": "", "validationType": "number"}, {"info": "Password to log on to the application", "name": "smscPwd", "size": 6, "type": "text", "title": "SMPP password", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "isPassword": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the password"}, {"type": "matches", "regex": "^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[^\\w\\s])[A-Za-z\\d\\W]+$", "message": "Password must contains atleast 1 uppercase, 1 lowercase , 1 numeric and 1 speacial character"}, {"type": "max", "value": 9, "message": "Password cannot be of more than 9 characters!"}], "defaultValue": "", "validationType": "string", "sharedFieldName": true}, {"info": "System type from which the SMPP client requests connection to SMS Hub to transmit messages\nFor example, enter VMA for Voice Mail Application or Bulk for BULK SMS.", "name": "systemType", "size": 6, "type": "text", "title": "System type", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "validations": [{"type": "matches", "regex": "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z][a-zA-Z0-9_]*$", "message": "Only alphanumeric characters and underscore are allowed(Should start with alphabet)"}, {"type": "max", "value": 20, "message": "System type cannot be of more than 20 characters!"}], "defaultValue": "", "validationType": "string"}, {"info": "Set of short codes to access the SMSC account. The SMSC account handles all messages sent to the short code defined in the address range.", "name": "addressRange", "size": 6, "type": "text", "title": "Address range", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "validations": [{"type": "matches", "regex": "^(0|[1-9][0-9]*)(\\.[0-9]+)?$", "message": "Please enter the Address range with only positive numeric value"}, {"type": "max", "value": 12, "message": "Address range cannot be of more than 12 characters!"}], "defaultValue": "", "validationType": "string"}, {"info": "Maximum number of messages that can be queued for delivery to destination", "name": "maximumPending", "size": 6, "type": "text", "title": "Maximum pending", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Maximum pending"}, {"type": "min", "value": 2, "message": "Min range allowed is 2"}, {"type": "max", "value": 100, "message": "Max range allowed is 100"}, {"type": "typeError", "message": "Please enter Maximum pending with numeric value"}, {"type": "integer", "message": "Please enter Maximum pending with valid integer value"}, {"type": "positive", "message": "Please enter Maximum pending with positive integer value"}], "defaultValue": "", "validationType": "number"}, {"info": "Indicates whether SMS Hub receives and transmits messages:TRUE,FALSE", "name": "tranceiverMode", "size": 6, "type": "switch", "title": "Transceiver mode", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "options": [{"label": "Yes", "value": 1, "checked": true}, {"label": "No", "value": 0, "checked": false}], "isMandatory": true, "validations": [{"type": "required", "message": "Please choose the Transceiver mode"}], "defaultValue": 0}, {"info": "Duration for which dummy data packets are to be sent (over the SMPP client to SMSC) from SMS Hub to the SMPP server to keep the SMPP connection alive", "name": "keepAlive", "size": 6, "type": "text", "title": "Keep alive (in sec)", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Keep alive (in sec)"}, {"type": "max", "value": 9999, "message": "Max length allowed is 4"}, {"type": "typeError", "message": "Please enter the Keep alive (in sec) with numeric value"}, {"type": "integer", "message": "Please enter Keep alive (in sec) with valid integer value"}, {"type": "positive", "message": "Please enter the Keep alive (in sec) with positive integer value"}], "defaultValue": "", "validationType": "number"}, {"info": "From HTTP template, select the template created under Environment Setup > HTTP Templates option.", "name": "httpTemplateId", "size": 6, "type": "select", "title": "HTTP template", "isInfo": true, "dynamic": {"field": "connType", "value": "H"}, "isAddButton": true, "isMandatory": true, "onClickPath": "http-templates", "validations": [{"type": "required", "message": "Please enter the HTTP template"}], "defaultValue": "", "isDropDownApi": true, "alloweDropDownEdit": true}, {"info": "Enter the HTTP URL where the HTTP message will be delivered to the supplier", "name": "supplierUrl", "size": 6, "type": "text", "title": "Supplier HTTP’s URL", "isInfo": true, "dynamic": {"field": "connType", "value": "H"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Supplier HTTP’s URL"}, {"type": "max", "value": 100, "message": "Max length allowed is 100"}, {"type": "matches", "regex": "^(http|https)://[a-zA-Z0-9.-]+(:\\d+)?(/[^\\s]*)?$", "message": "Please enter valid HTTP’s URL(which should begin with http:// or https://)"}], "defaultValue": ""}, {"info": "select the method over which the supplier wants to receive the HTTP message.\nGET\nPOST JSON\nPOST XML", "name": "httpMethod", "size": 6, "type": "select", "title": "Supplier’s preferred HTTP method", "isInfo": true, "dynamic": {"field": "connType", "value": "H"}, "options": [{"label": "GET", "value": "GET"}, {"label": "POST JSON", "value": "POST_JSON"}, {"label": "POST XML", "value": "POST_XML"}], "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Supplier’s preferred  HTTP method"}], "defaultValue": ""}, {"info": "Path of the SSL certificate to be used for for HTTPS communication", "name": "supplierFilePath", "size": 6, "type": "text", "title": "Supplier’s SSL certificate file path", "isInfo": true, "dynamic": {"field": "connType", "value": "H"}, "validations": [{"type": "min", "value": 2, "message": "Min length is 2"}, {"type": "max", "value": 50, "message": "Max length is 50"}, {"type": "matches", "regex": "^(/[^/ ]+)+\\.(crt|pem|key|pfx|p12)$", "message": "The file path must start with a forward slash and end with a valid SSL certificate extension (e.g., .crt, .pem, .key, .pfx, or .p12)."}], "defaultValue": ""}, {"info": "Enter the URL on which you want to receive the delivery report from the supplier.", "name": "drUrl", "size": 6, "type": "text", "title": "SMSHUB’s delivery report URL", "isInfo": true, "dynamic": {"field": "connType", "value": "H"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the SMSHUB’s delivery report URL"}, {"type": "matches", "regex": "^https?:\\/\\/(www\\.)?smshub\\.com\\/[^\\s]*(\\?.*)?$", "message": "The URL must start with 'http://' or 'https://', followed by 'smshub.com', and can include a delivery report path and optional query parameters."}, {"type": "max", "value": 100, "message": "Max length allowed is 100"}], "defaultValue": ""}, {"info": "Enter the user name to log on to the SMPP server", "name": "smscUserName", "size": 6, "type": "text", "title": "HTTP system id", "isInfo": true, "dynamic": {"field": "connType", "value": "H"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the System id"}, {"type": "matches", "regex": "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z][a-zA-Z0-9_]*$", "message": "Only alphanumeric characters and underscore are allowed(Should start with alphabet)"}, {"type": "max", "value": 20, "message": "System id cannot be of more than 20 characters!"}], "defaultValue": "", "validationType": "string", "sharedFieldName": true}, {"info": "Enter the password to log on to the application.", "name": "smscPwd", "size": 6, "type": "text", "title": "HTTP password ", "isInfo": true, "dynamic": {"field": "connType", "value": "H"}, "isPassword": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the password"}, {"type": "matches", "regex": "^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[^\\w\\s])[A-Za-z\\d\\W]+$", "message": "Password must contains atleast 1 uppercase, 1 lowercase , 1 numeric and 1 speacial character"}, {"type": "max", "value": 9, "message": "Password cannot be of more than 9 characters!"}], "defaultValue": "", "sharedFieldName": true}, {"info": "Enter the validity period of the message sent to the Fallback SMSC", "name": "valPeriod", "size": 6, "type": "text", "title": "Validity period (in mins)", "isInfo": true, "dynamic": {"field": "connType", "value": ["S", "H"]}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Validity period (in mins)"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 9999, "message": "Max range allowed is 9999"}, {"type": "typeError", "message": "Please enter Validity period (in mins) with numeric value"}, {"type": "integer", "message": "Please enter Validity period (in mins) with valid integer value"}, {"type": "positive", "message": "Please enter Validity period (in mins) with positive integer value"}], "defaultValue": "", "validationType": "number"}, {"name": "valPeriodType", "size": 6, "type": "radio", "title": "Validity period type", "dynamic": {"field": "connType", "value": "H"}, "options": [{"label": "Absolute", "value": 1}, {"label": "Relative", "value": 2}], "defaultValue": 1}, {"info": "Address of receiving node for which SMS Hub is relaying the MO-FSM", "name": "ss7DestGt", "size": 6, "type": "text", "title": "GT address", "isInfo": true, "dynamic": {"field": "connType", "value": "M"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the GT address"}, {"type": "min", "value": 10, "message": "Min length allowed is 2"}, {"type": "max", "value": 999999999999999, "message": "Max length allowed is 15"}, {"type": "typeError", "message": "Please enter GT address with numeric value"}, {"type": "integer", "message": "Please enter GT address with valid integer value"}, {"type": "positive", "message": "Please enter GT address with positive integer value"}], "defaultValue": "", "validationType": "number"}, {"info": "Type of receiving node for which SMS Hub is relaying the MO-FSM. It can be either VMSC or SGSN", "name": "gtAddrType", "size": 6, "type": "radio", "title": "GT address type", "isInfo": true, "dynamic": {"field": "connType", "value": "M"}, "options": [{"label": "VMSC", "value": 1}, {"label": "SGSN", "value": 2}], "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the GT address type"}], "defaultValue": 1}, {"info": "Parameter to define the address translation type of the receiving entity", "name": "tt", "size": 6, "type": "text", "title": "Called TT", "isInfo": true, "dynamic": {"field": "connType", "value": "M"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter Called TT"}, {"type": "max", "value": 256, "message": "Max range allowed is 256"}, {"type": "typeError", "message": "Please enter Called TT with numeric value"}], "defaultValue": "", "validationType": "number"}, {"info": "Parameter to define the numbering plan for the receiving entity", "name": "np", "size": 6, "type": "select", "title": "Called NP", "isInfo": true, "dynamic": {"field": "connType", "value": "M"}, "options": [{"label": "Unknown", "value": 0}, {"label": "ISDN", "value": 1}, {"label": "Telephony (E.164,E<PERSON>163)", "value": 2}, {"label": "Data(X.121)", "value": 3}, {"label": "Telex(F.69)", "value": 4}, {"label": "Maritime mobile", "value": 5}, {"label": "Land Mobile(E.121)", "value": 6}, {"label": "Private", "value": 7}, {"label": "ANSI SS7 PC and SSN", "value": 13}, {"label": "Internet", "value": 14}, {"label": "Extension", "value": 15}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Called NP"}], "defaultValue": ""}, {"info": "Parameter to define the address translation type of the sending entity", "name": "callingTt", "size": 6, "type": "text", "title": "Calling TT", "isInfo": true, "dynamic": {"field": "connType", "value": "M"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Calling TT"}, {"type": "typeError", "message": "Please enter Calling TT with numeric value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 255, "message": "Max range allowed is 255"}], "defaultValue": ""}, {"info": "Parameter to define the numbering plan for the sending entity", "name": "callingNp", "size": 6, "type": "select", "title": "Calling NP", "isInfo": true, "dynamic": {"field": "connType", "value": "M"}, "options": [{"label": "Unknown", "value": 0}, {"label": "ISDN", "value": 1}, {"label": "Telephony (E.164,E<PERSON>163)", "value": 2}, {"label": "Data(X.121)", "value": 3}, {"label": "Telex(F.69)", "value": 4}, {"label": "Maritime mobile", "value": 5}, {"label": "Land Mobile(E.121)", "value": 6}, {"label": "Private", "value": 7}, {"label": "ANSI SS7 PC and SSN", "value": 13}, {"label": "Internet", "value": 14}, {"label": "Extension", "value": 15}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Calling NP"}], "defaultValue": ""}, {"info": "Point code is an unique number assigned to a node in a network and SAP is an unique number assigned to the point code", "name": "pointcodeId", "size": 6, "type": "select", "title": "Point code list", "isInfo": true, "dynamic": {"field": "connType", "value": "M"}, "isAddButton": true, "isMandatory": true, "onClickPath": "point-code-lists", "validations": [{"type": "required", "message": "Please select the Point code"}], "defaultValue": "", "isDropDownApi": true, "alloweDropDownEdit": true}, {"info": "MAP version ID", "name": "mapVersion", "size": 6, "type": "select", "title": "Map version", "isInfo": true, "dynamic": {"field": "connType", "value": "M"}, "options": [{"label": "1", "value": 1}, {"label": "2", "value": 2}, {"label": "3", "value": 3}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Map version"}], "defaultValue": ""}, {"info": "Flag used to Enable/Disable the GSM conversion", "name": "gsmConvReqFlag", "size": 6, "type": "switch", "title": "ASCII conversion", "isInfo": true, "options": [{"label": "Enabled", "value": 1, "checked": true}, {"label": "Disabled", "value": 0, "checked": false}], "defaultValue": 1}, {"info": "Type of Number (TON) of the SMPP client application source address", "name": "srcAddressTon", "size": 6, "type": "select", "title": "Source address TON", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "options": [{"label": "Unknown", "value": 0}, {"label": "International", "value": 1}, {"label": "National", "value": 2}, {"label": "Networkspecific", "value": 3}, {"label": "Subscriber", "value": 4}, {"label": "Alphanumeric", "value": 5}, {"label": "Abbreviated", "value": 6}, {"label": "Extension", "value": 7}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select Source address TON"}], "defaultValue": ""}, {"info": "Numbering Plan Indicator (NPI) of the SMPP client application source address\nNote: If the type of NPI is not known, select Unknown", "name": "srcAddressNpi", "size": 6, "type": "select", "title": "Source address NPI", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "options": [{"label": "Unknown", "value": 0}, {"label": "ISDN[E.163/.164]", "value": 1}, {"label": "Data[X 121]", "value": 3}, {"label": "Telex [F.69]", "value": 4}, {"label": "National", "value": 8}, {"label": "Private", "value": 9}, {"label": "ERMES", "value": 10}, {"label": "Extension", "value": 15}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select Source address NPI"}], "defaultValue": ""}, {"info": "Type of Number (TON) of the SMPP client application destination address", "name": "dstAddressTon", "size": 6, "type": "select", "title": "Destination address TON", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "options": [{"label": "Unknown", "value": 0}, {"label": "International", "value": 1}, {"label": "National", "value": 2}, {"label": "Networkspecific", "value": 3}, {"label": "Subscriber", "value": 4}, {"label": "Alphanumeric", "value": 5}, {"label": "Abbreviated", "value": 6}, {"label": "Extension", "value": 7}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select Destination address TON"}], "defaultValue": ""}, {"info": "Numbering Plan Indicator (NPI) of the SMPP client application destination address", "name": "dstAddressNpi", "size": 6, "type": "select", "title": "Destination address NPI", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "options": [{"label": "Unknown", "value": 0}, {"label": "ISDN[E.163/.164]", "value": 1}, {"label": "Data[X 121]", "value": 3}, {"label": "Telex [F.69]", "value": 4}, {"label": "National", "value": 8}, {"label": "Private", "value": 9}, {"label": "ERMES", "value": 10}, {"label": "Extension", "value": 15}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select Destination address NPI"}], "defaultValue": ""}, {"info": "Message ID received from SMSC in response to the submit_sm query. This ID varies from SMSC to SMSC. It can be a value or string.\nValues can be:\n0: Both submit_sm and deliver_sm contain decimal message ID.\n1: submit_sm response contains decimal message ID and deliver_sm contains HEX message ID.\n2: submit_sm response contains HEX message ID and deliver_sm contains decimal message ID.\n3: Both submit_sm and deliver_sm contain HEX message ID.", "name": "msgIdType", "size": 6, "type": "select", "title": "Message ID Type", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "options": [{"label": "0 - SubsmResp:Dec, Dlvr:Dec", "value": 0}, {"label": "1 - SubsmResp:<PERSON><PERSON>, Dlvr:Dec", "value": 1}, {"label": "2 - SubsmResp:Dec, Dlvr:Hex", "value": 2}, {"label": "3 - SubsmResp:<PERSON>x, Dlvr:Hex", "value": 3}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select Message ID Type"}], "defaultValue": ""}, {"info": "Time (in seconds) between two retries to connect to the server", "name": "reconnectDelay", "size": 6, "type": "text", "title": "Reconnect delay", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Reconnect delay"}, {"type": "max", "value": 9999, "message": "Max length allowed is 4"}, {"type": "typeError", "message": "Please enter Reconnect delay with numeric value"}, {"type": "integer", "message": "Please enter Reconnect delay with valid integer value"}, {"type": "positive", "message": "Please enter Reconnect delay with positive integer value"}], "defaultValue": "", "validationType": "number"}, {"info": "Time interval (in seconds) to update the status of the Fallback SMSC in the database", "name": "statRefreshDelay", "size": 6, "type": "text", "title": "Status refresh delay", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Status Refresh Delay"}, {"type": "matches", "regex": "^(0|[1-9][0-9]*)(\\.[0-9]+)?$", "message": "Please enter the Status refresh delay with only positive numeric value"}, {"type": "matches", "regex": "^-?[0-9]+$", "message": "Please enter the Status Refresh Delay with Integer value"}, {"type": "max", "value": 4, "message": "Max length allowed is 4"}], "defaultValue": ""}, {"info": "Nodes of SMS Hub used to connect to external SMSCs for redirection", "name": "activeNodes", "size": 6, "type": "select", "title": "Active nodes", "isInfo": true, "dynamic": {"field": "connType", "value": "S"}, "options": [{"label": "1", "value": 1}, {"label": "2", "value": 2}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select Active node"}], "defaultValue": "", "validationType": "number"}], "formType": "simple", "buttonName": "+ Add redirectional account ", "moduleData": "redirectional-account", "moduleName": "Redirectional Account", "globalSearch": [{"label": "SMSC ID", "value": "id"}, {"label": "SMSC account name", "value": "smscName"}, {"label": "System ID", "value": "smsc_user_name"}, {"label": "Message redirection/sec", "value": "msgCapacityPerSec"}], "navigationPath": true, "dropdownOptions": {"field": "connType", "options": [{"label": "SMPP", "query": "filters[connType][$eq]=S", "value": "S"}, {"label": "HTTP", "query": "filters[connType][$eq]=H", "value": "H"}, {"label": "SS7", "query": "filters[connType][$eq]=M", "value": "M"}], "defaultQuery": "filters[connType][$eq]=S", "defaultValue": "S"}}