import LoadingButton from "@mui/lab/LoadingButton";
import React from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
const Button = ({ buttonClassName, textClassName, ...rest }) => {
  const { t } = useTranslation();
  return (
    <LoadingButton
      {...rest}
      // Below line is commented so that the text can be transformedto uppercase as well.Please set as buttonclassname for the required styles
      //style={{ textTransform: "none" }}
      style={{ fontFamily: "OpenSanHebrew" }}
      variant="outlined"
      className={twMerge(
        `h-10 bg-white text-errorColor font-medium w-[200px] rounded-lg border border-gray-600 normal-case text-sm ${buttonClassName}`
      )}
    >
      {rest.children}
      {t(`${rest.label}`)}
    </LoadingButton>
  );
};

export default Button;
