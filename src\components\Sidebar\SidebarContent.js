import React, { useState, useContext } from "react";
import { NavLink, useLocation } from "react-router-dom";
import * as Icons from "../../icons";
import SidebarSubmenu from "./SidebarSubmenu";
import { Transition } from "react-transition-group";
import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import NavigateBeforeIcon from "@mui/icons-material//NavigateBefore";
import { useTranslation } from "react-i18next";
import Zoom from "@mui/material/Zoom";
import { getMenu } from "../../lib/left-navigation-api";
import theme from "../../tailwind-theme";
import { CssTooltip } from "../StyledComponent";
import { useQuery } from "react-query";
import { DataContext } from "../../context/DataContext";

const duration = 400;
const sidebarStyle = {
  transition: `width ${duration}ms`,
  boxShadow: "0px 6px 26px #22377B15",
};
const sidebarTransitionStyles = {
  entering: { width: "80px" },
  entered: { width: "250px" },
  exiting: { width: "250px" },
  exited: { width: "80px" },
};

const menuTransistionStyle = {
  entering: { display: "none" },
  entered: { display: "block" },
  exiting: { display: "block" },
  exited: { display: "none" },
};
const footerTransistionStyle = {
  entering: { justifyContent: "end" },
  entered: { justifyContent: "end" },
  exiting: { justifyContent: "end" },
  exited: { justifyContent: "center" },
};

function Icon({ icon, ...props }) {
  const Icon = Icons[icon];
  if (Icon) return <Icon {...props} />;
}

function SidebarContent() {
  const [toggeled, setToggle] = useState(true);
  const [routes, setRoutes] = useState([]);
  const { t } = useTranslation();

  const location = useLocation();

  useQuery(["get menus"], getMenu, {
    onSuccess: ({ data }) => {
      setRoutes(data?.schemadetails);
    },
  });

  const handleMenuClick = () => {
    setToggle(false);
    setSideBarOpen(false);
  };

  const { sideBarOpen, setSideBarOpen } = useContext(DataContext);

  return (
    <Transition in={toggeled} timeout={duration}>
      {(state) => (
        <div
          id="sidebarMain"
          className="relative h-full bg-bgSideBar flex-col border-r hidden xl:block "
          style={{
            ...sidebarStyle,
            ...sidebarTransitionStyles[state],
            boxShadow: "1px 0px 10px #00000029",
          }}
        >
          <div className="absolute -right-0 top-1 border-gray-100">
            <div
              onClick={() => {
                setToggle(!toggeled);
                setSideBarOpen(!sideBarOpen);
              }}
              className="cursor-pointer bg-white p-1 border-2 border-solid rounded-full"
            >
              {toggeled ? (
                <div className="flex justify-end items-center">
                  <NavigateBeforeIcon className="text-black w-6 h-6" />
                </div>
              ) : (
                <div
                  className="flex justify-end items-center"
                  style={{ ...footerTransistionStyle[state] }}
                >
                  <NavigateNextIcon className="text-black w-6 h-6" />
                </div>
              )}
            </div>
          </div>

          <div className="grow overflow-x-hidden flex flex-col h-full">
            <ul className="mt-16">
              {routes?.map((route, i) => {
                const isActive = location.pathname.startsWith(route.path);
                return route?.routes ? (
                  <SidebarSubmenu
                    route={route}
                    key={route.name}
                    style={{ ...menuTransistionStyle[state] }}
                    toggeled={toggeled}
                    setToggle={setToggle}
                    setSideBarOpen={setSideBarOpen}
                    handleMenuClick={handleMenuClick}
                  />
                ) : route?.show ? (
                  <li className={`relative text-white mb-1.5`} key={route.name}>
                    <NavLink
                      to={route?.path}
                      className={`inline-flex items-center w-full text-sm text-white transition-colors pl-7 duration-150 p-3 font-semibold`}
                      style={({ isActive }) =>
                        isActive
                          ? {
                              backgroundColor: theme.backgroundColor.bgActive,
                              borderLeft: theme.borderLeft.activeLeftBorder,
                              color: theme.textColor.errorColor,
                            }
                          : { backgroundColor: "transparent" }
                      }
                      onClick={handleMenuClick}
                    >
                      <div className={`flex items-start gap-5`}>
                        <CssTooltip
                          title={!toggeled ? route.name : ""}
                          placement="right"
                          TransitionComponent={Zoom}
                        >
                          <div>
                            <Icon
                              className="w-5 h-4"
                              aria-hidden="true"
                              icon={route?.icon ? route.icon : ""}
                              style={{
                                fill: isActive
                                  ? theme.textColor.errorColor
                                  : "white",
                              }}
                            />
                          </div>
                        </CssTooltip>
                        <div
                          style={{ ...menuTransistionStyle[state] }}
                          className="text-xs"
                        >
                          {t(route.name)}
                        </div>
                      </div>
                    </NavLink>
                  </li>
                ) : null;
              })}
            </ul>
          </div>
        </div>
      )}
    </Transition>
  );
}

export default SidebarContent;
