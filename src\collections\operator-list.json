{"moduleName": "Operator", "header": "List of operator", "buttonName": "+ Add operator", "formType": "stepper", "moduleData": "operator", "globalSearch": [{"label": "Operator id", "value": "id"}, {"label": "Operator name", "value": "operatorName"}, {"label": "Country name", "value": "countryName"}, {"label": "Region", "value": "region"}, {"label": "MCC-MNC", "value": "mccMnc"}], "dropdownOptions": {"field": "countryName", "defaultQuery": "", "defaultValue": "All"}, "columns": [{"accessorKey": "id", "header": "Operator id", "filterVariant": "range"}, {"accessorKey": "attributes.operatorName", "header": "Operator name", "isHovered": true}, {"accessorKey": "attributes.countryName", "header": "Country name"}, {"accessorKey": "attributes.region", "header": "Region"}, {"accessorKey": "attributes.mccMnc", "header": "MCC-MNC"}, {"accessorKey": "actions", "buttons": [{"title": "viewData", "iconUrl": "", "type": "image"}, {"title": "Edit", "iconUrl": "", "type": "image"}, {"title": "Delete", "iconUrl": "", "type": "image"}]}], "forms": [{"formName": "General Information", "elements": [{"name": "operatorName", "title": "Operator name", "size": 6, "type": "text", "defaultValue": "", "isMandatory": true, "validationType": "string", "isInfo": true, "info": "Name of the operator", "validations": [{"type": "required", "message": "Please enter the operator name"}, {"type": "matches", "regex": "^(?=.*[0-9])(?=.*[A-Za-z])[A-Za-z][A-Za-z0-9_ ]*$", "message": "Only alphanumeric characters,underscore and space are allowed.It should start with an alphabet"}, {"type": "min", "value": 3, "message": "Min length allowed is 3"}, {"type": "max", "value": 128, "message": "Max length allowed is 128"}]}, {"name": "countryName", "title": "Country name", "size": 6, "type": "text", "defaultValue": "", "isMandatory": true, "isInfo": true, "info": "Country in which the operator resides", "validationType": "string", "validations": [{"type": "required", "message": "Please enter the Country name"}, {"type": "matches", "regex": "^[A-Za-z][A-Za-z_ ]*$", "message": "Only alphabetic character underscore and space are allowed"}, {"type": "min", "value": 3, "message": "Min length allowed is 3"}, {"type": "max", "value": 25, "message": "Max length allowed is 25"}]}, {"name": "region", "title": "Region", "size": 6, "type": "text", "defaultValue": "", "isMandatory": true, "placeholder": "Ex.,East,West,etc... ", "isInfo": true, "info": "Region name of the operator", "validationType": "string", "validations": [{"type": "required", "message": "Please enter the region"}, {"type": "matches", "regex": "^[A-Za-z][A-Za-z_ ]*$", "message": "Only alphabetic character underscore and space are allowed"}, {"type": "min", "value": 3, "message": "Min length allowed is 3"}, {"type": "max", "value": 50, "message": "Max length allowed is 50"}]}, {"name": "circle", "title": "Circle", "size": 6, "type": "text", "defaultValue": "", "isMandatory": false, "isInfo": true, "info": "Circle assigned to the operator", "validationType": "string", "validations": [{"type": "matches", "regex": "^[A-Za-z][A-Za-z_ ]*$", "message": "Only alphabetic character underscore and space are allowed"}, {"type": "min", "value": 2, "message": "Min length allowed is 2"}, {"type": "max", "value": 50, "message": "Max length allowed is 50"}]}, {"name": "gtList", "title": "GT List", "size": 6, "type": "ChipInput", "defaultValue": [], "isMandatory": false, "isInfo": true, "info": "Enter the list of numbers. GT is a unique address which refers to only one destination", "fieldBased": "operator", "validation": true, "validationType": "array", "validationConfig": {"maxValue": {"value": 21, "message": "Maximum 21 digits allowed"}, "integerError": "Please enter only positive numeric values"}}, {"name": "ccode", "title": "CC", "size": 6, "type": "text", "defaultValue": "", "isMandatory": true, "isInfo": true, "info": "Country code", "validationType": "string", "validations": [{"type": "required", "message": "Please enter the CC value"}, {"type": "matches", "regex": "^(0|[1-9][0-9]*)(\\.[0-9]+)?$", "message": "Only positive numeric value is allowed for country code"}, {"type": "min", "value": 1, "message": "Max length allowed is 1"}, {"type": "max", "value": 5, "message": "Max length allowed is 5"}]}, {"name": "ccNdc", "title": "CC/NDC (Ex.,54-435 or 67-43 ect...)", "size": 6, "type": "ChipInput", "defaultValue": [], "isMandatory": true, "isInfo": true, "info": "Enter the country code and national destination code", "fieldBased": "operator", "validation": true, "validationType": "array", "validationConfig": {"typeError": {"value": "^(\\d{2}-\\d+,)*\\d{2}-\\d+$", "message": "Value of CC/NDC is not proper. Please Check"}, "uniqueError": "Repeated values not allowed", "minArrayLength": {"value": 1, "message": "Please enter CC/NDC values"}, "maxArrayLength": {"value": 10, "message": "Maximum 10 CC/NDC values are allowed"}, "onlyZeroesError": {"value": "^(?!00-0+$)\\d{2,}-\\d+$", "message": "CC/NDC cannot consist only zeroes please enter integer values only"}}}, {"name": "mccMnc", "title": "MCC-MNC (Ex.,754-43 or 762-43)", "size": 6, "type": "ChipInput", "defaultValue": [], "isMandatory": true, "isInfo": true, "info": "Mobile country  network codes, which uniquely identifies a mobile phone operator", "fieldBased": "operator", "validation": true, "validationType": "array", "validationConfig": {"mccValue": {"value": 3, "message": "MCC should contains 3 digits"}, "mncValue": {"value": 3, "message": "MNC should contain 2 or 3 digits"}, "typeError": {"value": "^(\\d{3}-\\d{2,3},)*\\d{3}-\\d{2,3}$", "message": "Value of MCC-MNC is not proper. Please Check"}, "uniqueError": "Repeated values not allowed", "minArrayLength": {"value": 1, "message": "Please enter MCC-MNC value"}, "maxArrayLength": {"value": 1, "message": "Only 1 MCC-MNC is allowed"}, "invalidValueError": {"value": ["000-000", "000-00"], "message": "MCC-MNC cannot consist only zeroes please enter integer values only"}}}]}, {"formName": "MNP Information", "elements": [{"name": "lrn", "title": "LRN", "size": 6, "type": "text", "defaultValue": "", "isMandatory": false, "isInfo": true, "info": "Local routing number", "validationType": "string", "validations": [{"type": "matches", "regex": "^(0|[1-9][0-9]*)(\\.[0-9]+)?$", "message": "Only positive integer value is allowed for LRN"}, {"type": "min", "value": 3, "message": "Min length allowed is 3"}, {"type": "max", "value": 5, "message": "Max length allowed is 5"}]}, {"name": "mnpCorrection", "title": "MNP correction", "size": "6", "type": "radio", "defaultValue": "Disable", "isMandatory": true, "options": [{"label": "Enable", "value": "Enable"}, {"label": "Disable", "value": "Disable"}, {"label": "SRI based", "value": "SRI based"}], "isInfo": true, "info": "Flag to enable/disable MNP check for operator"}, {"name": "mnpGateway", "title": "MNP gateway", "size": 6, "type": "select", "isDropDownApi": true, "isMandatory": true, "defaultValue": "", "isInfo": true, "info": "Gateway to be considered for MNP check of operator", "onClickPath": "mnp-gateway-details", "validations": [{"type": "required", "message": "Please select a MNP gateway type"}], "dynamic": {"field": "mnpCorrection", "value": ["Enable", "SRI based"]}}, {"name": "mnpCacheFlag", "title": "MNP cache", "size": "6", "type": "radio", "isInfo": true, "info": "Cache gateway to be considered for MNP check for operator", "defaultValue": 1, "options": [{"label": "Enable", "value": 0}, {"label": "Disable", "value": 1}], "dynamic": {"mnpTypeVisible": true, "dynamicVisibleField": "mnpGateway", "dynamicVisibleValue": "Enum", "dynamicSubModule": "mnpCorrection", "dynamicSubModuleValue": ["Enable", "SRI based"]}}, {"name": "bilateralFlag", "title": "Bilateral ", "size": "6", "type": "radio", "defaultValue": 1, "isMandatory": true, "options": [{"label": "Yes", "value": 0}, {"label": "No", "value": 1}], "validations": [{"type": "required", "message": "Please select the bilateral value"}], "isInfo": true, "info": "Bilateral rules is a pre defined agreement between two operators to terminate messages"}, {"name": "maxMessagePerHour", "title": "Max messages throttle", "size": 6, "type": "text", "defaultValue": "", "isMandatory": true, "validationType": "number", "validations": [{"type": "required", "message": "Please enter the max messages throttle"}, {"type": "typeError", "message": "Only positive numeric value is allowed"}, {"type": "min", "value": 0, "message": "Min value allowed is 0"}, {"type": "max", "value": 99999, "message": "Max value allowed is 99999"}], "isInfo": true, "info": "Maximum messages that can be sent per hour"}]}, {"formName": "<PERSON><PERSON>", "elements": [{"name": "tadicCode", "title": "Tadig code", "size": 6, "type": "text", "defaultValue": "", "isMandatory": false}, {"name": "countryInitials", "title": "Country initials", "size": 6, "type": "text", "defaultValue": "", "isMandatory": false}, {"name": "organizationName", "title": "Organization name", "size": 6, "type": "text", "defaultValue": "", "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the organization name"}]}, {"name": "networkName", "title": "Network name", "size": 6, "type": "text", "defaultValue": "", "isMandatory": false}, {"name": "grxProvidersList", "title": "GRX providers list", "size": 6, "type": "ChipInput", "defaultValue": [], "isMandatory": false}, {"name": "asnsList", "title": "ASNs list", "size": 6, "type": "ChipInput", "defaultValue": [], "isMandatory": false}]}, {"formName": "Table Information", "elements": [{"name": "ccNdcInfo", "title": "MSIDN number range", "size": 12, "type": "fieldArray", "isMandatory": false, "initialValues": {"ccNdcInfo": [{"cc": "", "ndc": "", "snRangeStart": "", "snRangeStop": "", "isDelete": true}]}, "validations": [{"type": "fieldArray-number<PERSON>nly", "message": "Only numbers allowed"}], "headersData": ["CC", "NDC", "SN range start", "SN range stop", ""], "fieldsData": [{"name": "cc", "type": "text", "isInfo": true, "info": "Country Code"}, {"name": "ndc", "type": "text", "isInfo": true, "info": "Enter the National Destination Code"}, {"name": "snRangeStart", "type": "text"}, {"name": "snRangeStop", "type": "text"}, {"name": "isDelete", "type": "delete"}]}, {"name": "networkNodeGT", "title": "Network node GT number range", "size": 12, "type": "fieldArray", "isMandatory": false, "initialValues": {"networkNodeGT": [{"cc": "", "ndc": "", "snRangeStart": "", "snRangeStop": "", "isDelete": true}]}, "validations": [{"type": "fieldArray-number<PERSON>nly", "message": "Only numbers allowed"}], "headersData": ["CC", "NDC", "SN range start", "SN range stop", ""], "fieldsData": [{"name": "cc", "type": "text", "isInfo": true, "info": "Country Code"}, {"name": "ndc", "type": "text", "isInfo": true, "info": "Enter the National Destination Code"}, {"name": "snRangeStart", "type": "text"}, {"name": "snRangeStop", "type": "text"}, {"name": "isDelete", "type": "delete"}]}, {"name": "sscpCarrierInfo", "title": "SSCP carrier name", "size": 12, "type": "fieldArray", "initialValues": {"sscpCarrierInfo": [{"name": "", "connectivity": "", "comments": "", "dpc": [{"signature": "", "type": "", "internationalDPC": "", "comments": ""}], "isDelete": true}]}, "headersData": ["SSCP carrier name", "Connectivity", "Comments", "DPC information", ""], "fieldsData": [{"name": "name", "type": "text"}, {"name": "connectivity", "type": "text"}, {"name": "comments", "type": "text"}, {"name": "dpc", "type": "hiddeneye"}, {"name": "isDelete", "type": "delete"}]}]}]}