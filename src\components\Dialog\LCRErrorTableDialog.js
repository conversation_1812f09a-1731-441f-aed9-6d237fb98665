import React from "react";
import Dialog from "@mui/material/Dialog";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import Button from "../Buttons/Button";
import theme from "../../tailwind-theme";
import { DeleteAllIcon } from "../../icons";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";

function LCRErrorTableDialog({ errorMessages, open, onClose }) {
  const parseErrorMessages = (messages) => {
    if (!messages || typeof messages !== "string") {
      // Return an empty array if messages is undefined, null, or not a string
      return [];
    }

    const messageLines = messages
      .replace("Validation Errors:\n", "") // Remove the prefix
      .split("\n") // Split by line breaks
      .filter((line) => line.trim() !== ""); // Filter out empty lines

    const parsedMessages = {};
    messageLines.forEach((line) => {
      const rowMatch = line.match(/Row (\d+):/); // Match "Row X:"
      const rowNumber = rowMatch ? rowMatch[1] : "Unknown";
      const errorParts = line.split(":");
      const errorMessage = errorParts.slice(1).join(":").trim();

      // Add errors to the same row number
      if (parsedMessages[rowNumber]) {
        parsedMessages[rowNumber].push(errorMessage);
      } else {
        parsedMessages[rowNumber] = [errorMessage];
      }
    });

    // Convert the parsedMessages object into an array of rows
    return Object.entries(parsedMessages).map(([rowNumber, errors]) => ({
      rowNumber,
      errorMessage: errors
        .join(", ") // Combine messages with a comma
        .replace(/;/g, ","), // Remove semicolons
    }));
  };

  const data = parseErrorMessages(errorMessages);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 800,
          maxHeight: 600,
        },
      }}
    >
      <div className="flex justify-end pr-4 pt-2">
        <IconButton onClick={onClose} size="small">
          <CloseIcon className="w-4 h-4 text-black" />
        </IconButton>
      </div>

      <div className="flex justify-center items-center my-2">
        <DeleteAllIcon />
      </div>

      <div className="mt-2 mx-10">
        <h2 className="text-lg font-semibold flex justify-center items-center mb-4">
          Errors in Uploaded File
        </h2>
        <TableContainer
          component={Paper}
          variant="outlined"
          sx={{
            maxHeight: 300,
          }}
        >
          <Table stickyHeader sx={{ minWidth: 550 }} aria-label="sticky table">
            <TableHead sx={{ bgcolor: theme.backgroundColor.bgTable }}>
              <TableRow>
                <TableCell
                  sx={{
                    position: "sticky",
                    top: 0,
                    zIndex: 1,
                    backgroundColor: theme.backgroundColor.bgTable,
                    fontWeight: "bold",
                  }}
                >
                  <div className="whitespace-nowrap font-bold text-sm">{`Error on - row no`}</div>
                </TableCell>
                <TableCell
                  sx={{
                    position: "sticky",
                    top: 0,
                    zIndex: 1,
                    backgroundColor: theme.backgroundColor.bgTable,
                    fontWeight: "bold",
                  }}
                >
                  <div className="whitespace-nowrap font-bold text-sm">
                    Error message
                  </div>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data.map((row, index) => (
                <TableRow key={index}>
                  <TableCell>{row.rowNumber}</TableCell>
                  <TableCell>{row.errorMessage}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </div>

      <div className="text-center mt-10 mb-4">
        <Button
          type="submit"
          label={"Okay"}
          buttonClassName="w-[200px] h-9 text-xs ml-5"
          onClick={onClose}
        />
      </div>
    </Dialog>
  );
}

export default LCRErrorTableDialog;
