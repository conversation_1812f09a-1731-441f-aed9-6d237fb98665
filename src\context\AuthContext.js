import React, { useState, useMemo, useEffect, useCallback } from "react";
import { loginUrlBase } from "../routes/ApiUrls";
import ThemedSuspense from "../components/ThemedSuspense";
import moment from "moment";
import axios from "axios";
import { TIME_ZONE } from "../common/constants";
const loginUrl = loginUrlBase;

export const AuthContext = React.createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoaded, setLoaded] = useState(false);
  const [loginTime, setLoginTime] = useState(null);
  const [accessToken, setAccessToken] = useState(null);
  const [reset, setReset] = useState(false);
  const [storeLoginTime, setStoreLoginTime] = useState();
  const [timezone, setTimezone] = useState(
    Intl.DateTimeFormat().resolvedOptions().timeZone
  );

  const refreshTokens = useCallback(() => {
    return axios
      .post(`${loginUrl}/v1/auth/refresh-tokens`, {})
      .then(({ data }) => {
        setUser(data.user);
        setAccessToken(data.token);
        return data;
      })
      .catch((error) => {
        const currentPath = window.location.pathname;
        setAccessToken(null);
        if (
          !currentPath.includes("/auth/login") &&
          currentPath.includes("/auth/verify-otp")
        ) {
          window.location.href = "/auth/login";
        }

        return error;
      });
  }, []);

  const startSilentRefresh = useCallback(() => {
    if (accessToken) {
      const tokenExpires = moment(accessToken.expires);
      const tokenMaxAge = tokenExpires.diff(moment().add(1, "minutes"));
      setTimeout(() => {}, tokenMaxAge);
    }
  }, [accessToken, refreshTokens]);

  const syncLogout = (event) => {
    if (event.key === "logout") {
      setAccessToken(null);
    }
  };
  useEffect(() => {
    const interceptorId = axios.interceptors.request.use(
      (config) => {
        config.withCredentials = true;
        config.credentials = "include";
        if (accessToken) {
          config.headers.Authorization = `Bearer ${accessToken.token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
    return () => {
      axios.interceptors.request.eject(interceptorId);
    };
  }, [accessToken]);

  useEffect(() => {
    startSilentRefresh();
  }, [accessToken, startSilentRefresh]);

  useEffect(() => {
    refreshTokens().then(() => {
      setLoaded(true);
    });
  }, [refreshTokens]);

  useEffect(() => {
    window.addEventListener("storage", syncLogout);
    return function cleanup() {
      window.removeEventListener("storage", syncLogout);
    };
  }, []);

  const value = useMemo(() => {
    const login = async (email, password, navigate, callback = null) => {
      try {
        const response = await axios.post(`${loginUrl}/v1/auth/login`, {
          email,
          password,
          timezone: TIME_ZONE,
        });

        const { data } = response;
        if (!data) {
          throw new Error("Invalid response from server");
        }
        const setupAuth = (token) => {
          setUser(data.user);
          setAccessToken(token);
          const currentLoginTime = new Date();
          window.localStorage.setItem("loginTime", currentLoginTime);
          setLoginTime(currentLoginTime);
          return {
            headers: {
              Authorization: `Bearer ${token?.token}`,
            },
          };
        };
        if (data.resetPassword === true) {
          setReset(true);
          navigate("/app/home");

          return setupAuth(data.token);
        }
        if (!data.otpSent) {
          const config = setupAuth(data.token);
          navigate("/app/home");

          return config;
        }
        if (callback) {
          callback(response);
        }
        return response;
      } catch (error) {
        throw error;
      }
    };

    const logout = () => {
      return axios
        .post(`${loginUrl}/v1/auth/logout`, {})
        .then((response) => {
          setUser(null);
          setAccessToken(null);
          setLoginTime(null);
          window.localStorage.setItem("logout", moment());
        })
        .catch((err) => {});
    };

    const otpVerificationCall = async (email, OTP, password) => {
      try {
        const { data } = await axios.post(`${loginUrl}/v1/auth/verify-otp`, {
          email,
          otp: OTP,
          password: password,
        });
        const setupAuth = (token) => {
          setUser(data.user);
          setAccessToken(token);
          return {
            headers: {
              Authorization: `Bearer ${token.token}`,
            },
          };
        };
        if (data.resetPassword === true) {
          setReset(true);
        }
        const currentLoginTime = new Date();
        window.localStorage.setItem("loginTime", currentLoginTime);
        setLoginTime(currentLoginTime);
        const config = setupAuth(data.token);
        return config;
      } catch (error) {
        console.error("OTP verification error:", error);
        throw error;
      }
    };

    const forgotPassword = (email) => {
      return axios.post(`${loginUrl}/v1/auth/forgot-password`, {
        email: email,
      });
    };

    const resetPassword = (password, resetToken) => {
      return axios.post(
        `${loginUrl}/v1/auth/reset-password?token=${resetToken}`,
        {
          password: password,
        }
      );
    };

    const verifyEmail = (emailVerificationToken) => {
      return axios.post(
        `${loginUrl}/v1/auth/verify-email?token=${emailVerificationToken}`,
        {}
      );
    };

    return {
      user,
      setUser,
      setLoginTime,
      loginTime,
      login,
      logout,
      forgotPassword,
      resetPassword,
      verifyEmail,
      otpVerificationCall,
      reset,
      setReset,
      storeLoginTime,
      setStoreLoginTime,
      timezone,
      setTimezone,
    };
  }, [user, accessToken, loginTime, startSilentRefresh, reset]);

  if (!isLoaded) {
    return <ThemedSuspense />;
  }
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
