{"header": "List of supplier based MNP rules", "columns": [{"header": "Rule ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "Customer name", "idToName": true, "nameFiled": "name", "moduleName": "customer-supplier-management", "accessorKey": "attributes.customerName", "attributeKey": "custId"}, {"header": "Supplier name", "idToName": true, "nameFiled": "name", "moduleName": "customer-supplier-management", "accessorKey": "attributes.supplierName", "attributeKey": "supId"}, {"header": "Destination type", "accessorKey": "attributes.destinationType"}, {"header": "Operator name", "idToName": true, "nameFiled": "operatorName", "moduleName": "operator", "accessorKey": "attributes.operatorName", "attributeKey": "mnpOpId"}, {"header": "Operator group name", "idToName": true, "nameFiled": "clusterName", "moduleName": "operator-cluster", "accessorKey": "attributes.operatorGroupName", "attributeKey": "mnpClsId"}, {"header": "Rule status", "readOnly": true, "accessorKey": "attributes.ruleStatus"}, {"buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"info": "Select account of type customer or customer & supplier", "name": "custId", "size": 6, "type": "select", "title": "Customer name", "filter": {"operatorType": ["C", "C,S"]}, "isInfo": true, "isMandatory": true, "nonEditable": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select the customer"}], "defaultValue": "", "isDropDownApi": true, "validationType": "number"}, {"info": "Select account of type supplier or customer & supplier", "name": "supId", "size": 6, "type": "select", "title": "Supplier name", "filter": {"operatorType": ["S", "C,S"]}, "isInfo": true, "isMandatory": true, "nonEditable": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select the supplier"}], "defaultValue": "", "isDropDownApi": true, "validationType": "number"}, {"info": "Choose the destination operator.", "name": "destinationType", "size": 6, "type": "radio", "title": "Destination type", "isInfo": true, "options": [{"label": "Operator", "value": 0}, {"label": "Operator group", "value": 1}], "isMandatory": true, "nonEditable": true, "validations": [{"type": "required", "message": "Please select the destination type"}], "defaultValue": 0, "validationType": "number"}, {"info": "Choose the destination operator.", "name": "mnpOpId", "size": 6, "type": "select", "title": "Destination operator", "isInfo": true, "dynamic": {"field": "destinationType", "value": 0}, "isMandatory": true, "onClickPath": "operators", "validations": [{"type": "required", "message": "Please select the destination operator"}], "defaultValue": "", "isDropDownApi": true, "validationType": "number"}, {"info": "choose the destination operator group", "name": "mnpClsId", "size": 6, "type": "select", "title": "Destination operator group", "isInfo": true, "dynamic": {"field": "destinationType", "value": 1}, "isMandatory": true, "onClickPath": "operator-clusters", "validations": [{"type": "required", "message": "Please select the destination operator group"}], "defaultValue": "", "isDropDownApi": true, "validationType": "number"}, {"name": "ruleStatus", "size": 6, "type": "switch", "title": "Rule status", "fieldType": "number", "defaultValue": 127, "options": [{"checked": true, "label": "Active", "value": 1}, {"checked": false, "label": "Inactive", "value": 0}], "isInfo": true, "info": "Choose the status of the rule"}], "formType": "simple", "buttonName": "+ Add supplier based MNP rule", "moduleData": "supplier-mnp-rule", "moduleName": "Supplier MNP rule configuration", "globalSearch": [{"label": "Rule ID", "value": "id"}, {"label": "Customer name", "value": "custId"}, {"label": "Supplier name", "value": "supId"}, {"label": "Operator name", "value": "mnpOpId"}, {"label": "Operator group name", "value": "mnpClsId"}], "dropdownOptions": {"field": "destinationType", "options": [{"label": "ALL", "query": "", "value": "ALL"}, {"label": "Operator", "query": "filters[destinationType][$eq]=0", "value": "0"}, {"label": "Operator group", "query": "filters[destinationType][$eq]=1", "value": "1"}], "defaultQuery": "", "defaultValue": "ALL"}}