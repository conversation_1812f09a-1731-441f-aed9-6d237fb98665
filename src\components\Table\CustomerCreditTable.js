import React, { useState, useEffect } from "react";
import { <PERSON>Array, useField } from "formik";
import { PlusCircleIconOutline, DeleteIcon, HiddenEye } from "../../icons";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  InputAdornment,
} from "@mui/material";
import TextFieldWrapper from "../FormsUI/TextField";
import SelectWrapper from "../FormsUI/Select";
import CustomSwitch from "../ToggleSwitch/CustomSwitch";
import DialogBox from "../../components/Dialog/FieldArrayDialog";
import { DottedBox } from "../../components/Dialog/FieldArrayDialog";
import ResultPerPageComponent from "../Pagination/ResultPerPage";
import Pagination from "../Pagination/Pagination";
import theme from "../../tailwind-theme";
import DeleteDialog from "../Dialog/DeleteDialog";
import { MAX_THRESHOLDLEVEL } from "../../common/config";
import InputLabel from "../FormsUI/InputLabel";
import { CssTooltip } from "../FormsUI/StyledComponent";

const RenderTableBody = ({
  name,
  values,
  fields,
  hideFieldMatches,
  setFieldValue,
  isDisabled,
  paginatedRows,
  remove,
  push,
  totalRows,
  setCurrentPage,
  limitPerPage,
  currentPage,
  isInfo,
  info,
}) => {
  const [open, setOpen] = useState(false);
  const [dialogFormData, setDialogFormData] = useState({});
  const [deleteIndex, setDeleteIndex] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);

  const handleClickOpen = (index) => {
    const rowData = paginatedRows[index] || {};
    setDialogFormData({ ...rowData, index });
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setDialogFormData({});
  };

  const handleSaveDialogData = (newData, index) => {
    const updatedValues = [...values[name]];
    if (updatedValues[index]) {
      if (
        !updatedValues[index].dpc ||
        !Array.isArray(updatedValues[index].dpc)
      ) {
        updatedValues[index].dpc = [];
      }
      if (Array.isArray(newData.dpc)) {
        updatedValues[index].dpc = newData.dpc;
      } else {
        updatedValues[index].dpc.push(newData);
      }

      setFieldValue(name, updatedValues);
    }

    handleClose();
  };

  const totalPages = Math.ceil(totalRows / limitPerPage);

  const isRowFilled = (row) => {
    return fields.some((field) => {
      const fieldValue = row[field.name];
      if (field.type === "select" || field.type === "text") {
        return (
          fieldValue !== undefined && fieldValue !== null && fieldValue !== ""
        );
      }
      if (field.type === "switch") {
        return fieldValue === true;
      }
    });
  };

  const handleDeleteClick = (index) => {
    setDeleteIndex(index);
    setDeleteDialog(true);
  };

  const handleConfirmDelete = () => {
    setDeleteLoading(true);

    let updatedRows = JSON.parse(JSON.stringify(values[name]));

    if (deleteIndex <= 3 && values[name].length === 4) {
      updatedRows[deleteIndex] = Object.fromEntries(
        Object.keys(updatedRows[deleteIndex] || {}).map((key) => [
          key,
          key === "thresholdLevel" || key === "id"
            ? updatedRows[deleteIndex][key]
            : Array.isArray(updatedRows[deleteIndex][key])
            ? []
            : "",
        ])
      );
    } else {
      updatedRows.splice(deleteIndex, 1);
    }

    setFieldValue(name, updatedRows, false);
    setDeleteLoading(false);
    setDeleteDialog(false);
  };

  const saveFormData = () => {
    localStorage.setItem("formData", JSON.stringify(values));
  };

  useEffect(() => {
    const savedData = localStorage.getItem("formData");
    if (savedData) {
      setFieldValue(name, JSON.parse(savedData)[name]);
    }
  }, []);

  const [field, meta] = useField(name);
  const [showToolTip, setShowTooltip] = useState(true);

  return (
    <FieldArray name={name}>
      {() => (
        <>
          {paginatedRows.map((row, index) => {
            const globalIndex = (currentPage - 1) * limitPerPage + index;
            return (
              <TableRow key={row.id}>
                {fields.map((field, fieldIndex, setFieldValue) => (
                  <TableCell
                    key={fieldIndex}
                    sx={{
                      whiteSpace: "nowrap",
                      maxWidth: "200px",
                    }}
                  >
                    {field.type === "text" ? (
                      <div>
                        <TextFieldWrapper
                          name={`${name}.${index}.${field.name}`}
                          isDisabled={isDisabled}
                          isPassword={field.isPassword}
                          className="w-full"
                          sx={{
                            maxWidth: "150px",
                          }}
                          noError={true}
                          onFocus={() => setShowTooltip(false)}
                          onBlur={() => setShowTooltip(false)}
                          onMouseEnter={() => setShowTooltip(true)}
                          toolTip={showToolTip}
                        />
                      </div>
                    ) : field.type === "static" ? (
                      <InputLabel
                        label={`Threshold level ${globalIndex + 1}`}
                        isMandatory={true}
                      ></InputLabel>
                    ) : null}
                  </TableCell>
                ))}
                <TableCell>
                  {(globalIndex <= 3 &&
                    isRowFilled(paginatedRows[globalIndex])) ||
                  globalIndex > 3 ? (
                    <IconButton
                      type="button"
                      onClick={() => handleDeleteClick(globalIndex)}
                      disabled={isDisabled}
                    >
                      <DeleteIcon />
                    </IconButton>
                  ) : null}
                </TableCell>
              </TableRow>
            );
          })}

          {Array.length <= 0 ||
            (currentPage === totalPages &&
              (paginatedRows.length < MAX_THRESHOLDLEVEL ? (
                <TableRow className="sticky bottom-0 z-1 bg-white">
                  <TableCell className="text-textNAColor">
                    Threshold level {paginatedRows.length * currentPage + 1}
                  </TableCell>
                  {Array(fields.length - 1)
                    .fill("")
                    .map((_, idx) => (
                      <TableCell key={idx}>
                        <DottedBox />
                      </TableCell>
                    ))}
                  <TableCell colSpan={fields.length + 1} align="right">
                    <IconButton
                      type="button"
                      onClick={() => {
                        const newEntry = fields.reduce((acc, field) => {
                          // acc.id = Date.now();
                          if (field.name === "isDelete") {
                            acc[field.name] = true;
                          } else if (field.name === "thresholdLevel") {
                            acc[field.name] = paginatedRows.length + 1;
                          } else {
                            acc[field.name] = "";
                          }
                          return acc;
                        }, {});
                        push(newEntry);
                        const lastPage = Math.ceil(
                          (totalRows + 1) / limitPerPage
                        );
                        setCurrentPage(lastPage);
                      }}
                      disabled={
                        isDisabled ||
                        !isRowFilled(paginatedRows[paginatedRows.length - 1])
                      }
                    >
                      <PlusCircleIconOutline
                        style={{
                          color:
                            isDisabled ||
                            paginatedRows.some((row) => !isRowFilled(row))
                              ? theme.textColor.mildGray
                              : "black",
                        }}
                      />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ) : null))}

          <DialogBox
            open={open}
            handleClose={handleClose}
            name="dpc"
            setFieldValue={setFieldValue}
            values={dialogFormData}
            onSave={(newData) =>
              handleSaveDialogData(newData, dialogFormData.index)
            }
            isDisabled={isDisabled}
            // onRemoveRow={handleRemoveRow}
          />
          <DeleteDialog
            show={deleteDialog}
            onHide={() => setDeleteDialog(false)}
            onConfirm={handleConfirmDelete}
            title={
              <>
                Are you sure you want to delete?
                <br />
                (Deletion will only happen on click of save)
              </>
            }
            isLoading={deleteLoading}
          />
        </>
      )}
    </FieldArray>
  );
};

const RenderTable = ({
  name,
  values,
  headers,
  fields,
  hideFieldMatches,
  isDisabled,
  setFieldValue,
  isInfo,
  info,
  moduleName,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [limitPerPage, setLimitPerPage] = useState(10);

  const handleLimitChange = (e) => {
    setLimitPerPage(parseInt(e.target.value, 10));
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const rows = Array.isArray(values[name])
    ? values[name].filter((row) => row !== null && row !== undefined)
    : [];
  const totalRows = rows.length;

  const paginatedRows = rows.slice(
    (currentPage - 1) * limitPerPage,
    currentPage * limitPerPage
  );

  const filteredHeaders = headers.filter(
    (header, index) =>
      !(hideFieldMatches && fields[index]?.name === "targetPrice")
  );

  const rowsFilled = Array.isArray(values[name])
    ? values[name].filter((row) => {
        return (
          row &&
          fields.some((field) => {
            const fieldValue = row[field.name];
            if (field.type === "select" || field.type === "text") {
              return (
                fieldValue !== undefined &&
                fieldValue !== null &&
                fieldValue !== ""
              );
            }
          })
        );
      })
    : [];

  const totalFilledRows = rowsFilled.length;

  const startRow = (currentPage - 1) * limitPerPage + 1;
  let endRow = Math.min(totalFilledRows * limitPerPage, totalFilledRows);

  return (
    <div>
      <TableContainer
        component={Paper}
        sx={
          moduleName !== "channel-partners"
            ? {
                boxShadow: "none",
                maxHeight: "440px",
                overflowY: "auto",
                overflowX: "auto",
              }
            : {}
        }
      >
        <Table
          sx={
            moduleName !== "channel-partners"
              ? {
                  minWidth: 550,
                  border: "1.5px solid #BEBEBE",
                  // border: `1.5px solid ${
                  //   meta.error && meta.touched ? "#DC3833" : "#BEBEBE"
                  // }`,
                }
              : {}
          }
          aria-label="simple table"
        >
          <TableHead
            sx={{
              bgcolor: theme.backgroundColor.bgTable,
              height: 20,
              zIndex: 1,
            }}
            className="sticky top-0"
          >
            <TableRow>
              {filteredHeaders.map((header, index) => (
                <TableCell
                  key={index}
                  sx={{ fontSize: 14, fontWeight: 600, whiteSpace: "nowrap" }}
                >
                  {header}
                </TableCell>
              ))}
              <TableCell />
            </TableRow>
          </TableHead>
          <TableBody>
            <FieldArray name={name}>
              {({ push, remove }) => (
                <RenderTableBody
                  name={name}
                  values={values}
                  fields={fields}
                  hideFieldMatches={hideFieldMatches}
                  isDisabled={isDisabled}
                  paginatedRows={paginatedRows}
                  remove={remove}
                  push={push}
                  totalRows={totalRows}
                  setCurrentPage={setCurrentPage}
                  limitPerPage={limitPerPage}
                  currentPage={currentPage}
                  setFieldValue={setFieldValue}
                  isInfo={isInfo}
                  info={info}
                />
              )}
            </FieldArray>
          </TableBody>
        </Table>
      </TableContainer>
      {/* {meta.touched && meta.error ? (
        <div className="text-[#d32f2f] text-[0.75rem] mt-0 font-normal">
          {meta.error}
        </div>
      ) : null} */}
      <div className="flex items-center justify-between my-5 ml-5 w-[95%] bottom-0">
        <div className="flex items-center">
          <ResultPerPageComponent
            limit={limitPerPage}
            totalRows={totalRows}
            startRow={startRow}
            endRow={endRow}
            handleLimitChange={handleLimitChange}
          />
          <div className="text-[14px] text-gray-500 ml-5">
            {totalFilledRows === 0
              ? "0 - 0 of 0 rows"
              : `${startRow} - ${endRow} of ${totalFilledRows} rows`}
          </div>
        </div>
        <Pagination
          className="pagination-bar"
          currentPage={currentPage}
          totalCount={totalRows}
          pageSize={limitPerPage}
          onPageChange={handlePageChange}
        />
      </div>
    </div>
  );
};

const CustomerCreditTable = ({
  name,
  headers,
  fields,
  values,
  hideFieldMatches,
  isDisabled,
  setFieldValue,
  isInfo,
  info,
  moduleName,
}) => {
  return (
    <div>
      <RenderTable
        name={name}
        headers={headers}
        fields={fields}
        values={values}
        hideFieldMatches={hideFieldMatches}
        isDisabled={isDisabled}
        setFieldValue={setFieldValue}
        isInfo={isInfo}
        info={info}
        moduleName={moduleName}
      />
    </div>
  );
};

export default CustomerCreditTable;
