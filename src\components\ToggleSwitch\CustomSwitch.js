import React, { useEffect } from "react";
import Switch from "@mui/material/Switch";
import FormControlLabel from "@mui/material/FormControlLabel";
import theme from "../../tailwind-theme";
import { useField, useFormikContext } from "formik";

const CustomSwitch = ({
  name,
  label,
  disabled,
  isDisabled,
  marginLeft,
  options,
  onChange,
  defaultValue,
  action,
  values,
}) => {
  const [field] = useField(name);
  const { setFieldValue, setFieldTouched } = useFormikContext();
  const checkedOption = options?.find((opt) => opt.checked === true)?.value;
  const uncheckedOption = options?.find((opt) => opt.checked === false)?.value;

  // useEffect(() => {
  //   if (
  //     defaultValue !== undefined &&
  //     (field.value === undefined ||
  //       field.value === null ||
  //       field.value < 0 ||
  //       field.value === "")
  //   ) {
  //     setFieldValue(name, defaultValue);
  //   }
  // }, [defaultValue, name, setFieldValue, field.value, action]);

  const switchStyles = {
    thumb: {
      width: "19px",
      height: "19px",
      borderRadius: "50%",
      boxShadow: "none",
      backgroundColor: theme.backgroundColor.bgSwitch,
      border: "1px solid #7070708C",
    },
    track: {
      width: "33px",
      height: "14px",
      color: field.value === checkedOption ? "#3576eb" : "#e0dddd",
      opacity: field.value === checkedOption ? "1.5 !important" : "0.5",
      border: "1px solid black",
      cursor: isDisabled ? " " : "pointer",
    },
  };

  return (
    <FormControlLabel
      control={
        <Switch
          name={name}
          checked={field.value === checkedOption}
          onChange={
            !isDisabled
              ? (e) => {
                  const newValue = e.target.checked
                    ? checkedOption
                    : uncheckedOption;
                  setFieldValue(name, newValue);
                  setFieldTouched(name, true);
                  if (onChange) {
                    onChange(newValue);
                  }
                  if (name === "isCustomPath" && Number(newValue) === 0) {
                    //  setFieldValue("ss7PathExtended", null);
                    delete values["ss7PathExtended"];
                  }
                }
              : null
          }
          disabled={disabled}
          className={marginLeft ? "ml-5" : ""}
          sx={{
            "& .MuiSwitch-thumb": switchStyles.thumb,
            "& .MuiSwitch-track": switchStyles.track,
          }}
        />
      }
      label={label}
    />
  );
};

export default CustomSwitch;
