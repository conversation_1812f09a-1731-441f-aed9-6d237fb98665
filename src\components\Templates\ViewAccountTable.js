import React, { useContext, useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "react-query";
import {
  getGlobalSearchList,
  getPageSchema,
  getPointCodeData,
  getPointCodeListData,
} from "../../lib/list-api";
import SearchComponent from "../SearchComponent";
import CustomTable from "../Table";
import SuccessDialog from "../Dialog/SuccessDialog";
import ResultPerPageComponent from "../Pagination/ResultPerPage";
import Pagination from "../Pagination/Pagination";
import useDebounce from "../../common/useDebounce";
import viewing from "../../icons/viewing.png";
import { ShareIcon } from "../../icons";
import ErrorDialog from "../Dialog/ErrorDialog";
import { useLocation } from "react-router-dom";
import { CssTooltip } from "../FormsUI/StyledComponent";
import { TransformData } from "../TransformData/TransformData";
import { DownloadContext } from "../../context/DownloadContext";
import ExportPopup from "../Dialog/exportpopup";
import dayjs from "dayjs";
import { moduleConfiguration } from "../../common/constants";
import { DEFAULT_PAGE_SIZE } from "../../common/config";
import { DataContext } from "../../context/DataContext";

const ViewAccountTable = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const pointCodeData = location?.state?.rowData || [];
  const [currentPage, setCurrentPage] = useState(1);
  const [limitPerPage, setLimitPerPage] = useState(DEFAULT_PAGE_SIZE);
  const [searchText, setSearchText] = useState("");
  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErroDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [jsonData, setJsonData] = useState({});
  const [columnsData, setColumnsData] = useState([]);
  const [rowSelection, setRowSelection] = useState({});
  const [exportPath, setExportPath] = useState("");
  const debouncedValue = useDebounce(searchText, 500);
  const [listData, setListData] = useState([]);
  const [pagination, setPagination] = useState("");
  const [globalSearch, setGlobalSearch] = useState([]);
  const [globalSearchSelect, setGlobalSearchSelect] = useState("");
  const [exportAllDownload, setExportAllDownload] = useState(false);
  const { setIsDownloading, isDownloading } = useContext(DownloadContext);
  const [extensionType, setExtensionType] = useState("");
  const [selectedIds, setSelectedIds] = useState([]);
  const [showExportConfirmation, setShowExportConfirmation] = useState(false);
  const [exportFlag, setExportFlag] = useState(false);
  const [filteredRow, setFilteredRow] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [filteredData, setFilteredData] = useState([]);
  const [rowData, setRowData] = useState([]);
  const [refetchData, setRefetchData] = useState(false);
  const rowDataRef = useRef(rowData);
  const { rowDetails } = useContext(DataContext);

  const handleLimitChange = (e) => {
    setLimitPerPage(e.target.value);
    setCurrentPage(1);
  };
  const url = window.location.href;
  const id = url.match(/\/(\d+)$/)?.[1];

  const rowId = id ? id : location?.state?.rowId;
  const collectionName = location?.state?.collectionName;
  const isViewList = location?.state?.isViewList;
  const isViewAccount = location?.state?.isViewAccount;
  const name = location?.state?.name;
  const connType = location?.state?.connType;

  const exportReport = (type) => {
    if (type !== "") {
      setExportAllDownload(true);
      setIsDownloading(true);
      let extension = type === "EXCEL" ? "xls" : "csv";
      setExtensionType(extension);
    }
  };
  useEffect(() => {
    const selectedIds = Object.keys(rowSelection)
      .filter((key) => rowSelection[key] === true)
      .map((key) => Number(key));
    setSelectedIds(selectedIds);
  }, [rowSelection]);

  useEffect(() => {
    setRowData(filteredRow);
  }, [filteredRow]);
  useEffect(() => {
    rowDataRef.current = rowData;
  }, [rowData]);

  const handleExportCLick = () => {
    if (rowDataRef.current === 0) {
      setErroDialog(true);
      setMessage("No data to export");
      setRefetchData(true);
    } else {
      setShowExportConfirmation(true);
    }
  };

  const hasRowSelection = Object.keys(rowSelection).length > 0;
  const idsValue = (() => {
    if (!isSearching && !hasRowSelection) return "*";
    if (!isSearching && hasRowSelection) return `[${selectedIds}]`;
    if (isSearching && !hasRowSelection) return `[${filteredData}]`;
    if (isSearching && hasRowSelection) return `[${selectedIds}]`;
  })();

  const { isLoading, refetch } = useQuery(
    [
      "Point code filter data",
      rowId,
      collectionName === moduleConfiguration.pointCodeModuleName
        ? moduleConfiguration.pointCodeListImportName
        : moduleConfiguration.redirectionalListImportName,
      limitPerPage,
      currentPage,
    ],
    getPointCodeData,
    {
      enabled:
        isViewList === undefined && (!globalSearchSelect || !debouncedValue),
      onSuccess: (resp) => {
        setPagination(resp?.data || []);
        setListData(
          TransformData(
            resp?.data?.data || [],
            jsonData?.schemadetails?.elements || [],
            [],
            [],
            []
          )
        );
      },
      refetchOnWindowFocus: false,
    }
  );

  useQuery(
    [
      "Point code list filter data",
      rowId,
      collectionName === moduleConfiguration.pointCodeListModuleName
        ? moduleConfiguration.pointCodeImportName
        : moduleConfiguration.redirectionalAccImportName,
      limitPerPage,
      currentPage,
      connType,
    ],
    getPointCodeListData,
    {
      enabled:
        isViewList !== undefined && (!globalSearchSelect || !debouncedValue),
      onSuccess: (resp) => {
        setPagination(resp?.data || []);
        setListData(
          TransformData(
            resp?.data?.data || [],
            jsonData?.schemadetails?.elements || [],
            jsonData?.schemadetails || []
          )
        );
      },
      refetchOnWindowFocus: false,
    }
  );

  useQuery(
    [
      "collectionnName" + collectionName,
      exportPath,
      globalSearchSelect,
      debouncedValue,
      exportFlag,
      idsValue,
      extensionType,
      "",
      collectionName === moduleConfiguration.pointCodeListModuleName
        ? moduleConfiguration.pointCodeImportName
        : collectionName === moduleConfiguration.pointCodeModuleName
        ? moduleConfiguration.pointCodeListImportName
        : collectionName === moduleConfiguration.redirectionalListModuleName
        ? moduleConfiguration.redirectionalAccImportName
        : collectionName === moduleConfiguration.redirectionalAccModuleName
        ? moduleConfiguration.redirectionalListImportName
        : null,
      rowId ? rowId : pointCodeData,
      "",
      limitPerPage,
      currentPage,
      isViewAccount,
    ],
    getGlobalSearchList,
    {
      enabled: (!!globalSearchSelect && !!debouncedValue) || exportFlag,
      onSuccess: (resp) => {
        if (exportFlag) {
          setIsDownloading(false);
          const url = URL.createObjectURL(resp.data);
          const link = document.createElement("a");
          link.href = url;
          link.download = `${exportPath}-${dayjs(Date.now()).format(
            "DD-MM-YYYY"
          )}.${extensionType}`;
          link.click();
          setExportAllDownload(false);
          setSelectedIds([]);
          setExportFlag(false);
        } else {
          setPagination(resp?.data || []);
          setListData(
            TransformData(
              resp?.data?.data || [],
              jsonData?.schemadetails?.elements || [],
              [],
              [],
              []
            )
          );
        }
      },
      onError: () => {
        setIsDownloading(false);
        setErroDialog(true);
        setMessage(exportFlag ? "Download failed" : "");
        setExportFlag(false);
      },
    }
  );

  useEffect(() => {
    if (listData && jsonData?.schemadetails) {
      const transformedData = TransformData(
        listData,
        jsonData?.schemadetails?.elements,
        collectionName,
        jsonData?.schemadetails
      );
      setListData(transformedData);
    }
  }, [jsonData]);

  // Form Schema
  useQuery([collectionName], getPageSchema, {
    refetchOnWindowFocus: false,
    onSuccess: (resp) => {
      const schemaDetails = resp?.data?.data?.[0]?.attributes?.schemadetails;
      const columnsData = schemaDetails?.columns;
      const modulePath = schemaDetails?.moduleData;
      const search = schemaDetails?.globalSearch;
      setExportPath(modulePath);
      setGlobalSearch(search);

      const processColumn = (x) => {
        if (x.accessorKey === "actions") {
          return {
            accessorKey: x.accessorKey,
            header: (
              <div className="flex justify-center items-center gap-5 p-5">
                <CssTooltip title={"Export All"} placement="top" arrow>
                  <ShareIcon
                    className="text-black cursor-pointer"
                    onClick={() => {
                      handleExportCLick();
                    }}
                  />
                </CssTooltip>
              </div>
            ),
            enableColumnFilter: false,
            enableSorting: false,
            enableColumnPinning: true,
            size: 130,
            Cell: ({ row }) => (
              <div className="flex justify-center items-center gap-3">
                {x.buttons?.map((y) => {
                  switch (y.title) {
                    case "View":
                      return (
                        <>
                          <CssTooltip title={"View"} placement="top" arrow>
                            <img
                              key="view"
                              className="w-6 h-4 "
                              src={viewing}
                              alt={y.title}
                              onClick={(e) => {
                                e.stopPropagation();
                              }}
                            />
                          </CssTooltip>
                        </>
                      );

                    default:
                      return null;
                  }
                })}
              </div>
            ),
          };
        } else if (x.accessorKey === "Sno") {
          return {
            accessorKey: x.accessorKey,
            header: x.header,
            Cell: ({ row }) => row.index + 1 + (currentPage - 1) * limitPerPage,
            enableSorting: false,
          };
        } else {
          if (x.renderFunction) {
            x.Cell = new Function("row", `return ${x.renderFunction}`);
          }
          return { ...x, enableSorting: true };
        }
      };

      let cData = [];

      if (Array.isArray(columnsData) && columnsData[0]?.columns) {
        cData = columnsData[0].columns.map(processColumn);
      } else if (Array.isArray(columnsData)) {
        cData = columnsData.map(processColumn);
      }

      setJsonData(resp?.data?.data?.[0]?.attributes);
      setColumnsData(cData);
    },
  });

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  return (
    <>
      <div className="flex flex-col">
        <div className="font-bold text-2xl">
          <div className="flex gap-2 font-medium my-7">
            {isViewAccount ? (
              <>
                <div
                  className="text-textNAColor text-base cursor-pointer hover:underline"
                  onClick={() => {
                    collectionName === "point-codes"
                      ? navigate("/app/list/point-code-lists")
                      : navigate("/app/list/redirectional-lists");
                  }}
                >
                  {collectionName === "point-codes"
                    ? `List of point code list - ${name} >`
                    : `List of redirectional list - ${name} >`}
                </div>
                <div className="text-base">
                  {collectionName === "point-codes"
                    ? "List of point code account"
                    : "List of redirectional account"}
                </div>
              </>
            ) : (
              <>
                <div
                  className="text-textNAColor text-base cursor-pointer hover:underline"
                  onClick={() => {
                    collectionName === "point-code-lists"
                      ? navigate("/app/list/point-codes")
                      : navigate("/app/list/redirectional-accounts");
                  }}
                >
                  {collectionName === "point-code-lists"
                    ? `List of Point code - ${name} > `
                    : `List of Redirectional account - ${name} > `}
                </div>
                <div className="text-base">
                  {collectionName === "point-code-lists"
                    ? "List of Point code list"
                    : "List of Redirectional list"}
                </div>
              </>
            )}
          </div>
        </div>

        <div className="flex my-6 justify-between">
          <SearchComponent
            onChange={(e) => {
              setSearchText(e.target.value);
              setCurrentPage(1);
            }}
            globalSearch={globalSearch}
            setGlobalSearchSelect={setGlobalSearchSelect}
          />
        </div>

        <div className="my-4">
          <CustomTable
            data={listData || []}
            rowSelection={rowSelection}
            onRowSelectionChange={setRowSelection}
            columns={columnsData}
            onRowClick={() => {}}
            isLoading={isLoading}
            setFilteredRow={setFilteredRow}
            setIsSearching={setIsSearching}
            setSelectedIds={setSelectedIds}
            setFilteredData={setFilteredData}
          />
        </div>
        {pagination && pagination?.meta?.pagination?.total !== undefined && (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              margin: "5px 0px 5px 5px",
              width: "95%",
            }}
          >
            <div className="flex">
              <ResultPerPageComponent
                limit={limitPerPage}
                handleLimitChange={handleLimitChange}
              />
              <div
                style={{
                  display: "flex",
                  fontSize: "14px",
                  padding: "10px 0px 0px 10px",
                  color: "#808080",
                }}
              >
                {!isSearching ? (
                  <>
                    {pagination?.meta?.pagination?.total === 0
                      ? 0
                      : (currentPage - 1) * limitPerPage + 1}
                    -
                    {Math.min(
                      limitPerPage * currentPage,
                      pagination?.meta?.pagination?.total
                    )}{" "}
                    of {pagination?.meta?.pagination?.total} rows
                  </>
                ) : (
                  <>
                    {`${
                      filteredRow === 0
                        ? 0
                        : (currentPage - 1) * limitPerPage + 1
                    }  - ${(currentPage - 1) * limitPerPage + filteredRow} of ${
                      pagination?.meta?.pagination?.total
                    } rows (Matched for this page)`}
                  </>
                )}
              </div>
            </div>
            <Pagination
              className="pagination-bar"
              currentPage={currentPage}
              totalCount={pagination?.meta?.pagination?.total || 0}
              pageSize={limitPerPage}
              onPageChange={handlePageChange}
              isSearching={isSearching}
            />
          </div>
        )}

        <ExportPopup
          show={showExportConfirmation}
          onHide={() => setShowExportConfirmation(false)}
          onConfirm={(type) => {
            exportReport(type);
            setExportAllDownload(true);
            setShowExportConfirmation(false);
            setExportFlag(true);
          }}
          title={"Export Report"}
          identity={"Reports"}
        />
        <SuccessDialog
          show={successDialog}
          onHide={() => {
            setSuccessDialog(false);
          }}
          message={message}
        />
        <ErrorDialog
          show={errorDialog}
          onHide={() => {
            if (!refetchData) {
              refetch();
            }
            setErroDialog(false);
          }}
          message={message}
        />
      </div>
    </>
  );
};

export default ViewAccountTable;
