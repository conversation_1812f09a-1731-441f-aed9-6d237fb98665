const handleFocusFirstError = (updatedErrors, setTouched) => {
  const errorKeys = Object.keys(updatedErrors);

  if (errorKeys.length > 0) {
    const allTouched = errorKeys.reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {});
    setTouched(allTouched);

    const firstErrorField = document.querySelector(`[name="${errorKeys[0]}"]`);

    if (firstErrorField) {
      if (firstErrorField.type === "hidden") {
        firstErrorField.type = "text";
        firstErrorField.style.visibility = "hidden";

        setTimeout(() => {
          firstErrorField.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
          firstErrorField.focus();

          setTimeout(() => {
            firstErrorField.type = "hidden";
            firstErrorField.style.visibility = "visible";
          }, 100);
        }, 50);
      } else {
        firstErrorField.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
        firstErrorField.focus();
      }
    }
  }
};

export const handleSubmitWithFocus = async (
  validateForm,
  submitForm,
  setTouched
) => {
  const updatedErrors = await validateForm();

  if (Object.keys(updatedErrors).length > 0) {
    handleFocusFirstError(updatedErrors, setTouched);
  } else {
    await submitForm();
  }
};
