import React, { useEffect } from "react";
import {
  MaterialReactTable,
  useMaterialReactTable,
} from "material-react-table";
import { Search } from "../icons";
import UnfoldMoreIcon from "@mui/icons-material/UnfoldMore";
import InputAdornment from "@mui/material/InputAdornment";
import styled from "styled-components";
import theme from "../tailwind-theme";
import TrimFunction from "./trimFunction";
const TableWrapper = styled.div`
  .MuiTableCell-head {
    background-color: ${theme.backgroundColor.bgTable} !important;
    color: #000000 !important;
    position: sticky;
    top: 0;
    z-index: ${({ $enableRowSelection }) => ($enableRowSelection ? 1 : "")};
  }
  .MuiTableCell-root.MuiTableCell-head[data-pinned="true"]:before {
    background-color: ${theme.backgroundColor.bgTable} !important;
  }
  .MuiTableHead-root {
    position: sticky;
    top: 0;
    z-index: ${({ $enableRowSelection }) => ($enableRowSelection ? 2 : "")};
  }
`;

const CustomTable = ({
  data,
  columns,
  onRowClick,
  rowSelection,
  onRowSelectionChange,
  isLcrDetails,
  showLastColumn,
  setFilteredRow,
  setIsSearching,
  isLoading,
  setSelectedIds,
  setFilteredData,
  isEsmeSessionColumns,
  isMinimumWidth,
  isShortCode,
}) => {
  const table = useMaterialReactTable({
    columns: TrimFunction(columns),
    data,
    enableColumnActions: false,
    enableTopToolbar: false,
    enableBottomToolbar: false,
    enableCellActions: true,
    enableColumnPinning: true,
    enableRowSelection: isLcrDetails || isEsmeSessionColumns ? false : true,
    onRowSelectionChange,
    enablePagination: false,
    enableSorting: true,
    enableStickyHeader: true,
    muiTableContainerProps: {
      sx: { maxHeight: "50vh" },
      lg: { maxHeight: "62vh" },
    },
    icons: {
      SortIcon: (props) => <UnfoldMoreIcon {...props} />,
    },
    initialState: {
      enableToolbar: false,
      enableColumnFilters: true,
      rowSelection: rowSelection || {},
      columnPinning: {
        left: ["mrt-row-select"],
        right: ["actions"],
      },
    },
    state: {
      rowSelection: rowSelection || {},
      isLoading: isLoading,
      showColumnFilters: !isEsmeSessionColumns,
    },
    getRowId: (originalRow) => originalRow?.id ?? `row-${Math.random()}`,
    muiTableBodyRowProps: ({ row }) => ({
      onClick: () => onRowClick(row.original),
      sx: {
        cursor: "pointer",
        ...(showLastColumn
          ? {
              "& td:last-child div": {
                visibility: "visible",
              },
            }
          : {
              "& td:last-child div": {
                visibility: "hidden",
              },
              "&:hover td:last-child div": {
                visibility: "visible",
              },
            }),
      },
    }),
    muiTablePaperProps: {
      sx: { boxShadow: "0", borderRadius: "10px" },
    },
    muiTableHeadCellProps: {
      sx: {
        backgroundColor: "#374150",
        fontSize: "12px",
        color: "#ffffff",
        fontFamily: "OpenSanHebrew",
        "& .Mui-TableHeadCell-Content-Wrapper": {
          whiteSpace: "nowrap",
        },
      },
    },
    muiTableBodyCellProps: ({ cell, row }) => {
      const columnId = cell.column.id;
      const shortCode = cell.getValue();

      if (columnId === "attributes.shortCode" && isShortCode) {
        const visibleRows = table.getSortedRowModel().rows;
        const groupedRows = visibleRows.filter(
          (r) => r?.original?.attributes?.shortCode === shortCode
        );
        const isFirstRow = groupedRows[0]?.id === row.id;
        const rowSpan = isFirstRow ? groupedRows.length : undefined;

        return {
          sx: {
            display: isFirstRow ? "table-cell" : "none",
            verticalAlign: "middle",
            textAlign: "center",
          },
          rowSpan,
        };
      }

      if (columnId === "actions") {
        return {
          sx: {
            minWidth: isMinimumWidth ? "60px" : "150px",
          },
        };
      }

      return {};
    },

    muiSelectCheckboxProps: {},
    muiFilterTextFieldProps: {
      InputProps: {
        startAdornment: (
          <InputAdornment position="start">
            <Search />
          </InputAdornment>
        ),
        style: {
          backgroundColor: "#ffffff",
          height: "30px",
          fontSize: 12,
          boxShadow: "0px 4px 4px 0px #0000001A",
        },
      },
      variant: "outlined",
    },
    renderEmptyRowsFallback: () => (
      <div
        style={{
          padding: "25px",
          fontSize: "16px",
          color: "#808080",
          fontStyle: "italic",
          marginLeft: "400px",
        }}
      >
        No records to display
      </div>
    ),
  });

  useEffect(() => {
    const filteredRows = table.getRowModel().rows;
    const filteredIds = filteredRows.map((row) => row.id);
    setFilteredData(filteredIds);
    setFilteredRow(filteredRows.length);

    if (filteredRows.length > 0) {
      const filteredIds = filteredRows.map((row) => row.id);
      setSelectedIds(filteredIds);
    }

    const filterData = table.getState().columnFilters.every((filter) => {
      return (
        filter.value === undefined ||
        filter.value === "" ||
        (Array.isArray(filter.value) &&
          filter.value.every((val) => val === undefined))
      );
    });
    setIsSearching(!filterData);
  }, [table.getState().columnFilters.length, table.getRowModel().rows.length]);

  return (
    <TableWrapper $enableRowSelection={isEsmeSessionColumns}>
      <MaterialReactTable
        table={table}
        onRowSelectionChange={onRowSelectionChange}
      />
    </TableWrapper>
  );
};

export default CustomTable;
