import React, { useContext, useState, useEffect } from "react";
import { multiStepFormContext } from "../../context/MultiStepFormContext";
import CustomStepper from "../../components/Stepper/CustomStepper";
import InputLabel from "../../components/FormsUI/InputLabel";
import GeneralInformationForm from "../../components/Forms/CustomerSupplierForms/GeneralInformationForm";
import PaymentInformationForm from "../../components/Forms/CustomerSupplierForms/PaymentInformationForm";
import BillingInformationForm from "../../components/Forms/CustomerSupplierForms/BillingInformationForm";
import RequiredInformationForm from "../../components/Forms/CustomerSupplierForms/RequiredInformationForm";
import { CustomerSupplierContext } from "../../context/CustomerSupplierContext";
import { useNavigate, useLocation } from "react-router-dom";
import { useQuery } from "react-query";
import { getCustomerDataById } from "../../lib/customer-supplier-api";
import RequiredInformationFormSS7 from "../../components/Forms/CustomerSupplierForms/RequiredInformationFormSS7";
import RequiredInformationFormSMPPES from "../../components/Forms/CustomerSupplierForms/RequiredInformationFormSMPPES";
import RequiredInformationHTTP from "../../components/Forms/CustomerSupplierForms/RequiredInformationHTTP";

import {
  interfaceType,
  moduleConfiguration,
  subInterfaceEsme,
} from "../../common/constants";
// import { useNavigate } from "react-router-dom";
import { getActionFromUrl, getIdAfterEdit } from "../../common/urlUtils";
import { CloseIcon } from "../../icons";
import {
  deleteIndexedDB,
  getIndexedDBDataById,
  updateIndexedDBDataById,
} from "../../components/HelperFunction/localStorage";
import {
  getActionAndModuleNameFromURL,
  getModuleNameFromURL,
} from "../../common/urlUtils";

const CustomerSupplier = () => {
  const { currentStep, setCurrentStep } = useContext(multiStepFormContext);
  const {
    setGeneralInfo,
    setBillingInfo,
    setPaymentInfo,
    setRequiredInfo,
    generalInfoData,
  } = useContext(CustomerSupplierContext);
  const [editDetails, setEditDetails] = useState({});
  const location = useLocation();
  const isEdit = location?.state?.isEdit;
  const isView = location?.state?.isView;
  const id = location?.state?.id;
  const navigate = useNavigate();
  const action = getActionFromUrl(window.location.href);
  const searchParams = new URLSearchParams(location.search);
  const currentId = searchParams.get("currentId");
  const [deleteLastEntry, setDeleteLastEntry] = useState(false);
  const [existingData, setExistingData] = useState(null);
  const [breadcrumbs, setBreadcrumbs] = useState([]);
  const editId = getIdAfterEdit(window.location.href);
  const loactionData = getActionAndModuleNameFromURL(window.location.href);
  const currentModuleName = getModuleNameFromURL(window.location.href);
  const moduleName = moduleConfiguration.customerSupplier;

  const steps = [
    "General Information",
    "Billing Information",
    "Payment Information",
    "Required Information",
  ];

  const { data: customerIdDetails } = useQuery(
    ["customer-supplier-managements", id],
    getCustomerDataById,
    {
      enabled: id > 0,
      refetchOnWindowFocus: false,
      onSuccess: ({ data }) => {
        setEditDetails(data?.data?.attributes);
        setGeneralInfo([]);
        setBillingInfo([]);
        setPaymentInfo([]);
        setRequiredInfo([]);
      },
    }
  );

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const fetchData = async (isPrevious) => {
    if (currentId) {
      try {
        const data = await getIndexedDBDataById(
          "navigationDetails",
          "FormDB",
          parseInt(currentId)
        );

        if (isPrevious && data?.data?.length) {
          const lastRecord = data.data.find(
            (ele) => ele.moduleNameValue === moduleName
          );

          if (lastRecord) {
            setDeleteLastEntry(true);
          }
        }
        setExistingData(data);
      } catch (error) {
        console.error("Error fetching data from IndexedDB:", error);
      }
    }
  };

  const title =
    action === "edit"
      ? "Edit customer/supplier"
      : action === "view"
      ? "View customer/supplier"
      : "Add customer/supplier";

  const header = "List of Customers & Suppliers";

  const generateBreadcrumbs = () => {
    const breadcrumbsList = [
      {
        name: header,
        path: `/app/list/${currentModuleName}`,
      },
    ];

    if (existingData?.data?.length) {
      const { data = [] } = existingData;
      breadcrumbsList[0].name = data[0].listHeader;

      data.forEach((entry, index) => {
        if (currentModuleName === entry.moduleNameValue) {
          breadcrumbsList[0].path = `/app/list/${currentModuleName}`;
          return;
        }
        breadcrumbsList.push({
          name: entry.previousModuleName || loactionData,
          path: entry.currentPath,
        });
      });

      breadcrumbsList.forEach((entry, index) => {
        if (breadcrumbsList.length > 1 && index < breadcrumbsList.length - 1)
          entry.path = null;
      });

      breadcrumbsList.push({ name: title, path: null });
    } else {
      breadcrumbsList.push({
        name: loactionData,
        path: null,
      });
    }

    setBreadcrumbs(breadcrumbsList);
  };

  const handleBreadcrumbClick = (path, index) => {
    if (path) {
      navigate(path);
      const updatedData = { data: existingData.data.slice(0, index) };
      updateIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        parseInt(currentId),
        updatedData
      );
      setExistingData(updatedData);
      generateBreadcrumbs();
    }
  };
  useEffect(() => {
    fetchData();
  }, [currentId]);

  useEffect(() => {
    if (existingData && action !== "view") {
      generateBreadcrumbs();
    }
  }, [existingData]);

  const getFormComponent = () => {
    switch (currentStep) {
      case 0:
        return (
          <GeneralInformationForm
            handleNext={handleNext}
            editDetails={editDetails}
            isView={isView}
            handleButtonClick={handleButtonClick}
          />
        );
      case 1:
        return (
          <BillingInformationForm
            handleNext={handleNext}
            handleBack={handleBack}
            isView={isView}
            editDetails={editDetails.billingInformation}
          />
        );
      case 2:
        return (
          <PaymentInformationForm
            handleNext={handleNext}
            handleBack={handleBack}
            editDetails={editDetails}
            isView={isView}
          />
        );
      case 3:
        return (
          <>
            {generalInfoData.interfaceType === interfaceType.SS7 ? (
              <RequiredInformationFormSS7
                handleBack={handleBack}
                editDetails={editDetails}
                isEdit={isEdit}
                id={id}
                customerIdDetails={customerIdDetails}
                isView={isView}
              />
            ) : generalInfoData?.interfaceType === interfaceType["SMPP ES"] ? (
              <RequiredInformationFormSMPPES
                handleBack={handleBack}
                editDetails={editDetails}
                isEdit={isEdit}
                id={id}
                customerIdDetails={customerIdDetails}
                isView={isView}
              />
            ) : generalInfoData?.interfaceType === interfaceType.ESME &&
              Number(generalInfoData.subInterfaceEsme) ===
                subInterfaceEsme.SMPP ? (
              <RequiredInformationForm
                handleBack={handleBack}
                editDetails={editDetails}
                isEdit={isEdit}
                id={id}
                customerIdDetails={customerIdDetails}
                isView={isView}
              />
            ) : (
              <RequiredInformationHTTP
                handleBack={handleBack}
                editDetails={editDetails}
                isEdit={isEdit}
                id={id}
                customerIdDetails={customerIdDetails}
                isView={isView}
              />
            )}
          </>
        );

      default:
        return null;
    }
  };
  const handleButtonClick = async () => {
    if (existingData && existingData.data && existingData.data.length > 0) {
      const lastRecord = existingData.data[existingData.data.length - 1];
      if (lastRecord.moduleNameValue === moduleConfiguration.customerSupplier) {
        setCurrentStep(3);
      } else {
        setCurrentStep(0);
      }
      if (
        !lastRecord?.previousPath &&
        lastRecord?.currentPath === `${location.pathname}${location.search}`
      ) {
        try {
          doBeforeNavigate(`/app/list/${moduleName}`);
          await deleteIndexedDB("navigationDetails");
        } catch (error) {
          console.error("Error deleting database:", error);
        }
      } else if (lastRecord?.currentPath) {
        const fullUrl = window.location.href;
        const appUrl = fullUrl.substring(fullUrl.indexOf("/app"));

        if (lastRecord.currentPath === appUrl) {
          doBeforeNavigate(lastRecord.previousPath, true);
        } else {
          doBeforeNavigate(lastRecord.currentPath);
        }
      }
    } else {
      doBeforeNavigate(`/app/list/${moduleName}`);
    }
  };
  const doBeforeNavigate = async (path, forceDelete = false) => {
    if (deleteLastEntry || forceDelete) {
      const currentData = existingData.data.find(
        (entry) => entry.moduleNameValue === moduleName
      );
      existingData.data = existingData.data.filter(
        (entry) => entry.moduleNameValue !== moduleName
      );
      await updateIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        Number(currentId),
        existingData
      );
      if (currentData?.previousPath) {
        navigate(currentData.previousPath);
        return;
      }
    }
    navigate(path);
  };

  return (
    <>
      <div className="flex items-center gap-2 sticky top-0 bg-bgBody h-[30px] z-10 content-center mt-0 mb-0">
        {Array.isArray(breadcrumbs) && breadcrumbs.length > 0 ? (
          <>
            {action !== "viewData" ? (
              <>
                {" "}
                {breadcrumbs.map((crumb, index) => (
                  <React.Fragment key={index}>
                    <span
                      className={`text-base ${
                        crumb.path
                          ? "text-textNAColor cursor-pointer hover:underline"
                          : "text-black font-bold"
                      }`}
                      onClick={() =>
                        crumb.path && handleBreadcrumbClick(crumb.path, index)
                      }
                    >
                      {crumb?.name
                        ?.split(" ")
                        .map((word) => word)
                        .join(" ")}
                    </span>
                    {index < breadcrumbs.length - 1 && <span>{">"}</span>}
                  </React.Fragment>
                ))}
              </>
            ) : (
              <>
                {" "}
                <div
                  className="text-textNAColor cursor-pointer hover:underline"
                  onClick={() => {
                    navigate(`/app/list/${currentModuleName}`);
                  }}
                >{`${header} > `}</div>
                <div className="text-black font-bold">
                  {" "}
                  {action === "edit"
                    ? "Edit customer/supplier"
                    : action === "view"
                    ? "View customer/supplier"
                    : "Add customer/supplier"}
                </div>
              </>
            )}
          </>
        ) : (
          <>
            <div
              className="text-textNAColor cursor-pointer hover:underline"
              onClick={() => {
                navigate(`/app/list/${currentModuleName}`);
              }}
            >{`${header} > `}</div>
            <div className="text-black font-bold">
              <div className="text-black font-bold">
                {action === "edit"
                  ? "Edit customer/supplier"
                  : action === "view"
                  ? "View customer/supplier"
                  : "Add customer/supplier"}
              </div>
            </div>
          </>
        )}
      </div>

      <div className="flex flex-row mt-5">
        <CustomStepper
          steps={steps}
          atStep={currentStep}
          className="sticky top-0"
        />
        <div className="bg-white pb-5 w-full p-4 rounded-r-lg">
          <div className="bg-white h-[30px] z-10 content-center">
            <div className="flex mx-3 my-3 ">
              <div className="flex justify-between items-center w-full">
                <InputLabel
                  label={steps[currentStep]}
                  labelClassName="font-semibold"
                />
                <CloseIcon
                  className="w-3 h-4 cursor-pointer ml-auto"
                  onClick={() => handleButtonClick()}
                />
              </div>
            </div>
          </div>
          <div className="mt-3 mx-3 mb-3 border-b-2 border-listBorder" />
          <div className="mt-5">{getFormComponent()}</div>
        </div>
      </div>
    </>
  );
};

export default CustomerSupplier;
