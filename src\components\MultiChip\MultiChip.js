import React, { useRef, useState } from "react";
import { TextField, Chip, Box, Button } from "@mui/material";
import { styled } from "@mui/system";
import { CloseIcon } from "../../icons";
import { useField } from "formik";
import { Virtuoso } from "react-virtuoso";
import { SERIES_LIMITATION } from "../../common/config";
import ErrorDialog from "../Dialog/ErrorDialog";

const ChipContainer = styled(Box, {
  shouldForwardProp: (prop) => prop !== "hasError" && prop !== "isDisabled",
})(({ theme, hasError, isDisabled }) => ({
  display: "flex",
  flexDirection: "column",
  border: `1px solid ${hasError ? "#DC3833" : "#808080"}`,
  padding: theme.spacing(1),
  borderRadius: "10px",
  height: 180,
  maxHeight: 210,
  width: "100%",
  fontSize: 12,
  backgroundColor: "#F3F3F3",
  color: "#666666",
  pointerEvents: isDisabled ? "none" : "auto",
}));

const HeaderContainer = styled(Box)({
  display: "flex",
  justifyContent: "flex-end",
  width: "100%",
  marginBottom: "4px",
  marginTop: "5px",
});

const ClearAllButton = styled(Button)({
  marginTop: "3px",
  fontSize: "12px",
  padding: "4px 10px",
  minWidth: "unset",
  color: "#666666",
  backgroundColor: "#f3f3f3",
  borderRadius: "5px",
  textTransform: "none",
  border: "1px solid #ccc",
  "&:hover": {
    backgroundColor: "none",
  },
});

const ChipList = styled("div")({
  display: "flex",
  flexWrap: "wrap",
  gap: "8px",
  padding: "4px",
});

const MultiChip = ({ name, value, onChange, isDisabled, moduleName }) => {
  const [inputValue, setInputValue] = useState("");
  const inputRef = useRef(null);
  const virtuosoRef = useRef(null);
  const containerRef = useRef(null);
  const [errorDialog, setErrorDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [field, meta] = useField(name);
  const [inputWidth, setInputWidth] = useState(150);

  const itemsPerBatch = 50;
  const totalBatches = Math.ceil(value.length / itemsPerBatch) + 1;

  const focusInput = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleInputChange = (event) => {
    const newValue = event.target.value;
    setInputValue(newValue);
    setInputWidth(
      newValue.length <= 2 ? newValue.length * 60 : newValue.length * 16
    );
  };

  const handleKeyDown = (event) => {
    if (
      moduleName === "serieses" &&
      event.key === "Enter" &&
      inputValue.trim() !== ""
    ) {
      event.preventDefault();

      const allValues = inputValue
        .split(" ")
        .map((chip) => chip.trim())
        .filter((chip) => chip !== "");
      console.log("value.length", allValues.length);
      // Check if adding new chips would exceed the series limitation
      if (value.length + allValues.length > SERIES_LIMITATION) {
        setMessage(
          `Series limit of ${SERIES_LIMITATION} has been reached. Please add values below ${SERIES_LIMITATION}`
        );

        setErrorDialog(true);
        return;
      }

      const newChips = [...value, ...allValues];

      setInputValue("");
      onChange(newChips);
      setTimeout(focusInput, 50);
    }
  };

  const handleDelete = (indexToDelete) => () => {
    const newChips = value.filter((_, index) => index !== indexToDelete);
    onChange(newChips);
    setTimeout(focusInput, 50);
  };

  const handleClearAll = (event) => {
    // Check if event exists before using stopPropagation
    if (event) {
      event.stopPropagation(); // Prevent container click from triggering
    }
    onChange([]); // Clear all chips
    setInputValue(""); // Clear input value as well
    setTimeout(focusInput, 50);
  };

  // Function to handle dialog close without an event parameter
  const handleDialogClose = () => {
    setErrorDialog(false);
    // Clear all chips without needing an event parameter
    onChange([]);
    setInputValue("");
    setTimeout(focusInput, 50);
  };

  // Render batches of chips for better performance
  const renderBatch = (index) => {
    const startIndex = index * itemsPerBatch;
    const endIndex = Math.min(startIndex + itemsPerBatch, value.length);
    const isLastBatch = index === totalBatches - 1;

    // For the last batch, we might have fewer chips or no chips at all
    const batchChips =
      isLastBatch && startIndex >= value.length
        ? []
        : value.slice(startIndex, endIndex);

    return (
      <ChipList>
        {batchChips.map((chip, chipIndex) => (
          <Chip
            key={startIndex + chipIndex}
            label={
              <div
                style={{
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  position: "relative",
                  paddingRight: "8px",
                }}
              >
                {chip}
              </div>
            }
            onDelete={!isDisabled ? handleDelete(startIndex + chipIndex) : null}
            deleteIcon={
              !isDisabled ? (
                <CloseIcon
                  style={{
                    position: "absolute",
                    top: "4px",
                    right: "4px",
                    fontSize: "16px",
                    width: "8px",
                    height: "8px",
                    cursor: "pointer",
                  }}
                />
              ) : null
            }
            sx={{
              backgroundColor: "rgba(200, 220, 255, 0.9)",
              borderRadius: "4px",
              color: "#000",
              height: "32px",
              padding: "4px 8px",
              margin: "0",
              fontWeight: "normal",
              "& .MuiChip-label": {
                padding: "0 14px 0 6px",
              },
              "& .MuiChip-deleteIcon": {
                margin: 0,
              },
            }}
          />
        ))}

        {isLastBatch && (
          <TextField
            inputRef={inputRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            size="small"
            disabled={isDisabled || value.length >= SERIES_LIMITATION}
            autoComplete="off"
            placeholder={
              value.length >= SERIES_LIMITATION ? "Limit reached" : ""
            }
            sx={{
              width: inputWidth,
              maxWidth: "150px",
              minWidth: "100px",
              outline: "none",
              padding: "0px",
              "& .MuiOutlinedInput-root": {
                height: "32px",
                "& fieldset": {
                  border: "none",
                },
                "& .MuiInputBase-input": {
                  fontSize: 12,
                  padding: "4px 8px",
                },
                "& .MuiInputBase-input::placeholder": {
                  color: "black",
                  fontWeight: "bold",
                  opacity: 1,
                },
              },
            }}
            onFocus={(e) => {
              e.stopPropagation();
            }}
          />
        )}
      </ChipList>
    );
  };

  return (
    <div onClick={focusInput} style={{ width: "100%" }} ref={containerRef}>
      {/* Clear All button above the chip container */}

      <ChipContainer hasError={meta.touched && meta.error}>
        <Virtuoso
          ref={virtuosoRef}
          style={{ height: "100%", width: "100%" }}
          totalCount={totalBatches}
          itemContent={renderBatch}
          initialTopMostItemIndex={0}
          followOutput={true}
          overscan={3}
        />
      </ChipContainer>

      {meta.touched && meta.error && (
        <div className="text-errorColor text-[11px] mt-0.5">
          {Array.isArray(meta.error)
            ? meta.error[meta.error.length - 1]
            : meta.error}
        </div>
      )}

      {value.length > 0 && !isDisabled && (
        <HeaderContainer>
          <ClearAllButton onClick={handleClearAll} size="small" disableRipple>
            Clear All
          </ClearAllButton>
        </HeaderContainer>
      )}
      <ErrorDialog
        show={errorDialog}
        onHide={handleDialogClose} // Use the new function that doesn't require an event
        message={message}
      />
    </div>
  );
};

export default MultiChip;
