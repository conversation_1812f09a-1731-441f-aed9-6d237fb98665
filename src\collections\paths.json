{"blocks": [{"blockOf": {"field": "interfaceType", "value": [1, 2, 3, 4]}, "elements": [{"info": "Name of the configured path", "name": "pathName", "size": 6, "type": "text", "title": "Path name", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter Path name"}, {"type": "matches", "regex": "^[a-zA-Z](?=.*\\d)[a-zA-Z0-9_]*$", "message": "Only alphanumeric characters and underscore allowed.It should start with alphabet."}, {"type": "max", "value": 30, "message": "Max length is 30"}], "defaultValue": "", "validationType": "string"}, {"info": "Type of interface:SS7,SMPP,SMPP_ES,HTTP", "name": "interfaceType", "size": 6, "type": "select", "title": "Interface type", "isInfo": true, "options": [{"label": "SMPP", "value": 2}, {"label": "SMPP ES", "value": 3}, {"label": "HTTP", "value": 4}, {"label": "SS7", "value": 1}], "isMandatory": true, "nonEditable": true, "validations": [{"type": "required", "message": "Please select Interface type"}], "defaultValue": 2, "validationType": "string"}, {"info": "Flag to enable or disable the open connectivity compliance", "name": "ocComplianceFlag", "size": 6, "type": "switch", "title": "OC compliance", "isInfo": true, "options": [{"label": "Yes", "value": 1, "checked": true}, {"label": "No", "value": 0, "checked": false}], "isMandatory": true, "validations": [{"type": "required", "message": "Please choose OC compliance flag"}], "defaultValue": 1}, {"info": "Flag to enable or disable the customized path termination , If enabled the calling party details need to be provided", "name": "isCustomPath", "size": 6, "type": "switch", "title": "Customized path termination", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "options": [{"label": "yes", "value": 1, "checked": true}, {"label": "no", "value": 0, "checked": false}], "defaultValue": 0}, {"info": "List of suppliers for whom paths can be configured. One supplier can have only one path", "name": "supplierId", "size": 6, "type": "select", "title": "Supplier list", "isInfo": true, "dynamic": {"field": "interfaceType", "value": [1, 2, 3, 4]}, "isMandatory": true, "nonEditable": true, "onClickPath": "customer-supplier-managements", "validations": [{"type": "required", "message": "Please select Supplier list"}], "isDropDownApi": true, "validationType": "string", "selectiveDetail": {"filters": [{"operatorType": ["S", "C,S"]}], "attributeName": "name"}}, {"info": "Configure the List consisting of SMSCs to be mapped for SMPP supplier", "name": "supplierListId", "size": 6, "type": "select", "title": "Add redirection list | Add ESME account", "isInfo": true, "dynamic": {"field": "interfaceType", "value": [2, 3, 4]}, "isAddButton": true, "isMandatory": true, "onClickPath": "redirectional-lists | esme-accounts", "validations": [{"type": "required", "message": "Please select redirection list | Please select esme account"}], "isDropDownApi": true, "alloweDropDownEdit": true}, {"info": "Sub Interface of ESME protocol to classify Redirection list type : \n1.SMPP\n2. HTTP", "name": "subInterface", "size": 6, "type": "radio", "title": "Sub interface type", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "options": [{"label": "SS7", "value": 1}, {"label": "SIGTRAN", "value": 2}], "defaultValue": 1}, {"name": "billingLogic", "type": "none", "options": [{"label": "Delivery", "value": "D"}, {"label": "Submission", "value": "S"}, {"label": "Successful termination", "value": "T"}], "defaultValue": "D"}], "blockName": "Basic details", "blockType": "defaultBlock"}, {"blockOf": {"field": "interfaceType", "value": [1]}, "elements": [{"info": "List of point codes. Point codes are unique address for a node to identify the destination", "name": "listId", "size": 6, "type": "select", "title": "Point code list", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "isAddButton": true, "isMandatory": false, "onClickPath": "point-code-lists", "isDropDownApi": true, "alloweDropDownEdit": true}, {"info": "An SS7 point code is similar to an IP address in an IP network. It is a unique address for a node (Signaling Point, or SP). Id of the point code", "name": "pointCode", "size": 6, "type": "select", "title": "Point code", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "isMandatory": true, "onClickPath": "point-codes", "validations": [{"type": "required", "message": "Please select point code value"}], "isDropDownApi": true}, {"info": "Self GT of the HUB, which needs to be configured for outgoing message.\nThe options are:\nLRN Prefix: An LRN needs to be prefixed on the destination MSISDN so that the message terminates on its designated operator network.\nHUB Prefix: HUB GT needs to be prefixed if the message is sent from SMS HUB to an operator via some other HUB.\nMSISDN: Default case.\nHUB Replace: HUB GT needs to be prefixed if the message is sent from SMS HUB to an operator via some other HUB.", "name": "sccpAddType", "size": 6, "type": "select", "title": "Called party address", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "options": [{"label": "LRN prefix", "value": 1}, {"label": "HUB prefix", "value": 2}, {"label": "HUB replace", "value": 3}, {"label": "MSISDN", "value": 4}], "isMandatory": true, "validations": [{"type": "required", "message": "Please Select Called Party address"}], "defaultValue": ""}, {"info": "Translation type of the called party", "name": "calledTTBasic", "size": 6, "type": "text", "title": "Called TT", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter Called TT value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 255, "message": "Max range allowed is 255"}, {"type": "typeError", "message": "Please enter Called TT with numeric value"}], "defaultValue": "", "validationType": "number"}, {"info": "Value to be considered for prefixing/replacing HUB GT value", "name": "hubPreReplace", "size": 6, "type": "text", "title": "Prefix/Replace", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter Prefix/Replace"}, {"type": "typeError", "message": "Please enter Prefix/Replace with numeric value"}, {"type": "integer", "message": "Please enter the Prefix/Replace valid integer value"}], "defaultValue": "", "validationType": "number", "visibilityConditions": {"sccpAddType": [2, 3], "interfaceType": [1]}}, {"info": "Numbering plan of the called party", "name": "calledNPBasic", "size": 6, "type": "select", "title": "Called NP", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "options": [{"label": "Unknown", "value": 0}, {"label": "ISDN", "value": 1}, {"label": "Telephony (E.164,E<PERSON>163)", "value": 2}, {"label": "Data (X.121)", "value": 3}, {"label": "Telex (F.69)", "value": 4}, {"label": "Maritime mobile", "value": 5}, {"label": "Land Mobile (E.212)", "value": 6}, {"label": "Private", "value": 7}, {"label": "ANSI SS7 PC and SSN", "value": 13}, {"label": "Internet (IP)", "value": 14}, {"label": "Extension", "value": 15}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select Called NP"}], "defaultValue": "", "validationType": "number"}, {"info": "Self address of MAP layer", "name": "mapRpOaBasic", "size": 6, "type": "radio", "title": "MAP RP OA", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "options": [{"label": "Dummy <PERSON>", "value": 1}, {"label": "A2P GT", "value": 2}, {"label": "P2P GT", "value": 3}], "isMandatory": true, "validations": [{"type": "required", "message": "Please choose MAP RP OA"}], "defaultValue": 1, "validationType": "number"}, {"info": "Sub system number of the called party", "name": "calledSSNBasic", "size": 6, "type": "text", "title": "Called SSN", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter Called SSN value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 255, "message": "Max range allowed is 255"}, {"type": "typeError", "message": "Please enter Called SSN with numeric value"}], "defaultValue": "", "validationType": "number"}, {"info": "Dummy GT value", "name": "dummyGTValueBasic", "size": 6, "type": "select", "title": "Dummy GT value", "gtType": "dummyGT", "isInfo": true, "isMandatory": true, "onClickPath": "service-managements", "validations": [{"type": "required", "message": "Please select Dummy GT value"}], "isDropDownApi": true, "validationType": "number", "visibilityConditions": {"mapRpOaBasic": [1], "interfaceType": [1]}}, {"info": "GT value when the message is exchanged between an application and a mobile subscriber", "name": "a2pGTValueBasic", "size": 6, "type": "select", "title": "A2P GT value", "gtType": "a2pGt", "isInfo": true, "isMandatory": true, "onClickPath": "service-managements", "validations": [{"type": "required", "message": "Please enter A2P GT Value"}], "isDropDownApi": true, "validationType": "number", "visibilityConditions": {"mapRpOaBasic": [2], "interfaceType": [1]}}, {"info": "GT value when the message is exchanged between two subscribers", "name": "p2pGTValueBasic", "size": 6, "type": "select", "title": "P2P GT value", "gtType": "p2pGt", "isInfo": true, "isMandatory": true, "onClickPath": "service-managements", "validations": [{"type": "required", "message": "Please enter P2P GT Value"}], "isDropDownApi": true, "validationType": "number", "visibilityConditions": {"mapRpOaBasic": [3], "interfaceType": [1]}}], "blockName": "Basic called party details", "blockType": "defaultBlock"}, {"blockOf": {"field": "interfaceType", "value": [1]}, "elements": [{"info": "Self GT of the HUB, which needs to be configured for outgoing message when destination is roaming", "name": "roamSccpAddType", "size": 6, "type": "radio", "title": "Roaming called party address", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "options": [{"label": "HUB prefix", "value": 2}, {"label": "HUB replace", "value": 3}, {"label": "VLR", "value": 4}], "isMandatory": true, "defaultValue": 2}, {"info": "Self address of MAP layer", "name": "MapRpOaRoaming", "size": 6, "type": "radio", "title": "MAP RP OA", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "options": [{"label": "Dummy <PERSON>", "value": 1}, {"label": "A2P GT", "value": 2}, {"label": "P2P GT", "value": 3}], "isMandatory": true, "defaultValue": 1, "validationType": "number"}, {"name": "roamHubPreReplace", "size": 6, "type": "text", "title": "Prefix/Replace", "isMandatory": true, "validations": [{"type": "required", "message": "Please enter Prefix/Replace"}, {"type": "typeError", "message": "Please enter Prefix/Replace with numeric value"}, {"type": "integer", "message": "Please enter the Prefix/Replace valid integer value"}], "defaultValue": "", "validationType": "number", "visibilityConditions": {"interfaceType": [1], "roamSccpAddType": [2, 3]}}, {"info": "Translation type of the called party", "name": "calledTTRoaming", "size": 6, "type": "text", "title": "Called TT", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter Called TT value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 255, "message": "Max range allowed is 255"}, {"type": "typeError", "message": "Please enter Called TT with numeric value"}], "defaultValue": "", "validationType": "number"}, {"info": "Dummy GT value", "name": "dummyGTValueRoaming", "size": 6, "type": "select", "title": "Dummy GT value", "gtType": "dummyGT", "isInfo": true, "isMandatory": true, "onClickPath": "service-managements", "validations": [{"type": "required", "message": "Please select Dummy GT value"}], "isDropDownApi": true, "validationType": "string", "visibilityConditions": {"interfaceType": [1], "MapRpOaRoaming": [1]}}, {"info": "GT value when the message is exchanged between an application and a mobile subscriber", "name": "a2pGTValueRoaming", "size": 6, "type": "select", "title": "A2P GT value", "gtType": "a2pGt", "isInfo": true, "isMandatory": true, "onClickPath": "service-managements", "validations": [{"type": "required", "message": "Please enter A2P GT Value"}], "isDropDownApi": true, "validationType": "number", "visibilityConditions": {"interfaceType": [1], "MapRpOaRoaming": [2]}}, {"info": "GT value when the message is exchanged between two subscribers", "name": "p2pGTValueRoaming", "size": 6, "type": "select", "title": "P2P GT value", "gtType": "p2pGt", "isInfo": true, "isMandatory": true, "onClickPath": "service-managements", "validations": [{"type": "required", "message": "Please select P2P GT Value"}], "isDropDownApi": true, "validationType": "number", "visibilityConditions": {"interfaceType": [1], "MapRpOaRoaming": [3]}}, {"info": "Sub system number of the called party", "name": "calledSSNRoaming", "size": 6, "type": "text", "title": "Called SSN", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter Called SSN value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 255, "message": "Max range allowed is 255"}, {"type": "typeError", "message": "Please enter Called SSN with numeric value"}], "defaultValue": "", "validationType": "number"}, {"info": "Numbering plan of the called party", "name": "calledNPRoaming", "size": 6, "type": "select", "title": "Called NP", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "options": [{"label": "Unknown", "value": 0}, {"label": "ISDN", "value": 1}, {"label": "Telephony (E.164,E<PERSON>163)", "value": 2}, {"label": "Data (X.121)", "value": 3}, {"label": "Telex (F.69)", "value": 4}, {"label": "Maritime mobile", "value": 5}, {"label": "Land Mobile (E.212)", "value": 6}, {"label": "Private", "value": 7}, {"label": "ANSI SS7 PC and SSN", "value": 13}, {"label": "Internet (IP)", "value": 14}, {"label": "Extension", "value": 15}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select Called NP"}], "defaultValue": "", "validationType": "number"}, {"info": "List of point codes. Point codes are unique address for a node to identify the destination", "name": "roamListId", "size": 6, "type": "select", "title": "Point code list", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "isAddButton": true, "isMandatory": false, "onClickPath": "point-code-lists", "isDropDownApi": true, "alloweDropDownEdit": true}], "blockName": "Roaming called party details", "blockType": "defaultBlock"}, {"blockOf": {"field": "interfaceType", "value": [1]}, "elements": [{"info": "GT of the destination address needs to be configured for outgoing messages.\nThe options are:\nDummy GT: Dummy GT is added if there is a bilateral agreement between the originator and destination operator.\nA2P GT: A2P GT is added if the message type is A2P.\nP2P GT: P2P GT is added if the message type is P2P.", "name": "callingPartyAddress", "size": 6, "type": "radio", "title": "Calling party address", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "options": [{"label": "Dummy <PERSON>", "value": 1}, {"label": "A2P GT", "value": 2}, {"label": "P2P GT", "value": 3}], "isMandatory": true, "validations": [{"type": "required", "message": "Please choose Calling party address"}], "defaultValue": 1, "validationType": "number"}, {"info": "Numbering plan of the receiving party", "name": "callingNP", "size": 6, "type": "select", "title": "Calling NP", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "options": [{"label": "Unknown", "value": 0}, {"label": "ISDN", "value": 1}, {"label": "Telephony (E.164,E<PERSON>163)", "value": 2}, {"label": "Data (X.121)", "value": 3}, {"label": "Telex (F.69)", "value": 4}, {"label": "Maritime mobile", "value": 5}, {"label": "Land Mobile (E.212)", "value": 6}, {"label": "Private", "value": 7}, {"label": "ANSI SS7 PC and SSN", "value": 13}, {"label": "Internet (IP)", "value": 14}, {"label": "Extension", "value": 15}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select Calling NP value"}], "validationType": "number"}, {"info": "Dummy GT value", "name": "dummyGTValueCalling", "size": 6, "type": "select", "title": "Dummy GT value", "gtType": "dummyGT", "isInfo": true, "isMandatory": true, "onClickPath": "service-managements", "validations": [{"type": "required", "message": "Please select Dummy GT value"}], "isDropDownApi": true, "validationType": "number", "visibilityConditions": {"interfaceType": [1], "callingPartyAddress": [1]}}, {"info": "GT value when the message is exchanged between an application and a mobile subscriber", "name": "a2pGTValueCalling", "size": 6, "type": "select", "title": "A2P GT value", "gtType": "a2pGt", "isInfo": true, "isMandatory": true, "onClickPath": "service-managements", "validations": [{"type": "required", "message": "Please enter A2P GT Value"}], "isDropDownApi": true, "validationType": "number", "visibilityConditions": {"interfaceType": [1], "callingPartyAddress": [2]}}, {"info": "GT value when the message is exchanged between two subscribers", "name": "p2pGTValueCalling", "size": 6, "type": "select", "title": "P2P GT value", "gtType": "p2pGt", "isInfo": true, "isMandatory": true, "onClickPath": "service-managements", "validations": [{"type": "required", "message": "Please enter P2P GT Value"}], "isDropDownApi": true, "validationType": "number", "visibilityConditions": {"interfaceType": [1], "callingPartyAddress": [3]}}, {"info": "Sub system number of the receiving party", "name": "callingSSN", "size": 6, "type": "text", "title": "Calling SSN", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter Calling SSN value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 255, "message": "Max range allowed is 255"}, {"type": "typeError", "message": "Please enter Calling SSN with numeric value"}], "defaultValue": "", "validationType": "number"}, {"info": "Translation type of the receiving entity", "name": "callingTT", "size": 6, "type": "text", "title": "Calling TT", "isInfo": true, "dynamic": {"field": "interfaceType", "value": 1}, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter Calling TT value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 255, "message": "Max range allowed is 255"}, {"type": "typeError", "message": "Please enter Calling TT with numeric value"}], "defaultValue": "", "validationType": "number"}], "blockName": "Calling party details", "blockType": "defaultBlock"}, {"fields": [{"info": "GT of the destination address needs to be configured for outgoing messages.\nThe options are:\nDummy GT: Dummy GT is added if there is a bilateral agreement between the originator and destination operator.\nA2P GT: A2P GT is added if the message type is A2P.\nP2P GT: P2P GT is added if the message type is P2P.", "name": "callingPartyAddress", "type": "radio", "title": "Calling party address", "isInfo": true, "options": [{"label": "Dummy <PERSON>", "value": 1}, {"label": "A2P GT", "value": 2}, {"label": "P2P GT", "value": 3}], "isMandatory": true, "validations": [{"type": "required", "message": "Please choose Calling party address"}], "validationType": "number"}, {"info": "Numbering plan of the receiving party", "name": "callingNP", "type": "select", "title": "Calling NP", "isInfo": true, "options": [{"label": "Unknown", "value": 0}, {"label": "ISDN", "value": 1}, {"label": "Telephony (E.164,E<PERSON>163)", "value": 2}, {"label": "Data (X.121)", "value": 3}, {"label": "Telex (F.69)", "value": 4}, {"label": "Maritime mobile", "value": 5}, {"label": "Land Mobile (E.212)", "value": 6}, {"label": "Private", "value": 7}, {"label": "ANSI SS7 PC and SSN", "value": 13}, {"label": "Internet (IP)", "value": 14}, {"label": "Extension", "value": 15}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select Calling NP value"}], "validationType": "number"}, {"info": "Dummy GT value", "name": "dummyGTValueCalling", "type": "select", "title": "Dummy GT value", "gtType": "dummyGT", "isInfo": true, "dynamic": {"field": "callingPartyAddress", "value": 1}, "isMandatory": true, "onClickPath": "service-managements", "validations": [{"type": "required", "message": "Please select Dummy GT value"}], "isDropDownApi": true, "validationType": "number"}, {"info": "GT value when the message is exchanged between an application and a mobile subscriber", "name": "a2pGTValueCalling", "type": "select", "title": "A2P GT value", "gtType": "a2pGt", "isInfo": true, "dynamic": {"field": "callingPartyAddress", "value": 2}, "isMandatory": true, "onClickPath": "service-managements", "validations": [{"type": "required", "message": "Please select A2P GT Value"}], "isDropDownApi": true, "validationType": "number"}, {"info": "GT value when the message is exchanged between two subscribers", "name": "p2pGTValueCalling", "type": "select", "title": "P2P GT value", "gtType": "p2pGt", "isInfo": true, "dynamic": {"field": "callingPartyAddress", "value": 3}, "isMandatory": true, "onClickPath": "service-managements", "validations": [{"type": "required", "message": "Please select P2P GT Value"}], "isDropDownApi": true, "validationType": "number"}, {"info": "Sub system number of the receiving party", "name": "callingSSN", "type": "text", "title": "Calling SSN", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter Calling SSN value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 255, "message": "Max range allowed is 255"}, {"type": "typeError", "message": "Please enter Calling SSN with numeric value"}], "validationType": "number"}, {"info": "Translation type of the receiving entity", "name": "callingTT", "type": "text", "title": "Calling TT", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter Calling TT value"}, {"type": "min", "value": 0, "message": "Min range allowed is 0"}, {"type": "max", "value": 255, "message": "Max range allowed is 255"}, {"type": "typeError", "message": "Please enter Calling TT with numeric value"}], "validationType": "number"}, {"info": "List of operator clusters available in platform", "name": "clusterId", "type": "select", "title": "Cluster List", "isInfo": true, "isMandatory": true, "onClickPath": "operator-clusters", "validations": [{"type": "required", "message": "Please select cluster list"}], "isDropDownApi": true, "validationType": "string"}], "dynamic": {"field": "isCustomPath", "value": 1}, "maxLimit": 10, "addButton": "+ Add new calling party address", "blockName": "ss7PathExtended", "blockType": "customizedBlocks", "blockTitle": "Customized calling party details", "headersData": ["Calling party address", "Calling NP", "Dummy GT value", "A2P GT value", "P2P GT value", "Calling SSN", "Calling TT", "Cluster List"], "initialValues": {"ss7PathExtended": [{"callingNP": "", "callingTT": "", "clusterId": "", "callingSSN": "", "a2pGTValueCalling": "", "p2pGTValueCalling": "", "callingPartyAddress": 1, "dummyGTValueCalling": ""}]}, "validationType": "array"}], "header": "List of paths", "columns": [{"header": "Path ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "Path name", "isHovered": true, "accessorKey": "attributes.pathName"}, {"header": "Interface type", "accessorKey": "attributes.interfaceType"}, {"header": "OC Compliance", "accessorKey": "attributes.ocComplianceFlag"}, {"header": "Supplier ID", "accessorKey": "attributes.supplierId", "filterVariant": "range"}, {"header": "Supplier name", "idToName": true, "isHovered": true, "nameFiled": "name", "moduleName": "customer-supplier-management", "accessorKey": "attributes.supplierName", "attributeKey": "supplierId"}, {"header": "Billing logic", "accessorKey": "attributes.billingLogic"}, {"buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "formType": "multipleBlock", "addButton": "+ Add new calling party address", "buttonName": "+ Add path", "moduleData": "path", "moduleName": "Path", "globalSearch": [{"label": "Path ID", "value": "id"}, {"label": "Path name", "value": "pathName"}, {"label": "Supplier name", "value": "supplierName"}], "navigationPath": true, "dropdownOptions": {"field": "interfaceType", "options": [{"label": "All Interface type", "query": "", "value": "ALL"}, {"label": "SMPP", "query": "filters[interfaceType][$eq]=2", "value": 2}, {"label": "SMPP ES", "query": "filters[interfaceType][$eq]=3", "value": 3}, {"label": "HTTP", "query": "filters[interfaceType][$eq]=4", "value": 4}, {"label": "SS7", "query": "filters[interfaceType][$eq]=1", "value": 1}], "defaultQuery": "", "defaultValue": "ALL"}}