import React from "react";
import { MaterialReactTable } from "material-react-table";
import styled from "styled-components";
import theme from "../../../tailwind-theme";
import { Switch } from "@mui/material";

const CommonTable = ({ columns, data, selectAll, setSelectAll }) => {
  // console.log("data", data);
  const TableWrapper = styled.div`
    .MuiTableCell-head {
      background-color: ${theme.backgroundColor.bgTable} !important;
      color: #000000 !important;
    }
    .MuiTableCell-root.MuiTableCell-head[data-pinned="true"]:before {
      background-color: ${theme.backgroundColor.bgTable} !important;
    }
  `;
  return (
    <TableWrapper>
      <MaterialReactTable
        columns={columns}
        data={data}
        enableStickyHeader={true}
        enableBottomToolbar={false}
        enableTopToolbar={false}
        enableGlobalFilter={false}
        enableColumnFilters={false}
        enableSorting={false}
        enableRowActions={false}
        enablePagination={false}
        enableColumnActions={false}
        muiTableBodyCellProps={{
          sx: {
            padding: "4px 8px", // Adjust the padding to reduce row gap
          },
        }}
        muiTableHeadCellProps={{
          sx: {
            padding: "4px 8px",
            textAlign: "center",
            justifyContent: "center",
          },
        }}
        // renderTopToolbarCustomActions={() => (
        //   <div style={{ display: "flex", justifyContent: "flex-end" }}>
        //     <span>Select all</span>
        //     <Switch
        //       checked={selectAll}
        //       onChange={(e) => setSelectAll(e.target.checked)}
        //     />
        //   </div>
        // )}
      />
    </TableWrapper>
  );
};

export default CommonTable;
