import React, { useEffect } from "react";
import {
  MaterialReactTable,
  useMaterialReactTable,
} from "material-react-table";
import { SearchIcon } from "../icons";
import UnfoldMoreIcon from "@mui/icons-material/UnfoldMore";
import InputAdornment from "@mui/material/InputAdornment";
import styled from "styled-components";
import theme from "../tailwind-theme";

const TableWrapper = styled.div`
  .MuiTableCell-head {
    background-color: ${theme.backgroundColor.bgTable} !important;
    color: #000000 !important;
  }
  .MuiTableCell-root.MuiTableCell-head[data-pinned="true"]:before {
    background-color: ${theme.backgroundColor.bgTable} !important;
  }
`;

const CustomTable = ({
  data,
  columns,
  isLoading,
  setFilteredData,
  setFilteredRow,
  setIsSearching,
}) => {
  const table = useMaterialReactTable({
    columns: columns,
    data,
    enableColumnActions: false,
    enableTopToolbar: false,
    enableBottomToolbar: false,
    enableCellActions: true,
    enableColumnPinning: true,
    enablePagination: false,
    enableSorting: true,
    enableStickyHeader: true,

    muiTableContainerProps: {
      sx: { maxHeight: "49vh" },
      lg: { maxHeight: "62vh" },
    },
    icons: {
      SortIcon: (props) => <UnfoldMoreIcon {...props} />,
    },
    initialState: {
      showColumnFilters: true,
      enableToolbar: false,
      enableColumnFilters: true,
      columnPinning: {
        left: ["mrt-row-select"],
        right: ["actions"],
      },
    },
    state: {
      isLoading: isLoading,
      showColumnFilters: true,
    },
    getRowId: (originalRow) => originalRow?.id ?? `row-${Math.random()}`,
    muiTableBodyRowProps: ({ row }) => ({
      sx: {
        cursor: "pointer",
        "& td:last-child div": {
          visibility: "hidden",
        },
        "&:hover td:last-child div": {
          visibility: "visible",
        },
      },
    }),

    muiTablePaperProps: {
      sx: { boxShadow: "0", borderRadius: "10px" },
    },
    muiTableHeadCellProps: {
      sx: {
        backgroundColor: "#374150",
        fontSize: "12px",
        color: "#ffffff",
        fontFamily: "OpenSanHebrew",
        "& .Mui-TableHeadCell-Content-Wrapper": {
          whiteSpace: "nowrap",
        },
      },
    },

    muiSelectCheckboxProps: {},
    muiFilterTextFieldProps: {
      InputProps: {
        startAdornment: (
          <InputAdornment position="start">
            <SearchIcon />
          </InputAdornment>
        ),
        style: {
          backgroundColor: "#ffffff",
          height: "30px",
          fontSize: 12,
          boxShadow: "0px 4px 4px 0px #0000001A",
        },
      },
      variant: "outlined",
    },
    renderEmptyRowsFallback: () => (
      <div
        style={{
          padding: "25px",
          fontSize: "16px",
          color: "#808080",
          fontStyle: "italic",
          marginLeft: "400px",
        }}
      >
        No records to display
      </div>
    ),
  });

  useEffect(() => {
    const filteredRows = table.getRowModel().rows;
    const filteredIds = filteredRows.map((row) => row.id);
    setFilteredData(filteredIds);
    setFilteredRow(filteredRows.length);

    const filterData = table.getState().columnFilters.every((filter) => {
      return (
        filter.value === undefined ||
        filter.value === "" ||
        (Array.isArray(filter.value) &&
          filter.value.every((val) => val === undefined))
      );
    });
    setIsSearching(!filterData);
  }, [table.getState().columnFilters.length, table.getRowModel().rows.length]);

  return (
    <TableWrapper>
      <MaterialReactTable table={table} />
    </TableWrapper>
  );
};

export default CustomTable;
