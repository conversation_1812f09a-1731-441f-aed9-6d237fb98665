import React, { memo } from "react";
import { Typography, Stack } from "@mui/material";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import { fontSize, styled } from "@mui/system";
import InputBase from "@mui/material/InputBase";
import { resultPerPage } from "../../common/config";

function ResultPerPageComponent(props) {
  const { limit, handleLimitChange, disabled, colors } = props;

  const PaginationDropdownInput = styled(InputBase)(({ theme }) => ({
    "& .MuiInputBase-root": { lineHeight: "-1.5625em" },
    "& .MuiInputBase-input": {
      height: "10px",
      width: "10px",
      opacity: 1,
      fontWeight: 700,
      fontSize: "14px",
      color: `#707070`,
      background: "#fff",
      border: "1px solid #D9D9D9",
    },
    "& .MuiOutlinedInput-notchedOutline": {
      border: "1.8px solid #909bb8",
    },
    "& .MuiOutlinedInput-root": {
      "&:hover fieldset": {
        borderColor: "#909bb8",
      },
      "& fieldset": {
        borderColor: "1.8px solid #909bb8",
        "&.Mui-focused": {
          borderColor: "#909bb8",
        },
      },
    },

    "& .MuiSvgIcon-root": {
      color: `${colors?.tpBlue}`,
    },

    [theme.breakpoints.down("md")]: {
      "& .MuiInputBase-input": {
        padding: "7px 0px 7px 10px",
        width: "30px",
        fontSize: "14px",
      },
    },
    [theme.breakpoints.up("md")]: {
      "& .MuiInputBase-input": {
        padding: "7px 0px 7px 10px",
        width: "30px",
        fontSize: "14px",
      },
    },
    [theme.breakpoints.up("xl")]: {
      "& .MuiInputBase-input": {
        padding: "7px 0px 7px 10px",
        width: "30px",
        fontSize: "14px",
      },
    },
  }));

  return (
    <Stack
      direction="row"
      spacing="10px"
      justifyContent="space-between"
      alignItems="center"
    >
      <Typography
        style={{
          textAlign: "left",
          opacity: 1,
          color: "#707070",
          fontSize: "14px",
          fontWeight: 500,
          lineHeight: "19px",
        }}
      >
        Rows&nbsp;per&nbsp;page&nbsp;:
      </Typography>
      <FormControl sx={{ Width: "45px", Height: "19px" }}>
        <Select
          value={limit}
          onChange={handleLimitChange}
          disabled={disabled}
          displayEmpty
          inputProps={{ "aria-label": "Without label" }}
          input={<PaginationDropdownInput />}
          MenuProps={{
            disableScrollLock: true,
            PaperProps: {
              sx: {
                fontSize: "14px",
                color: `#707070`,
                lineHeight: "18px",
                fontWeight: 400,
                width: "70px",
                minWidth: "40px !important",
                boxShadow: "0px 3px 6px #00000029",
                border: "1px solid #E5E5E5",
                borderRadius: "6px",
                opacity: 1,
              },
            },
          }}
        >
          {resultPerPage?.map((e, i) => {
            return (
              <MenuItem value={e.values} key={i}>
                {e.name}
              </MenuItem>
            );
          })}
        </Select>
      </FormControl>
    </Stack>
  );
}

export default memo(ResultPerPageComponent);
