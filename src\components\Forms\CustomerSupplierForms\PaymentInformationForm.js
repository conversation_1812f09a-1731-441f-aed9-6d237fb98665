import React, { useContext, useEffect, useState } from "react";
import InputLabel from "../../FormsUI/InputLabel";
import TextFieldWrapper from "../../FormsUI/TextField";
import { Form, Formik } from "formik";
import Grid from "@mui/material/Grid";
import { CustomerSupplierContext } from "../../../context/CustomerSupplierContext";
import ConfirmNNextButton from "../../Buttons/Button";
import Button from "../../Buttons/OutlinedButton";
import {
  paymentInitialValue,
  paymentValidationSchema,
} from "../../../payloadDetails/PaymentPayload";
import Select from "../../FormsUI/Select";
import { countryCodeArr } from "../../../common/constants";

function PaymentInformationForm({
  handleNext,
  handleBack,
  editDetails,
  isView,
}) {
  const { paymentInfo, setPaymentInfo } = useContext(CustomerSupplierContext);

  const [formValues, setFormValues] = useState(paymentInitialValue || {});

  useEffect(() => {
    if (paymentInfo) {
      setFormValues({
        ...formValues,
        ...paymentInfo,
      });
    }
  }, [paymentInfo]);

  useEffect(() => {
    if (editDetails && paymentInfo.length === 0) {
      setFormValues({
        ...formValues,
        ...editDetails,
      });
    }
  }, [editDetails]);

  const handleSubmit = (values) => {
    const paymentData = {
      creditLine: values.creditLine,
      oracleCustomer: values.oracleCustomer,
      creditLimit: values.creditLimit,
      oracleVendor: values.oracleVendor,
      creditCurrency: values.creditCurrency,
      airtelKam: values.airtelKam,
      billingTerm: values.billingTerm,
      airtelKamContact: values.airtelKamContact,
      paymentTerm: values.paymentTerm,
      airtelKamEmail: values.airtelKamEmail,
      vatNo: values.vatNo,
    };
    setPaymentInfo(paymentData);
    handleNext();
  };

  return (
    <Formik
      initialValues={formValues}
      validationSchema={paymentValidationSchema}
      validateOnMount={true}
      enableReinitialize={true}
      onSubmit={(values) => {
        handleSubmit(values);
      }}
    >
      {({ values, setFieldValue, errors }) => (
        <Form>
          <div className="mx-4">
            <Grid container spacing={4} sx={{ p: 2 }}>
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Credit line"}
                  info={
                    "Line to be sent into the alert E-mail when the Credit Limit thresholds breached/exhausted"
                  }
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="creditLine"
                  placeholder={"Enter credit line"}
                  isDisabled={isView}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Oracle ID customer"}
                  info={
                    "Oracle ID, defined into the Customer billing system with Oracle Database"
                  }
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="oracleCustomer"
                  placeholder={"Enter Oracle ID customer"}
                  isDisabled={isView}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Credit limit"}
                  info={
                    "Credit Limit Amount, if decided already for the customer/supplier"
                  }
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="creditLimit"
                  placeholder={"Enter credit limit"}
                  isDisabled={isView}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Oracle ID vendor"}
                  info={
                    "Oracle ID, defined into the Vendor billing system with Oracle Database"
                  }
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="oracleVendor"
                  placeholder={"Enter Oracle ID vendor"}
                  isDisabled={isView}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Credit currency"}
                  info={"Currency of the Credit Amount"}
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="creditCurrency"
                  placeholder={"Enter credit currency"}
                  isDisabled={isView}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Airtel KAM"}
                  info={
                    "The Key Account Manager is the person who is responsible and handles the customer/supplier account"
                  }
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="airtelKam"
                  placeholder={"Enter Airtel KAM"}
                  isDisabled={isView}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Billing term"}
                  info={
                    "Billing Term [Weekly/Fortnight/Monthly] decided for customer"
                  }
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="billingTerm"
                  placeholder={"Enter billing term"}
                  isDisabled={isView}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Airtel KAM contact No."}
                  info={"Airtel - Key Account Manager Contact Number"}
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="airtelKamContact"
                  placeholder={"Enter Airtel KAM contact No."}
                  isDisabled={isView}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Payment term"}
                  info={
                    "Payment Term [Weekly/Fortnight/Monthly] decided for customer"
                  }
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="paymentTerm"
                  placeholder={"Enter payment term"}
                  isDisabled={isView}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"Airtel KAM Email ID"}
                  info={"Airtel - Key Account Manager Email ID"}
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="airtelKamEmail"
                  placeholder={"Enter Airtel KAM Email ID"}
                  isDisabled={isView}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <InputLabel
                  label={"VAT number"}
                  info={
                    "VAT Number of the customer, if provided. To be used for Billing/Invoicing purposes"
                  }
                  isInfo={true}
                />
                <TextFieldWrapper
                  name="vatNo"
                  placeholder={"Enter VAT number"}
                  isDisabled={isView}
                />
              </Grid>
            </Grid>
          </div>
          <div className="flex justify-center gap-5 pt-5 pb-10">
            <Button
              label={"Back"}
              buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
              onClick={() => {
                handleBack();
              }}
            />
            <ConfirmNNextButton
              label={"Next"}
              buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
              type="submit"
            />
          </div>
        </Form>
      )}
    </Formik>
  );
}

export default PaymentInformationForm;
