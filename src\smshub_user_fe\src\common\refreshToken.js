import mem from "mem";

import axios from "axios";
import getAPIMap from "../routes/ApiUrls";

const RefreshTokenFn = async () => {
  let url = getAPIMap("refreshToken");

  try {
    const response = await axios.post(url, null, { withCredentials: true });

    const session = response.data;
    console.log(session);

    if (!session?.token?.token) {
      localStorage.removeItem("session");
      localStorage.removeItem("user");
    }

    localStorage.setItem("session", JSON.stringify(session?.token?.token));
    window.dispatchEvent(new Event("storage"));

    return session;
  } catch (error) {
    localStorage.removeItem("session");
    localStorage.removeItem("user");
    window.dispatchEvent(new Event("storage"));
  }
};

const maxAge = 10000;

export const memoizedRefreshToken = mem(RefreshTokenFn, {
  maxAge,
});
