import React, { useState, useEffect } from "react";
import { Dialog, DialogTitle } from "@mui/material";
import { CloseIcon, ImportFileIcon, DeleteIcon } from "../../icons";
import { useDropzone } from "react-dropzone";
import CloudUploadImg from "../../icons/cloud.png";
import ConfirmNNextButton from "../Buttons/Button";
import ErrorDialog from "../Dialog/ErrorDialog";

function UploadLCRFile({
  show,
  onClose,
  setSelectFile,
  onUploadClick,
  createLoading,
}) {
  const [selectedFileName, setSelectedFileName] = useState("");
  const [selectedFile, setSelectedFile] = useState(null);
  const [errorDialog, setErrorDialog] = useState(false);
  const [message, setMessage] = useState("");

  const onDrop = (acceptedFiles) => {
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      if (file.type !== "text/csv") {
        setMessage("Only CSV files are allowed");
        setErrorDialog(true);
        return;
      }
      setSelectFile(file);
      setSelectedFileName(file.name);
      setSelectedFile(file);
    }
  };

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      "text/csv": [".csv"],
    },
    multiple: false,
    maxSize: 5 * 1024 * 1024,
  });

  const clearFile = () => {
    setSelectedFileName("");
    setSelectedFile(null);
  };

  useEffect(() => {
    if (show) {
      clearFile();
    }
  }, [show]);

  return (
    <>
      <Dialog
        sx={{
          "& .MuiDialog-paper": {
            width: "100%",
            maxWidth: 500,
            maxHeight: 370,
            borderRadius: "10px",
          },
        }}
        maxWidth="xs"
        open={show}
        onClose={() => {
          onClose();
          clearFile();
        }}
      >
        <DialogTitle className="pb-0 flex justify-end">
          <CloseIcon
            className="w-3.5 h-3.5 cursor-pointer"
            onClick={() => {
              onClose();
              clearFile();
            }}
          />
        </DialogTitle>
        <div className="flex justify-center font-bold text-sm ">
          {selectedFile
            ? "Selected csv file ready for upload"
            : "Choose an LCR file to upload"}
        </div>
        <div className="flex justify-center text-xs text-textNAColor mt-2">
          {selectedFile ? "" : "Only CSV file allowed"}
        </div>
        <div className="flex flex-col items-center p-6 text-medium py-1">
          <div
            {...getRootProps()}
            className="container border-2 border-dashed border-tableBorder rounded-lg flex flex-col items-center mt-2"
          >
            <input {...getInputProps()} />
            {selectedFile ? (
              <div className="flex items-center w-full p-20 justify-center align-center">
                <span className="truncate">{selectedFileName}</span>
                <button
                  className="text-red-500 hover:text-red-700"
                  onClick={clearFile}
                >
                  <DeleteIcon className="w-4 h-4 ml-3" />
                </button>
              </div>
            ) : (
              <>
                <img
                  src={CloudUploadImg}
                  className="text-textNAColor cursor-pointer"
                  alt="Upload"
                />
                <div className="text-textNAColor mt-0 mb-0 text-[12px]">
                  Drag & drop CSV file here
                </div>
                <div className="text-textNAColor mt-1 text-small mb-2">or</div>
              </>
            )}
          </div>
          {!selectedFile ? (
            <div
              {...getRootProps()}
              className="bg-bgSelectOption font-bold mt-7 mb-5 p-2 rounded-lg flex justify-center text-center items-center cursor-pointer px-10"
            >
              <ImportFileIcon className="w-4 h-4" />
              <div className="ml-3">Import LCR file</div>
            </div>
          ) : (
            <ConfirmNNextButton
              buttonClassName={`mt-7 mb-5 px-20 cursor-pointer`}
              onClick={() => {
                onUploadClick();
              }}
              label={"Upload"}
              loading={createLoading}
            />
          )}
        </div>
        {/* <ErrorDialog
          show={errorDialog}
          onHide={() => setErrorDialog(false)}
          message={message}
        /> */}
      </Dialog>
    </>
  );
}

export default UploadLCRFile;
