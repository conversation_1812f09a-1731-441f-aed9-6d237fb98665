@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  @variants responsive {
    /* Hide scrollbar for Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    .no-scrollbar {
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
    }
  }
}
@font-face {
  font-family: "OpenSanHebrew";
  src: url("../src/fonts/open-sans-hebrew/OpenSansHebrew-Regular.ttf")
    format("truetype");
}
body {
  font-family: "OpenSanHebrew" !important;
  font-weight: 500;
}
/* Works on Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: rgb(192, 192, 192);
}
.scroller {
  overflow-y: scroll;
  overflow-x: scroll;
  scrollbar-color: rgb(192, 192, 192);
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
*::-webkit-scrollbar-corner {
  background: transparent;
}
*::-webkit-scrollbar-track {
  /* box-shadow: inset 0 0 2px rgba(112, 112, 112, 0.3); */
}

*::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: darkgrey;
  /* outline: 1px solid slategrey; */
}
/* @font-face {
  font-family: "Sora";
  src: url("/src/Fonts/static/Sora-Regular.ttf");
  font-weight: 400;
  font-display: swap;
} */

/* Hide up/down arrow  for input type number*/
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
/* .css-1wc848c-MuiFormHelperText-root.Mui-error {
  margin-left: 0px;
  font-size: 11px;
  margin-top: 0px;
  font-family: "Poppins";
} */
