import React from "react";
import { MaterialReactTable } from "material-react-table";

const ErrorCommonTable = ({ columns, data }) => {
  return (
    <MaterialReactTable
      columns={columns}
      data={data}
      enableRowSelection={false}
      state={{
        columnPinning: {
          right: ["actions"],
        },
        initialState: {
          enableToolbar: false,
          enableColumnFilters: true,
        },
      }}
      enableColumnActions={false}
      enableColumnFilters={false}
      enableSorting={true}
      enablePagination={false}
      enableBottomToolbar={false}
      enableTopToolbar={false}
      positionToolbarAlertBanner="none"
      muiSelectCheckboxProps={{
        sx: { padding: "0 4px" },
      }}
      muiTableHeadCellProps={{
        sx: {
          backgroundColor: "#DDE9FD", // Updated header color
          color: "#000", // Ensure text is visible on light background
          fontWeight: "normal",
        },
      }}
      muiTableBodyRowProps={{
        sx: {
          cursor: "pointer",
          "& td:last-child div": {
            visibility: "hidden",
          },
          "&:hover td:last-child div": {
            visibility: "visible",
          },
          "&:nth-of-type(odd)": {
            backgroundColor: "#f9f9f9",
          },
        },
      }}
      muiTableProps={{
        sx: { border: "1px solid #ddd" },
      }}
      defaultColumn={{
        minSize: 20,
        maxSize: 400,
      }}
    />
  );
};

export default ErrorCommonTable;
