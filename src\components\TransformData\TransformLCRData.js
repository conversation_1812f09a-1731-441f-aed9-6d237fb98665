const TransformLCRData = (listData, currentPage, limitPerPage) => {
  return listData.map((item, index) => {
    const transformedItem = {
      serialNoId: (currentPage - 1) * limitPerPage + (index + 1),
      destinationOperator: item?.sourceopidName || item.destopidName || "---",
      regionCountry: item?.regionCountryName || "---",
      mccMnc: item?.mccMnc || "---",
      parentId: item?.id || "---",
      shortCode: item?.shortCode || "---",
    };

    item.policyDetails.forEach((policy, i) => {
      const {
        supplierName,
        cost,
        percentage,
        position,
        quality,
        fromTime,
        toTime,
      } = policy;
      const getValue = (value, defaultValue = "") => value ?? defaultValue;
      const isValidValue = (value) => value !== null && value !== undefined;

      const supplierData = isValidValue(cost)
        ? [
            supplierName,
            isValidValue(cost) ? getValue(cost) : null,
            isValidValue(percentage) ? getValue(percentage) : null,
            isValidValue(position) ? getValue(position) : null,
            isValidValue(quality) ? getValue(quality) : null,
            isValidValue(fromTime) ? getValue(fromTime) : null,
            isValidValue(toTime) ? getValue(toTime) : null,
          ]
            .filter((value) => value !== null && value !== undefined)
            .join("|")
        : "---";

      const tooltipTitle = isValidValue(cost)
        ? [
            `Cost: ${getValue(cost)}`,
            isValidValue(percentage) && `Percentage: ${getValue(percentage)}`,
            isValidValue(position) && `Position: ${getValue(position)}`,
            isValidValue(quality) && `Quality: ${getValue(quality)}`,
            isValidValue(fromTime) && `Start Time: ${getValue(fromTime)}`,
            isValidValue(toTime) && `End Time: ${getValue(toTime)}`,
          ]
            .filter(Boolean)
            .join(", ")
        : "";

      transformedItem[`supplier${i + 1}List`] = supplierData;
      transformedItem[`supplier${i + 1}Tooltip`] = tooltipTitle;
    });

    for (let i = item.policyDetails.length; i < 10; i++) {
      transformedItem[`supplier${i + 1}List`] = "---";
      transformedItem[`supplier${i + 1}Tooltip`] = "---";
    }

    return transformedItem;
  });
};

export default TransformLCRData;
