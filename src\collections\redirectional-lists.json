{"header": "List of redirectional list", "columns": [{"header": "List ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "List name", "accessorKey": "attributes.redirectionListName"}, {"header": "List type", "accessorKey": "attributes.redirectionType"}, {"buttons": [{"type": "button", "title": "View account"}, {"type": "button", "title": "View rules"}, {"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"name": "redirectionListType", "type": "none", "defaultValue": "E"}, {"name": "redirectionListName", "size": 6, "type": "text", "title": "List name", "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the List name"}, {"type": "matches", "regex": "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z][a-zA-Z0-9_ ]*$", "message": "Only alphanumeric characters, underscore and sapce are allowed(Should start with alphabet)"}], "defaultValue": "", "validationType": "string"}, {"info": "Same as Redirection Type. [The of distribution of SMS traffic among SMSC/point codes]", "name": "redirectionType", "size": 6, "type": "select", "title": "Load distribution type", "isInfo": true, "options": [{"label": "Round robin", "value": 1}, {"label": "Priority", "value": 2}, {"label": "Percentage", "value": 3}], "isMandatory": true, "nonEditable": true, "validations": [{"type": "required", "message": "Please select the Load distribution type"}], "defaultValue": ""}, {"name": "httpListType", "size": 6, "type": "radio", "title": "List sub type", "options": [{"label": "SMPP", "value": "0"}, {"label": "HTTP", "value": "1"}], "nonEditable": true, "defaultValue": "0"}, {"name": "sampleVal", "size": 6, "type": "text", "title": "Sample Value", "dynamic": {"field": "redirectionType", "value": 3}, "isMandatory": true, "validations": [{"type": "required", "message": "Sample value is required"}, {"type": "matches", "regex": "^\\d+$", "message": "Please enter a valid numeric value for sample value"}, {"type": "matches", "regex": "^[1-9]\\d*(?:\\.\\d+)?$|^0?\\.\\d*[1-9]\\d*$", "message": "Sample Value should be greater than 0"}, {"type": "max", "value": 5, "message": "Sample Value can be upto 5 digits"}], "defaultValue": "", "validationType": "string"}, {"name": "smsc_hosts", "size": 12, "type": "multiselect", "title": "Add redirectional accounts", "apiURL": "", "apiOptions": true, "isAddButton": true, "isMandatory": false, "placeholder": "Select redirectional account", "onClickPath": "redirectional-accounts", "defaultValue": []}, {"name": "priorityTable", "size": 12, "type": "FieldTable", "dynamic": {"field": "redirectionType", "value": 3}, "fieldsData": [{"name": "SMSC account", "type": "text"}, {"name": "percentage"}, {"name": "Traffic"}], "tableTitle": "Sample value distribution", "headersData": ["Account", "percentage", "Traffic"], "initialValues": {"items": [{"Traffic": "", "percentage": "", "SMSC account": ""}]}}, {"name": "priorityTable", "size": 12, "type": "FieldTable", "dynamic": {"field": "redirectionType", "value": 2}, "fieldsData": [{"name": "Account", "type": "text"}, {"name": "priority"}], "tableTitle": "Priority table", "headersData": ["Account", "Priority"], "isMandatory": false, "initialValues": {"items": [{"Account": "", "priority": ""}]}}], "formType": "simple", "buttonName": "+ Add redirectional list", "moduleData": "redirectional-list", "moduleName": "Redirectional list", "globalSearch": [{"label": "List ID", "value": "id"}, {"label": "List name", "value": "redirectionListName"}], "navigationPath": "true", "dropdownOptions": {"field": "redirectionType", "options": [{"label": "All List type", "query": "", "value": "ALL"}, {"label": "Round robin", "query": "filters[redirectionType][$eq]=1", "value": 1}, {"label": "Priority", "query": "filters[redirectionType][$eq]=2", "value": 2}, {"label": "Percentage", "query": "filters[redirectionType][$eq]=3", "value": 3}], "defaultQuery": "", "defaultValue": "ALL"}}