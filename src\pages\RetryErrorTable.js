import React, { useState, useMemo } from "react";
import ErrorCommonTable from "../components/Table/ErrorCommonTable";
import viewing from "../icons/viewing.png";
import thrash from "../icons/bin.png";
import { CssTooltip } from "../components/FormsUI/StyledComponent";
import RetryPolicyFilter from "../components/Filters/RetryPolicyFilter";
import { useNavigate } from "react-router-dom";
import { useMutation, useQuery } from "react-query";
import {
  deleteRetry,
  getRetryGroupName,
  getViewRetryGroup,
} from "../lib/retry-policy-api";
import { moduleConfiguration } from "../common/constants";
import Pagination from "../components/Pagination/Pagination";
import ResultPerPage from "../components/Pagination/ResultPerPage";
import { DEFAULT_PAGE_SIZE } from "../common/config";
import RetryViewDialog from "../components/Dialog/RetryViewDialog";
import DeleteDialog from "../components/Dialog/DeleteDialog";
import ErrorDialog from "../components/Dialog/ErrorDialog";
import SuccessDialog from "../components/Dialog/SuccessDialog";

const ErrorTable = () => {
  const navigate = useNavigate();
  const [retryGrpName, setRetryGrpName] = useState("");
  const [retryGroupOptions, setRetryGroupOptions] = useState([]);
  const [limitPerPage, setLimitPerPage] = useState(DEFAULT_PAGE_SIZE);
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState(null);
  const [data, setData] = useState([]);
  const [rowData, setRowData] = useState({});
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [maxRetryAttempts, setMaxRetryAttempts] = useState(0);
  const [showRetryViewDialog, setShowRetryViewDialog] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [message, setMessage] = useState("");

  const { mutate: deleteRecordAPI } = useMutation(deleteRetry);

  const handleLimitChange = (e) => {
    setLimitPerPage(e.target.value);
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  useQuery(["retry-group"], getRetryGroupName, {
    onSuccess: ({ data }) => {
      const formattedData =
        data?.data.map((item) => ({
          label: item.attributes.retryGrpName,
          value: item.id,
        })) || [];
      setRetryGroupOptions(formattedData);
    },
  });

  const { refetch } = useQuery(
    ["retry-group", limitPerPage, currentPage, retryGrpName],
    getViewRetryGroup,
    {
      onSuccess: (response) => {
        let transformedData = [];
        let maxAttempt = 0;
        let responseData = response.data.data;

        if (!Array.isArray(responseData)) {
          responseData = responseData ? [responseData] : [];
        }
        responseData.forEach((errorGroup) => {
          const groupedErrors = {};

          errorGroup.flat().forEach((error) => {
            if (error.retryAttempt > maxAttempt) {
              maxAttempt = error.retryAttempt;
            }
            const key = error.errorDescription;
            if (!groupedErrors[key]) {
              groupedErrors[key] = {
                id: error.id,
                category: error.category,
                errorName: error.errorDescription,
                errorGroupID: error.retryPolicyId,
                failureError: error.failureError,
                failureResult: error.failureResult,
              };
            }

            groupedErrors[key][`retry${error.retryAttempt}`] =
              error.retryInterval;
          });
          transformedData.push(...Object.values(groupedErrors));
        });

        setMaxRetryAttempts(maxAttempt);
        setData(transformedData);

        setPagination({
          attributes: {
            meta: {
              pagination: response.data.meta?.pagination,
            },
          },
        });
      },
    }
  );

  const [rowSelection, setRowSelection] = useState({});

  const generateRetryColumns = () => {
    const retryColumns = [];
    for (let i = 1; i <= maxRetryAttempts; i++) {
      retryColumns.push({
        accessorKey: `retry${i}`,
        header: i.toString(),
        size: 80,
      });
    }
    return retryColumns;
  };

  const columns = useMemo(
    () => [
      {
        header: "Error description",
        columns: [
          { accessorKey: "category", header: "Category", size: 150 },
          { accessorKey: "errorName", header: "Error name", size: 250 },
        ],
        id: "errorDescription",
      },
      {
        header: "Retry intervals (in secs)",
        columns: generateRetryColumns(),
        id: "retryIntervals",
      },
      {
        id: "actions",
        header: "",
        size: 80,
        Cell: ({ row }) => (
          <div className="flex justify-center items-center gap-4">
            <CssTooltip title="View" placement="top" arrow>
              <img
                key="viewRetry"
                className="w-6 h-4 cursor-pointer"
                src={viewing}
                alt={"viewing"}
                onClick={() => {
                  console.log("row.original", row);
                  setRowData(row.original);
                  setShowRetryViewDialog(true);
                }}
              />
            </CssTooltip>
            <CssTooltip title="Delete" placement="top" arrow>
              <img
                key="delete"
                className="w-4 h-4 cursor-pointer"
                src={thrash}
                alt={"delete"}
                onClick={(e) => {
                  setRowData(row.original);
                  setDeleteDialog(true);
                }}
              />
            </CssTooltip>
          </div>
        ),
      },
    ],
    [maxRetryAttempts]
  );
  return (
    <div>
      <div className="flex gap-2 font-medium my-5">
        <div
          className="text-textNAColor text-base cursor-pointer hover:underline"
          onClick={() => {
            navigate(`/app/list/${moduleConfiguration.retryPolicy}`);
          }}
        >{`List of Retry policy > `}</div>
        <div className="text-base">{"View Retry policy group details"}</div>
      </div>
      <RetryPolicyFilter
        setRetryGrpName={setRetryGrpName}
        retryGrpName={retryGrpName}
        dropdownDetails={retryGroupOptions || []}
        refetch={refetch}
      />
      <ErrorCommonTable
        columns={columns}
        data={data}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
      />
      {pagination &&
        pagination?.attributes?.meta?.pagination?.total !== undefined && (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              margin: "20px 0px 20px 20px",
              width: "95%",
            }}
          >
            <div className="flex">
              <ResultPerPage
                limit={limitPerPage}
                handleLimitChange={handleLimitChange}
              />
              <div
                style={{
                  display: "flex",
                  fontSize: "14px",
                  padding: "10px 0px 0px 10px",
                  color: "#808080",
                }}
              >
                {pagination?.attributes?.meta?.pagination?.total === 0
                  ? 0
                  : (currentPage - 1) * limitPerPage + 1}{" "}
                -{" "}
                {Math.min(
                  limitPerPage * currentPage,
                  pagination?.attributes?.meta?.pagination?.total
                )}{" "}
                of {pagination?.attributes?.meta?.pagination?.total} rows
              </div>
            </div>
            <Pagination
              className="pagination-bar"
              currentPage={currentPage}
              totalCount={pagination?.attributes?.meta?.pagination?.total || 0}
              pageSize={limitPerPage}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      <RetryViewDialog
        open={showRetryViewDialog}
        onClose={() => setShowRetryViewDialog(false)}
        rowData={rowData}
      />
      <SuccessDialog
        show={successDialog}
        onHide={() => {
          setSuccessDialog(false);
        }}
        message={message}
      />
      <ErrorDialog
        show={errorDialog}
        onHide={() => {
          setErrorDialog(false);
        }}
        message={message}
      />
      <DeleteDialog
        show={deleteDialog}
        onHide={() => setDeleteDialog(false)}
        onConfirm={() =>
          deleteRecordAPI(
            {
              groupId: rowData.errorGroupID,
              failureError: rowData.failureError,
              failureResult: rowData.failureResult,
            },
            {
              onSuccess: (resp) => {
                setDeleteDialog(false);
                setSuccessDialog(true);
                setMessage(`Record deleted successfully`);
                setCurrentPage(1);
                refetch();
              },
              onError: ({ response }) => {
                setDeleteDialog(false);
                setErrorDialog(true);
                setMessage(response?.data?.error?.message);
                refetch();
              },
            }
          )
        }
        title={"Are you sure you want to delete?"}
      />
    </div>
  );
};

export default ErrorTable;
