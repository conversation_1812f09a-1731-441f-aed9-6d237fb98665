import React, { useContext, useState } from "react";
import { FieldArray } from "formik";
import { Grid, IconButton } from "@mui/material";
import { DeleteIcon } from "../../icons";
import TextFieldWrapper from "../../components/FormsUI/TextField";
import CustomRadioButton from "../../components/RadioButton/RadioButton";
import Select from "../../components/FormsUI/Select";
import CustomSwitch from "../../components/ToggleSwitch/CustomSwitch";
import OutlinedButton from "../../components/Buttons/OutlinedButton";
import { DataContext } from "../../context/DataContext";
import InputLabel from "../FormsUI/InputLabel";
import DeleteDialog from "../Dialog/DeleteDialog";
import SuccessDialog from "../Dialog/SuccessDialog";

const MultiBlocks = ({
  blockName,
  name,
  blockIndex,
  block,
  values,
  moduleName,
  action,
}) => {
  const { updateShowFieldState } = useContext(DataContext);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [successDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [rowToDelete, setRowToDelete] = useState(null);

  const onDeleteClick = (index, remove) => {
    setRowToDelete(() => () => remove(index));
    setDeleteDialog(true);
  };
  const handleDeleteConfirm = () => {
    if (rowToDelete) {
      rowToDelete();
      setRowToDelete(null);
      setDeleteDialog(false);
      setSuccessDialog(true);
      setMessage("Customized calling party details deleted successfully");
    }
  };
  return (
    <>
      <FieldArray name={name}>
        {({ push, remove }) => (
          <>
            {values[name]?.map((row, index) => (
              <fieldset
                key={index}
                className="border border-gray-300 p-1 m-0 md:p-10 md:m-8 rounded-md"
              >
                <legend className="text-sm">{`${block.blockTitle} ${
                  index + 1
                }`}</legend>

                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12}>
                    <IconButton
                      isDisabled={action === "viewData" ? true : false}
                      type="button"
                      onClick={() =>
                        action !== "viewData"
                          ? onDeleteClick(index, remove)
                          : null
                      }
                      style={{ float: "right" }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                  {block.fields.map((element, elementIndex) => {
                    let showFieldIndex = element.dynamic
                      ? typeof element.dynamic["value"] === "string" ||
                        typeof element.dynamic["value"] === "number"
                        ? values.ss7PathExtended[index][
                            element.dynamic["field"]
                          ] === element.dynamic["value"]
                        : Array.isArray(element.dynamic["value"]) &&
                          element.dynamic["value"].includes(
                            values.ss7PathExtended[index][
                              element.dynamic["field"]
                            ]
                          )
                      : true;

                    if (showFieldIndex === true) {
                      updateShowFieldState(moduleName, name, showFieldIndex);
                    }
                    // if (!showFieldIndex) return null;
                    if (!showFieldIndex) {
                      updateShowFieldState(moduleName, name, false);
                      return null;
                    }
                    const size = 6;

                    return (
                      <Grid item xs={12} md={size} key={elementIndex}>
                        {/* Input Label */}
                        <InputLabel
                          label={element.title}
                          isMandatory={element.isMandatory}
                          color={"text-bgSecondary"}
                        />
                        {/* Rendering the field based on type */}
                        {(() => {
                          switch (element.type) {
                            case "radio":
                              return (
                                <CustomRadioButton
                                  name={`${name}.${index}.${element.name}`}
                                  options={element.options}
                                  isDisabled={
                                    action === "viewData" ? true : false
                                  }
                                />
                              );
                            case "text":
                              return (
                                <TextFieldWrapper
                                  name={`${name}.${index}.${element.name}`}
                                  placeholder={`Enter ${element.title}`}
                                  isDisabled={
                                    element.isDisabled || action === "viewData"
                                      ? true
                                      : false
                                  }
                                />
                              );
                            case "select":
                              return (
                                <Select
                                  name={`${name}.${index}.${element.name}`}
                                  options={element.options}
                                  isDropDownApi={element.isDropDownApi}
                                  collectionName={element.onClickPath}
                                  moduleName={"paths"}
                                  gtType={element.gtType}
                                  placeholder={`Select ${element.title}`}
                                  isDisabled={
                                    action === "viewData" ? true : false
                                  }
                                  values={values}
                                />
                              );
                            case "switch":
                              return (
                                <CustomSwitch
                                  name={`${name}.${index}.${element.name}`}
                                  generalInfo={element.generalInfo}
                                  isDisabled={
                                    action === "viewData" ? true : false
                                  }
                                />
                              );
                            default:
                              return null;
                          }
                        })()}
                      </Grid>
                    );
                  })}
                </Grid>
              </fieldset>
            ))}
            <div className="  p-1 m-0 md:p-10 md:m-8 relative">
              <OutlinedButton
                isDisabled={action === "viewData"}
                label={block.addButton}
                buttonClassName="border-[#bebebe] w-[270px]  text-sm font-bold  rounded-[10px] absolute top-0 right-0 "
                onClick={() => {
                  const newItem = {
                    callingPartyAddress: 1,
                    callingNP: "",
                    dummyGTValueCalling: "",
                    a2pGTValueCalling: "",
                    p2pGTValueCalling: "",
                    callingSSN: "",
                    callingTT: "",
                    clusterId: "",
                  };

                  const filteredItem = {};
                  // block.fields.forEach((element) => {
                  //   let showFieldIndex = element.dynamic
                  //     ? typeof element.dynamic["value"] === "string" ||
                  //       typeof element.dynamic["value"] === "number"
                  //       ? values[element.dynamic["field"]] ===
                  //         element.dynamic["value"]
                  //       : Array.isArray(element.dynamic["value"]) &&
                  //         element.dynamic["value"].includes(
                  //           values[element.dynamic["field"]]
                  //         )
                  //     : true;

                  //   if (showFieldIndex) {
                  //     filteredItem[element.name] = newItem[element.name];
                  //   }
                  // });

                  push(newItem);
                }}
              />
            </div>
          </>
        )}
      </FieldArray>
      <DeleteDialog
        show={deleteDialog}
        onHide={() => {
          setDeleteDialog(false);
        }}
        onConfirm={handleDeleteConfirm}
        title={"Are you sure you want to delete?"}
      />
      <SuccessDialog
        show={successDialog}
        onHide={() => {
          setSuccessDialog(false);
        }}
        message={message}
      />
    </>
  );
};

export default MultiBlocks;
