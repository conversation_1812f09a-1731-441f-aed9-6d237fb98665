{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "eslint.alwaysShowStatus": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "files.trimTrailingWhitespace": true, "diffEditor.ignoreTrimWhitespace": false, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "editor.suggestSelection": "first", "workbench.preferredHighContrastColorTheme": "Default Dark+", "workbench.preferredLightColorTheme": "Default Dark+", "workbench.editor.wrapTabs": true, "workbench.editor.tabSizing": "shrink", "javascript.updateImportsOnFileMove.enabled": "always", "gitlens.advanced.messages": {"suppressLineUncommittedWarning": true}, "git.autofetch": true, "[jsonc]": {"editor.defaultFormatter": "SimonSiefke.prettier-vscode"}}