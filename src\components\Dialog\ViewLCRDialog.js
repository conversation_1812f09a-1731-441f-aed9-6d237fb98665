import React, { useState, useContext } from "react";
import Dialog from "@mui/material/Dialog";
import Button from "../Buttons/Button";
import { CloseIcon } from "../../icons";
import { IconButton } from "@mui/material";
import { moduleConfiguration } from "../../common/constants";
import { useMutation, useQuery } from "react-query";
import { DEFAULT_PAGE_SIZE } from "../../common/config";
import { getParentDetails } from "../../lib/lcr-profile-api";
import CustomTable from "../Table";
import TransformLCRData from "../TransformData/TransformLCRData";
import { useNavigate } from "react-router-dom";
import { getColumnsConfig } from "../../common/listPageUtils";
import Pagination from "../Pagination/Pagination";
import ResultPerPageComponent from "../Pagination/ResultPerPage";
import { LCRType } from "../../common/constants";
import { DropdownContext } from "../../context/DropDownContext";

function ViewLCRDialog({ open, handleClose, name, values, formData }) {
  const navigate = useNavigate();

  const id = values ? values[name] : "";
  const [limitPerPage, setLimitPerPage] = useState(DEFAULT_PAGE_SIZE);
  const [currentPage, setCurrentPage] = useState(1);
  const [listData, setListData] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [filteredRow, setFilteredRow] = useState("");
  const [pagination, setPagination] = useState("");
  const [filteredData, setFilteredData] = useState([]);
  const [selectedIds, setSelectedIds] = useState([]);
  const [showLastColumn, setShowLastColumn] = useState(false);
  const { selectedData } = useContext(DropdownContext);

  const handleLimitChange = (e) => {
    setLimitPerPage(e.target.value);
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const lcrTypeValue = LCRType.find((lcr) => name === lcr.label)?.value;

  const LCRName =
    selectedData &&
    Object.entries(selectedData).find(
      ([key, value]) => key === name && value
    )?.[1]?.label;

  useQuery(
    [
      formData?.rule === "AT LCR"
        ? moduleConfiguration.scAtLCRModuleName
        : moduleConfiguration.lcrConfiguration,
      id,
      moduleConfiguration?.parentDetails,
      lcrTypeValue,
      limitPerPage,
      currentPage,
    ],
    getParentDetails,
    {
      enabled: id > 0 && id !== "",
      refetchOnWindowFocus: false,
      onSuccess: ({ data }) => {
        setListData(data?.data?.attributes?.parentDetails);
        //   setOpenDialog(false);
        setPagination(data?.data || []);
      },
    }
  );
  const transformedData = TransformLCRData(listData, currentPage, limitPerPage);
  const total = pagination?.attributes?.meta?.pagination?.total;
  const moduleData =
    formData?.rule === "AT LCR"
      ? moduleConfiguration.scAtLCRModuleName
      : moduleConfiguration.lcrConfiguration;

  const columnsData = getColumnsConfig({
    navigate,
    moduleData,
    moduleConfiguration,
    undefined,
    undefined,
    undefined,
    lcrId: id,
    lcrTypeValue: 5,
    total,
    actions: false,
    setShowLastColumn,
  });

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 800,
          maxHeight: 550,
        },
      }}
    >
      {/* Close Button */}
      <div className="flex justify-end m-2">
        <IconButton onClick={handleClose} size="small">
          <CloseIcon className="w-3 h-3 text-black" />
        </IconButton>
      </div>
      <div className="ml-6 font-bold">
        {formData?.rule === "AT LCR"
          ? "List of SC AT LCR > "
          : "List of LCR > "}
        {` LCR Name - ${LCRName}`}
      </div>
      <div className="my-4 max-w-[750px] text-center mx-auto">
        <CustomTable
          data={transformedData || []}
          columns={columnsData}
          onRowClick={() => {}}
          isLcrDetails={true}
          setIsSearching={setIsSearching}
          setFilteredRow={setFilteredRow}
          setFilteredData={setFilteredData}
          setSelectedIds={setSelectedIds}
          showLastColumn={showLastColumn}
        />
      </div>

      {pagination &&
        pagination?.attributes?.meta?.pagination?.total !== undefined && (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              margin: "20px 0px 20px 20px",
              width: "95%",
            }}
          >
            <div className="flex">
              <ResultPerPageComponent
                limit={limitPerPage}
                handleLimitChange={handleLimitChange}
              />
              <div
                style={{
                  display: "flex",
                  fontSize: "14px",
                  padding: "10px 0px 0px 10px",
                  color: "#808080",
                }}
              >
                {!isSearching ? (
                  <>
                    {(pagination?.attributes?.meta?.pagination?.total === 0
                      ? 0
                      : (currentPage - 1) * limitPerPage + 1) +
                      " - " +
                      Math.min(
                        limitPerPage * currentPage,
                        pagination?.attributes?.meta?.pagination?.total
                      )}{" "}
                    of {pagination?.attributes?.meta?.pagination?.total} rows
                  </>
                ) : (
                  <>
                    <>
                      {`${
                        filteredRow === 0
                          ? 0
                          : (currentPage - 1) * limitPerPage + 1
                      }  - ${
                        (currentPage - 1) * limitPerPage + filteredRow
                      } of ${
                        pagination?.attributes?.meta?.pagination?.total
                      } rows (Matched for this page)`}
                    </>
                  </>
                )}
              </div>
            </div>
            <Pagination
              className="pagination-bar"
              currentPage={currentPage}
              totalCount={pagination?.attributes?.meta?.pagination?.total || 0}
              pageSize={limitPerPage}
              onPageChange={handlePageChange}
              isSearching={isSearching}
            />
          </div>
        )}
      {/* Okay Button */}
      <div className="text-center mt-4 mb-4">
        <Button
          type="submit"
          label={"Close"}
          buttonClassName="w-[200px] h-9 text-xs ml-5"
          onClick={handleClose}
        />
      </div>
    </Dialog>
  );
}

export default ViewLCRDialog;
