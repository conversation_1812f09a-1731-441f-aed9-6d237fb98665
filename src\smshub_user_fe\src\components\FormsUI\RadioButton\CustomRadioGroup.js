import React from "react";
import { Radio, RadioGroup, FormControlLabel } from "@mui/material";
import InputLabel from "../InputLabel";

const CustomRadioGroup = ({
  id,
  name,
  value,
  options,
  onChange,
  onBlur,
  label,
  isMandatory,
  disabled,
  labelClassName,
  radioStyles,
}) => {
  return (
    <div className="flex flex-col">
      <InputLabel
        label={label}
        isMandatory={isMandatory}
        labelClassName={labelClassName || "mb-2"}
      />
      <RadioGroup
        row
        id={id}
        name={name}
        onChange={onChange}
        onBlur={onBlur}
        value={value}
      >
        {options.map((option) => (
          <FormControlLabel
            key={option.value}
            value={option.value}
            control={
              <Radio
                sx={{
                  "& .MuiSvgIcon-root": { fontSize: "16px" },
                  "&.Mui-checked .MuiSvgIcon-root": { color: "#3576EB" },
                  ...radioStyles,
                }}
              />
            }
            label={
              <span className="text-tabColor text-sm">{option.label}</span>
            }
            checked={String(value) === String(option.value)}
            disabled={disabled}
          />
        ))}
      </RadioGroup>
    </div>
  );
};

export default CustomRadioGroup;
