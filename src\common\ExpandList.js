import React, { useState } from "react";
import ExpandListDialog from "../components/Dialog/ExpandListDialog";

export const ExpandListCellValue = ({ row }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showExpandList, setShowExpandList] = useState(false);

  const numbersArray = row.original.attributes.msisdn || [];

  return (
    <>
      {" "}
      <span
        style={{ display: "flex", whiteSpace: "nowrap" }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {numbersArray.join(", ")} ...{" "}
        {isHovered && numbersArray.length > 0 && (
          <span
            style={{
              fontStyle: "italic",
              color: "#3576EB",
              fontWeight: 700,
              cursor: "pointer",
              whiteSpace: "nowrap",
            }}
            onClick={() => {
              setShowExpandList(true);
            }}
          >
            expand list
          </span>
        )}
      </span>
      <ExpandListDialog
        show={showExpandList}
        onHide={() => {
          setShowExpandList(false);
        }}
        row={row}
      />
    </>
  );
};
