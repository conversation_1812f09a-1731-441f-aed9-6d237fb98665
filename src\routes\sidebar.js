/**
 * ⚠ These are used just to render the Sidebar!
 * You can include any link here, local or external.
 *
 * If you're looking to actual Router routes, go to
 * `routes/index.js`
 */
///Candidate Left menu
const routes = [
  {
    icon: "Home",
    name: "Home",
    path: "/app/home",
    show: true,
  },
  {
    icon: "ServiceManagementIcon",
    name: "Service Management",
    path: "/app/list/service-managements/edit",
    show: true,
    featureName: "service_params",
  },
  {
    icon: "EnvironmentIcon",
    name: "Environmental Setup",
    path: "/app/environment",
    show: true,
    routes: [
      {
        name: "Retry policy",
        path: "/app/list/retry-policies",
        show: true,
      },
      {
        name: "Group customer supplier",
        path: "/app/list/group-customer-suppliers",
        show: true,
      },
      {
        name: "Deal management",
        path: "/app/list/deal-managements",
        show: true,
      },
      {
        name: "Channel partner",
        path: "/app/list/channel-partners",
        show: true,
      },
      {
        name: "Customer credit profile",
        path: "/app/list/customer-credit-profiles",
        show: true,
      },
      {
        name: "Credit transaction",
        path: "/app/list/credit-transactions",
        show: true,
      },
    ],
    featureName: "",
  },
  {
    icon: "PrerequisitesIcon",
    name: "Prerequisites",
    path: "/app/prerequisite",
    show: true,
    routes: [
      {
        name: "Series",
        path: "/app/list/seriess",
        show: true,
      },
      {
        name: "MNP Gateway",
        path: "/app/list/mnp-gateway-details",
        show: true,
      },
      {
        name: "Operator",
        path: "/app/list/operators",
        show: true,
        featureName: "operator_details",
      },
      {
        path: "/app/list/operator-clusters",
        show: true,
        featureName: "operator_cluster",
      },
      {
        name: "Point code",
        path: "/app/list/point-codes",
        show: true,
      },
      {
        name: "Point code list",
        path: "/app/list/point-code-lists",
        show: true,
      },
      {
        name: "HTTP template",
        path: "/app/list/http-templates",
        show: true,
      },
      {
        name: "Redirectional account",
        path: "/app/list/redirectional-accounts",
        show: true,
      },
      {
        name: "Redirectional list",
        path: "/app/list/redirectional-lists",
        show: true,
      },
      {
        name: "Path",
        path: "/app/list/paths",
        show: true,
        featureName: "path_details",
      },
      {
        name: "Port",
        path: "/app/list/ports",
        show: true,
      },
      {
        name: "ESME account",
        path: "/app/list/esme-accounts",
        show: true,
      },
      {
        name: "LCR profile",
        path: "/app/list/lcr-profiles",
        show: true,
      },
      {
        name: "SC AT LCR profile",
        path: "/app/list/sc-at-lcrs",
        show: true,
      },
    ],
  },
  {
    icon: "CSMManagementIcon",
    name: "Customer/ Supplier management",
    path: "/app/list/customer-supplier-managements",
    show: true,
  },
  {
    icon: "UserIcon",
    name: "User management",
    path: "/app/user",
    show: true,
  },
  {
    icon: "RulesIcon",
    name: "Rules management",
    path: "/app/rule",
    show: true,
    routes: [
      {
        name: "Rule configuration",
        path: "/app/list/rule-configurations",
        show: true,
        featureName: "rules_configuration",
      },
      {
        name: "Hub rule configuration",
        path: "/app/list/hub-rule-configurations",
        show: true,
        featureName: "hub_rules_configuration",
      },
      {
        name: "Supplier MNP rule configuration",
        path: "/app/list/supplier-mnp-rules",
        show: true,
        featureName: "supplier_based_mnp_rule_configuration",
      },
    ],
  },
  {
    icon: "SessionIcon",
    name: "Session management",
    path: "/app/session",
    show: true,
    routes: [
      {
        name: "Node sessions",
        path: "/app/list/node-sessions",
        show: true,
        featureName: "node_sessions",
      },
      {
        name: "SMSC sessions",
        path: "/app/list/smsc-sessions",
        show: true,
        featureName: "smsc_sessions",
      },
      {
        name: "ESME sessions",
        path: "/app/list/esme-sessions",
        show: true,
        featureName: "esme_sessions",
      },
    ],
  },
  {
    icon: "ContactUsIcon",
    name: "Contact us",
    path: "/app/contact-us",
    show: true,
    featureName: "contact_us",
  },
  {
    icon: "AboutUsIcon",
    name: "About us",
    path: "/app/about-us",
    show: true,
    featureName: "about",
  },
];

export default routes;
