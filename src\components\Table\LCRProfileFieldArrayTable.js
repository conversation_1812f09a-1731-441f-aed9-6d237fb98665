import React, { useState, useEffect } from "react";
import { FieldArray, useFormikContext } from "formik";
import Select from "../FormsUI/Select";
import TextFieldWrapper from "../FormsUI/TextField";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
} from "@mui/material";
import { PlusCircleIcon, DeleteIcon } from "../../icons";
import AddButton from "../Buttons/OutlinedButton";
import { useOperatorData } from "../Dropdown";
import { MAX_SUPPLIERS } from "../../common/config";
import theme from "../../tailwind-theme";
import DeleteDialog from "../Dialog/DeleteDialog";

const RenderTableBody = ({ name, values, fields, headers, onDeleteClick }) => (
  <FieldArray name={name}>
    {({ push, remove }) => {
      const selectedSuppliers = values
        .map((row) => row.supplier)
        .filter(Boolean);

      return (
        <>
          {values?.map((_, index) => (
            <TableRow key={index}>
              {fields?.map((field, fieldIndex) => (
                <TableCell key={fieldIndex}>
                  {field.options ? (
                    <Select
                      name={`${name}.${index}.${field.name}`}
                      options={field.options[0].filter(
                        (option) =>
                          !selectedSuppliers.includes(option.value) ||
                          option.value === values[index].supplier
                      )}
                      className="w-full md:w-[160px] "
                    />
                  ) : (
                    <TextFieldWrapper name={`${name}.${index}.${field.name}`} />
                  )}
                </TableCell>
              ))}
              <TableCell>
                {values.length > 1 && (
                  <IconButton
                    type="button"
                    onClick={() => onDeleteClick(index, remove)}
                  >
                    <DeleteIcon />
                  </IconButton>
                )}
              </TableCell>
            </TableRow>
          ))}
          {values.length < MAX_SUPPLIERS && (
            <TableRow>
              <>
                {" "}
                {Array(fields.length)
                  .fill("")
                  .map((_, idx) => (
                    <TableCell key={idx}>
                      <div
                        style={{
                          border: "2px dotted #BEBEBE",
                          padding: "8px",
                          borderRadius: "10px",
                          height: "36px",
                          width: "100%",
                        }}
                      ></div>
                    </TableCell>
                  ))}
              </>
              <TableCell colSpan={headers.length + 1} align="right">
                <IconButton
                  type="button"
                  onClick={() =>
                    // allFieldsFilled &&
                    push(
                      fields.reduce(
                        (acc, field) => ({ ...acc, [field.name]: "" }),
                        {}
                      )
                    )
                  }
                  // disabled={!allFieldsFilled}
                >
                  <PlusCircleIcon />
                </IconButton>
              </TableCell>
            </TableRow>
          )}
        </>
      );
    }}
  </FieldArray>
);

const RenderTable = ({ name, values, headers, fields, onDeleteClick }) => (
  <TableContainer component={Paper} sx={{ boxShadow: "none", maxHeight: 400 }}>
    <Table sx={{ minWidth: 550 }} aria-label="sticky table" stickyHeader>
      <TableHead sx={{ bgcolor: theme.backgroundColor.bgTable }}>
        <TableRow>
          {headers?.map((header, index) => (
            <TableCell
              key={index}
              sx={{
                position: "sticky",
                top: 0,
                zIndex: 1,
                backgroundColor: theme.backgroundColor.bgTable,
                fontWeight: "bold",
              }}
            >
              <div className="ml-0.5 text-xs font-semibold">{header}</div>
            </TableCell>
          ))}
          <TableCell
            sx={{
              position: "sticky",
              top: 0,
              zIndex: 1,
              backgroundColor: theme.backgroundColor.bgTable,
              fontWeight: "bold",
            }}
          />
        </TableRow>
      </TableHead>
      <TableBody>
        <RenderTableBody
          name={name}
          values={values}
          fields={fields}
          headers={headers}
          onDeleteClick={onDeleteClick}
        />
      </TableBody>
    </Table>
  </TableContainer>
);

const LCRProfileFieldArrayTable = ({
  name,
  values,
  headers,
  fields,
  setFieldValue,
  isEdit,
}) => {
  const [policies, setPolicies] = useState(values?.parentDetails || []);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [rowToDelete, setRowToDelete] = useState(null);

  const { setFieldValue: formikSetFieldValue } = useFormikContext();

  const operatorDetails = useOperatorData();

  useEffect(() => {
    policies.forEach((policy, policyIndex) => {
      const selectedOperator = operatorDetails.find(
        (operator) => operator.value === policy.destOpId
      );
      if (selectedOperator) {
        formikSetFieldValue(
          `parentDetails[${policyIndex}].region`,
          selectedOperator.region
        );
        formikSetFieldValue(
          `parentDetails[${policyIndex}].mccMnc`,
          selectedOperator.mccMnc
        );
      }
    });
  }, [policies, operatorDetails, formikSetFieldValue]);

  useEffect(() => {
    if (!values?.parentDetails) {
      setPolicies([]);
    } else {
      setPolicies(values.parentDetails);
    }
  }, [values]);

  const onDeleteClick = (index, remove) => {
    setRowToDelete(() => () => remove(index));
    setDeleteDialog(true);
  };

  const handleDeleteConfirm = () => {
    if (rowToDelete) {
      rowToDelete();
      setRowToDelete(null);
      setDeleteDialog(false);
    }
  };

  const addNewLCRPolicy = () => {
    const newPolicy = {
      destOpId: "",
      mccMnc: "",
      policyDetails: [
        {
          supplier: "",
          cost: "",
          percentage: "",
          position: "",
          quality: "",
        },
      ],
      isNew: true,
    };

    let newPolicyData = [...policies, newPolicy];

    setPolicies(newPolicyData);
    setFieldValue(name, newPolicyData);
  };

  const selectedOperators = policies.map((policy) => policy.destOpId);

  return (
    <>
      {" "}
      <div>
        {policies?.map((policy, policyIndex) => (
          <div
            key={policyIndex}
            className="border border-subNavBorder p-3 bg-bgField mb-5 rounded-[10px]"
          >
            <div className="flex justify-between mb-3">
              <div className="flex items-center">
                <div className="text-sm font-extrabold ml-1">
                  {`LCR policy ${policyIndex + 1}`}
                </div>
              </div>
              <div className="flex flex-wrap items-center">
                <>
                  <div className="flex items-center mr-3">
                    <div className="text-xs mr-3">Destination operator</div>
                    <Select
                      name={`parentDetails[${policyIndex}].destOpId`}
                      options={operatorDetails.filter(
                        (operator) =>
                          !selectedOperators.includes(operator.value) ||
                          operator.value === policy.destOpId
                      )}
                      value={policy.destOpId}
                      onChange={(value) => {
                        formikSetFieldValue(
                          `parentDetails[${policyIndex}].destOpId`,
                          value
                        );
                      }}
                      className="md:w-[180px] w-full"
                      bgColor="#FFFFFF"
                      isDisabled={isEdit && !policy.isNew}
                    />
                  </div>
                  <div className="flex items-center ">
                    <div className="text-xs mr-3">Region</div>
                    <TextFieldWrapper
                      name={`parentDetails[${policyIndex}].region`}
                      className="md:w-[180px] w-full"
                      bgColor="#FFFFFF"
                      isDisabled={true}
                    />
                  </div>
                </>
              </div>
            </div>
            <RenderTable
              name={`parentDetails[${policyIndex}].policyDetails`}
              values={values?.parentDetails?.[policyIndex]?.policyDetails || []}
              headers={headers}
              fields={policy.fieldsData || fields}
              onDeleteClick={onDeleteClick}
            />
          </div>
        ))}
      </div>
      <div className="mt-5 flex justify-end items-end">
        <AddButton
          label="+ Add new destination operator"
          buttonClassName="text-xs font-bold"
          onClick={addNewLCRPolicy}
          // disabled={!areSuppliersChosen}
        />
      </div>
      <DeleteDialog
        show={deleteDialog}
        onHide={() => setDeleteDialog(false)}
        message={message}
        onConfirm={handleDeleteConfirm}
        title={
          <>
            Are you sure you want to delete?
            <br />
            (Deletion will only happen on click of save)
          </>
        }
      />
    </>
  );
};

export default LCRProfileFieldArrayTable;
