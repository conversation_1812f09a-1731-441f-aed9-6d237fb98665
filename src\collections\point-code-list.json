{"header": "List of point code list", "columns": [{"header": "List id", "accessorKey": "id"}, {"header": "List name", "accessorKey": "attributes.redirectionListName"}, {"header": "List type", "accessorKey": "attributes.redirectionType"}, {"buttons": [{"title": "View account"}, {"title": "View rules"}, {"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"name": "redirectionListType", "type": "none", "defaultValue": "P"}, {"info": "Name of Point code list", "name": "redirectionListName", "size": 6, "type": "text", "title": "List name", "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the List name"}, {"type": "matches", "regex": "^[a-zA-Z][a-zA-Z0-9_]*$", "message": "Only alphanumeric characters and underscores are allowed, and it must start with a letter"}], "defaultValue": "", "validationType": "string"}, {"info": "Same as Redirection Type. [The of distribution of SMS traffic among SMSC/point codes]", "name": "redirectionType", "size": 6, "type": "select", "title": "Load distribution type", "isInfo": true, "options": [{"label": "Round robin", "value": 1}, {"label": "Priority", "value": 2}, {"label": "Percentage", "value": 3}], "isMandatory": true, "nonEditable": true, "validations": [{"type": "required", "message": "Please select the Load distribution type"}], "defaultValue": ""}, {"name": "sampleVal", "size": 6, "type": "text", "title": "Sample Value", "dynamic": {"field": "redirectionType", "value": 3}, "isMandatory": false, "validations": [{"type": "max", "value": 5, "message": "Sample Value can be upto 5 digits"}, {"type": "matches", "regex": "^(0|[1-9][0-9]*)(\\.[0-9]+)?$", "message": "Please enter the Sample Value with Positive numeric value"}], "defaultValue": "", "validationType": "string"}, {"info": "Point code available/configured on the platform", "name": "point_codes", "size": 6, "type": "multiselect", "title": "Add point code accounts", "isInfo": true, "apiOptions": true, "isAddButton": true, "isMandatory": false, "onClickPath": "point-codes", "validations": [{"type": "required", "message": "At least one option must be selected"}], "defaultValue": []}, {"name": "priorityTable", "size": 12, "type": "FieldTable", "dynamic": {"field": "redirectionType", "value": 3}, "fieldsData": [{"name": "SMSC account", "type": "text"}, {"name": "percentage"}, {"name": "Traffic"}], "tableTitle": "Sample value distribution", "headersData": ["Account", "percentage", "Traffic"], "initialValues": {"items": [{"Traffic": "", "percentage": "", "SMSC account": ""}]}}, {"name": "priorityTable", "size": 12, "type": "FieldTable", "dynamic": {"field": "redirectionType", "value": 2}, "fieldsData": [{"name": "Account", "type": "text"}, {"name": "priority"}], "tableTitle": "Priority table", "headersData": ["Account", "Priority"], "isMandatory": false, "initialValues": {"items": [{"Account": "", "priority": ""}]}, "validationType": "string"}], "formType": "simple", "buttonName": "+ Add new point code list", "moduleData": "point-code-list", "moduleName": "Point Code List", "globalSearch": [{"label": "List id", "value": "id"}, {"label": "List name", "value": "redirectionListName"}], "dropdownOptions": {"field": "redirectionType", "options": [{"label": "Round robin", "query": "filters[redirectionType][$eq]=1", "value": 1}, {"label": "Priority", "query": "filters[redirectionType][$eq]=2", "value": 2}, {"label": "Percentage", "query": "filters[redirectionType][$eq]=3", "value": 3}], "defaultQuery": "filters[redirectionType][$eq]=1", "defaultValue": 1}}