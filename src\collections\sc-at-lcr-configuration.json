{"header": "List of LCR", "columns": [{"header": "LCR ID", "accessorKey": "id"}, {"header": "LCR name", "accessorKey": "attributes.lcrName"}, {"header": "LCR type", "accessorKey": "attributes.lcrType"}, {"buttons": [{"type": "image", "title": "View", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}, {"type": "image", "title": "Export", "iconUrl": ""}], "accessorKey": "actions"}], "formType": "simple", "elements": [{"name": "lcrType", "title": "Lcr type", "options": [{"label": "SC_AT", "value": 2}]}, {"name": "custType", "title": "Customer Type", "options": [{"label": "Operator", "value": "S"}, {"label": "<PERSON><PERSON>", "value": "H"}]}], "buttonName": "+ Add LCR", "moduleData": "sc-at-lcr", "moduleName": "LCR", "globalSearch": [{"label": "LCR ID", "value": "id"}, {"label": "LCR name", "value": "lcrName"}]}