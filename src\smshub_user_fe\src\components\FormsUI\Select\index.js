//import React from "react";
import { ErrorMessage, useField, useFormikContext } from "formik";
import ReactSelect, { components, createFilter } from "react-select";
import theme from "../../../tailwind-theme";

const Select = ({
  fontSize,
  minHeight,
  isDisabled,
  setFormData,
  title,
  bgColor,
  values,
  borderRadius,
  filter,
  queryInfo,
  isSearchable,
  width,
  border,
  paddingX,

  ...props
}) => {
  const [field, meta, helpers] = useField(props.name);
  const { setFieldValue, setFieldTouched } = useFormikContext();
  // console.log(`isDisabled: ${props.isDisabled}`);
  const customSelectStyles = {
    control: (baseStyles, state) => ({
      ...baseStyles,
      fontWeight: theme.fontWeight.fields,
      fontSize: theme.fontSize.xs,
      minWidth: width || "initial",
      minHeight: minHeight || "40px",
      backgroundColor: bgColor ? bgColor : theme.backgroundColor.bgField,
      pointerEvents: isDisabled ? "none" : "auto",
      cursor: isDisabled ? "default" : "pointer",
      borderWidth: "1px",
      border: border
        ? border
        : meta.error && meta.touched
        ? `1px solid ${theme.borderColor.errorBorder}`
        : `1px solid ${theme.borderColor.tableBorder}`,
      "&:hover": {
        border: `1px solid ${theme.borderColor.tableBorder}`,
      },
      padding: paddingX || "none",
      borderRadius: borderRadius || theme.borderRadius.fieldRadius,
      boxShadow: "none",
      borderTopRightRadius: props.borderTopRightRadius || "",
      borderBottomRightRadius: props.borderBottomRightRadius || "",
    }),
    menuPortal: (base) => ({
      ...base,
    }),
    option: (provided, { isDisabled, isSelected }) => ({
      ...provided,
      color: theme.textColor.headerColor,
      background: isSelected
        ? theme.backgroundColor.bgSelected
        : theme.backgroundColor.bgPrimary,
      ":hover": {
        background: theme.backgroundColor.bgSelected,
      },
      display: "flex",
      justifyContent: "space-between",
      cursor: "pointer",
    }),
    placeholder: (provided) => ({
      ...provided,
      fontSize: fontSize || "12px",
      fontWeight: "500",
      color: "#666666",
    }),
    menu: (styles) => ({
      ...styles,
      backgroundColor: "white",
      fontSize: fontSize || "12px",
      position: "absolute",
      minWidth: "100%",
      border: "1px solid #666666",
      boxShadow: "0 !important",
      "&:hover": {
        border: "1px solid #666666",
      },
    }),
  };
  const IconOption = (props) => (
    <components.Option {...props} className="flex justify-between">
      {props.data.label}
    </components.Option>
  );

  return (
    <div className="relative">
      <ReactSelect
        aria-label={props.name}
        className={`input ${props.className} ${
          meta.touched && meta.error && "is-danger"
        }`}
        options={props.options}
        isSearchable={props.isSearchable || true}
        styles={customSelectStyles}
        type="text"
        filterOption={createFilter({ ignoreAccents: false })}
        isClearable={props.isClearable}
        maxMenuHeight={props.maxMenuHeight}
        name={props.name}
        isDisabled={props.disabled}
        isLoading={props.isLoading}
        menuPortalTarget={document.body}
        menuPlacement="bottom"
        menuPosition="fixed"
        menuShouldScrollIntoView={false}
        onMenuOpen={() => {
          const dropdownElement = document.querySelector(
            ".react-select__control"
          );
          dropdownElement?.scrollIntoView({
            behavior: "smooth",
            block: "nearest",
          });
        }}
        value={
          !props.isMulti
            ? props.options
              ? props?.options?.find(
                  (option) => option.value === field.value
                ) || ""
              : ""
            : props?.options?.filter(
                (option) => field.value && field.value.includes(option.value)
              )
        }
        onChange={(option) => {
          setFieldTouched(props.name, true);
          if (props.onChange) props.onChange(option);
          if (!props.isMulti) {
            helpers.setValue(option);
            setFieldValue(props.name, option.value);
          } else {
            let options = option?.map((option) => option.value);
            setFieldValue(props.name, options);
          }
        }}
        onBlur={() => setFieldTouched(props.name, true)}
        placeholder={props.placeholder}
        components={{
          IndicatorSeparator: () => null,
          Option: IconOption,
        }}
      ></ReactSelect>
      <ErrorMessage
        component="div"
        className={` text-[#d32f2f] text-[0.75rem] mt-0 font-normal`}
        name={field.name}
      />
    </div>
  );
};

export default Select;
