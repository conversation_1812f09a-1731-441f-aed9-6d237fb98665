import React from "react";
import { useNavigate, useLocation } from "react-router-dom";

function StepperLayout({ title, children, respData, header, lcrDetails }) {
  const navigate = useNavigate();
  const location = useLocation();
  const collectionName = location?.state?.collectionName;

  return (
    <div className="w-full h-full  ">
      <div className="flex gap-2 font-medium my-7">
        <div
          className="text-textNAColor text-base cursor-pointer"
          onClick={() => {
            navigate(`/app/list/${collectionName || lcrDetails}`);
          }}
        >{`${respData?.header || header} > `}</div>
        <div className="text-base ">{title}</div>
      </div>

      {/* <div className="mt-5 bg-bgHeader text-headerColor font-bold rounded-t-lg align-middle flex justify-center py-5 text-lg">
        {title}
      </div> */}
      <div className="">{children}</div>
    </div>
  );
}

export default StepperLayout;
