export const initializeIndexedDB = (dbName, storeName, keyPath = "id") => {
  return new Promise((resolve, reject) => {
    let request = indexedDB.open(dbName, 1);

    request.onupgradeneeded = function (event) {
      let db = event.target.result;

      if (!db.objectStoreNames.contains(storeName)) {
        let objectStore = db.createObjectStore(storeName, {
          keyPath: keyPath,
          autoIncrement: true,
        });

        objectStore.createIndex("dataIndex", "data", { unique: false });
        let addRequest = objectStore.add({ data: [] });
        addRequest.onsuccess = function (event) {
          resolve({ id: event.target.result, message: "Store initialized" });
        };
        addRequest.onerror = function (event) {
          reject(event.target.errorCode);
        };
      }
    };

    request.onsuccess = function (event) {
      let db = event.target.result;
      let transaction = db.transaction(storeName, "readonly");
      let store = transaction.objectStore(storeName);

      let getRequest = store.getAll();
      getRequest.onsuccess = function () {
        if (getRequest.result.length > 0) {
          resolve({
            data: getRequest.result,
            message: "Data fetched successfully",
          });
        } else {
          resolve({ data: [], message: "No records found in the store" });
        }
      };

      getRequest.onerror = function (event) {
        reject(event.target.errorCode);
      };
    };

    request.onerror = function (event) {
      reject(event.target.errorCode);
    };
  });
};

export const updateIndexedDBDataById = (dbName, storeName, id, updatedData) => {
  return new Promise((resolve, reject) => {
    let request = indexedDB.open(dbName, 1);

    request.onsuccess = function (event) {
      let db = event.target.result;
      let transaction = db.transaction(storeName, "readwrite");
      let store = transaction.objectStore(storeName);

      let getRequest = store.get(id);
      getRequest.onsuccess = function () {
        if (getRequest.result) {
          let recordToUpdate = { ...getRequest.result, ...updatedData };
          let putRequest = store.put(recordToUpdate);

          putRequest.onsuccess = function () {
            resolve(`Record with ID: ${id} successfully updated`);
          };

          putRequest.onerror = function (event) {
            reject(
              `Failed to update record with ID: ${id}, Error: ${event.target.errorCode}`
            );
          };
        } else {
          reject(`No record found with ID: ${id}`);
        }
      };

      getRequest.onerror = function (event) {
        reject(
          `Error fetching record with ID: ${id}, Error: ${event.target.errorCode}`
        );
      };
    };

    request.onerror = function (event) {
      reject(`Error opening database: ${event.target.errorCode}`);
    };
  });
};

export const getIndexedDBDataById = (dbName, storeName, id) => {
  return new Promise((resolve, reject) => {
    if (
      id === undefined ||
      id === null ||
      typeof id !== "number" ||
      isNaN(id)
    ) {
      return;
    }

    const request = indexedDB.open(dbName);

    request.onsuccess = function (event) {
      const db = event.target.result;
      const transaction = db.transaction(storeName, "readonly");
      const store = transaction.objectStore(storeName);

      const getRequest = store.get(id);

      getRequest.onsuccess = function () {
        resolve(getRequest.result);
      };

      getRequest.onerror = function (event) {
        reject(new Error(`Error fetching data: ${event.target.error}`));
      };
    };

    request.onerror = function (event) {
      reject(new Error(`Error opening database: ${event.target.error}`));
    };
  });
};
export const deleteIndexedDB = (dbName) => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.deleteDatabase(dbName);

    request.onsuccess = function () {
      resolve(`Database "${dbName}" deleted successfully.`);
    };

    request.onerror = function (event) {
      reject(new Error(`Error deleting database: ${event.target.error}`));
    };

    request.onblocked = function () {
      reject(
        new Error(
          `Database deletion blocked. Close all connections to "${dbName}" and try again.`
        )
      );
    };
  });
};
export const checkAndDeleteIndexedDB = async (dbName) => {
  const dbExists = await indexedDB
    .databases()
    .then((dbs) => dbs.some((db) => db.name === dbName))
    .catch(() => false);

  if (dbExists) {
    try {
      await deleteIndexedDB(dbName);
    } catch (error) {
      console.error(`Failed to delete ${dbName}:`, error);
    }
  }
};
