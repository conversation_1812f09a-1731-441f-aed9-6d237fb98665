import { Formik, Form } from "formik";
import Grid from "@mui/material/Grid";
import Button from "../Buttons/OutlinedButton";
import ConfirmNNextButton from "../Buttons/Button";
import * as Yup from "yup";
import { useNavigate } from "react-router-dom";
import { useMutation, useQuery } from "react-query";
import { createRecord, updateRecord, getDataById } from "../../lib/list-api";
import SuccessDialog from "../Dialog/SuccessDialog";
import { useContext, useEffect, useState } from "react";
import {
  getDefaultInitialValues,
  renderFields,
  generateValidationSchema,
  getRetryInitialValues,
} from "../../common/common";
import ErrorDialog from "../Dialog/ErrorDialog";
import { useLocation } from "react-router-dom";
import { DataContext } from "../../context/DataContext";
import { ErrorContext } from "../../context/ErrorContext";
import { MAX_PRIORITY } from "../../common/config";
import { ALLOWED_KEYS, moduleConfiguration } from "../../common/constants";
import {
  formatRetryGroupPolicy,
  listFormTemplate,
  processRetryPolicies,
} from "../../common/formTemplateUtils";
import {
  deleteIndexedDB,
  getIndexedDBDataById,
  initializeIndexedDB,
  updateIndexedDBDataById,
} from "../HelperFunction/localStorage";
import { CustomerSupplierContext } from "../../context/CustomerSupplierContext";
import { formatErrorMessage } from "../TransformData/TransformErrorMessage";
import { handleSubmitWithFocus } from "./SubmitWithFocus";
import { multiStepFormContext } from "../../context/MultiStepFormContext";
import { getIdAfterEdit } from "../../common/urlUtils";
const FormTemplate = (props) => {
  let {
    formTitle,
    elements,
    handleCancel,
    moduleName,
    action,
    moduleNameValue,
    navigationPath,
    header,
  } = props;
  const { mutate: createRecordAPI, isLoading: createLoading } =
    useMutation(createRecord);
  const { mutate: updateRecordAPI, isLoading: updateLoading } =
    useMutation(updateRecord);
  const { setCurrentStep } = useContext(multiStepFormContext);

  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErroDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [formData, setFormData] = useState({});
  const [previousFormData, setPreviousFormData] = useState({});
  const [existingData, setExistingData] = useState(null);
  const [multiSelectValue, setMultiSelectValue] = useState([]);
  const [singleSelectValue, setSingleSelectValue] = useState([]);
  const [fieldArrayError, setFieldArrayError] = useState("");
  const { showfieldNames, connType } = useContext(DataContext);

  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const currentId = searchParams.get("currentId");
  const [deleteLastEntry, setDeleteLastEntry] = useState(false);

  const lastElement = location.pathname.split("/").pop();
  const idValue = getIdAfterEdit(window.location.href);
  const { isLoading: isFormDataLoading } = useQuery(
    [moduleName, idValue, "getAll", lastElement || connType],
    getDataById,
    {
      enabled: idValue > 0 ? true : false,
      refetchOnWindowFocus: false,
      onSuccess: (resp) => {
        const filteredAttributes = Object.fromEntries(
          Object.entries(resp?.data?.data?.attributes || {}).filter(
            ([_, value]) => filterEmptyValues(value)
          )
        );
        const formattedAttributes = formatRetryGroupPolicy(filteredAttributes);
        setFormData(formattedAttributes);
      },
    }
  );

  const fetchData = async (isPrevious) => {
    if (currentId) {
      try {
        const data = await getIndexedDBDataById(
          "navigationDetails",
          "FormDB",
          parseInt(currentId)
        );
        if (isPrevious && data?.data?.length) {
          const lastRecord = data.data.find(
            (ele) => ele.moduleNameValue === moduleName
          );

          if (lastRecord) {
            setPreviousFormData(lastRecord);
            setDeleteLastEntry(true);
          }
        }
        setExistingData(data);
      } catch (error) {
        console.error("Error fetching data from IndexedDB:", error);
      }
    }
  };

  useEffect(() => {
    fetchData(action === "add" ? true : false);
  }, [currentId]);

  const doBeforeNavigate = async (path, forceDelete = false) => {
    if (deleteLastEntry || forceDelete) {
      const currentData = existingData.data.find(
        (entry) => entry.moduleNameValue === moduleName
      );
      existingData.data = existingData.data.filter(
        (entry) => entry.moduleNameValue !== moduleName
      );
      await updateIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        Number(currentId),
        existingData
      );
      if (currentData?.previousPath) {
        navigate(currentData.previousPath);
        return;
      }
    }
    navigate(path);
  };

  const handleButtonClick = async () => {
    if (existingData && existingData.data && existingData.data.length > 0) {
      const lastRecord = existingData.data[existingData.data.length - 1];
      if (lastRecord.moduleNameValue === moduleConfiguration.customerSupplier) {
        setCurrentStep(3);
      } else {
        setCurrentStep(0);
      }
      if (
        !lastRecord?.previousPath &&
        lastRecord?.currentPath === `${location.pathname}${location.search}`
      ) {
        try {
          doBeforeNavigate(`/app/list/${moduleName}`);
          await deleteIndexedDB("navigationDetails");
        } catch (error) {
          console.error("Error deleting database:", error);
        }
      } else if (lastRecord?.currentPath) {
        const fullUrl = window.location.href;
        const appUrl = fullUrl.substring(fullUrl.indexOf("/app"));

        if (lastRecord.currentPath === appUrl) {
          doBeforeNavigate(lastRecord.previousPath, true);
        } else {
          doBeforeNavigate(lastRecord.currentPath);
        }
      }
    } else {
      doBeforeNavigate(`/app/list/${moduleName}`);
    }
  };

  useEffect(() => {
    if (navigationPath) {
      const dbName = "navigationDetails";
      const storeName = "FormDB";

      initializeIndexedDB(dbName, storeName)
        .then((result) => {
          const currentUrl = new URL(window.location.href);

          if (!currentUrl.searchParams.has("currentId")) {
            currentUrl.searchParams.set(
              "currentId",
              result?.id ? result.id : result?.data?.[0]?.id || 1
            );
            navigate(currentUrl.pathname + currentUrl.search, {
              replace: true,
            });
          }
        })
        .catch((error) => {
          console.error("Error initializing IndexedDB:", error);
        });
    }
  }, [navigationPath, navigate]);

  const handleSubmit = (values) => {
    let filteredValues = Object.keys(values)
      .filter((key) =>
        Object.values(showfieldNames).some((array) => array.includes(key))
      )
      .reduce((obj, key) => {
        obj[key] = values[key];
        return obj;
      }, {});

    const allowedValues = {};
    let hasDuplicates = false;
    if (moduleName === "channel-partners") {
      elements.forEach((field) => {
        const dynamic = field.dynamic;

        let isDynamicValid = true;

        if (dynamic) {
          const fieldValue = String(values[dynamic.field]).toLowerCase();
          if (Array.isArray(dynamic.value)) {
            isDynamicValid = dynamic.value
              .map((val) => String(val).toLowerCase())
              .includes(fieldValue);
          } else {
            isDynamicValid = fieldValue === String(dynamic.value).toLowerCase();
          }
        }

        if (isDynamicValid) {
          allowedValues[field.name] = values[field.name];
        }
      });

      // Remove `id` and `isDelete` properties from objects in arrays
      Object.keys(allowedValues).forEach((key) => {
        if (Array.isArray(allowedValues[key])) {
          allowedValues[key] = allowedValues[key].map((item) => {
            const { id, isDelete, ...rest } = item;
            return rest;
          });
        }
      });

      function formatKey(key) {
        return key
          .replace(/([a-z])([A-Z])/g, "$1 $2")
          .replace(/_/g, " ")
          .replace(/^\w/, (char) => char.toUpperCase());
      }

      function formatCombination(combination) {
        return combination
          .split(" and ")
          .map((word) =>
            word
              .split(/(?=[A-Z])/)
              .map((part, index) =>
                index === 0
                  ? part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
                  : part.toLowerCase()
              )
              .join(" ")
          )
          .join(" and ");
      }

      Object.keys(allowedValues).forEach((key) => {
        const array = allowedValues[key];

        if (Array.isArray(array)) {
          const seenCombinations = new Set();
          let duplicateCombination = null;

          array.forEach((item) => {
            const keysToCheck = Object.keys(item).filter(
              (field) => field !== "targetPrice"
            );

            if (keysToCheck.length > 0) {
              const uniqueCombination = keysToCheck
                .map((field) => `${field}:${item[field]}`)
                .join(", ");

              if (seenCombinations.has(uniqueCombination)) {
                duplicateCombination = keysToCheck.join(" and ");
              } else {
                seenCombinations.add(uniqueCombination);
              }
            }
          });

          if (duplicateCombination) {
            hasDuplicates = true;
            const formattedKey = formatKey(key);
            const formattedCombination =
              formatCombination(duplicateCombination);
            setErroDialog(true);
            setMessage(
              `${formattedKey} has duplicate combinations of ${formattedCombination}`
            );
            return;
          }
        }
      });
    }
    if (hasDuplicates) {
      return;
    }

    let sampleData = {};
    // Redirectional account
    if (moduleName === moduleConfiguration.redirectionalAccModuleName) {
      const connType = filteredValues?.connType;
      const allowedKeys = ALLOWED_KEYS[connType] || [];

      Object.keys(filteredValues).forEach((key) => {
        if (!allowedKeys.includes(key)) {
          delete filteredValues[key];
        }
      });
    }

    if (filteredValues?.httpListType === "SMPP") {
      filteredValues.httpListType = 0;
    } else if (filteredValues?.httpListType === "HTTP") {
      filteredValues.httpListType = 1;
    }
    // Retry policies
    filteredValues = processRetryPolicies(filteredValues);

    // Redirectional list Point code list
    if (
      moduleName === moduleConfiguration.pointCodeListModuleName ||
      moduleName === moduleConfiguration.redirectionalListModuleName
    ) {
      const processedValues = listFormTemplate({
        moduleName,
        values,
        filteredValues,
        elements,
        setErroDialog,
        setMessage,
        MAX_PRIORITY,
      });

      if (!processedValues) return;
      filteredValues = processedValues;
    }

    Object.keys(filteredValues)?.forEach((x) => {
      if (filteredValues[x] !== undefined && filteredValues[x] !== "") {
        sampleData[x] = filteredValues[x];
      }
    });
    let dealFiltered = Object.keys(values).reduce(
      (acc, key) => {
        if (document.querySelector(`[name="${key}"]`)) {
          acc[key] = values[key];
        }
        return acc;
      },
      { startDate: values.startDate }
    );

    let reqData = {
      data:
        moduleName === "channel-partners"
          ? allowedValues
          : moduleName === moduleConfiguration.dealManagement
          ? dealFiltered
          : sampleData,
    };

    if (action === "add") {
      createRecordAPI(
        { moduleName, reqData },
        {
          onSuccess: (resp) => {
            setSuccessDialog(true);
            setMessage(`${moduleNameValue} created successfully`);
          },
          onError: ({ response }) => {
            const errorMessage = response?.data?.error?.message;
            if (moduleName === "operator-clusters") {
              const formattedMessage = formatErrorMessage(errorMessage);
              setErroDialog(true);
              setMessage(formattedMessage);
            } else {
              setErroDialog(true);
              setMessage(errorMessage);
            }
          },
        }
      );
    } else {
      updateRecordAPI(
        { moduleName, id: idValue, reqData },
        {
          onSuccess: (resp) => {
            setSuccessDialog(true);
            setMessage(`${moduleNameValue} updated successfully`);
          },
          onError: ({ response }) => {
            const errorMessage = response?.data?.error?.message;
            if (moduleName === "operator-clusters") {
              const formattedMessage = formatErrorMessage(errorMessage);
              setErroDialog(true);
              setMessage(formattedMessage);
            } else {
              setErroDialog(true);
              setMessage(errorMessage);
            }
          },
        }
      );
    }
  };
  function filterEmptyValues(obj) {
    if (obj === null || obj === undefined) {
      return false;
    }
    if (Array.isArray(obj)) {
      if (obj.length === 0) {
        return false;
      }
      return obj.map((item) => filterEmptyValues(item)).some(Boolean);
    }
    if (typeof obj === "object") {
      if (Object.keys(obj).length === 0) {
        return false;
      }
      return Object.entries(obj)
        .map(([_, value]) => filterEmptyValues(value))
        .some(Boolean);
    }
    return true;
  }

  const replaceNullInArrays = (formData) => {
    //channel-partner
    if (typeof formData !== "object" || formData === null) return formData;

    Object.keys(formData).forEach((key) => {
      if (Array.isArray(formData[key])) {
        formData[key] = formData[key].map((item) => {
          if (typeof item === "object" && item !== null) {
            Object.keys(item).forEach((itemKey) => {
              if (item[itemKey] === null) {
                item[itemKey] = "";
              }
            });
          }
          return item;
        });
      }
    });

    return formData;
  };

  const { generalInfoData } = useContext(CustomerSupplierContext);
  const protocolType = Number(generalInfoData?.subInterfaceEsme);
  const value =
    protocolType === "SS7"
      ? 1
      : protocolType === 1
      ? "S"
      : protocolType === "SMPP ES"
      ? 3
      : protocolType === 2
      ? "H"
      : null;

  const updatedElements = elements.map((element) => {
    if (element.name === "protocol" && protocolType) {
      return {
        ...element,
        defaultValue: value,
        addEditable: true,
      };
    }
    return element;
  });

  return (
    <div>
      {!isFormDataLoading ? (
        <Formik
          initialValues={
            action === "add" && !previousFormData?.formData
              ? getDefaultInitialValues(updatedElements, formData, moduleName)
              : previousFormData?.formData
              ? moduleName === moduleConfiguration.pointCodeListModuleName ||
                moduleName === moduleConfiguration.redirectionalListModuleName
                ? previousFormData?.formData
                : getDefaultInitialValues(
                    updatedElements,
                    previousFormData?.formData,
                    moduleName
                  )
              : moduleName === moduleConfiguration.retryPolicy
              ? getRetryInitialValues(updatedElements, formData)
              : moduleName === "channel-partners" ||
                moduleName === moduleConfiguration.pointCodeListModuleName ||
                moduleName === moduleConfiguration.redirectionalListModuleName
              ? replaceNullInArrays(formData)
              : getDefaultInitialValues(updatedElements, formData, moduleName)
          }
          validationSchema={Yup.object().shape(
            generateValidationSchema(updatedElements, formData)
          )}
          validateOnChange={true}
          validateOnBlur={true}
          onSubmit={(values) => handleSubmit(values)}
          // onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({
            values,
            submitForm,
            errors,
            touched,
            setFieldValue,
            ...formikProps
          }) => {
            return (
              <Form>
                <fieldset className="border border-gray-300 p-1 m-0 md:p-10 md:m-8 rounded-md">
                  <legend className="text-sm">{formTitle}</legend>
                  <Grid container spacing={4}>
                    {renderFields(
                      updatedElements,
                      values,
                      setFieldValue,
                      navigate,
                      formData,
                      action,
                      setMultiSelectValue,
                      multiSelectValue,
                      fieldArrayError,
                      setFieldArrayError,
                      moduleName,
                      singleSelectValue,
                      setSingleSelectValue,
                      previousFormData.formData,
                      header,
                      formikProps.setFieldTouched,
                      formikProps
                    )}
                  </Grid>
                </fieldset>
                <div className="flex justify-center gap-5 pt-5 pb-10">
                  {action === "viewData" ? (
                    <Button
                      label={"Close"}
                      buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
                      onClick={() => handleCancel()}
                    />
                  ) : (
                    <Button
                      label={"Cancel"}
                      buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
                      onClick={() => {
                        handleButtonClick();
                      }}
                    />
                  )}

                  {action === "viewData" ? null : (
                    <ConfirmNNextButton
                      label={"Save"}
                      buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
                      // onClick={() =>
                      //   handleSubmitWithFocus(
                      //     formikProps.validateForm,
                      //     submitForm,
                      //     formikProps.setTouched
                      //   )
                      // }
                      onClick={() => submitForm()}
                      loading={createLoading || updateLoading}
                    />
                  )}
                </div>
                <SuccessDialog
                  show={successDialog}
                  onHide={() => {
                    handleButtonClick();
                    setSuccessDialog(false);
                  }}
                  message={message}
                ></SuccessDialog>
                <ErrorDialog
                  show={errorDialog}
                  onHide={() => {
                    setErroDialog(false);
                  }}
                  message={message}
                ></ErrorDialog>
              </Form>
            );
          }}
        </Formik>
      ) : null}
    </div>
  );
};
export default FormTemplate;
