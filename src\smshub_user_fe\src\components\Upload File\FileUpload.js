import React, { useState, useRef } from "react";
import { ErrorMessage } from "formik";
import theme from "../../tailwind-theme";
import { ProfileIcon } from "../../icons";

const FileUpload = ({ profileData, setFieldValue }) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [imgErr, setImgErr] = useState(null);
  const fileInputRef = useRef(null);

  const handleImageChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const maxSize = 3 * 1024 * 1024; // 3MB limit
      const validTypes = ["image/jpeg", "image/png", "image/bmp", "image/gif"];

      if (file.size > maxSize) {
        setImgErr("File size must be less than 3MB");
        return;
      }

      if (!validTypes.includes(file.type)) {
        setImgErr("Invalid file type. Only jpeg/png/bmp/gif are allowed.");
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        setSelectedImage(reader.result);
        setFieldValue("image", file); // Set Formik field value
        setImgErr(null);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current.click();
  };

  const removeImage = () => {
    setSelectedImage(null);
    setFieldValue("image", null); // Remove Formik field value
  };

  return (
    <div className="flex flex-row mt-5">
      {profileData || selectedImage ? (
        <div
          style={{
            width: "16%",
          }}
        >
          <img
            src={selectedImage !== null ? selectedImage : profileData}
            style={{
              borderRadius: "25px",
              width: "50px",
              height: "50px",
            }}
            alt="Profile"
          />
        </div>
      ) : (
        <div
          style={{
            width: "50px",
            height: "50px",
            borderRadius: "25px",
            backgroundColor: "#D9D9D9",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <ProfileIcon style={{ width: "24px", height: "24px" }} />
        </div>
      )}
      <div
        style={{
          width: "75%",
          padding: "12px",
          position: "relative",
        }}
      >
        <input
          ref={fileInputRef}
          style={{
            display: "none",
          }}
          type="file"
          name="image"
          accept=".jpg, .jpeg, .png, .bmp, .gif"
          onChange={handleImageChange}
        />
        <div className="flex gap-2">
          <div
            style={{
              border: `1px solid ${theme.borderColor.errorBorder}`,
              color: theme.borderColor.errorBorder,
            }}
            className="cursor-pointer rounded-[3px] w-14 h-6 text-xs items-center"
            onClick={handleButtonClick}
          >
            <span className="flex justify-center items-center mt-0.5 font-semibold">
              {profileData !== null || selectedImage ? "Edit" : "Upload"}
            </span>
          </div>
          {profileData !== null || selectedImage ? (
            <div
              style={{
                border: `1px solid ${theme.borderColor.errorBorder}`,
                color: theme.borderColor.errorBorder,
              }}
              className="cursor-pointer rounded-[3px] w-14 h-6 text-xs items-center"
              onClick={removeImage}
            >
              <span className="flex justify-center items-center mt-0.5 font-semibold">
                {"Remove"}
              </span>
            </div>
          ) : null}
        </div>

        <p
          style={{
            color: theme.textColor.tabColor,
            fontSize: "10px",
            fontWeight: "300",
            fontStyle: "italic",
            paddingTop: "2px",
          }}
        >
          Jpeg/png/bmp/gif only . 3MB max
        </p>
        <p className="text-errorColor text-xs">
          {imgErr ? imgErr : <ErrorMessage name="image" />}
        </p>
      </div>
    </div>
  );
};

export default FileUpload;
