import React, { useState, useRef, useEffect, memo, useContext } from "react";
import ExpandMore from "@mui/icons-material/ExpandMore";
import ExpandLess from "@mui/icons-material/ExpandLess";
import ClickAwayListener from "@mui/material/ClickAwayListener";
import { CircularProgress } from "@mui/material";
import { VariableSizeList as List, areEqual } from "react-window";
import { CloseIcon, EditIcon, Search } from "../../../icons";
import theme from "../../../tailwind-theme";
import { useQuery } from "react-query";
import { getMultiSelectListData } from "../../../lib/list-api";
import { useLocation, useNavigate } from "react-router-dom";
import {
  getActionAndModuleNameFromURL,
  getPathIdFromUrl,
  getTrimmedUrl,
} from "../../../common/urlUtils";
import { MAX_PRIORITY } from "../../../common/config";
import { useFormikContext } from "formik";
import {
  getIndexedDBDataById,
  updateIndexedDBDataById,
} from "../../HelperFunction/localStorage";
import { DataContext } from "../../../context/DataContext";
import { moduleConfiguration } from "../../../common/constants";

const Dropdown = ({
  btnName,
  btnWidth,
  btnHeight,
  width,
  data,
  isLoading,
  reset,
  onSelectionChange,
  isMulti = true,
  disabled,
  defaultSelectedData,
  commonValues,
  isSearch = true,
  optionDataList,
  apiOptions,
  collectionName,
  setMultiSelectValue,
  values,
  moduleName,
  defaultData,
  onBlur,
  header,
  setCurrentStep,
  placeholder,
  action,
  requiredInfoDetails,
  pathDetail,
}) => {
  const [open, setOpen] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [searchText, setSearchText] = useState("");
  const [inputValue, setInputValue] = useState("");
  const [hoveredIndex, setHoveredIndex] = useState(null);
  const buttonRef = useRef(null);
  const dropDownRef = useRef(null);
  const [optionsData, setOptionsData] = useState([]);
  const [httpAccounts, setHttpAccounts] = useState([]);
  const [smppAccounts, setSmppAccounts] = useState([]);
  const [placeholderText, setPlaceholderText] = useState("");
  const navigate = useNavigate();
  const location = useLocation();
  const url = getTrimmedUrl();
  const hasEffectRun = useRef(false);
  const roamingValue = values?.clusterType;
  const { touched, errors } = useFormikContext();
  const [oldRedirectionType, setOldRedirectionType] = useState(
    values?.httpListType
  );
  const { setConnType } = useContext(DataContext);

  const searchParams = new URLSearchParams(location.search);
  const currentId = searchParams.get("currentId");
  const [isLoadingProcess, setIsLoading] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState("");
  const [maxListHeight, setMaxListHeight] = useState(150);

  useEffect(() => {
    if (open) {
      const buttonRect = buttonRef.current?.getBoundingClientRect();
      const spaceBelow = Math.round(window.innerHeight - buttonRect.bottom);
      const spaceAbove = buttonRect.top;

      if (spaceBelow < 150 && spaceAbove >= spaceBelow) {
        setMaxListHeight(150);
        // setMaxListHeight(Math.round(spaceBelow) - 100);
        setDropdownPosition("top");
      } else {
        setDropdownPosition("bottom");
        setMaxListHeight(Math.round(spaceBelow) - 100);
      }
    }
  }, [open, selectedOptions]);

  useEffect(() => {
    if (selectedOptions.length > 0) {
      setIsLoading(true);
      const timeout = setTimeout(() => {
        setIsLoading(false);
      }, 500);
      return () => clearTimeout(timeout);
    } else {
      setIsLoading(false);
    }
  }, []);

  const clusterId = getPathIdFromUrl(window.location.href);
  const handleEditButtonClick = async (e, currentOption) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentStep(0);
    setConnType(currentOption?.connType);
    const nextPath =
      collectionName === moduleConfiguration.redirectionalAccModuleName
        ? `/app/list/${collectionName}/edit/${currentOption?.id}/${currentOption?.connType}`
        : `/app/list/${collectionName}/edit/${currentOption?.id}`;
    const currentPath = url;
    const formData = values;
    const moduleNameValue = moduleName;
    const id = parseInt(currentId, 10);
    const previousModuleName = action
      ? `${action === "add" ? "Add" : "Edit"} customer/supplier`
      : getActionAndModuleNameFromURL(window.location.href);
    const listHeader = header;
    const pathId = pathDetail ?? "";
    try {
      const existingData = await getIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        id
      );

      const updatedData = existingData || { data: [] };

      const existingIndex = updatedData.data.findIndex(
        (entry) => entry.moduleNameValue === moduleNameValue
      );

      if (existingIndex !== -1) {
        updatedData.data.splice(existingIndex, 1);
      }

      const newEntry = {
        currentPath,
        formData,
        previousPath:
          updatedData.data[updatedData.data.length - 1]?.currentPath || null,
        moduleNameValue,
        previousModuleName,
        requiredInfoDetails: requiredInfoDetails ? requiredInfoDetails : null,
        pathId,
      };

      if (updatedData.data.length === 0) {
        newEntry.listHeader = listHeader;
      }

      updatedData.data.push(newEntry);

      await updateIndexedDBDataById(
        "navigationDetails",
        "FormDB",
        id,
        updatedData
      );
      navigate(`${nextPath}?currentId=${id}`, {
        state: { id: currentOption.id },
      });
    } catch (error) {
      console.error("Error updating IndexedDB:", error);
    }
  };

  useQuery(
    [
      "collectionName" + collectionName,
      collectionName,
      { limit: -1 },
      roamingValue,
      clusterId,
      oldRedirectionType,
    ],
    getMultiSelectListData,
    {
      enabled: apiOptions,
      onSuccess: ({ data }) => {
        if (collectionName === moduleConfiguration.pointCodeModuleName) {
          const optionsData = data?.data?.map((list) => ({
            value: list?.attributes?.pointCode,
            label: list?.attributes?.pointCode,
            id: list?.id,
          }));
          setOptionsData(optionsData);
        } else if (
          collectionName === moduleConfiguration.redirectionalAccModuleName
        ) {
          const optionData = data?.data?.map((list) => ({
            value: list?.attributes?.smscName,
            label: list?.attributes?.smscName,
            id: list?.id,
            connType: list?.attributes?.connType,
          }));

          setHttpAccounts(optionData);
          setSmppAccounts(optionData);
          setPlaceholderText("redirectional account");
        } else if (collectionName === moduleConfiguration.operators) {
          const optionsData = data?.data?.map((list) => ({
            value: `${list?.attributes?.operatorName}--${list?.attributes?.mccMnc}`,
            label: `${list?.attributes?.operatorName}--${list?.attributes?.mccMnc}`,
            id: list?.id,
          }));
          setOptionsData(optionsData);
          setPlaceholderText("operator");
        } else if (collectionName === moduleConfiguration.customerSupplier) {
          const optionsData = data?.data?.map((list) => ({
            value: list?.attributes?.name,
            label: list?.attributes?.name,
            id: list?.id,
          }));
          setOptionsData(optionsData);
          setPlaceholderText("name");
        }
      },
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    if (Number(values?.httpListType) === 1 && moduleName !== "paths") {
      setOptionsData(httpAccounts);
      setMultiSelectValue(httpAccounts);
    } else if (Number(values?.httpListType) === 0 && moduleName !== "paths") {
      setOptionsData(smppAccounts);
      setMultiSelectValue(smppAccounts);
    } else if (
      values?.redirectionListType === "P" ||
      moduleName === "point-code-lists"
    ) {
      setMultiSelectValue(optionsData);
    } else if (
      (values?.interfaceType === 2 || values?.interfaceType === 3) &&
      moduleName === "paths"
    ) {
      setOptionsData(smppAccounts);
      setMultiSelectValue(smppAccounts);
    } else if (values?.interfaceType === 4 && moduleName === "paths") {
      setOptionsData(httpAccounts);
      setMultiSelectValue(httpAccounts);
    }
  }, [httpAccounts, smppAccounts, optionsData, values]);

  useEffect(() => {
    if (!apiOptions) {
      setOptionsData(data);
    }
  }, [apiOptions, data, values]);

  const itemHeight = 30;
  // const maxListHeight = 150;

  const handleOpen = (e) => {
    e.preventDefault();
    setOpen(!open);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    if (commonValues) {
      const valuesArray = Array.isArray(commonValues)
        ? commonValues
        : commonValues.split(",");
      setSelectedOptions(valuesArray);
      setInputValue(valuesArray.join(", "));
    } else {
      setSelectedOptions([]);
      setInputValue("");
    }
  }, [commonValues]);

  const handleCheckboxChange = (option, filterType) => {
    setSelectedOptions((prevSelectedOptions) => {
      let updatedOptions;
      if (
        values?.redirectionType === 2 &&
        prevSelectedOptions.length >= MAX_PRIORITY
      ) {
        return prevSelectedOptions;
      }
      if (!isMulti) {
        updatedOptions = [{ value: option.value, id: option.id }];
      } else {
        if (optionDataList) {
          updatedOptions = prevSelectedOptions.some(
            (selectedOption) => selectedOption.value === option.value
          )
            ? prevSelectedOptions.filter(
                (selectedOption) => selectedOption.value !== option.value
              )
            : prevSelectedOptions.length >= optionDataList
            ? [
                ...prevSelectedOptions.slice(1),
                { value: option.value, id: option.id },
              ]
            : [...prevSelectedOptions, { value: option.value, id: option.id }];
        } else {
          updatedOptions = prevSelectedOptions.some(
            (selectedOption) => selectedOption.value === option.value
          )
            ? prevSelectedOptions.filter(
                (selectedOption) => selectedOption.value !== option.value
              )
            : [...prevSelectedOptions, { value: option.value, id: option.id }];
        }
      }
      return updatedOptions;
    });
  };
  const handleSearchChange = (event) => {
    setSearchText(event.target.value);
  };

  useEffect(() => {
    setOldRedirectionType((prev) => {
      if (prev !== values?.httpListType) {
        setSelectedOptions([]);
        return values?.httpListType;
      }
      return prev;
    });
    setSearchText("");
  }, [reset, values?.httpListType]);

  useEffect(() => {
    onSelectionChange(selectedOptions);
    setInputValue(selectedOptions.map((option) => option.value).join(", "));
  }, [selectedOptions]);

  useEffect(() => {
    if (defaultData) {
      setSelectedOptions(defaultData);
    }
  }, []);

  const filteredOptions =
    optionsData?.length > 0
      ? optionsData.filter((option) =>
          String(option.label).toLowerCase().includes(searchText.toLowerCase())
        )
      : [];

  useEffect(() => {
    if (hasEffectRun.current) return;

    if (defaultSelectedData?.length > 0 && optionsData?.length > 0) {
      const selectedOptions = defaultSelectedData
        .map((selectedId) => {
          const option = optionsData.find(
            (option) => option.id === selectedId || option.id === selectedId.id
          );

          if (option) {
            // If option is found, return its properties
            return {
              value: option.value,
              id: option.id,
              label: option.label,
            };
          } else {
            // Option not found, return a fallback or skip
            return null;
          }
        })
        .filter(Boolean);

      setSelectedOptions(selectedOptions);

      hasEffectRun.current = true;
    }
  }, [optionsData, defaultSelectedData]);

  const removeSelectedOption = (e, optionValue) => {
    e.preventDefault();
    e.stopPropagation();
    setSelectedOptions((prevSelectedOptions) => {
      const updatedOptions = prevSelectedOptions.filter(
        (selectedOption) => selectedOption.value !== optionValue
      );
      if (updatedOptions.some((option) => option.value === "Select All")) {
        return updatedOptions.filter(
          (selectedOption) => selectedOption.value !== "Select All"
        );
      }

      // setFieldValue(
      //   name,
      //   updatedOptions.map((option) => option.value)
      // );
      return updatedOptions;
    });
  };
  let combinedOptions;
  if (
    (selectedOptions && optionsData !== undefined) ||
    optionsData !== null ||
    optionsData !== ""
  ) {
    combinedOptions = [
      ...(selectedOptions || [])
        .map((selectedOption) =>
          optionsData?.find((option) => option?.value === selectedOption?.value)
        )
        .filter(Boolean),
      ...filteredOptions.filter((option) => {
        return (
          option &&
          option?.value &&
          option?.label &&
          !(selectedOptions || []).some(
            (selectedOption) => selectedOption?.value === option?.value
          )
        );
      }),
    ];
  }
  const selectAllIndex = combinedOptions.findIndex(
    (option) => option.value === "Select All"
  );

  if (selectAllIndex !== -1) {
    const selectAllOption = combinedOptions.splice(selectAllIndex, 1)[0];
    combinedOptions = [selectAllOption, ...combinedOptions];
  }

  const Row = memo(({ index, style, combinedOptions }) => {
    const currentOption = combinedOptions?.[index];
    const isHovered = hoveredIndex === index;
    const rowStyle = {
      ...style,
      backgroundColor: isHovered
        ? theme.backgroundColor.bgField
        : "transparent",
    };

    const currentOptionLabel =
      currentOption.label.length > 30
        ? `${currentOption.label.substring(0, 30)}...`
        : currentOption.label;

    return (
      <div
        key={index}
        style={rowStyle}
        className={`mb-4 ${isHovered ? "relative" : ""}`}
        onMouseEnter={() => setHoveredIndex(index)}
        onMouseLeave={() => setHoveredIndex(null)}
      >
        <label
          className="flex justify-between items-center gap-2 text-xs"
          title={currentOption?.label}
        >
          <div className="text-xs">{currentOptionLabel}</div>
          <div className="flex-grow flex justify-end mr-3 gap-3 mt-2">
            {isHovered && (
              <EditIcon
                onClick={(e) => {
                  handleEditButtonClick(e, currentOption);
                }}
                className="h-4 w-4 ml-2 cursor-pointer"
              />
            )}
            <input
              type="checkbox"
              checked={selectedOptions.some(
                (selectedOption) =>
                  selectedOption.value === currentOption?.value
              )}
              onChange={() => handleCheckboxChange(currentOption)}
              className="min-w-[20px] w-4 h-4 "
              style={{
                accentColor: `${theme?.backgroundColor.bgCheckboxSelection}`,
              }}
            />
          </div>
        </label>
      </div>
    );
  }, areEqual);

  return (
    <ClickAwayListener onClickAway={handleClose}>
      <div className="relative">
        <button
          ref={buttonRef}
          onClick={handleOpen}
          onBlur={onBlur}
          style={{
            border: `1px solid ${
              (errors.point_codes && touched.point_codes) ||
              (Object.keys(errors).length > 0 &&
                Object.keys(touched).length > 0)
                ? theme.borderColor.errorBorder
                : theme.borderColor.tableBorder
            }`,
            backgroundColor: theme.backgroundColor.bgField,
            color: theme.textColor.textNAColor,
            width: "100%",
            display: "flex",
            minHeight: "40px",
            alignItems: "center",
            borderRadius: theme.borderRadius.fieldRadius,
            minWidth: btnWidth ? btnWidth : "200px",
            height: btnHeight ? btnHeight : "100%",
          }}
          disabled={disabled}
        >
          <div className="max-h-40 overflow-y-auto w-full flex">
            <div className="flex w-11/12 flex-wrap">
              <div className="text-xs my-1 ml-1 flex flex-wrap">
                {selectedOptions.length > 0 ? (
                  isLoadingProcess ? (
                    <div className="flex justify-center ml-2">
                      Processing...
                    </div>
                  ) : (
                    selectedOptions.map((selectedValue, index) => {
                      let option = combinedOptions.find(
                        (option) => option.value === selectedValue.value
                      );
                      return option && option.label !== "Select All" ? (
                        <div
                          key={index}
                          className="flex text-black m-1 rounded-[3px] p-2 bg-bgSelectOption relative"
                          style={{
                            whiteSpace: "normal",
                            wordBreak: "break-word",
                            maxWidth: "100%",
                            overflowWrap: "break-word",
                            position: "relative",
                            paddingRight: "20px",
                          }}
                        >
                          <div>{option.label}</div>
                          <CloseIcon
                            onClick={(e) =>
                              removeSelectedOption(e, option.value)
                            }
                            className="absolute top-1 right-1 text-xs cursor-pointer"
                            style={{
                              position: "absolute",
                              top: "4px",
                              right: "4px",
                              fontSize: "16px",
                              width: "8px",
                              height: "8px",
                              cursor: "pointer",
                            }}
                          />
                        </div>
                      ) : null;
                    })
                  )
                ) : btnName ? (
                  btnName
                ) : (
                  <span className="text-gray-500 ml-2">{placeholder}</span>
                )}
              </div>
            </div>
            <div>
              {open ? (
                <ExpandLess className="ml-2 opacity-60" />
              ) : (
                <ExpandMore className="ml-2 opacity-60" />
              )}
            </div>
          </div>
        </button>

        {open && (
          <div
            ref={dropDownRef}
            className="absolute z-50 bg-bgPrimary shadow-md p-4 rounded w-full"
            style={{
              top: dropdownPosition === "top" ? "auto" : "100%",
              bottom: dropdownPosition === "top" ? "100%" : "auto",
            }}
          >
            {isSearch && (
              <div className="mb-4">
                <div className="flex items-center border border-tableBorder px-2 py-2 rounded-full w-full">
                  <Search className="mr-2 ml-1" />
                  <input
                    type="text"
                    placeholder={`Search ${placeholderText}`}
                    value={searchText}
                    onChange={handleSearchChange}
                    className="border-none outline-none w-full text-xs"
                  />
                </div>
              </div>
            )}
            {isLoading ? (
              <div className="flex justify-center">
                <CircularProgress />
              </div>
            ) : (
              <div className="w-full max-h-64 overflow-auto">
                {combinedOptions.length > 0 ? (
                  <List
                    width={width}
                    height={Math.min(
                      combinedOptions.length * itemHeight,
                      maxListHeight
                    )}
                    itemCount={combinedOptions.length}
                    itemData={combinedOptions}
                    itemSize={(index) =>
                      combinedOptions[index]?.label?.length > 150 ? 75 : 30
                    }
                  >
                    {({ index, style }) => (
                      <Row
                        index={index}
                        style={style}
                        combinedOptions={combinedOptions}
                      />
                    )}
                  </List>
                ) : (
                  <div className="p-2 text-center text-gray-500">
                    No options
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </ClickAwayListener>
  );
};

export default Dropdown;
