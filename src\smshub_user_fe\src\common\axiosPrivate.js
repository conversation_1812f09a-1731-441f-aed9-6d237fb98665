import axios from "axios";

import { memoizedRefreshToken } from "./refreshToken";
import getAPIMap from "../routes/ApiUrls";
axios.defaults.baseURL = process.env.REACT_APP_API_URL;

axios.interceptors.request.use(
  async (config) => {
    const session = JSON.parse(localStorage.getItem("session"));
    config.withCredentials = true;
    config.credentials = "include";
    if (session) {
      config.headers = {
        //...config.headers,
        Authorization: `Bearer ${session}`,
      };
    }

    return config;
  },
  (error) => Promise.reject(error)
);

axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    const config = error?.config;

    if (
      error?.response?.status === 401 &&
      !config?.sent &&
      !config.url.includes(getAPIMap("refreshToken"))
    ) {
      config.sent = true;

      const result = await memoizedRefreshToken();

      if (result?.token?.token) {
        config.headers = {
          ...config.headers,
          authorization: `Bearer ${result?.token?.token}`,
        };
      }
      return axios(config);
    }

    return Promise.reject(error);
  }
);

export const axiosPrivate = axios;
