import React, { useState, useEffect } from "react";
import Dialog from "@mui/material/Dialog";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";
import { CancelIcon } from "../../icons";
import { useQuery } from "react-query";
import { getDataById } from "../../lib/list-api";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from "@mui/material";

function StepperViewDialog({ show, onHide, viewDetails }) {
  const [formData, setFormData] = useState({});
  const [tabIndex, setTabIndex] = useState(0);

  const id = viewDetails?.row?.original?.id;
  const collectionName = viewDetails?.collectionName;
  const moduleName = viewDetails?.schemaDetails?.moduleName;

  useQuery([collectionName, id], getDataById, {
    enabled: id > 0,
    refetchOnWindowFocus: false,
    onSuccess: ({ data }) => {
      setFormData(data?.data?.attributes);
    },
  });

  const handleChange = (event, newValue) => {
    setTabIndex(newValue);
  };

  const renderTable = (data) => (
    <TableContainer component={Paper} sx={{ boxShadow: "none" }}>
      <Table
        sx={{ minWidth: 550, border: "1.5px solid #D9D9D9" }}
        aria-label="simple table"
      >
        <TableHead sx={{ bgcolor: "#D9D9D9", height: 20 }}>
          <TableRow>
            <TableCell>Comviva parameter name</TableCell>
            <TableCell>Third-party parameter name</TableCell>
            <TableCell>Default value</TableCell>
            <TableCell>Status</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row, index) => (
            <TableRow key={index}>
              <TableCell>{row.COMVIVA_PARAM}</TableCell>
              <TableCell>{row.THIRD_PARTY_PARAM}</TableCell>
              <TableCell>{row.DEFAULT_VAL}</TableCell>
              <TableCell>{row.ACTIVE ? "Active" : "Inactive"}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 700,
          maxHeight: 400,
          borderRadius: "10px",
        },
      }}
      maxWidth="md"
      open={show}
      onClose={onHide}
    >
      <div className="bg-bgBox w-full h-14 justify-center flex items-center p-4">
        <div className="text-white">{`View ${moduleName}`}</div>
      </div>
      <div
        className="flex justify-end absolute right-0 top-0"
        style={{ cursor: "pointer" }}
        onClick={onHide}
      >
        <CancelIcon className="flex items-center mx-3 my-3 " />
      </div>

      <Tabs
        value={tabIndex}
        onChange={handleChange}
        sx={{
          "& .MuiTabs-indicator": {
            backgroundColor: "#121313",
          },
        }}
      >
        <Tab
          label="Basic"
          sx={{
            textTransform: "none",
            paddingBottom: 0,
          }}
          className={`${tabIndex === 0 ? "text-black" : "text-tabColor"}  `}
        />
        <Tab
          label="Push Parameter"
          sx={{
            textTransform: "none",
            color: tabIndex === 1 ? "black" : "#9C9898",
            paddingBottom: 0,
          }}
          className={`${tabIndex === 1 ? "text-black" : "text-tabColor"}  `}
        />
        <Tab
          label="DLR Parameter"
          sx={{
            textTransform: "none",
            paddingBottom: 0,
          }}
          className={`${tabIndex === 2 ? "text-black" : "text-tabColor"}  `}
        />
      </Tabs>
      <div className="border-b border-borderIndicatorLine mx-4" />

      <Box sx={{ padding: 2 }}>
        {tabIndex === 0 && (
          <div className="flex justify-center items-center my-7 gap-20">
            <div className="text-center">
              <div className="text-textNAColor text-sm font-normal">
                Template name
              </div>
              <div className="text-black text-sm font-normal mt-2">
                {formData.templateName}
              </div>
              <div className="border-b border-viewBorder mt-2 w-5 mx-auto" />
            </div>
            <div className="text-center">
              <div className="text-textNAColor text-sm font-normal">
                Relation type
              </div>
              <div className="text-black text-sm font-normal mt-2">
                {formData.relationType}
              </div>
              <div className="border-b border-viewBorder mt-2 w-5 mx-auto" />
            </div>
          </div>
        )}
        {tabIndex === 1 && renderTable(formData?.templateParams?.PUSH || [])}
        {tabIndex === 2 && renderTable(formData?.templateParams?.DLR || [])}
      </Box>
    </Dialog>
  );
}

export default StepperViewDialog;
