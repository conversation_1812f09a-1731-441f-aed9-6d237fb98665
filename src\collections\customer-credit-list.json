{"header": "List of customer credit profile", "columns": [{"header": "Profile id", "accessorKey": "id", "filterVariant": "range"}, {"header": "Customer name", "idToName": true, "moduleName": "customer-supplier-management", "accessorKey": "attributes.customerName", "attributeKey": "customerId"}, {"header": "Credit profile id", "accessorKey": "attributes.customerId"}, {"header": "Status", "accessorKey": "attributes.activation"}, {"buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"info": "Choose the name of the customer/supplier for whom you need to define the credit limit", "name": "customerId", "size": 6, "type": "select", "title": "Customer name", "isInfo": true, "isMandatory": true, "nonEditable": true, "onClickPath": "customer-supplier-managements", "isUnique": true, "filter": {"operatorType": ["C", "C,S"]}, "validations": [{"type": "required", "message": "Please select a customer"}], "defaultValue": "", "isDropDownApi": true, "validationType": "number"}, {"info": "Enter the amount of credit limit you are allocating to the customer/supplier", "name": "creditLimit", "size": 6, "type": "text", "title": "Credit limit", "isInfo": true, "isMandatory": true, "validations": [{"type": "min", "value": 1, "message": "Credit Limit cannot be zero"}, {"type": "max", "value": 5000, "message": "Credit Limit cannot be more than 5000"}, {"type": "required", "message": "Please enter credit limit for the customer"}], "defaultValue": "", "validationType": "number"}, {"info": "Select whether it is a prepaid or a post paid customer", "name": "customerType", "size": 6, "type": "select", "title": "Customer type", "isInfo": true, "options": [{"label": "Prepaid", "value": 1}, {"label": "Postpaid", "value": 2}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select a customer type for the customer"}], "defaultValue": "", "validationType": "string"}, {"info": "Choose weekly, fortnightly, monthly, or custom where you can define your own billing date for the billing cycle", "name": "billingCycle", "size": 6, "type": "select", "title": "Billing cycle", "isInfo": true, "options": [{"label": "Weekly(7 days)", "value": 1}, {"label": "Fortnight(14 days)", "value": 2}, {"label": "Monthly(30 days)", "value": 3}, {"label": "Custom", "value": 4}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select a billing cycle for the customer"}], "defaultValue": "", "validationType": "string"}, {"info": "Start date of billing cycle", "name": "billingCycleBeginDate", "size": 6, "type": "date", "title": "Billing cycle begin date", "minDate": true, "isInfo": true, "isMandatory": true, "validations": [{"type": "required", "message": "Please select a billing cycle begin date for the customer"}], "defaultValue": ""}, {"name": "billingCycleEndDate", "size": 6, "type": "date", "title": "Billing cycle end date", "dynamic": {"field": "billingCycle", "value": 4}, "dynamicEndDate": {"field": "billingCycleBeginDate"}, "defaultValue": "", "validations": [{"type": "required", "message": "Please select the billing cycle end date"}, {"type": "greaterThan", "field": "billingCycleBeginDate", "message": "Billing cycle end date cannot be before the start date of billing cycle"}]}, {"info": "Select whether you want to set the threshold based on percentage of credit usage or number of remaining days", "name": "thresholdType", "size": 6, "type": "select", "title": "Threshold type", "isInfo": true, "options": [{"label": "Percentage usage based", "value": 1}, {"label": "Remaining usage days", "value": 2}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select threshold type for the customer"}], "defaultValue": "", "validationType": "string"}, {"title": "Percentage usage based", "name": "threshold", "size": 12, "type": "customerCreditTable", "dynamic": {"field": "thresholdType", "value": [1, 2], "dynamicValue": 2, "dynamicTitle": "Remaining usage days"}, "validations": [{"type": "threshHoldValidation", "message": "Only numbers allowed"}], "fieldsData": [{"name": "thresholdLevel", "type": "static"}, {"name": "thresholdValue", "type": "text", "validations": [{"type": "required", "message": "required"}]}, {"name": "thresholdMessage", "type": "text", "validations": [{"type": "required", "message": "required"}]}], "validation": true, "headersData": ["Threshold Level", "Value", "Message"], "initialValues": {"threshold": [{"thresholdLevel": 1, "thresholdValue": "", "thresholdMessage": ""}, {"thresholdLevel": 2, "thresholdValue": "", "thresholdMessage": ""}, {"thresholdLevel": 3, "thresholdValue": "", "thresholdMessage": ""}, {"thresholdLevel": 4, "thresholdValue": "", "thresholdMessage": ""}]}, "validationType": "array"}, {"info": "Currency type", "name": "currency", "size": 6, "type": "select", "title": "<PERSON><PERSON><PERSON><PERSON>", "isInfo": true, "options": [{"label": "EURO", "value": 1}, {"label": "USD", "value": 2}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select currency for the customer"}], "defaultValue": "", "validationType": "string"}, {"name": "activation", "size": 6, "type": "radio", "title": "Activation", "options": [{"label": "Auto activate", "value": 1}, {"label": "Auto deactivate", "value": 0}, {"label": "Both", "value": 2}], "isMandatory": true, "defaultValue": 1}], "formType": "simple", "buttonName": "+ Add new customer credit profile", "moduleData": "customer-credit-profile", "moduleName": "Customer credit profile", "globalSearch": [{"label": "Profile id", "value": "id"}, {"label": "Customer name", "value": "customerName"}], "dropdownOptions": {"field": "activation", "options": [{"label": "ALL", "query": "", "value": "ALL"}, {"label": "Auto activate", "query": "filters[activation][$eq]=1", "value": 1}, {"label": "Auto deactivate", "query": "filters[activation][$eq]=0", "value": 0}, {"label": "Both", "query": "filters[activation][$eq]=2", "value": 2}], "defaultQuery": "", "defaultValue": "ALL"}}