import React, { useState, useRef } from "react";
import { Container, Tooltip, styled } from "@mui/material";
import { tooltipClasses } from "@mui/material/Tooltip";
import "tailwindcss/tailwind.css";
import worldMap from "../assets/img/world.png";
import { useQuery } from "react-query";
import { getContactUsData } from "../lib/list-api";

const points = [
  { id: 1, x: "48%", y: "35%" },
  { id: 2, x: "61%", y: "50%" },
  { id: 3, x: "56%", y: "77%" },
  { id: 4, x: "69%", y: "50%" },
  { id: 5, x: "28%", y: "45%" },
  { id: 6, x: "76%", y: "62%" },
  { id: 7, x: "84%", y: "80%" },
  { id: 8, x: "70%", y: "60%" },
  { id: 9, x: "73%", y: "51%" },
  { id: 10, x: "31%", y: "85%" },
  { id: 11, x: "28%", y: "62%" },
  { id: 12, x: "49%", y: "36%" },
  { id: 13, x: "47%", y: "60%" },
  { id: 14, x: "59%", y: "63%" },
  { id: 15, x: "69%", y: "57%" },
];

const CustomTooltip = styled(({ className, ...props }) => (
  <Tooltip
    {...props}
    classes={{ popper: className }}
    placement="top-start"
    PopperProps={{
      modifiers: [
        {
          name: "offset",
          options: {
            offset: [6, -15],
          },
        },
      ],
    }}
  />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#374150",
    color: "white",
    maxWidth: 296,
    padding: "10px",
    whiteSpace: "pre-line",
    borderRadius: "15px 15px 15px 0px",
    fontFamily: "OpenSanHebrew",
    zIndex: 10000,
  },
}));

const ContactUs = () => {
  const [hoveredPointId, setHoveredPointId] = useState(null);
  const leaveTimeoutRef = useRef(null);

  const { data, error, isLoading } = useQuery(
    "contactUsData",
    getContactUsData
  );

  if (isLoading) {
    return <div>Loading...</div>;
  }

  const addresses =
    data?.data?.data[0]?.attributes?.schemadetails?.address || {};

  const details = data?.data?.data[0]?.attributes?.schemadetails?.details || {};

  const formatAddress = (text) => {
    const parts = text.split("\n");
    return parts
      .map((part, index) => {
        if (index === 0 || (parts[index - 1] === "" && part.trim() !== "")) {
          return `<b>${part}</b><br/>`;
        }
        return `<span style="font-size:10px">${part}</span><br/>`;
      })
      .join("");
  };

  const handleMouseEnter = (id) => {
    if (leaveTimeoutRef.current) {
      clearTimeout(leaveTimeoutRef.current);
      leaveTimeoutRef.current = null;
    }
    setHoveredPointId(id);
  };

  const handleMouseLeave = () => {
    leaveTimeoutRef.current = setTimeout(() => {
      setHoveredPointId(null);
    }, 10);
  };

  return (
    <Container className="overflow-visible">
      <h4 className="mb-4 text-title font-bold text-center md:text-left sticky top-0 bg-bgBody h-[60px] z-10 content-center mt-0">
        {details.title}
      </h4>
      <div className="text-center flex flex-col md:flex-row justify-between mb-4 mt-10">
        <div className="mb-4 md:mb-0 md:w-1/2">
          <div className="text-xl font-bold">{details.leftTitle}</div>
          <div className="text-subtitle">{details.leftHeader}</div>
          <div className="text-subtitle text-locationColor" color="primary">
            {details.leftSubTitle}
          </div>
        </div>
        <div className="md:w-1/2">
          <div className="text-xl font-bold">{details.rightTitle}</div>
          <div className="text-subtitle">{details.rightHeader}</div>
          <div className="text-subtitle text-locationColor" color="primary">
            {details.rightSubTitle}
          </div>
        </div>
      </div>
      <div className="relative w-full h-80 md:h-full bg-cover bg-center mt-10">
        <img
          src={worldMap}
          alt="World Map"
          className="w-full h-full object-contain"
        />
        {points.map((point) => (
          <CustomTooltip
            key={point.id}
            title={
              <div
                className="text-xs"
                dangerouslySetInnerHTML={{
                  __html: formatAddress(addresses[`id${point.id}`]),
                }}
              />
            }
            open={hoveredPointId === point.id}
          >
            <div
              className="absolute w-2 h-2 bg-white border-location border-locationBorder rounded-full cursor-pointer transition-colors hover:border-location"
              style={{
                left: point.x,
                top: point.y,
              }}
              onMouseEnter={() => handleMouseEnter(point.id)}
              onMouseLeave={handleMouseLeave}
            />
          </CustomTooltip>
        ))}
      </div>
    </Container>
  );
};

export default ContactUs;
