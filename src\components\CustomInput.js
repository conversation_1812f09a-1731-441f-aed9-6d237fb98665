import React from "react";
import { useJsonForms } from "@jsonforms/react";
import TextField from "@mui/material/TextField";
import { withJsonFormsControlProps } from "@jsonforms/react";

const CustomInput = ({ id, label, scopedSchema, value, onChange }) => {
  const handleChange = (event) => onChange(id, event.target.value);

  return (
    <TextField
      label={label}
      variant="outlined"
      fullWidth
      value={value === undefined ? "" : value}
      onChange={handleChange}
      sx={{ borderColor: "red" }} // Using sx prop for styling
    />
  );
};

export default withJsonFormsControlProps(CustomInput);
