import Drawer from "@mui/material/Drawer";
import * as Icons from "../../icons";
import { SidebarContext } from "../../context/SidebarContext";

import routes from "../../routes/sidebar";
import { NavLink } from "react-router-dom";
import { useContext } from "react";
import { useTranslation } from "react-i18next";
import theme from "../../tailwind-theme";
import { Box } from "@mui/system";

function Icon({ icon, ...props }) {
  //const Icon = Icons[icon];
  return <Icon {...props} />;
}
export default function TemporaryDrawer() {
  const { toggleSidebar, isSidebarOpen } = useContext(SidebarContext);

  const { t } = useTranslation();
  // const [routes, setRoutes] = useState(menu);

  // const { isLoading: isMenuLoading } = useQuery(["sidebarMenu"], sidebarMenu, {
  //   onSuccess: (resp) => {
  //     setRoutes(resp?.data?.data);
  //   },
  // });
  function Icon({ icon, ...props }) {
    const Icon = Icons[icon];
    return <Icon {...props} />;
  }
  return (
    <Box
      role="presentation"
      onClick={toggleSidebar}
      onKeyDown={toggleSidebar}
      className="mt-5"
    >
      <Drawer
        anchor={"left"}
        open={isSidebarOpen}
        onClose={toggleSidebar}
        className="bg-bgBody "
      >
        <ul className="bg-bgBody h-full text-xs w-[250px] pt-10">
          {routes.map((x, i) => (
            <li key={i} className="py-3 px-2">
              <NavLink
                className={
                  "inline-flex items-center rounded-lg w-full text-sm text-black pl-7 p-3 transition-colors duration-150"
                }
                to={x.path}
                style={({ isActive }) =>
                  isActive
                    ? {
                        backgroundColor: theme.backgroundColor.bgLeftNavHover,
                        fontWeight: "600",
                        boxShadow: "0px 1px 1px #D3D3D3",
                      }
                    : { backgroundColor: "transparent" }
                }
              >
                <div className="flex flex-row">
                  <div className="">
                    <Icon
                      className="w-5 h-4"
                      aria-hidden="true"
                      icon={x.icon ? x.icon : ""}
                    />
                  </div>
                  <div>{x.name}</div>
                </div>
              </NavLink>
            </li>
          ))}
        </ul>
      </Drawer>
    </Box>
  );
}
