import Button from "@mui/material/Button";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import { useState } from "react";
//import { statusFilterOptions } from "../common/constants";
import { MagnifierIcon } from "../icons";
const StatusFilter = (props) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const [selectedOption, setSelectedOption] = useState(0);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenuClick = (e, i) => {
    console.log("e", e);
    setSelectedOption(i);
    props.setStatus(props.statusFilterOptions[i]);
    setAnchorEl(null);
    props.setPageNumber(1);
  };
  return (
    <div className=" h-10 bg-bgTableFilter border border-tableFilter rounded-lg">
      <Button
        className="text-xs font-bold w-full md:w-[147px] h-10 grid grid-cols-3"
        onClick={handleClick}
        sx={{
          fontFamily: "Poppins",
          fontSize: "12px",
          textTransform: "capitalize",
          color: "#111111",
          padding: 0,
        }}
      >
        <MagnifierIcon className="col-span-1"></MagnifierIcon>
        {props.statusFilterOptions[selectedOption]}
      </Button>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
      >
        {props.statusFilterOptions.map((x, i) => {
          return (
            <MenuItem
              onClick={(e) => handleMenuClick(e, i)}
              className="text-xs font-bold"
              key={i}
              sx={{ fontFamily: "Poppins", justifyContent: "center" }}
            >
              {x}
            </MenuItem>
          );
        })}
      </Menu>
    </div>
  );
};
export default StatusFilter;
