import React, { useContext, useState, useEffect, useRef } from "react";
import { Form, Formik } from "formik";
import Button from "../Buttons/Button";
import CancelButton from "../Buttons/OutlinedButton";
import { multiStepFormUserRoleContext } from "../../context/UserRoleContext";
import CustomTab from "../FormsUI/CustomTab";
import CommonTable from "../FormsUI/Table/RolesTable";
import RoleColumns from "./RoleColumns";

function RoleReportingForm(props) {
  const [selectedTab, setSelectedTab] = useState(0);
  const {
    handleNextClick,
    setFormData,
    formData,
    setCurrentStep,
    currentStep,
  } = useContext(multiStepFormUserRoleContext);

  const [selectAll, setSelectAll] = useState(false);
  const [counts, setCounts] = useState({
    panelCount: formData?.panelCount ?? 0,
    dashboardCount: formData?.dashboardCount ?? 0,
  });

  const formValuesRef = useRef({});

  const dataFields =
    props?.props?.data?.forms?.[props.currentStep]?.tabs?.[selectedTab]
      ?.elements || [];

  const tabItems = props?.props?.data?.forms?.[props.currentStep]?.tabs?.map(
    (tab) => tab.tabName
  );

  const selectedTabElements =
    props?.props?.data?.forms?.[props.currentStep]?.tabs?.[selectedTab];

  const tabsData = props?.props?.data?.forms?.[props.currentStep]?.tabs;

  const columns = (setFieldValue, values) =>
    RoleColumns({
      selectedTabElements,
      selectedTab,
      counts,
      setFieldValue,
      tabsData,
      handleCountChange: (type, action, maxLimit) => {
        setCounts((prevCounts) => {
          const newValue =
            action === "increase"
              ? Math.min(prevCounts[type] + 1, maxLimit)
              : Math.max(prevCounts[type] - 1, 0);
          return { ...prevCounts, [type]: newValue };
        });
      },
    });

  const formValues = props?.props?.formValues;
  useEffect(() => {
    if (formData && formValues) {
      const mergedData = { ...formData, ...formValues };
      setFormData(mergedData);
    }
  }, [formValues]);

  const handleSubmit = (values) => {
    // Store all previous tab values persistently
    formValuesRef.current = { ...formValuesRef.current, ...values };

    const staticReports = [];
    const dynamicReports = [];
    const resources = [];

    tabsData.forEach((tab) => {
      const tabData = (tab.elements || []).map((item) => ({
        name: item.resource,
        permissions: {
          view: formValuesRef.current[item.resource]?.view ? 1 : 0,
          update: formValuesRef.current[item.resource]?.update ? 1 : 0,
          delete: formValuesRef.current[item.resource]?.delete ? 1 : 0,
          create: formValuesRef.current[item.resource]?.create ? 1 : 0,
          download: formValuesRef.current[item.resource]?.download ? 1 : 0,
        },
      }));

      if (tab.tabName === "Static Reports") {
        staticReports.push(...tabData);
      } else if (tab.tabName === "Dynamic Dashboard") {
        dynamicReports.push(...tabData);
      } else {
        resources.push(...tabData);
      }
    });

    setFormData((prevData) => ({
      ...prevData,
      resources,
      staticReports,
      dynamicReports,
      dashboardCount: counts.dashboardCount,
      panelCount: counts.panelCount,
    }));

    handleNextClick();
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  useEffect(() => {
    setCounts({
      panelCount: formData?.panelCount ?? 0,
      dashboardCount: formData?.dashboardCount ?? 0,
    });
  }, [formData]);

  useEffect(() => {
    if (Object.keys(formValuesRef.current).length > 0) {
      // Update stored form values on tab change
      formValuesRef.current = { ...formValuesRef.current };
    }
  }, [selectedTab, formData, currentStep]);

  return (
    <Formik
      initialValues={{
        ...(formData?.resources?.reduce((acc, item) => {
          acc[item.name] = {
            view: item.permissions.view === 1,
            update: item.permissions.update === 1,
            delete: item.permissions.delete === 1,
            create: item.permissions.create === 1,
            download: item.permissions.download === 1,
          };
          return acc;
        }, {}) || {}),
        panelCount: formData?.panelCount ?? 0,
        dashboardCount: formData?.dashboardCount ?? 0,
      }}
      validateOnMount
      enableReinitialize
      onSubmit={handleSubmit}
    >
      {({ values, setFieldValue }) => (
        <Form>
          {/* Tabs */}
          <div className="md:mx-10">
            <CustomTab
              tabs={tabItems}
              defaultTab={selectedTab}
              onChange={(event, newValue) => {
                event.preventDefault();
                setSelectedTab(newValue);
              }}
              backgroundColor="bgField"
            />
          </div>

          {/* Table */}
          <div className="md:mx-10 md:my-10">
            <CommonTable
              columns={columns(setFieldValue, values)}
              data={dataFields}
            />
          </div>

          {/* Buttons */}
          <div className="flex justify-end items-end mt-10 mr-10 mb-5">
            <CancelButton
              label="< Back"
              buttonClassName="w-full md:w-[154px] h-[40px] text-base mb-3 rounded-[5px] ml-5"
              onClick={() => handleBack()}
            />
            <Button
              type="submit"
              label="Next >"
              value="submit"
              buttonClassName="w-full md:w-[154px] h-[40px] text-base mb-3 rounded-[5px] ml-5"
            />
          </div>
        </Form>
      )}
    </Formik>
  );
}

export default RoleReportingForm;
