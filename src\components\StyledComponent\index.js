import styled from "@emotion/styled";

import Tooltip, { tooltipClasses } from "@mui/material/Tooltip";

const CssTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#000",
    color: "#FFF",
    fontSize: 12,
    // border: "2px solid #7070708C",
    padding: "5px 15px",
    boxShadow: "0px 3px 6px #00000029",
    borderRadius: 10,
  },
  [`& .${tooltipClasses.arrow}`]: {
    color: "#000",
    "&::before": {
      backgroundColor: "#000",
      //boxShadow: "3px 3px 3px 3px #00000029",
      //border: "0.5px solid #7070708C",
    },
  },
}));

export { CssTooltip };
