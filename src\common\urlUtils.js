import { operatorType } from "./constants";

export const getTrimmedUrl = () => {
  const currentPageUrl = window.location.href;
  const index = currentPageUrl.indexOf("/app");
  return currentPageUrl.slice(index);
};

export const getModuleNameFromURL = (url) => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const parts = pathname.split("/");
    const listIndex = parts.indexOf("list");
    if (listIndex !== -1 && parts.length > listIndex + 1) {
      return parts[listIndex + 1];
    }
    return null;
  } catch (error) {
    console.error("Invalid URL:", error);
    return null;
  }
};

export const getActionAndModuleNameFromURL = (url) => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const parts = pathname.split("/");

    const listIndex = parts.indexOf("list");
    let moduleName =
      listIndex !== -1 && parts.length > listIndex + 1
        ? parts[listIndex + 1].replace(/-/g, " ")
        : null;
    if (moduleName && moduleName.endsWith("s")) {
      moduleName = moduleName.slice(0, -1);
    }
    if (moduleName) {
      if (moduleName === "group customer supplier") {
        moduleName = "Customer/supplier group";
      } else if (moduleName === "customer supplier management") {
        moduleName = "customer/supplier";
      } else {
        moduleName = moduleName
          .split(" ")
          .map((word) => {
            if (word.toLowerCase() === "esme") return "ESME";
            if (word.toLowerCase() === "http") return "HTTP";
            return word;
          })
          .join(" ");
      }
    }

    const action = parts.includes("add")
      ? "Add"
      : parts.includes("edit")
      ? "Edit"
      : null;

    if (moduleName && action) {
      return `${action} ${moduleName}`;
    }
    return null;
  } catch (error) {
    console.error("Invalid URL:", error);
    return null;
  }
};

export const getLastUrlSegment = (url) => {
  const segments = url.split("/").filter(Boolean);
  return segments.length > 0 ? segments[segments.length - 1] : null;
};

export const getLastNumberFromURL = (url) => {
  const match = url.match(/\/(\d+)(\/)?$/);
  return match ? parseInt(match[1], 10) : null;
};

export const getPathIdFromUrl = (url, position = -1) => {
  try {
    const pathSegments = new URL(url).pathname.split("/").filter(Boolean);
    return position < 0
      ? pathSegments[pathSegments.length + position]
      : pathSegments[position];
  } catch (error) {
    console.error("Invalid URL:", error);
    return null;
  }
};

export const getActionFromUrl = (url) => {
  if (url.includes("/edit/")) {
    return "edit";
  } else if (url.includes("/add")) {
    return "add";
  } else if (url.includes("/view")) {
    return "view";
  }
  return null;
};

export const getIdAfterEdit = (url) => {
  const actions = ["edit", "view", "viewData"];
  for (const action of actions) {
    const parts = url.split(`/${action}/`);
    if (parts.length > 1) {
      const id = parts[1].split(/[/?#]/)[0];
      return id;
    }
  }
  return null;
};

export const findCustomerSupplier = (type) => {
  const typeMapping = {
    [operatorType.Customer]: "Customer",
    [operatorType.Supplier]: "Supplier",
    [operatorType.Both]: "Customer & Supplier",
  };

  return typeMapping[type] || null;
};
