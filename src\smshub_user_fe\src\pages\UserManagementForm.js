import React from "react";
import UserManagement from "../../src/pages/UserManagement";
import { useNavigate, useLocation } from "react-router-dom";

function UserManagementForm() {
  const navigate = useNavigate();
  const location = useLocation();

  const action = location.pathname.split("/").filter(Boolean).pop();

  return (
    <>
      <div className="flex flex-col">
        <div className="sticky top-0 h-20 bg-bgBody z-10 ml-5 font-bold text-subtitle font-openSanHebrew">
          <div className="flex gap-2 font-medium my-7">
            <>
              <div
                className="text-textNAColor text-base cursor-pointer font-"
                onClick={() => navigate("/app/list/user-managements")}
              >
                {"List of Users >"}
              </div>
              <div className="text-black font-bold">
                {action === "add" ? "Add user" : "Edit user"}
              </div>
            </>
          </div>
        </div>
        <div className="bg-white mx-5">
          <UserManagement action={action} />
        </div>
      </div>
    </>
  );
}

export default UserManagementForm;
