import React from "react";
import Dialog from "@mui/material/Dialog";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import Button from "../Buttons/Button";
import { CloseIcon, DeleteAllIcon } from "../../icons";
import theme from "../../tailwind-theme";
import { IconButton } from "@mui/material";
import { trimName } from "../../common/trimNameUtils";
import { moduleConfiguration } from "../../common/constants";

function DeleteAllDialog({ show, onHide, message, moduleName }) {
  function parseMessage(message) {
    const data = [];
    const entries = message.split("&&"); // Splitting based on '&&' to separate different customers

    entries.forEach((entry) => {
      const nameMatch = entry.match(/'([^']+)'/); // Extracting customer name

      if (nameMatch) {
        let name = nameMatch[1];
        if (name.length > 15) {
          name = trimName(name);
        }

        const linkedModules = [];
        const relatedIds = [];

        // Extract all mapped entities dynamically
        const mappings = entry.match(/mapped with ([^:]+): ([^;]+)/g);
        if (mappings) {
          mappings.forEach((match) => {
            const [module, values] = match
              .replace("mapped with ", "")
              .split(": ");
            linkedModules.push(module.trim()); // Extract Linked Module
            relatedIds.push(values.trim()); // Extract Related IDs
          });
        }

        data.push({
          name,
          linkedModules: linkedModules.map((mod, idx) => (
            <div key={idx}>{mod}</div>
          )),
          relatedIds: relatedIds.map((id, idx) => <div key={idx}>{id}</div>),
        });
      }
    });

    return data;
  }

  // Parse the message to get structured data
  const data = parseMessage(message);

  return (
    <Dialog
      open={show}
      onClose={onHide}
      maxWidth="sm"
      fullWidth
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 800,
          maxHeight: 550,
        },
      }}
    >
      {/* Close Button */}
      <div className="flex justify-end ">
        <IconButton onClick={onHide} size="small">
          <CloseIcon className="w-3 h-3 text-black" />
        </IconButton>
      </div>

      {/* Warning Icon */}
      <div className="flex justify-center items-center my-2">
        <DeleteAllIcon />
      </div>

      {/* Warning Text */}
      <div className="flex justify-center items-center my-2 text-sm font-bold">
        {`The following ${
          moduleName === moduleConfiguration.customerSupplier
            ? "Customer/Supplier(s)"
            : `${moduleName}(s)`
        } are not allowed to be deleted.`}
      </div>

      {/* Table Container */}
      <div className="mt-2 mx-10">
        <TableContainer
          component={Paper}
          variant="outlined"
          sx={{ maxHeight: 300 }} // Enables scrolling
        >
          <Table stickyHeader sx={{ minWidth: 550 }} aria-label="sticky table">
            <TableHead sx={{ bgcolor: theme.backgroundColor.bgTable }}>
              <TableRow>
                <TableCell
                  sx={{
                    position: "sticky",
                    top: 0,
                    zIndex: 1,
                    backgroundColor: theme.backgroundColor.bgTable,
                    fontWeight: "bold",
                  }}
                >
                  <div className="whitespace-nowrap font-bold text-sm">{`${
                    moduleName === moduleConfiguration.customerSupplier
                      ? "Customer/Supplier"
                      : moduleName
                  }`}</div>
                </TableCell>
                <TableCell
                  sx={{
                    position: "sticky",
                    top: 0,
                    zIndex: 1,
                    backgroundColor: theme.backgroundColor.bgTable,
                    fontWeight: "bold",
                  }}
                >
                  <div className="whitespace-nowrap font-bold text-sm">
                    Linked Module
                  </div>
                </TableCell>
                <TableCell
                  sx={{
                    position: "sticky",
                    top: 0,
                    zIndex: 1,
                    backgroundColor: theme.backgroundColor.bgTable,
                    fontWeight: "bold",
                  }}
                >
                  <div className="whitespace-nowrap font-bold text-sm">
                    Related Name/IDs
                  </div>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data.length > 0 ? (
                data.map((row, index) => (
                  <TableRow key={index}>
                    <TableCell>{row.name}</TableCell>
                    <TableCell>{row.linkedModules}</TableCell>
                    <TableCell>{row.relatedIds}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={3} className="text-center">
                    No data available
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </div>

      {/* Okay Button */}
      <div className="text-center mt-10 mb-4">
        <Button
          type="submit"
          label={"Okay"}
          buttonClassName="w-[200px] h-9 text-xs ml-5"
          onClick={onHide}
        />
      </div>
    </Dialog>
  );
}

export default DeleteAllDialog;
