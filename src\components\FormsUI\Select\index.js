import React, { useContext, useEffect, useState } from "react";
import { ErrorMessage, useField, useFormikContext } from "formik";
import ReactSelect, { components, createFilter } from "react-select";
import { useQuery } from "react-query";
import theme from "../../../tailwind-theme";
import { getListData, getErrorDetails } from "../../../lib/list-api";
import {
  getFilterData,
  getSelectiveDetail,
} from "../../../lib/customer-supplier-api";
import { getCommonFilterData } from "../../../lib/common-filter-api";
import {
  getActionAndModuleNameFromURL,
  getIdAfterEdit,
  getTrimmedUrl,
} from "../../../common/urlUtils";
import { DropdownContext } from "../../../context/DropDownContext";
import { EditIcon } from "../../../icons";
import {
  getIndexedDBDataById,
  updateIndexedDBDataById,
} from "../../HelperFunction/localStorage";
import { useLocation, useNavigate } from "react-router-dom";
import { moduleConfiguration } from "../../../common/constants";

const Select = ({
  fontSize,
  minHeight,
  isDisabled,
  title,
  bgColor,
  isDropDownApi,
  collectionName,
  values,
  borderRadius,
  filter,
  queryInfo,
  isSearchable,
  width,
  border,
  paddingX,
  dynamicList,
  dynamicOptions,
  isCountry,
  isCustomer,
  gtType,
  moduleName,
  isUnique,
  alloweDropDownEdit,
  header,
  setCurrentStep,
  isView,
  action,
  esmeAccounts,
  pathId,
  ...props
}) => {
  const [field, meta, helpers] = useField(props.name);
  const [optionsData, setOptionsData] = useState([]);
  const { setFieldValue, setFieldTouched } = useFormikContext();
  const location = useLocation();
  const navigate = useNavigate();
  const { setErrorDetails, setSelectedData } = useContext(DropdownContext);

  const customSelectStyles = {
    control: (baseStyles, state) => ({
      ...baseStyles,
      fontWeight: theme.fontWeight.fields,
      fontSize: theme.fontSize.xs,
      minWidth: width || "initial",
      maxWidth: props.maxWidth || "",
      minHeight: minHeight || "40px",
      backgroundColor: bgColor || theme.backgroundColor.bgField,
      pointerEvents: isDisabled ? "none" : "auto",
      cursor: isDisabled ? "default" : "pointer",
      borderWidth: "1px",
      border: border
        ? border
        : meta.error && meta.touched
        ? `1px solid ${theme.borderColor.errorBorder}`
        : `1px solid ${theme.borderColor.tableBorder}`,
      "&:hover": {
        border: `1px solid ${theme.borderColor.tableBorder}`,
      },
      padding: paddingX || "none",
      borderRadius: borderRadius || theme.borderRadius.fieldRadius,
      boxShadow: "none",
      borderTopRightRadius: props.borderTopRightRadius || "",
      borderBottomRightRadius: props.borderBottomRightRadius || "",
    }),
    dropdownIndicator: (base) => ({
      ...base,
      color: theme.textColor.textNAColor,
      opacity: 0.6,
      "&:hover": {
        color: theme.textColor.textNAColor,
      },
    }),
    menuPortal: (base) => ({
      ...base,
      zIndex: 9999,
    }),
    option: (provided, { isDisabled, isSelected }) => ({
      ...provided,
      color: theme.textColor.headerColor,
      background: isSelected
        ? theme.backgroundColor.bgField
        : theme.backgroundColor.bgPrimary,
      ":hover": {
        background: isDisabled
          ? theme.backgroundColor.bgPrimary
          : theme.backgroundColor.bgField,
      },
      display: "flex",
      justifyContent: "space-between",
      cursor: "pointer",
    }),
    placeholder: (provided) => ({
      ...provided,
      fontSize: theme.fontSize.xs,
      fontWeight: theme.fontWeight.fields,
      color: theme.textColor.textNAColor,
      fontFamily: "OpenSanHebrew",
      opacity: 1,
    }),
    menu: (styles) => ({
      ...styles,
      backgroundColor: theme.backgroundColor.bgPrimary,
      fontSize: theme.fontSize.xs,
      position: "absolute",
      minWidth: "100%",
      border: `1px solid ${theme.borderColor.tableBorder}`,
      boxShadow: "0 !important",
      "&:hover": {
        border: `1px solid ${theme.borderColor.tableBorder}`,
      },
      // zIndex: 9999,
    }),
  };

  const IconOption = (props) => {
    const [isHovered, setIsHovered] = useState(false);
    const searchParams = new URLSearchParams(location.search);
    const currentId = searchParams.get("currentId");
    const handleEditClick = async (e) => {
      e.preventDefault();
      e.stopPropagation();
      setCurrentStep(0);
      const nextPath = `/app/list/${collectionName}/edit/${
        props?.data?.id ? props?.data?.id : props?.data?.value
      }`;
      const currentPath = url;
      const formData = values;
      const moduleNameValue = moduleName;
      const id = parseInt(currentId, 10);
      const previousModuleName = action
        ? `${action === "add" ? "Add" : "Edit"} customer/supplier`
        : getActionAndModuleNameFromURL(window.location.href);
      const listHeader = header;

      try {
        const existingData = await getIndexedDBDataById(
          "navigationDetails",
          "FormDB",
          id
        );

        const updatedData = existingData || { data: [] };

        const existingIndex = updatedData.data.findIndex(
          (entry) => entry.moduleNameValue === moduleNameValue
        );

        if (existingIndex !== -1) {
          updatedData.data.splice(existingIndex, 1);
        }

        const newEntry = {
          currentPath,
          formData,
          previousPath:
            updatedData.data[updatedData.data.length - 1]?.currentPath || null,
          moduleNameValue,
          previousModuleName,
          esmeAccounts,
          pathId,
        };

        if (updatedData.data.length === 0) {
          newEntry.listHeader = listHeader;
        }

        updatedData.data.push(newEntry);

        await updateIndexedDBDataById(
          "navigationDetails",
          "FormDB",
          id,
          updatedData
        );

        navigate(`${nextPath}?currentId=${id}`);
      } catch (error) {
        console.error("Error updating IndexedDB:", error);
      }
    };

    return (
      <components.Option
        {...props}
        innerRef={props.innerRef}
        innerProps={{
          ...props.innerProps,
          onMouseMove: (e) => {
            setIsHovered(true);
            if (props.innerProps.onMouseMove) {
              props.innerProps.onMouseMove(e);
            }
          },
          onMouseLeave: (e) => {
            setIsHovered(false);
            if (props.innerProps.onMouseLeave) {
              props.innerProps.onMouseLeave(e);
            }
          },
        }}
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "10px 12px",
          cursor: "pointer",
          backgroundColor: props.isFocused ? "#f0f0f0" : "#fff",
        }}
      >
        <span style={{ fontSize: "14px" }}>{props.data.label}</span>

        {isHovered && alloweDropDownEdit && !isView && (
          <EditIcon onClick={handleEditClick} />
        )}
      </components.Option>
    );
  };
  useQuery(
    [
      "collectionName" + collectionName,
      collectionName,
      { limit: -1 },
      filter,
      isUnique,
      isDisabled,
    ],
    getFilterData,
    {
      enabled: isDropDownApi !== undefined && filter !== undefined,
      onSuccess: ({ data }) => {
        const optionsData = data?.data?.map((list) => ({
          value: list?.id,
          label: list?.attributes?.name,
          // id: list?.id,
        }));
        setOptionsData(optionsData);
      },
      refetchOnWindowFocus: false,
    }
  );
  const getId = getIdAfterEdit(window.location.href) || "";
  useQuery(
    ["collectionName", collectionName, props?.selectiveDetail?.selectiveDetail],
    getSelectiveDetail,
    {
      enabled: !!props?.selectiveDetail?.selectiveDetail,
      onSuccess: ({ data }) => {
        if (data?.data?.length > 0) {
          let filteredData = data.data;

          // if (collectionName === moduleConfiguration.customerSupplier) {
          //   filteredData = filteredData.filter(
          //     (item) => !item?.attributes?.paths
          //   );
          // }
          // console.log("filteredData", filteredData);
          const optionsData = filteredData.map((list) => ({
            value: list?.id,
            label:
              list?.attributes?.[
                props?.selectiveDetail?.selectiveDetail?.attributeName
              ],
          }));

          setOptionsData(optionsData);
        } else {
          setOptionsData([]);
        }
      },
      refetchOnWindowFocus: false,
    }
  );

  const { refetch } = useQuery(
    [
      getId,
      collectionName === "retry-policy"
        ? moduleConfiguration.retryPolicy
        : collectionName,
      ...(collectionName === "service-managements" ? [] : [{ limit: -1 }]),
    ],
    getListData,
    {
      enabled:
        isDropDownApi !== undefined &&
        filter === undefined &&
        !props?.selectiveDetail?.selectiveDetail,
      onSuccess: ({ data }) => {
        const removeDuplicates = (array) => {
          const seen = new Set();
          return array.filter((item) => {
            const key = `${item.value}-${item.label}`;
            if (seen.has(key)) return false;
            seen.add(key);
            return true;
          });
        };

        if (collectionName === "service-managements") {
          const gtDataMap = {
            dummyGT: "dummy_gt",
            a2pGt: "a2p_gt",
            p2pGt: "p2p_gt",
          };

          const selectedGtData = data?.data?.[gtDataMap[gtType]] || [];
          let optionsData = selectedGtData.map((value) => ({
            value: Number(value),
            label: value,
            id: Number(value),
          }));

          optionsData = removeDuplicates(optionsData);
          setOptionsData(optionsData);
        } else if (collectionName === "mnp-gateway-details") {
          let combinedOptionsData = data?.data?.map((list) => ({
            value: list?.id,
            label: list?.attributes?.gatewayName,
            id: list?.id,
            mnpType: list?.attributes?.mnpType,
          }));

          combinedOptionsData = removeDuplicates(combinedOptionsData);
          setOptionsData(combinedOptionsData);
        } else {
          const mapAttributes = {
            operators: isCountry ? "countryName" : "operatorName",
            "point-code-lists": "redirectionListName",
            "point-codes": "pointCode",
            "http-templates": "templateName",
            "retry-policies": "retryGrpName",
            "esme-accounts": "systemId",
            "redirectional-lists": "redirectionListName",
            "operator-clusters": "clusterName",
            "customer-supplier-managements": "name",
            ports: "port",
            "customer-credit-profiles": "customerName",
          };

          const attributeKey = mapAttributes[collectionName];
          if (attributeKey) {
            let optionsData = data?.data
              ?.filter((list) => {
                if (
                  collectionName === "customer-supplier-managements" &&
                  !isUnique
                ) {
                  const operatorType = isCustomer ? "C" : "S";
                  return (
                    list?.id !== null &&
                    list?.attributes?.[attributeKey] !== null
                    // &&
                    // list?.attributes?.operatorType === operatorType
                  );
                }
                if (collectionName === "ports") {
                  if (values?.protocol === 5) {
                    return (
                      list?.attributes?.[attributeKey] !== null &&
                      list?.attributes?.protocol === "S"
                    );
                  }
                  if (values?.protocol === 12) {
                    return (
                      list?.attributes?.[attributeKey] !== null &&
                      list?.attributes?.protocol === "H"
                    );
                  }
                  return (
                    list?.id !== null &&
                    list?.attributes?.[attributeKey] !== null
                  );
                }

                if (
                  collectionName ===
                  moduleConfiguration.redirectionalListModuleName
                ) {
                  if (values?.interfaceType === 2) {
                    return (
                      list?.attributes?.[attributeKey] !== null &&
                      Number(list?.attributes?.httpListType) === 0
                    );
                  }
                  if (values?.interfaceType === 4) {
                    return (
                      list?.attributes?.[attributeKey] !== null &&
                      Number(list?.attributes?.httpListType) === 1
                    );
                  }
                }
                return (
                  list?.id !== null && list?.attributes?.[attributeKey] !== null
                );
              })
              .map((list) => {
                if (collectionName === "operators" && isCountry) {
                  const mccMncArray = list?.attributes?.mccMnc || [];
                  const mcc =
                    mccMncArray.length > 0
                      ? mccMncArray[0]?.split("-")[0]
                      : null;
                  return {
                    value: Number(mcc),
                    label: list?.attributes?.countryName,
                    id: Number(mcc),
                  };
                }

                return {
                  value: list?.id,
                  label: list?.attributes?.[attributeKey],
                  id: list?.id,
                };
              });

            optionsData = removeDuplicates(optionsData);
            setOptionsData(optionsData);
          }
        }
      },
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    const shouldRefetch = () => {
      if (collectionName === "redirectional-lists" && moduleName === "paths") {
        return [2, 3, 4].includes(values?.interfaceType);
      }
      if (collectionName === "ports" && moduleName === "esme-accounts") {
        return [5, 12].includes(values?.protocol);
      }
      return false;
    };
    if (shouldRefetch()) {
      refetch();
    }
  }, [
    values?.interfaceType,
    values?.protocol,
    collectionName,
    moduleName,
    refetch,
  ]);

  useQuery(["collectionName", queryInfo], getCommonFilterData, {
    enabled: queryInfo !== undefined,
    onSuccess: ({ data }) => {
      if (data?.data?.length > 0) {
        const optionsData = data.data.map((list) => ({
          value: list?.id,
          label: list?.attributes?.[queryInfo.label],
        }));
        setOptionsData(optionsData);
      } else {
        setOptionsData([]);
      }
    },
    refetchOnWindowFocus: false,
  });

  useQuery(["errorDetails", values?.failureResult], getErrorDetails, {
    enabled: collectionName === "retry-policy" && !!values?.failureResult,
    onSuccess: (responseData) => {
      const formattedOptions = responseData?.data?.data.map((item) => ({
        label: `${item.attributes.errorDescription}`,
        value: item.attributes.errorCode,
      }));

      setOptionsData(formattedOptions);
    },
  });

  useEffect(() => {
    if (dynamicList === true && moduleName === "deal-managements") {
      const dealType = values?.type;
      if (dealType) {
        const matchedOption = dynamicOptions?.find((option) => {
          return Array.isArray(option.dealType)
            ? option.dealType.includes(dealType)
            : option.dealType === dealType;
        });
        setOptionsData(matchedOption ? matchedOption?.options : []);
      }
    } else if (dynamicList === true && moduleName === "channel-partners") {
      const partnership = values?.partnershipType;
      if (partnership) {
        const matchedOption = dynamicOptions?.find((option) => {
          return Array.isArray(option.partnershipType)
            ? option.partnershipType.includes(partnership)
            : option.partnershipType === partnership;
        });
        setOptionsData(matchedOption ? matchedOption?.options : []);
      }
    }
  }, [values]);

  const selectedOption = Array.isArray(
    isDropDownApi || dynamicList || queryInfo ? optionsData : props.options
  )
    ? (isDropDownApi || dynamicList || queryInfo
        ? optionsData
        : props.options
      ).find((option) => String(option.value) === String(field.value)) || null
    : null;

  useEffect(() => {
    if (!selectedOption) {
      const newError = {
        fieldName: field.name,
        fieldValue: field.value,
      };

      setErrorDetails((prevErrors) => {
        // Check if the error already exists
        const isDuplicate = prevErrors.some(
          (error) =>
            error.fieldName === newError.fieldName &&
            error.fieldValue === newError.fieldValue
        );

        return isDuplicate ? prevErrors : [...prevErrors, newError];
      });
    }
  }, []);

  const url = getTrimmedUrl();
  // setSelectedData(selectedOption);
  useEffect(() => {
    const collectionNamee = url.split("/")[3];
    if (
      (collectionNamee === "deal-managements" &&
        field.value &&
        optionsData.length > 0) ||
      (collectionNamee === "retry-policies" &&
        field.value &&
        optionsData.length > 0 &&
        props.options === undefined) ||
      (collectionNamee === "channel-partners" &&
        field.value &&
        optionsData.length > 0)
    ) {
      const isOptionAvailable = optionsData.some(
        (option) => option.value == field.value
      );

      if (!isOptionAvailable) {
        setFieldValue(props.name, null);
      }
    } else if (collectionNamee === moduleConfiguration.operators) {
      setSelectedData(selectedOption);
    } else if (collectionNamee === moduleConfiguration.hubRuleConfiguration) {
      setSelectedData((prev) => ({
        ...prev,
        [field?.name]: selectedOption,
      }));
    }
  }, [
    collectionName,
    optionsData,
    field.value,
    setFieldValue,
    props.name,
    selectedOption,
  ]);

  return (
    <div className="relative">
      <ReactSelect
        aria-label={props.name}
        className={`input ${props.className} ${
          meta.touched && meta.error && "is-danger"
        }`}
        options={
          isDropDownApi || dynamicList || queryInfo
            ? optionsData
            : props.options
            ? props.options
            : []
        }
        isSearchable={
          isSearchable !== undefined && isSearchable !== null
            ? isSearchable
            : true
        }
        styles={customSelectStyles}
        isMulti={props.isMulti}
        type="text"
        filterOption={createFilter({ ignoreAccents: false })}
        isClearable={props.isClearable}
        maxMenuHeight={props.maxMenuHeight}
        name={props.name}
        isDisabled={props.disabled || isDisabled}
        isLoading={props.isLoading}
        value={selectedOption}
        menuPortalTarget={document.body}
        menuPlacement="bottom"
        menuPosition="fixed"
        menuShouldScrollIntoView={false}
        onMenuOpen={() => {
          const dropdownElement = document.querySelector(
            ".react-select__control"
          );
          dropdownElement?.scrollIntoView({
            behavior: "smooth",
            block: "nearest",
          });
        }}
        onChange={(option) => {
          setFieldTouched(props.name, true);
          if (props.onChange) props.onChange(option);
          if (!props.isMulti) {
            helpers.setValue(option);
            setFieldValue(props.name, option.value);
          } else {
            let options = option.map((option) => option.label);
            setFieldValue(props.name, options);
          }
        }}
        onBlur={() => setFieldTouched(props.name, true)}
        placeholder={props.placeholder}
        components={{
          IndicatorSeparator: () => null,
          Option: IconOption,
        }}
      ></ReactSelect>

      <ErrorMessage
        component="div"
        className={` text-[#d32f2f] text-[0.75rem] mt-0 font-normal`}
        name={field.name}
      />
    </div>
  );
};

export default Select;
