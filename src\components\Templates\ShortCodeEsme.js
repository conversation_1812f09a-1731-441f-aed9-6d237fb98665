import React, { useEffect, useState, useContext, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useQuery, useMutation } from "react-query";
import {
  getPageSchema,
  getListData,
  getGlobalSearchList,
  updateRecord,
} from "../../lib/list-api";
import ListPageDropdownFilter from "../Filters/ListPageDropdownFilter";
import CustomTable from "../Table";
import ResultPerPageComponent from "../Pagination/ResultPerPage";
import Pagination from "../Pagination/Pagination";
import useDebounce from "../../common/useDebounce";
import { ShareIcon } from "../../icons";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { DownloadContext } from "../../context/DownloadContext";
import { CssTooltip } from "../FormsUI/StyledComponent";
import ExportPopup from "../Dialog/exportpopup";
import { TransformData } from "../TransformData/TransformData";
import CustomSwitchComponent from "../ToggleSwitch/CustomSwitchComponent";
import { DEFAULT_PAGE_SIZE } from "../../common/config";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { checkAndDeleteIndexedDB } from "../HelperFunction/localStorage";

import ErrorDialog from "../Dialog/ErrorDialog";

const ShortCodeEsme = ({ collectionName }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [limitPerPage, setLimitPerPage] = useState(DEFAULT_PAGE_SIZE);
  const [searchText, setSearchText] = useState("");
  const [message, setMessage] = useState("");
  const [errorDialog, setErroDialog] = useState(false);

  const [jsonData, setJsonData] = useState({});
  const [listData, setListData] = useState([]);
  const [columnsData, setColumnsData] = useState([]);
  const [respData, setRespData] = useState([]);

  const [rowSelection, setRowSelection] = useState({});
  const [exportAllDownload, setExportAllDownload] = useState(false);
  const [exportPath, setExportPath] = useState("");
  const debouncedValue = useDebounce(searchText, 500);
  const [showExportConfirmation, setShowExportConfirmation] = useState(false);
  const [exportFlag, setExportFlag] = useState(false);
  const [extensionType, setExtensionType] = useState("");
  const [selectedIds, setSelectedIds] = useState([]);
  const [pagination, setPagination] = useState("");
  const [filteredRow, setFilteredRow] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [filteredData, setFilteredData] = useState([]);
  const [showLastColumn, setShowLastColumn] = useState(false);
  const [dropdownFilter, setDropdownFilter] = useState("");
  const [globalSearch, setGlobalSearch] = useState([]);
  const [globalSearchSelect, setGlobalSearchSelect] = useState("");
  const [rowData, setRowData] = useState([]);
  const [refetchData, setRefetchData] = useState(false);
  const rowDataRef = useRef(rowData);
  const { setIsDownloading } = useContext(DownloadContext);
  const navigate = useNavigate();
  const location = useLocation();
  const { customerBindName, systemId } = location.state || {};

  const { mutate: updateRecordAPI } = useMutation(updateRecord);

  const getLastUrlSegment = (url) => {
    const segments = url.split("/").filter(Boolean);
    return segments.length > 0 ? segments[segments.length - 1] : null;
  };

  const accId = getLastUrlSegment(window.location.href);

  // const { accType, esmeStatus } = location.state || {};
  dayjs.extend(utc);

  const handleLimitChange = (e) => {
    setLimitPerPage(e.target.value);
    setCurrentPage(1);
  };

  const prevCollectionName = useRef(collectionName);
  useEffect(() => {
    if (prevCollectionName.current !== collectionName) {
      setCurrentPage(1);
      window.location.reload();
    }
    prevCollectionName.current = collectionName;
  }, [collectionName]);

  useEffect(() => {
    if (collectionName) {
      checkAndDeleteIndexedDB("navigationDetails");
    }
  }, [collectionName]);

  // Export All API
  const exportReport = (type) => {
    if (type !== "") {
      setExportAllDownload(true);
      setIsDownloading(true);
      let extension = type === "EXCEL" ? "xls" : "csv";
      setExtensionType(extension);
    }
  };
  useEffect(() => {
    const selectedIds = Object.keys(rowSelection)
      .filter((key) => rowSelection[key] === true)
      .map((key) => Number(key));
    setSelectedIds(selectedIds);
  }, [rowSelection, extensionType]);

  useEffect(() => {
    setRowData(filteredRow);
  }, [filteredRow]);

  useEffect(() => {
    rowDataRef.current = rowData;
  }, [rowData]);

  const handleExportCLick = () => {
    if (rowDataRef.current === 0) {
      setErroDialog(true);
      setMessage("No data to export");
      setRefetchData(true);
    } else {
      setShowExportConfirmation(true);
    }
  };

  const hasRowSelection = Object.keys(rowSelection).length > 0;
  const idsValue = (() => {
    //  if (exportIdLCR) return `[${exportIdLCR}]`;
    if (!isSearching && !hasRowSelection) return "*";
    if (!isSearching && hasRowSelection) return `[${selectedIds}]`;
    if (isSearching && !hasRowSelection) return `[${filteredData}]`;
    if (isSearching && hasRowSelection) return `[${selectedIds}]`;
  })();

  useQuery(
    [
      "collectionnName" + collectionName,
      exportPath,
      globalSearchSelect,
      debouncedValue,
      exportFlag,
      idsValue,
      extensionType,
      dropdownFilter,
      "",
      "",
      "",
      "",
      "",
      undefined,
      "",
      accId,
    ],
    getGlobalSearchList,
    {
      enabled: (!!globalSearchSelect && !!debouncedValue) || exportFlag,
      onSuccess: (resp) => {
        if (exportFlag) {
          setIsDownloading(false);
          const url = URL.createObjectURL(resp.data);
          const link = document.createElement("a");
          link.href = url;
          link.download = `${exportPath}-${dayjs(Date.now()).format(
            "DD-MM-YYYY"
          )}.${extensionType}`;
          link.click();
          setExportAllDownload(false);
          if (!hasRowSelection) {
            setSelectedIds([]);
          }
          setExportFlag(false);
        } else {
          setPagination(resp?.data || []);
          setRespData(resp?.data?.data);
          setListData(
            TransformData(
              resp?.data?.data || [],
              jsonData?.schemadetails?.elements || [],
              jsonData?.schemadetails || []
            )
          );
        }
      },
      onError: () => {
        setIsDownloading(false);
        setErroDialog(true);
        setMessage(exportFlag ? "Download failed" : "");
        setExportFlag(false);
      },
    }
  );

  const { isLoading, refetch } = useQuery(
    [
      "collectionName" + collectionName,
      "esme-sessions",
      "",
      limitPerPage,
      currentPage,
      debouncedValue,
      "",
      accId,
      collectionName,
      dropdownFilter,
    ],
    getListData,
    {
      enabled: !debouncedValue,
      onSuccess: (resp) => {
        setRespData(resp?.data?.data);
        // setListData(resp?.data?.data);
        setListData(
          TransformData(
            resp?.data?.data || [],
            jsonData?.schemadetails?.elements || [],
            jsonData?.schemadetails || []
          )
        );

        setPagination(resp?.data || []);
      },
    }
  );

  useQuery(["esme-sessions", selectedIds], getPageSchema, {
    refetchOnWindowFocus: false,
    onSuccess: (resp) => {
      const schemaDetails = resp?.data?.data?.[0]?.attributes?.schemadetails;
      const columnsData = schemaDetails?.columns;

      const columnsSecondary = schemaDetails?.columnsSecondary;
      const modulePath = schemaDetails?.moduleData;
      const search = schemaDetails?.globalSearch;
      //setDropdownFilter(schemaDetails?.dropdownOptions?.defaultQuery || "");
      setExportPath(modulePath);
      setGlobalSearch(search);
      const processColumn = (x) => {
        if (x.accessorKey === "actions") {
          return {
            accessorKey: x.accessorKey,
            header: (
              <div
                className={`flex justify-center items-center gap-6 p-5 ${
                  x.align === "center" ? "ml-12" : "ml-0"
                }`}
              >
                <CssTooltip title={"Export All"} placement="top" arrow>
                  <ShareIcon
                    className="text-black cursor-pointer"
                    onClick={() => {
                      handleExportCLick();
                    }}
                  />
                </CssTooltip>
              </div>
            ),
            enableColumnFilter: false,
            enableSorting: false,
            enableColumnPinning: true,
            size: 60,
            Cell: ({ row }) => (
              <div className="flex justify-center items-center gap-4">
                {x.buttons?.map((y) => {
                  switch (y.title) {
                    default:
                      return null;
                  }
                })}
              </div>
            ),
          };
        } else if (x.isToggle) {
          setShowLastColumn(true);
          return {
            accessorKey: x.accessorKey,
            header: x.header,
            enableColumnFilter: false,
            enableSorting: false,
            Cell: ({ cell }) => {
              const currentRuleStatus = cell.row.original?.attributes?.status;

              const isConnected = currentRuleStatus === "A";
              const handleSwitchChange = (newValue) => {
                const newStatus = newValue ? "A" : "I";

                cell.row.original.attributes.status = newStatus;

                const id = cell.row.original.id;
                const moduleName = collectionName;
                const reqData = {
                  data: {
                    status: newStatus,
                  },
                };
                updateRecordAPI({ moduleName: "smsc-sessions", id, reqData });
              };

              return (
                <div className="display-flex">
                  <CustomSwitchComponent
                    name={x.accessorKey}
                    onChange={handleSwitchChange}
                    checked={isConnected}
                  />
                  {isConnected ? "Connected" : "Disconnected"}
                </div>
              );
            },
          };
        } else if (x.accessorKey === "attributes.shortCode") {
          return {
            accessorKey: x.accessorKey,
            header: x.header,
            enableColumnFilter: false,
            enableSorting: false,
            size: 100,
            Cell: ({ cell, row, table }) => {
              const { rows } = table.getSortedRowModel();
              const shortCode = cell.getValue();

              const groupedRows = rows.filter(
                (r) => r?.original?.attributes?.shortCode === shortCode
              );

              const isFirstRow = groupedRows[0]?.id === row.id;

              if (isFirstRow) {
                return (
                  <div
                    style={{
                      textAlign: "center",
                      verticalAlign: "middle",
                      height: "100%",
                      width: "100%",
                    }}
                  >
                    All
                  </div>
                );
              }

              return null;
            },
          };
        } else if (x.isDateTime) {
          return {
            id: x.id,
            header: x.header,
            filterVariant: x.filterVariant,
            accessorFn: (originalRow) => {
              const date = x.id
                .split(".")
                .reduce((acc, part) => acc?.[part], originalRow.attributes);
              return date ? new Date(date) : null;
            },
            Cell: ({ cell }) => {
              const date = cell.getValue();
              if (date instanceof Date && !isNaN(date)) {
                return dayjs(date).format(x.dateTimeFormat);
              }
              return "";
            },
          };
        } else {
          if (x.renderFunction) {
            x.Cell = new Function("row", `return ${x.renderFunction}`);
          }
          return { ...x, enableSorting: true };
        }
      };

      let cData = [];

      if (Array.isArray(columnsData) && columnsData[0]?.columns) {
        cData = columnsData[0].columns.map(processColumn);
        setColumnsData(cData);
      } else if (Array.isArray(columnsData)) {
        cData = columnsData.map(processColumn);
        setColumnsData(cData);
      } else if (Array.isArray(columnsSecondary)) {
      }
      cData = cData.filter((column) => {
        return !(column.accessorKey === "attributes.systemId");
      });

      setJsonData(resp?.data?.data?.[0]?.attributes);
      const uniqueColumnsData = cData.reduce((acc, current) => {
        const isDuplicate = acc.some((item) => item.header === current.header);
        if (!isDuplicate) {
          acc.push(current);
        }
        return acc;
      }, []);
      setColumnsData(uniqueColumnsData);
    },
  });

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };
  return (
    <>
      <div className="flex flex-col">
        <div className="font-bold text-subtitle font-openSanHebrew">
          <div className="flex gap-2 font-medium my-7">
            <>
              <div
                className="text-textNAColor text-base cursor-pointer font-"
                onClick={() => navigate("/app/list/number-of-sessions")}
              >
                {"ESME Sessions >"}
              </div>
              <div className="text-black font-bold">{`${customerBindName} (${systemId}) Sessions`}</div>
            </>
          </div>
        </div>

        <div className="flex items-center justify-between my-2">
          {" "}
          <div className="flex gap-5">
            {jsonData?.schemadetails?.dropdownOptions ? (
              <ListPageDropdownFilter
                setDropdownFilter={setDropdownFilter}
                dropdownDetails={
                  jsonData?.schemadetails?.dropdownOptions?.options || []
                }
                defaultValue={
                  jsonData?.schemadetails?.dropdownOptions?.defaultValue
                }
                defaultQuery={
                  jsonData?.schemadetails?.dropdownOptions?.defaultQuery
                }
                collectionName={collectionName}
                limitPerPage={limitPerPage}
                currentPage={currentPage}
              />
            ) : null}
          </div>
        </div>
        <div className="my-4">
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <CustomTable
              data={listData}
              rowSelection={rowSelection}
              onRowSelectionChange={setRowSelection}
              columns={columnsData}
              onRowClick={() => {}}
              showLastColumn={showLastColumn}
              globalSearch={globalSearch}
              isLoading={isLoading}
              setFilteredRow={setFilteredRow}
              setIsSearching={setIsSearching}
              setSelectedIds={setSelectedIds}
              setFilteredData={setFilteredData}
              isEsmeSessionColumns={false}
              isMinimumWidth={true}
              isShortCode={true}
            />
          </LocalizationProvider>
        </div>

        {pagination && pagination?.meta?.pagination?.total !== undefined && (
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              margin: "5px 0px 5px 5px",
              width: "95%",
            }}
          >
            <div className="flex">
              <ResultPerPageComponent
                limit={limitPerPage}
                handleLimitChange={handleLimitChange}
              />
              <div
                style={{
                  display: "flex",
                  fontSize: "14px",
                  padding: "10px 0px 0px 10px",
                  color: "#808080",
                }}
              >
                <>
                  <>
                    {!isSearching ? (
                      <>
                        {filteredRow === 0
                          ? 0
                          : (currentPage - 1) * limitPerPage + 1}
                        -{Math.min(limitPerPage * currentPage, filteredRow)} of{" "}
                        {filteredRow} rows
                      </>
                    ) : (
                      <>
                        {`${
                          filteredRow === 0
                            ? 0
                            : (currentPage - 1) * limitPerPage + 1
                        }  - ${
                          (currentPage - 1) * limitPerPage + filteredRow
                        } of ${filteredRow} rows (Matched for this page)`}
                      </>
                    )}
                  </>
                </>
              </div>
            </div>
            <Pagination
              className="pagination-bar"
              currentPage={currentPage}
              totalCount={pagination?.meta?.pagination?.total || 0}
              pageSize={limitPerPage}
              onPageChange={handlePageChange}
              isSearching={isSearching}
            />
          </div>
        )}
        <ExportPopup
          show={showExportConfirmation}
          onHide={() => setShowExportConfirmation(false)}
          onConfirm={(type) => {
            exportReport(type);
            setExportAllDownload(true);
            setShowExportConfirmation(false);
            setExportFlag(true);
          }}
          title={"Export Report"}
          identity={"Reports"}
        />
        <ErrorDialog
          show={errorDialog}
          onHide={() => {
            if (!refetchData) {
              refetch();
            }
            setErroDialog(false);
          }}
          message={message}
        />
      </div>
    </>
  );
};
export default ShortCodeEsme;
