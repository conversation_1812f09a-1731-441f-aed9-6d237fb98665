const ManagementPayload = (values, selectedForm, formTitle) => {
  let validKeys = [];

  if (selectedForm.hasChildren) {
    const targetChild = selectedForm.children.find(
      (child) => child.formName === formTitle
    );
    if (targetChild) {
      validKeys = targetChild.elements
        .filter((element) => element.name)
        .map((element) => element.name);
    }
  } else {
    validKeys = selectedForm.elements
      .filter((element) => element.name)
      .map((element) => element.name);
  }
  const payload = Object.keys(values)
    .filter((key) => validKeys.includes(key))
    .reduce((obj, key) => {
      obj[key] = values[key];
      return obj;
    }, {});

  const conditionalFields = [
    {
      field: "mt_ind_resp_type",
      check: 0,
      target: "mt_ind_mask_error_dtls",
      value: null,
    },
    {
      field: "sri_ind_resp_type",
      check: 0,
      target: "sri_ind_mask_error_dtls",
      value: null,
    },
    {
      field: "is_disabled_mnp_exp",
      check: 0,
      target: "exp_cc_for_mnp",
      value: [],
    },
    {
      field: "is_enabled_dnd_exp",
      check: 0,
      target: "exp_cc_for_dnd",
      value: [],
    },
    {
      field: "is_enabled_vip",
      check: 0,
      targets: ["A2P_cust_id", "P2P_cust_id"],
      value: null,
    },
    { field: "is_enabled_ATI", check: 0, target: "ATI_supp_id", value: null },
  ];

  conditionalFields.forEach(({ field, check, target, targets, value }) => {
    if (payload[field] === check) {
      if (target) payload[target] = value;
      if (targets) targets.forEach((t) => (payload[t] = value));
    }
  });

  return { formTitle, data: payload };
};

export default ManagementPayload;
