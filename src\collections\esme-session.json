{"header": "Shortcode ESME sessions", "columns": [{"header": "Short code", "accessorKey": "attributes.shortCode"}, {"header": "Session type", "accessorKey": "attributes.sessType"}, {"header": "Node IP", "accessorKey": "attributes.nodeIp"}, {"header": "Remote IP", "accessorKey": "attributes.remoteClientIp"}, {"id": "bindTime", "header": "Bind date & time", "isDateTime": true, "accessorKey": "attributes.bindTime", "filterVariant": "datetime-range", "dateTimeFormat": "ddd DD MMM YYYY hh:mm:ss:SSS A"}, {"header": "Status", "isToggle": true, "accessorKey": "attributes.status"}, {"buttons": [{"type": "image", "title": "", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"name": "sessType", "options": [{"label": "Transceiver", "value": 3}, {"label": "Transmitter", "value": 2}, {"label": "Receiver", "value": 1}]}, {"name": "status", "options": [{"label": "Connected", "value": "A"}, {"label": "Disconnected", "value": "I"}]}, {"name": "accType", "options": [{"label": "ESME", "value": "E"}, {"label": "SMSC", "value": "R"}]}], "searchBox": false, "esmeHeader": "ESME sessions", "moduleData": "esme-session", "moduleName": "ESME sessions", "globalSearch": [{"label": "System ID", "value": "accId"}], "confirmButton": false, "dropdownOptions": {"field": "status", "options": [{"label": "All status", "query": "", "value": "All"}, {"label": "Connected", "query": "filters[status][$eq]=A", "value": "A"}, {"label": "Disconnected", "query": "filters[status][$eq]=I", "value": "I"}], "defaultQuery": "", "defaultValue": "All"}, "esmeSessionColumns": [{"header": "Number of sessions", "columns": [{"header": "System ID", "idToName": true, "moduleName": "esme-account", "accessorKey": "attributes.systemId", "attributeKey": "accId"}, {"header": "Transmitter", "accessorKey": "attributes.transmitter"}, {"header": "Receiver", "accessorKey": "attributes.receiver"}, {"header": "Transceiver", "accessorKey": "attributes.transceiver"}, {"header": "Total", "accessorKey": "attributes.total"}, {"header": "Status", "accessorKey": "attributes.status", "isToggle": true}, {"buttons": [{"type": "image", "title": "", "iconUrl": ""}], "accessorKey": "actions"}]}]}