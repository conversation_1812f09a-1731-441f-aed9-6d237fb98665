import { axiosPrivate } from "../common/axiosPrivate";
import { moduleConfiguration, subInterfaceEsme } from "../common/constants";
import { getIdAfterEdit } from "../common/urlUtils";
import getAPIMap from "../routes/ApiUrls";

/**
 *This method is used to get the data by Id
 * @param {*} param0
 * @returns
 */
export async function getCustomerDataById({ queryKey }) {
  let url = getAPIMap("listUrl");
  //url = `${url}/${queryKey[0]}/${queryKey[1]}?populate[billingInformation][populate]=*&populate[custShortCodes][populate]=*`;
  url = `${url}/${queryKey[0]}/${queryKey[1]}?populate[billingInformation][populate][operatorGroupDetails][populate]=*&populate[billingInformation][populate][slabWiseDifferentialRate][populate]=*&populate[billingInformation][populate][fixedFeePerSms][populate]=*&populate[custShortCodes][populate]=*&populate[billingInformation][populate][mrc][populate]=*&populate[billingInformation][populate][fixedFeeUsagePerSms][populate]=*&populate[billingInformation][populate][slabWiseSingleRate][populate]=*&populate[fraudManagement]=*&populate[esme_accounts]=*&populate[operator_clusters]=*&populate[esme_account_smpp_es]=*&populate[paths]=*`;
  let response = await axiosPrivate.get(url);
  return response;
}
/**
 *This method is used to get the operator list filter data
 * @param {*} param0
 * @returns
 */
export async function getOperatorListData({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${queryKey[1]}?sort=createdAt:desc`;

  if (queryKey[1] === "customer-supplier-managements") {
    url += `&populate[billingInformation][populate][operatorGroupDetails][populate]=*&populate[billingInformation][populate][slabWiseDifferentialRate][populate]=*&populate[billingInformation][populate][fixedFeePerSms][populate]=*&populate[custShortCodes][populate]=*&populate[billingInformation][populate][mrc][populate]=*&populate[billingInformation][populate][fixedFeeUsagePerSms][populate]=*&populate[billingInformation][populate][slabWiseSingleRate][populate]=*&populate[fraudManagement]=*`;
  }

  if (queryKey[2]?.limit) {
    url += `&pagination[limit]=${queryKey[2].limit}`;
  }
  if (queryKey[3]) {
    url += `&customerRelation=true`;
  }
  if (queryKey[4]) {
    url += `&calledAddrType=${queryKey[4]}`;
  }
  if (queryKey[5]) {
    url += `&fields[0]=${queryKey[5]}`;
  }
  if (queryKey[6]) {
    url += `&calledGtPrefix=${queryKey[6]}`;
  }
  if (queryKey[7]) {
    url += `&customerId=${queryKey[7]}`;
  }
  url += `&filters[clusterType][$eq]=0`;

  let response = await axiosPrivate.get(url);
  return response;
}

/**
 *This method is used to get the ESME list filter data
 * @param {*} param0
 * @returns
 */
export async function getSmppListData({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${queryKey[1]}?sort=createdAt:desc`;

  if (queryKey[2]?.limit) {
    url += `&pagination[limit]=${queryKey[2].limit}`;
  }
  if (queryKey[3]) {
    url += `&customerRelation=true`;
  }
  if (queryKey[4]) {
    url += `&filters[protocol]=${
      Number(queryKey[4]) === subInterfaceEsme.SMPP ? 5 : 12
    }`;
  }
  if (queryKey[5]) {
    url += `&customerId=${queryKey[5]}`;
  }

  url += `&fields[0]=${queryKey[6]}`;
  url += `&fields[1]=${queryKey[7]}`;
  url += `&fields[2]=id`;
  if (queryKey[8]) {
    url += `&pathRelation=true`;
  }
  let response = await axiosPrivate.get(url);
  return response;
}

/**
 *This method is used to get the operator list filter data
 * @param {*} param0
 * @returns
 */
export async function getOperatorList({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${queryKey[1]}?sort=createdAt:desc`;

  if (queryKey[2]?.limit) {
    url += `&pagination[limit]=${queryKey[2].limit}`;
  }
  if (queryKey[3]) {
    url += `&fields[0]=${queryKey[3]}`;
  }
  if (queryKey[4]) {
    url += `&fields[1]=${queryKey[4]}`;
  }
  if (queryKey[5]) {
    url += `&fields[2]=${queryKey[5]}`;
  }
  if (queryKey[6]) {
    url += `&fields[3]=${queryKey[6]}`;
  }

  let response = await axiosPrivate.get(url);
  return response;
}

/**
 *This method is used to get the ESME list filter data
 * @param {*} param0
 * @returns
 */
export async function getSmppEsListData({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${queryKey[1]}?sort=createdAt:desc`;

  if (queryKey[2]?.limit) {
    url += `&pagination[limit]=${queryKey[2].limit}`;
  }
  // if (queryKey[3]) {
  //   url += `&customerRelation=true`;
  // }
  if (queryKey[4]) {
    url += `&filters[protocol][$eq]=${queryKey[4]}`;
  }
  if (queryKey[5]) {
    url += `&customerId=${queryKey[5]}`;
  }
  if (queryKey[6]) {
    url += `&fields[0]=${queryKey[6]}`;
  }
  if (queryKey[7]) {
    url += `&fields[1]=${queryKey[7]}`;
  }
  if (queryKey[8]) {
    url += `&operatorType=${queryKey[8]}`;
  }
  if (queryKey[9]) {
    url += `&interfaceType=${queryKey[9]}`;
  }

  let response = await axiosPrivate.get(url);
  return response;
}

export async function getFilterData({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${queryKey[1]}?sort=createdAt:desc`;
  url += `&pagination[limit]=-1`;
  const { protocolType, operatorType, interfaceType } = queryKey[3];

  if (protocolType) {
    url += `&filters[protocolType][$eq]=${protocolType}`;
  }

  if (Array.isArray(operatorType)) {
    operatorType.forEach((type) => {
      url += `&filters[operatorType][$in]=${type}`;
    });
  }

  if (interfaceType) {
    url += `&filters[interfaceType][$eq]=${interfaceType}`;
  }

  if (queryKey[4] === true && queryKey[5] !== true) {
    url += "&creditProfileRelation=true";
  }

  let response = await axiosPrivate.get(url);
  return response;
}

export async function getGTDefinitionData({ queryKey }) {
  let url = `${getAPIMap("listUrl")}/${queryKey[1]}?sort=createdAt:desc`;

  url += `&pagination[limit]=-1`;

  if (queryKey[2]) {
    url += `&fields[0]=${queryKey[2]}`;
  }
  if (queryKey[3]) {
    url += `&fields[1]=${queryKey[3]}`;
  }
  if (queryKey[4]) {
    url += `&fields[2]=${queryKey[4]}`;
  }

  let response = await axiosPrivate.get(url);
  return response;
}
export async function getSelectiveDetail({ queryKey }) {
  const pathId = getIdAfterEdit(window.location.href);
  let url = `${getAPIMap("listUrl")}/${queryKey[1]}`;

  const queryParams = [];

  if (queryKey[1] === moduleConfiguration.esmeAccounts) {
    queryParams.push("pathRelation=true");
  }
  if (pathId) {
    queryParams.push(`pathId=${pathId}`);
  }

  if (queryKey[2]?.filters?.length > 0) {
    queryKey[2].filters.forEach((filter) => {
      Object.entries(filter).forEach(([key, value]) => {
        if (Array.isArray(value) && value.length > 0) {
          value.forEach((val) => {
            queryParams.push(`filters[${key}][$in]=${val}`);
          });
        } else if (value !== undefined && value !== null) {
          queryParams.push(`filters[${key}]=${value}`);
        }
      });
    });
  }

  if (queryKey[2].fields && Array.isArray(queryKey[2].fields)) {
    queryKey[2].fields.forEach((field) => {
      queryParams.push(`fields[]=${field}`);
    });
  }

  if (queryParams.length) {
    url += `${url.includes("?") ? "&" : "?"}${queryParams.join("&")}`;
  }

  let response = await axiosPrivate.get(url);
  return response;
}
