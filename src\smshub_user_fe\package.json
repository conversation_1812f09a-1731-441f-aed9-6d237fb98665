{"name": "sms_hub", "version": "0.1.0", "private": true, "dependencies": {"@cyntler/react-doc-viewer": "^1.14.1", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.15", "@mui/lab": "^5.0.0-alpha.163", "@mui/material": "^5.15.15", "@mui/styled-engine-sc": "^6.0.0-alpha.18", "@mui/x-date-pickers": "^7.1.1", "@tanstack/react-query": "^5.28.14", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "date-fns": "^3.6.0", "dayjs": "^1.11.10", "formik": "^2.2.9", "i18next": "^22.5.0", "i18next-browser-languagedetector": "^7.0.1", "jwt-decode": "^4.0.0", "material-react-table": "^2.12.1", "mem": "^10.0.0", "moment": "^2.29.4", "otp-input-react": "^0.3.0", "react": "^18.2.0", "react-datepicker": "^6.6.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-i18next": "^12.3.1", "react-phone-input-2": "^2.15.1", "react-query": "^3.39.3", "react-router-dom": "^6.11.2", "react-scripts": "5.0.1", "react-select": "^5.8.0", "react-window": "^1.8.11", "styled-components": "^6.1.8", "tailwind-merge": "^2.2.1", "web-vitals": "^2.1.4", "yup": "^1.1.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint-config-prettier": "^8.8.0", "prettier": "2.8.8", "tailwindcss": "^3.3.2"}}