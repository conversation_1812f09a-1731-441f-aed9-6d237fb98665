{"header": "Node sessions", "columns": [{"header": "Node id", "accessorKey": "id", "filterVariant": "range"}, {"header": "Node IP", "accessorKey": "attributes.nodeIP"}, {"isBg": true, "header": "Status", "accessorKey": "attributes.status"}, {"id": "lastUpdatedTime", "header": "Last updated time", "isDateTime": true, "accessorKey": "attributes.lastUpdatedTime", "filterVariant": "datetime-range", "dateTimeFormat": "ddd DD MMM YYYY hh:mm:ss:SSS A"}, {"buttons": [{"type": "image", "title": "", "iconUrl": ""}], "accessorKey": "actions"}], "moduleData": "node-session", "globalSearch": [{"label": "Node id", "value": "id"}, {"label": "Node IP", "value": "nodeIP"}], "elements": [{"name": "status", "options": [{"label": "NODE UP", "value": "A"}, {"label": "NODE DOWN", "value": "I"}]}], "isDeleteIcon": false, "confirmButton": false, "dropdownOptions": {"field": "status", "options": [{"label": "ALL", "query": "", "value": "ALL"}, {"label": "NODE UP", "query": "filters[status][$eq]=a", "value": "a"}, {"label": "NODE DOWN", "query": "filters[status][$eq]=i", "value": "i"}], "defaultQuery": "", "defaultValue": "ALL"}}