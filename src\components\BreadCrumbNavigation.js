import React from "react";
import Breadcrumbs from "@mui/material/Breadcrumbs";
import Link from "@mui/material/Link";
import Typography from "@mui/material/Typography";

function handleClick(event) {
  console.info("You clicked a breadcrumb.");
}

export default function BreadcrumbNavigation({
  linkOne,
  linkOnePath,
  linkTwo,
  linkTwoPath,
  title,
}) {
  return (
    <div
      role="presentation"
      onClick={handleClick}
      className="sticky top-0 bg-bgBody z-10 content-center mt-0 mb-0 overflow-visible"
    >
      <Breadcrumbs
        separator={
          <Typography
            variant="inherit"
            fontWeight="700"
            fontSize={"24px"}
            color="#000000"
          >
            ›
          </Typography>
        }
        aria-label="breadcrumb"
      >
        {" "}
        {linkOne && linkOnePath ? (
          <Link
            color="#000000"
            underline="hover"
            href={linkOnePath}
            sx={{
              fontSize: "24px",
              //fontFamily: "Open Sans Hebrew",
              fontWeight: 700,
              "&:hover": { cursor: "pointer" },
            }}
          >
            {linkOne}
          </Link>
        ) : null}
        {linkTwo && linkTwoPath ? (
          <Link
            color="#000000"
            underline="hover"
            href={linkTwoPath}
            sx={{
              //fontFamily: "Open Sans Hebrew",
              fontSize: "24px",
              fontWeight: 700,
              "&:hover": { cursor: "pointer" },
            }}
          >
            {linkTwo}
          </Link>
        ) : null}
        {title ? (
          <Link
            color="#000000"
            underline="none"
            sx={{
              fontSize: "24px",
              //fontFamily: "Open Sans Hebrew",
              fontWeight: 700,
              "&:hover": { cursor: "auto" },
            }}
          >
            {title}
          </Link>
        ) : null}
      </Breadcrumbs>
    </div>
  );
}
