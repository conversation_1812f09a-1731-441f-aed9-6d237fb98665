const PointCodeListPayload = (values, elements) => {
  const payload = { ...values };

  if (values.redirectionType === 3) {
    values.priorityTable.forEach((item) => {
      delete item["SMSC account"];
      delete item["Traffic"];
    });

    values.redirectionListType = elements[0].defaultValue;
  }

  if (values.redirectionType === 2) {
    delete values.sampleVal;
    values.priorityTable.forEach((item) => {
      delete item.Account;
    });
    values.redirectionListType = elements[0].defaultValue;
  }

  if (values.redirectionType === 1) {
    delete values.sampleVal;
    values.point_codes = values.point_codes || [];

    values.priorityTable = values.point_codes.map((code) => ({
      redirectedEntityId: code,
    }));

    values.redirectionListType = elements[0].defaultValue;
  }
  //console.log("payload", payload);
  return payload;
};
export default PointCodeListPayload;
