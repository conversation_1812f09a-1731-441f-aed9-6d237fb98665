import React, { useEffect, useState } from "react";
import { Form, Formik } from "formik";
import { useMutation, useQuery } from "react-query";
import {
  createLcr,
  createRecord,
  updateParentDetail,
  getParentDetailsById,
} from "../../lib/lcr-profile-api";
import InputLabel from "../FormsUI/InputLabel";
import Select from "../FormsUI/Select";
import TextFieldWrapper from "../FormsUI/TextField";
import ImportFiles from "../Files/ImportFiles";
import SuccessDialog from "../Dialog/SuccessDialog";
import ErrorDialog from "../Dialog/ErrorDialog";
import Button from "../Buttons/Button";
import CancelButton from "../Buttons/OutlinedButton";
import LCRFieldArrayTable from "../Table/LCRFieldArrayTable";
import { useNavigate } from "react-router-dom";
import RadioButtonGroup from "../RadioButton/RadioButtonGroup";
import { moduleConfiguration } from "../../common/constants";
import { useSupplierData } from "../Dropdown";
import {
  getInitialValues,
  getValidationSchema,
} from "../HelperFunction/ScAtLcr";
import LCRErrorTable from "../Dialog/LCRErrorTable";

function ScAtLcrpolicyForm({
  isEdit,
  id,
  parentId,
  dropdownDetails,
  isViewLCR,
  lcrTypeValue,
}) {
  const navigate = useNavigate();
  const [file, setFile] = useState(null);
  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [formData, setFormData] = useState(null);
  const [customerType, setCustomerType] = useState("H");
  const [errorMessage, setErrorMessage] = useState("");

  const { mutate: createLCRAPI, isLoading: createLCRLoading } =
    useMutation(createLcr);
  const { mutate: createRecordAPI, isLoading: createLoading } =
    useMutation(createRecord);
  const {
    mutate: updateParentDetailAPI,
    isLoading: updateParentDetailLoading,
  } = useMutation(updateParentDetail);

  const supplierData = [
    { label: "No routes", value: -1 },
    ...useSupplierData(),
  ];

  useQuery(
    [
      moduleConfiguration.scAtLCRModuleName,
      moduleConfiguration.parentDetails,
      id,
      parentId,
      lcrTypeValue,
    ],
    getParentDetailsById,
    {
      enabled: parentId > 0,
      refetchOnWindowFocus: false,
      onSuccess: ({
        data: {
          data: { attributes },
        },
      }) => {
        setFormData(attributes);
      },
    }
  );

  const handleSubmit = (values) => {
    const modifiedValues = {
      ...values,
      parentDetails: values.parentDetails.map(
        ({ sourceOpId, mccMnc, region, policyDetails, ...rest }) => {
          const updatedMccMnc = sourceOpId === -99999 ? "1-1" : mccMnc;

          const updatedValues =
            values.custType === "S"
              ? { ...rest }
              : { sourceOpId, mccMnc: updatedMccMnc, region, ...rest };

          return {
            ...updatedValues,
            policyDetails: policyDetails.map((item) => ({
              supplier: item.supplier,
              cost: item.supplier === -1 ? "0" : item.cost,
              percentage: item.supplier === -1 ? 0 : item.percentage,
              position: item.supplier === -1 ? 0 : item.position,
            })),
          };
        }
      ),
    };

    const reqData = { data: modifiedValues, ...(file && { fileUpload: file }) };

    const apiConfig = {
      moduleData: moduleConfiguration.scATLCRImportName,
      reqData,
    };

    const apiCallbacks = {
      onSuccess: () => {
        setSuccessDialog(true);
        setMessage(`SC AT LCR ${isEdit ? "updated" : "created"} successfully`);
      },
      onError: ({ response }) => {
        const errorMessage = response?.data?.error?.message;
        const firstErrorMessage = Array.isArray(errorMessage)
          ? errorMessage[0]
          : errorMessage;
        if (
          file &&
          typeof firstErrorMessage === "string" &&
          firstErrorMessage.startsWith("Validation Errors")
        ) {
          setErrorMessage(firstErrorMessage);
        } else {
          setErrorDialog(true);
          setMessage(firstErrorMessage);
        }
      },
    };

    const commonUpdateAPI = (apiMethod, additionalConfig = {}) =>
      apiMethod({ ...apiConfig, ...additionalConfig }, apiCallbacks);

    if (isEdit) {
      commonUpdateAPI(updateParentDetailAPI, {
        moduleName: moduleConfiguration.scAtLCRModuleName,
        parentDetail: moduleConfiguration.parentDetails,
        lcrId: id,
        parentId: parentId,
      });
    } else {
      commonUpdateAPI(file ? createLCRAPI : createRecordAPI, {
        moduleName: moduleConfiguration.scAtLCRModuleName,
      });
    }
  };

  useEffect(() => {
    if (!file) {
      setErrorMessage("");
    }
  }, [file]);

  return (
    <Formik
      initialValues={getInitialValues(formData)}
      validateOnMount={true}
      validationSchema={getValidationSchema(file, customerType)}
      enableReinitialize={true}
      onSubmit={(values) => {
        handleSubmit(values);
      }}
    >
      {({ values, setFieldValue, errors }) => (
        <Form>
          {setCustomerType(values?.custType)}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-10 md:mx-14 mt-10">
            <div>
              <InputLabel label="LCR profile name" isMandatory={true} />
              <TextFieldWrapper
                name="lcrName"
                placeholder="Enter LCR profile name"
                isDisabled={isEdit ? true : false}
              />
            </div>
            <div>
              <InputLabel label={dropdownDetails[0].title} isMandatory={true} />
              <Select
                name={dropdownDetails[0].name}
                options={dropdownDetails[0].options}
                isDisabled={isEdit ? true : false}
              />
            </div>
            <div>
              <InputLabel label={dropdownDetails[1].title} isMandatory={true} />
              <RadioButtonGroup
                name={dropdownDetails[1].name}
                value={values.custType}
                onChange={(e) => {
                  if (!isEdit) {
                    setFieldValue("custType", e.target.value);
                    setCustomerType(e.target.value);
                  }
                }}
                options={dropdownDetails[1].options}
                isDisabled={isEdit ? true : false}
              />
            </div>
          </div>
          {!isViewLCR && !isEdit ? (
            <div className="md:mx-14 mt-10">
              <ImportFiles setFile={setFile} file={file} isScAtLCR={true} />
            </div>
          ) : null}

          <div className="mt-10">
            {!file && (
              <>
                <LCRFieldArrayTable
                  name="parentDetails"
                  values={values}
                  headers={["Supplier", "Cost", "Percentage", "Position"]}
                  fields={[
                    {
                      name: "supplier",
                      options: [supplierData],
                    },
                    { name: "cost" },
                    { name: "percentage" },
                    { name: "position" },
                  ]}
                  setFieldValue={setFieldValue}
                  isEdit={isEdit}
                />
              </>
            )}
          </div>

          {errorMessage && (
            <div className="mt-10">
              <LCRErrorTable errorMessages={errorMessage} />
            </div>
          )}

          <div className="flex justify-center items-center mt-10">
            <CancelButton
              label="Cancel"
              buttonClassName="w-full md:w-[154px] h-[40px] text-base mb-3 rounded-[20px]"
              onClick={() => navigate("/app/list/sc-at-lcrs")}
            />
            {!errorMessage && (
              <Button
                type="submit"
                label="Save"
                value="submit"
                buttonClassName="w-full md:w-[154px] h-[40px] text-base mb-3 rounded-[20px] ml-5"
                loading={
                  createLCRLoading || createLoading || updateParentDetailLoading
                }
              />
            )}
          </div>

          <SuccessDialog
            show={successDialog}
            onHide={() => {
              setSuccessDialog(false);
              navigate(`/app/list/${moduleConfiguration.scAtLCRModuleName}`);
            }}
            message={message}
          />
          <ErrorDialog
            show={errorDialog}
            onHide={() => setErrorDialog(false)}
            message={message}
          />
        </Form>
      )}
    </Formik>
  );
}

export default ScAtLcrpolicyForm;
