import { useEffect, useState } from "react";
import { Tabs, Tab, Box, IconButton } from "@mui/material";
import theme from "../../../tailwind-theme";

function CustomTab({
  tabs,
  defaultTab,
  onChange = false,
  backgroundColor,
  color,
  enableCloseIcon,
  onClose,
  indicatorWidth,
  selectedColor,
  isCreatePopup,
}) {
  const [selectedTab, setSelectedTab] = useState(defaultTab);

  const handleTabChange = (event, newValue) => {
    if (onChange) {
      onChange(event, newValue);
    }
    setSelectedTab(isCreatePopup ? 0 : newValue);
  };

  useEffect(() => setSelectedTab(defaultTab), [defaultTab]);

  const getTabColor = (index) => {
    if (index < selectedTab) return `${theme.borderColor.tabColor}`;
    else if (index === selectedTab)
      return `${theme.backgroundColor.bgSecondary}`;
    return `${color && color ? color : theme.textColor.tabTextColor}`;
  };

  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        flexDirection: "row",
        borderRadius: "2px",
      }}
    >
      <Tabs
        value={selectedTab}
        onChange={handleTabChange}
        variant="scrollable"
        scrollButtons="auto"
        sx={{
          width: enableCloseIcon ? "94%" : "100%",
          backgroundColor: `${
            backgroundColor && backgroundColor
              ? backgroundColor
              : theme.backgroundColor.bgHeader
          }`,
          borderBottom: `transparent`,
          borderTopLeftRadius: "4px",
          borderTopRightRadius: "4px",
          height: "52px",
          // paddingTop: "12px",
          color: `${color && color ? color : theme.textColor.tabTextColor}`,
          "& .MuiTabs-indicator": {
            backgroundColor: theme.backgroundColor.bgSecondary,
            height: "1px",
          },
          "& .Mui-selected": {
            color: `${
              selectedColor && selectedColor
                ? selectedColor
                : theme.backgroundColor.bgHeader
            }`,
            fontWeight: "600",
          },
          "& .MuiTabScrollButton-root": {
            width: "20px",
          },
          "& .MuiTab-root": {
            minWidth: "0",
            width: `${
              indicatorWidth && indicatorWidth ? indicatorWidth : "fit-content"
            }`,
          },
          "& .MuiButtonBase-root.MuiTab-root.Mui-selected": {
            // Updated selector
            color: theme.backgroundColor.bgSecondary, // Changed color for selected tab
          },
          justifyContent: "space-between",
        }}
      >
        {tabs.map((tab, index) => (
          <Tab
            key={index}
            label={tab.label ? tab?.label : tab}
            sx={{
              textTransform: "none",
              fontSize: "12px",
              //fontFamily: "Open Sans Hebrew",
              color: getTabColor(index),
              fontWeight: "400",
              paddingBottom: 0,
            }}
          />
        ))}
      </Tabs>
      <Box style={{ border: "", borderRadius: "4px" }}>
        {tabs.map((tab, index) => (
          <div key={index}></div>
        ))}
      </Box>
      {enableCloseIcon && <IconButton sx={{ width: "6%" }}></IconButton>}
    </Box>
  );
}

export default CustomTab;
