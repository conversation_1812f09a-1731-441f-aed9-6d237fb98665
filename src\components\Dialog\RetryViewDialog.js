import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

function RetryViewDialog({ open, onClose, rowData }) {
  const retryDetailsArray = Object.keys(rowData)
    .filter((key) => key.startsWith("retry"))
    .map((key, index) => ({
      retryAttempt: index + 1,
      retryInterval: rowData[key],
    }));

  const oddRows = retryDetailsArray.filter((_, index) => index % 2 === 0);
  const evenRows = retryDetailsArray.filter((_, index) => index % 2 !== 0);

  const maxLength = Math.max(oddRows.length, evenRows.length);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <div className="m-3 mt-2">
        <div className="flex justify-end ">
          <IconButton onClick={onClose} size="small">
            <CloseIcon className="w-4 h-4 text-black my-2" />
          </IconButton>
        </div>{" "}
        <TableContainer
          sx={{
            minHeight: "300px",
            maxHeight: "400px",
            overflow: "auto",
            borderRadius: "8px",
          }}
        >
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell
                  sx={{
                    backgroundColor: "#DDE9FD",
                    color: "black",
                    fontWeight: "bold",
                    textAlign: "center",
                  }}
                >
                  Retry attempt
                </TableCell>
                <TableCell
                  sx={{
                    backgroundColor: "#DDE9FD",
                    color: "black",
                    fontWeight: "bold",
                    textAlign: "center",
                  }}
                >
                  Retry interval
                </TableCell>
                <TableCell
                  sx={{
                    backgroundColor: "#DDE9FD",
                    color: "black",
                    fontWeight: "bold",
                    textAlign: "center",
                  }}
                >
                  Retry attempt
                </TableCell>
                <TableCell
                  sx={{
                    backgroundColor: "#DDE9FD",
                    color: "black",
                    fontWeight: "bold",
                    textAlign: "center",
                  }}
                >
                  Retry interval
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.from({ length: maxLength }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell sx={{ textAlign: "center" }}>
                    {oddRows[index] ? oddRows[index].retryAttempt : ""}
                  </TableCell>
                  <TableCell sx={{ textAlign: "center" }}>
                    {oddRows[index] ? oddRows[index].retryInterval : ""}
                  </TableCell>

                  <TableCell
                    sx={{ textAlign: "center", backgroundColor: "#EFEFEF" }}
                  >
                    {evenRows[index] ? evenRows[index].retryAttempt : ""}
                  </TableCell>
                  <TableCell
                    sx={{ textAlign: "center", backgroundColor: "#EFEFEF" }}
                  >
                    {evenRows[index] ? evenRows[index].retryInterval : ""}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </div>
    </Dialog>
  );
}

export default RetryViewDialog;
