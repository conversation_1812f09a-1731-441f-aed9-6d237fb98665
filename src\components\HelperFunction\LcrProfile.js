import * as Yup from "yup";
import {
  validationLcrConfig,
  allowedEndMinutes,
  allowedStartMinutes,
} from "../../common/config";

export const getValidationSchema = (file, lcrTypee) => {
  const startTimeValidation = Yup.string()
    .matches(/^\d{2}:\d{2}$/, "Start Time must be in hh:mm format")
    .test(
      "valid-start-time-hours",
      "Invalid value for hours in Start Time",
      (value) => {
        if (!value) return true;
        const [hours] = value.split(":").map(Number);
        return hours >= 0 && hours <= 23;
      }
    )
    .test(
      "valid-start-time-minutes",
      "Start Time minutes must be one of 00, 15, 30, 45",
      (value) => {
        if (!value) return true;
        const [, minutes] = value.split(":");
        return allowedStartMinutes.includes(minutes);
      }
    );

  const endTimeValidation = Yup.string()
    .matches(/^\d{2}:\d{2}$/, "End Time must be in hh:mm format")
    .test(
      "valid-end-time-hours",
      "Invalid value for hours in End Time",
      (value) => {
        if (!value) return true;
        const [hours] = value.split(":").map(Number);
        return hours >= 0 && hours <= 23;
      }
    )
    .test(
      "valid-end-time-minutes",
      "End Time minutes must be one of 14, 29, 44, 59",
      (value) => {
        if (!value) return true;
        const [, minutes] = value.split(":");
        return allowedEndMinutes.includes(minutes);
      }
    )

    .test(
      "is-greater-than-from-time",
      "End Time must be greater than Start Time",
      function (value) {
        const { fromTime } = this.parent;
        if (!value || !fromTime) return true;

        const [fromHours, fromMinutes] = fromTime.split(":").map(Number);
        const [toHours, toMinutes] = value.split(":").map(Number);

        if (toHours < fromHours) return false;
        if (toHours === fromHours && toMinutes <= fromMinutes) return false;

        return true;
      }
    );

  return Yup.object().shape({
    lcrName: Yup.string()
      .matches(validationLcrConfig.lcrName.regex, {
        message: validationLcrConfig.lcrName.regexMessage,
        excludeEmptyString: true,
      })
      .required("Please Enter LCR Name")
      .min(4, "LCR Name must be at least 4 characters")
      .max(30, "LCR Name must be at most 30 characters"),
    lcrType: Yup.string().required("LCR type is required"),
    startDate: Yup.string().when("dateReq", (dateReq, schema) => {
      return dateReq[0] === 1 && lcrTypee === 4
        ? schema.required("Start Date is required")
        : schema.notRequired();
    }),
    parentDetails: file
      ? Yup.array()
      : Yup.array().of(
          Yup.object().shape({
            policyDetails: Yup.array()
              .of(
                Yup.object().shape({
                  supplier: Yup.string().required("Supplier is required"),
                  cost: Yup.number()
                    .min(0, "Cost should be between 1 and 100")
                    .max(100, "Cost should be between 1 and 100")
                    .required("Cost is required")
                    .typeError("Cost should be a number"),
                  percentage:
                    lcrTypee !== 4
                      ? Yup.number()
                          .required("Percentage is required")
                          .min(0, "Percentage must be positive")
                          .max(100, "Percentage cannot be more than 100")
                          .typeError("Percentage should be a number")
                      : Yup.string().nullable(),
                  position:
                    lcrTypee !== 4
                      ? Yup.number()
                          .required("Position is required")
                          .min(0, "Position must be positive")
                          .max(10, "Position cannot be more than 10")
                          .typeError("Position should be a number")
                      : Yup.string().nullable(),
                  quality:
                    lcrTypee !== 4
                      ? Yup.number()
                          .required("Quality is required")
                          .min(1, "Quality must be greater than 0")
                          .max(10, "Quality cannot be more than 10")
                          .typeError("Quality should be a number")
                      : Yup.number().nullable(),
                  fromTime:
                    lcrTypee === 4
                      ? startTimeValidation.required("Start Time is required")
                      : Yup.string().nullable(),

                  toTime:
                    lcrTypee === 4
                      ? endTimeValidation.required("End Time is required")
                      : Yup.string().nullable(),
                })
              )
              .test(
                "unique-quality",
                "Each quality value must be unique",
                function (policyDetails, context) {
                  if (!Array.isArray(policyDetails) && lcrTypee === 4)
                    return true;
                  const qualityValues = policyDetails.map(
                    (item) => item.quality
                  );
                  const duplicates = qualityValues.filter(
                    (item, index) => qualityValues.indexOf(item) !== index
                  );
                  if (duplicates.length > 0 && lcrTypee !== 4) {
                    const lastDuplicate = duplicates[duplicates.length - 1];
                    const lastIndex = qualityValues.lastIndexOf(lastDuplicate);
                    return context.createError({
                      path: `${context.path}[${lastIndex}].quality`,
                      message: "Each quality value must be unique",
                    });
                  }
                  return true;
                }
              )
              .test(
                "total-percentage",
                "Total percentage must equal 100",
                function (policyDetails, context) {
                  const totalPercentage = policyDetails.reduce(
                    (acc, item) => acc + (item.percentage || 0),
                    0
                  );

                  if (totalPercentage !== 100 && lcrTypee !== 4) {
                    const lastIndex = policyDetails.length - 1;
                    return context.createError({
                      path: `${context.path}[${lastIndex}].percentage`,
                      message: "Total percentage must equal 100",
                    });
                  }

                  return true;
                }
              ),
            percentage:
              lcrTypee === 4
                ? Yup.string()
                    .required("Percentage is required")
                    .test(
                      "comma-separated-count",
                      "Number of percentage entered exceeds different suppliers entered",
                      function (value) {
                        const policyCount =
                          this.parent.policyDetails?.length || 0;
                        const percentageArray =
                          value?.split(",").map(Number) || [];
                        return percentageArray.length <= policyCount;
                      }
                    )
                    .test(
                      "valid-numbers",
                      "Each percentage should be a valid number between 0 and 100",
                      function (value) {
                        const percentageArray =
                          value?.split(",").map(Number) || [];
                        return percentageArray.every(
                          (percentage) =>
                            !isNaN(percentage) &&
                            percentage >= 0 &&
                            percentage <= 100
                        );
                      }
                    )
                    .test(
                      "total-100",
                      "Total percentage must equal 100",
                      function (value) {
                        const percentageArray =
                          value?.split(",").map(Number) || [];
                        const total = percentageArray.reduce(
                          (acc, curr) => acc + curr,
                          0
                        );
                        return total === 100;
                      }
                    )
                    .test(
                      "descending-order",
                      "Percentages must be in descending order",
                      function (value) {
                        const percentageArray =
                          value?.split(",").map(Number) || [];
                        for (let i = 0; i < percentageArray.length - 1; i++) {
                          if (percentageArray[i] < percentageArray[i + 1]) {
                            return false;
                          }
                        }
                        return true;
                      }
                    )
                : Yup.string().nullable(),

            destOpId: Yup.string().required("Destination Operator is required"),
          })
        ),
  });
};

const defaultPolicyDetails = {
  supplier: null,
  cost: null,
  percentage: null,
  position: null,
  fromTime: "",
  toTime: "",
  quality: null,
};

const defaultParentDetails = {
  shortCode: "",
  destOpId: "",
  region: "",
  mccMnc: "",
  percentage: "",
  policyDetails: [defaultPolicyDetails],
};

export const getInitialValues = (formData) => ({
  lcrName: formData?.lcrName || "",
  lcrType: formData?.lcrType || 0,
  trafType: formData?.trafType || 1,
  startDate: formData?.startDate || null,
  isReve: formData?.isReve || 0,
  reveLcrStatus: formData?.reveLcrStatus || 1,
  reveCusType: formData?.reveCusType || "1",
  dateReq: formData?.dateReq || 0,
  parentDetails: formData?.parentDetails?.map((policy) => ({
    shortCode: policy.shortCode || "",
    destOpId: policy.destOpId || "",
    region: policy.region || "",
    mccMnc: policy.mccMnc || "",
    percentage: policy.percentage || "",
    policyDetails: policy.policyDetails?.map((item) => ({
      ...defaultPolicyDetails,
      ...item,
    })) || [defaultPolicyDetails],
  })) || [defaultParentDetails],
});
