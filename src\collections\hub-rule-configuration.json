{"forms": [{"elements": [{"name": "rule_type", "size": 6, "type": "select", "title": "Rule type", "options": [{"label": "HUB Routing Rules", "value": 9}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select the rule type"}], "defaultValue": ""}, {"name": "rule", "size": 6, "type": "select", "title": "Rule", "options": [{"label": "LCR", "value": "LCR"}, {"label": "Supplier", "value": "Supplier"}, {"label": "AT LCR", "value": "AT LCR"}], "isMandatory": true, "validations": [{"type": "required", "message": "Please select rule"}], "defaultValue": "", "validationType": "string"}], "formName": "Basic"}, {"elements": [{"info": "Type of traffic.", "name": "traffic_type", "size": 6, "type": "radio", "title": "Traffic Type", "isInfo": true, "options": [{"label": "A2P", "value": "-1"}, {"label": "P2P", "value": "-2"}], "inputType": 46, "isMandatory": true, "validations": [{"type": "required", "message": "Required"}, {"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "defaultValue": "-1", "guiInputType": 1135, "visibilityConditions": {"rule": ["LCR", "Supplier"]}}, {"info": "Transmission mode type for hub rules", "name": "trans_mode", "size": 6, "type": "radio", "title": "Transmission Mode", "isInfo": true, "options": [{"label": "<PERSON><PERSON><PERSON>", "value": "2"}, {"label": "Transit", "value": "-1"}], "inputType": 46, "isMandatory": true, "validations": [{"type": "required", "message": "Required"}, {"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "defaultValue": "2", "guiInputType": 46, "visibilityConditions": {"rule": ["LCR", "Supplier", "AT LCR"]}}, {"info": "If subscriber is ported or not.", "name": "is_ported", "size": 6, "type": "radio", "title": "Ported Subscriber", "isInfo": true, "options": [{"label": "Ported", "value": "-1"}, {"label": "Normal", "value": "-2"}], "inputType": 46, "isMandatory": true, "validations": [{"type": "required", "message": "Required"}, {"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "defaultValue": "-1", "guiInputType": 1132, "visibilityConditions": {"rule": ["LCR", "Supplier"]}}, {"info": "Destination Subscriber is Roaming or Not Roaming", "name": "is_roaming", "size": 6, "type": "radio", "title": "Subscriber Status", "isInfo": true, "options": [{"label": "Roaming", "value": "1"}, {"label": "Non Roaming", "value": "2"}], "inputType": 46, "isMandatory": true, "validations": [{"type": "required", "message": "Required"}, {"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "defaultValue": "1", "guiInputType": 1137, "visibilityConditions": {"rule": ["LCR", "Supplier"]}}, {"info": "Origin Operator type", "name": "orig_op_type", "size": 6, "type": "select", "title": "Origin Operator Type", "isInfo": true, "options": [{"label": "Customer", "value": "0$1126"}, {"label": "Operator", "value": "-2$1124"}], "inputType": 0, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1133, "visibilityConditions": {"rule": ["LCR", "Supplier", "AT LCR"]}}, {"info": "Customer Account List", "name": "customer_acc", "size": 6, "type": "select", "title": "Customer Account", "isInfo": true, "inputType": 1126, "queryInfo": {"label": "name", "filters": [{"field": "operatorType", "value": "C"}, {"field": "interfaceType", "value": "SMPP"}], "moduleName": "customer-supplier-managements"}, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1126, "visibilityConditions": {"rule": ["LCR", "Supplier", "AT LCR"], "orig_op_type": ["0$1126"]}}, {"info": "Orig Cluster List", "name": "orig_cluster_list", "size": 6, "type": "select", "title": "Orig Cluster List", "isInfo": true, "inputType": 1124, "queryInfo": {"label": "clusterName", "fields": ["clusterName", "id"], "filters": [], "moduleName": "operator-clusters"}, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1124, "visibilityConditions": {"rule": ["LCR", "Supplier"], "orig_op_type": ["-2$1124"]}}, {"info": "Time Based settings", "name": "Time_Based_LCR", "size": 6, "type": "select", "title": "Time Based LCR", "isInfo": true, "inputType": 0, "queryInfo": {"label": "lcrName", "fields": ["lcrName", "id"], "filters": [{"field": "lcr_type", "value": "4"}], "moduleName": "time-based-lcrs"}, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1144, "visibilityConditions": {"rule": ["LCR"]}}, {"info": "SC LCR settings", "name": "SC_LCR_MT", "size": 6, "type": "select", "title": "SC LCR MT", "isInfo": true, "inputType": 0, "queryInfo": {"label": "lcrName", "fields": ["lcrName", "id"], "filters": [{"field": "lcrType", "value": 1}], "moduleName": "lcr-profiles"}, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1140, "visibilityConditions": {"rule": ["LCR"]}}, {"info": "SC LCR Type", "name": "SC_LCR_type", "size": 6, "type": "select", "title": "SC LCR MT Type", "isInfo": true, "options": [{"label": "Cost Based", "value": "-2"}, {"label": "Sequential", "value": "-3"}, {"label": "Quality Based", "value": "-6"}], "inputType": 0, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1141, "visibilityConditions": {"rule": ["LCR"]}}, {"info": "Spec Route settings", "name": "spec_route", "size": 6, "type": "select", "title": "Spec Route", "isInfo": true, "inputType": 0, "queryInfo": {"label": "lcrName", "fields": ["lcrName", "id"], "filters": [{"field": "lcrType", "value": 3}], "moduleName": "lcr-profiles"}, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1138, "visibilityConditions": {"rule": ["LCR"]}}, {"info": "Spec LCR Type", "name": "spec_lcr_type", "size": 6, "type": "select", "title": "Spec LCR Type", "isInfo": true, "options": [{"label": "Cost Based", "value": "-2"}, {"label": "Sequential", "value": "-3"}, {"label": "Quality Based", "value": "-6"}], "inputType": 0, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1139, "visibilityConditions": {"rule": ["LCR"]}}, {"info": "LCR Settings", "name": "lcr_profile", "size": 6, "type": "select", "title": "LCR", "isInfo": true, "inputType": 0, "queryInfo": {"label": "lcrName", "fields": ["lcrName", "id"], "filters": [{"field": "lcrType", "value": 0}], "moduleName": "lcr-profiles"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please select the LCR"}, {"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1130, "visibilityConditions": {"rule": ["LCR"]}}, {"info": "LCR Type", "name": "lcr_type", "size": 6, "type": "select", "title": "LCR Type", "isInfo": true, "options": [{"label": "Cost Based", "value": "-2"}, {"label": "Sequential", "value": "-3"}, {"label": "Quality Based", "value": "-6"}], "inputType": 0, "isMandatory": true, "validations": [{"type": "required", "message": "Please select the LCR Type"}, {"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1136, "visibilityConditions": {"rule": ["LCR", "AT LCR"]}}, {"info": "Dest Cluster List", "name": "dest_cluster_list", "size": 6, "type": "select", "title": "Dest Cluster List", "isInfo": true, "inputType": 1125, "queryInfo": {"label": "clusterName", "fields": ["clusterName", "id"], "filters": [], "moduleName": "operator-clusters"}, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1125, "visibilityConditions": {"rule": ["Supplier"]}}, {"info": "Bilateral Settings", "name": "bilateral", "size": 6, "type": "select", "title": "Suppliers", "isInfo": true, "inputType": 0, "queryInfo": {"label": "name", "fields": ["name", "id"], "moduleName": "supplier-path-lists"}, "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Suppliers"}, {"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1129, "visibilityConditions": {"rule": ["Supplier"]}}, {"info": "AT LCR setting", "name": "AT_LCR", "size": 6, "type": "select", "title": "AT LCR", "isInfo": true, "inputType": 0, "queryInfo": {"label": "lcrName", "fields": ["lcrName", "id"], "filters": [], "moduleName": "sc-at-lcrs"}, "isMandatory": false, "validations": [{"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1142, "visibilityConditions": {"rule": ["AT LCR"]}}, {"info": "Rule Status indicating active or not active", "name": "rule_status", "size": 6, "type": "select", "title": "Rule Status", "isInfo": true, "options": [{"label": "Active", "value": "1"}, {"label": "Inactive", "value": "2"}], "inputType": 1011, "isMandatory": true, "validations": [{"type": "required", "message": "Please select the Rule Status"}, {"type": "min", "value": 0, "message": "Minimum value allowed is 0"}], "guiInputType": 1011, "visibilityConditions": {"rule": ["LCR", "Supplier", "AT LCR"]}}, {"info": "Reason for the rule configured", "name": "rule_comment", "size": 6, "type": "text", "title": "Reason", "isInfo": true, "inputType": 1012, "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the Reason"}, {"type": "min", "value": 0, "message": "Minimum value allowed is 0"}, {"type": "max", "value": 128, "message": "Maximum value allowed is 128"}], "guiInputType": 1012, "visibilityConditions": {"rule": ["LCR", "Supplier", "AT LCR"]}}], "formName": "Advanced"}], "header": "List of hub rules", "columns": [{"header": "Rule ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "Customer name", "idToName": true, "nameFiled": "name", "moduleName": "customer-supplier-management", "accessorKey": "attributes.customerName", "attributeKey": "customer_acc"}, {"header": "Supplier/LCR", "idToName": true, "nameFiled": "name", "moduleName": "customer-supplier-management", "accessorKey": "attributes.supplier/Lcr", "attributeKey": "bilateral"}, {"header": "Supplier/LCR", "idToName": true, "nameFiled": "lcrName", "moduleName": "lcr-profile", "attributeKey": "lcr_profile"}, {"header": "Supplier/LCR", "idToName": true, "nameFiled": "lcrName", "moduleName": "sc-at-lcr", "attributeKey": "AT_LCR"}, {"header": "Traffic Type", "accessorKey": "attributes.traffic_type"}, {"header": "Transmission mode", "accessorKey": "attributes.trans_mode"}, {"header": "Subscriber Status", "accessorKey": "attributes.is_roaming"}, {"header": "Ported Subscriber", "accessorKey": "attributes.is_ported"}, {"header": "Status", "accessorKey": "attributes.rule_status"}, {"header": "Reason", "accessorKey": "attributes.rule_comment"}, {"buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "formType": "stepper", "buttonName": "+ Add Rule", "moduleData": "hub-rule-configuration", "moduleName": "Hub rule configuration", "globalSearch": [{"label": "Rule ID", "value": "id"}, {"label": "Reason", "value": "reason"}], "dropdownOptions": {"field": "rule", "options": [{"label": "ALL", "query": "", "value": "ALL"}, {"label": "LCR", "query": "filters[editInfoMaster][rule][$eq]=LCR", "value": "LCR"}, {"label": "Supplier", "query": "filters[editInfoMaster][rule][$eq]=Supplier", "value": "Supplier"}, {"label": "AT LCR", "query": "filters[editInfoMaster][rule][$eq]=AT LCR", "value": "AT LCR"}], "defaultQuery": "", "defaultValue": "ALL"}}