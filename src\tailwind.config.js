///This file is created to import the configurations of colors and styles in application
/** @type {import('tailwindcss').Config} */

module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  important: "#root",
  theme: {
    extend: {
      backgroundImage: {
        logo: "url('/src/icons/logo.svg')",
      },
      fontSize: {
        small: 10,
        xs: 12,
        medium: 15,
        subtitle: 16,
        title: 24,
        xm: 42,
      },
      backgroundColor: {
        bgSideBar: "#121313",
        bgActive: "#2D3446",
        bgTable: "#DDE9FD",
        bgPrimary: "#FFFFFF",
        bgSecondary: "#E31937",
        bgLeftNavHover: "#3576EB",
        bgBody: "#F0F2F1",
        bgTableFilter: "#F9FAFB",
        bgSwitch: "#e0dddd",
        bgHeader: "#DDE9FD",
        bgBox: "#374150",
        bgField: "#F3F3F3",
        bgSubNavActive: "#3741501A",
        bgSelectOption: "#D9D9D9",
        bgStatusDown: "#F6E3E2",
        bgStatusUp: "#E7FAF4",
        bgAboutUs: "#374150",
        bgLCRTable: "#37415033",
        bgLogin: "#DC3833",
      },
      borderColor: {
        tableBorder: "#808080",
        tableFilter: "#F4F5F6",
        buttonOutline: "#000000",
        uploadBorer: "#38693C",
        browseFilesBorder: "#1C70FA",
        browseFilesNameBorder: "#E7E7E7",
        outerBorder: "#808080",
        subNavBorder: "#9C9898",
        viewBorder: "#3576EB",
        borderIndicatorLine: "#E8E4E4",
        location: "#E20010",
        locationBorder: "#374150",
        homePageBorder: "#BEBEBE",
        errorBorder: "#DC3833",
      },
      textColor: {
        headingColor: "#666666",
        titleColor: "#111111",
        errorColor: "#DC3833",
        linkColor: "#1C70FA",
        textNAColor: "#808080",
        tabColor: "#9C9898",
        locationColor: "#E20010",
        statusDown: "#E31937",
        statusUp: "#4D9D75",
        headerColor: "#000000",
        mildGray: "#BEBEBE",
        multiSelectColor: "#D9D9D9",
      },
      textDecorationColor: {
        customColor: "#DC3833",
      },
      borderWidth: {
        location: "2px",
      },
      fontWeight: {
        sideBar: 600,
        fields: 500,
      },
      borderLeft: {
        activeLeftBorder: " 3px solid  #EC5958",
      },
      borderRadius: {
        fieldRadius: "10px",
        filterRadius: "20px",
      },
      minHeight: {
        lcrPolicyheight: "34px",
      },
    },
  },
  plugins: [],
};
