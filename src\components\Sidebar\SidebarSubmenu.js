import React, { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import * as Icons from "../../icons";
import { CssTooltip } from "../StyledComponent";
import { useTranslation } from "react-i18next";
import Zoom from "@mui/material/Zoom";
import { RightArrowIcon, DownArrowIcon } from "../../icons";
import theme from "../../tailwind-theme";

function Icon({ icon, ...props }) {
  const Icon = Icons[icon];
  return <Icon {...props} />;
}

function SidebarSubmenu({
  route,
  style,
  toggeled,
  setToggle,
  setSideBarOpen,
  handleMenuClick,
}) {
  const [isDropdownMenuOpen, setIsDropdownMenuOpen] = useState(false);
  const { t } = useTranslation();
  const [activeSubMenu, setActiveSubMenu] = useState(true);

  function handleDropdownMenuClick() {
    setIsDropdownMenuOpen(!isDropdownMenuOpen);
    setToggle(true);
    setSideBarOpen(true);
  }

  const location = useLocation();

  const isAnySubMenuActive = route.routes.some((r) =>
    location.pathname.startsWith(r.path)
  );

  return (
    <li className={`relative text-white mb-3`} key={route.name}>
      <div
        onClick={handleDropdownMenuClick}
        className={`inline-flex items-center w-full text-sm text-white transition-colors pl-7 duration-150 p-3 cursor-pointer`}
      >
        <div className={`flex items-start gap-5`}>
          <CssTooltip
            title={!toggeled ? t(route.name) : ""}
            placement="right"
            TransitionComponent={Zoom}
          >
            <div>
              <Icon
                className="w-5 h-4"
                style={{
                  fill: isAnySubMenuActive
                    ? theme.textColor.errorColor
                    : "white",
                }}
                aria-hidden="true"
                icon={route?.icon ? route.icon : ""}
              />
            </div>
          </CssTooltip>
          <div style={style} className="text-xs font-semibold">
            {t(route.name)}
          </div>
          {isDropdownMenuOpen ? (
            <DownArrowIcon className="my-auto" />
          ) : (
            <RightArrowIcon className="my-auto" />
          )}
        </div>
      </div>
      {isDropdownMenuOpen && toggeled ? (
        <>
          {route.routes.map((r) =>
            r.show ? (
              <NavLink
                className="inline-flex items-center w-full text-xs pl-[74px] text-white transition-colors duration-150 p-2"
                key={r.path}
                to={r.path}
                style={({ isActive }) =>
                  isActive
                    ? {
                        backgroundColor: "#2D3446",
                        color: theme.textColor.errorColor,
                        fontWeight: 600,
                        borderLeft: "3px solid  #EC5958",
                      }
                    : { backgroundColor: "transparent" }
                }
                onClick={handleMenuClick}
              >
                {t(r.name)}
              </NavLink>
            ) : null
          )}
        </>
      ) : null}
    </li>
  );
}

export default SidebarSubmenu;
