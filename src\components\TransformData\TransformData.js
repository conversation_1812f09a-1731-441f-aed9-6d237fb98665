import {
  billingLogic,
  billingType,
  ocCompliance,
  operatorType,
  PortedSubscriber,
  protocolType,
  ruleStatus,
  SubscriberStatus,
  trafficType,
  transmissonMode,
} from "../../common/constants";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

export const TransformData = (
  listData,
  schemaElements,
  stepFormDetails,
  collectionName
) => {
  if (!Array.isArray(listData)) {
    listData = [];
  }

  return listData.map((item) => {
    const newItem = { ...item };

    // Relation Type
    if (item?.attributes?.operatorType === operatorType.Supplier) {
      newItem.attributes.operatorType = "Supplier";
    }
    if (item?.attributes?.operatorType === operatorType.Both) {
      newItem.attributes.operatorType = "Customer & Supplier";
    }
    if (item?.attributes?.operatorType === operatorType.Customer) {
      newItem.attributes.operatorType = "Customer";
    }

    // Hub flag
    if (newItem.attributes?.hubFlag === ocCompliance.hubFlag) {
      newItem.attributes.hubFlag = "Yes";
    }
    if (newItem.attributes?.hubFlag === ocCompliance.hubFlagNo) {
      newItem.attributes.hubFlag = "No";
    }
    // Billing Type
    const billingInfo = newItem?.attributes || {};
    if (
      billingInfo.billingType === billingType.Commercial ||
      newItem?.attributes?.billingInformation?.billingType ===
        billingType.Commercial
    ) {
      billingInfo.billingType = "Commercial";
    }
    if (
      billingInfo.billingType === billingType["Non-commercial"] ||
      newItem?.attributes?.billingInformation?.billingType ===
        billingType["Non-commercial"]
    ) {
      billingInfo.billingType = "Non-Commercial";
    }

    // Billing Logic
    if (
      billingInfo.billingLogic === billingLogic.Submission ||
      newItem?.attributes?.billingInformation?.billingLogic ===
        billingLogic.Submission
    ) {
      billingInfo.billingLogic = "Submission";
    }
    if (
      billingInfo.billingLogic === billingLogic.Delivery ||
      newItem?.attributes?.billingInformation?.billingLogic ===
        billingLogic.Delivery
    ) {
      billingInfo.billingLogic = "Delivery";
    }
    if (
      billingInfo.billingLogic === billingLogic["Successful termination"] ||
      newItem?.attributes?.billingInformation?.billingLogic ===
        billingLogic["Successful termination"]
    ) {
      billingInfo.billingLogic = "Successful termination";
    }

    if (newItem.attributes?.hubFlag === "0") {
      billingInfo.billingLogic = "NO";
    }
    if (newItem.attributes?.ECPaaS === "0") {
      newItem.attributes.ECPaaS = "No";
    }
    if (newItem.attributes?.ECPaaS === "1") {
      newItem.attributes.ECPaaS = "Yes";
    }
    //redirectional-acc
    if (newItem?.attributes?.connType === "H") {
      newItem.attributes.connType = "HTTP";
    }
    if (newItem?.attributes?.connType === "S") {
      newItem.attributes.connType = "SMPP";
    }
    if (newItem?.attributes?.connType === "M") {
      newItem.attributes.connType = "SS7";
    }
    //PATH module billing logic
    if (newItem?.attributes?.billingLogic === "T") {
      newItem.attributes.billingLogic = "Successful termination";
    }
    if (newItem?.attributes?.billingLogic === "S") {
      newItem.attributes.billingLogic = "Submission";
    }
    if (newItem?.attributes?.billingLogic === "D") {
      newItem.attributes.billingLogic = "Delivery";
    }
    //Channel-partner
    if (newItem?.attributes?.commissionType === 1) {
      newItem.attributes.commissionType = "Gross revenue";
    }
    if (newItem?.attributes?.commissionType === 2) {
      newItem.attributes.commissionType = "Net revenue";
    }
    if (newItem?.attributes?.commissionType === 3) {
      newItem.attributes.commissionType = "Savings";
    }

    // PATH flag
    if (item?.attributes?.ocComplianceFlag !== undefined) {
      newItem.attributes.ocComplianceFlag =
        newItem.attributes.ocComplianceFlag === 0 ||
        newItem.attributes.ocComplianceFlag === "No"
          ? "No"
          : "Yes";
    }

    //PATH interfacetype

    if (newItem?.attributes?.interfaceType === 1) {
      newItem.attributes.interfaceType = "SS7";
    }
    if (newItem?.attributes?.interfaceType === 2) {
      newItem.attributes.interfaceType = "SMPP";
    }
    if (newItem?.attributes?.interfaceType === 3) {
      newItem.attributes.interfaceType = "SMPP ES";
    }
    if (newItem?.attributes?.interfaceType === 4) {
      newItem.attributes.interfaceType = "HTTP";
    }
    //ESME
    if (newItem?.attributes?.protocol === 5) {
      newItem.attributes.protocol = "SMPP";
    }
    if (newItem?.attributes?.protocol === 12) {
      newItem.attributes.protocol = "HTTP";
    }

    // Protocol Type
    if (item?.attributes?.protocolType === protocolType.A2P) {
      newItem.attributes.protocolType = "A2P";
    }
    if (item?.attributes?.protocolType === protocolType.P2P) {
      newItem.attributes.protocolType = "P2P";
    }
    if (item?.attributes?.protocolType === protocolType["A2P / P2P"]) {
      newItem.attributes.protocolType = "A2P / P2P";
    }

    // Relation Type
    if (item?.attributes?.relationType === operatorType.Supplier) {
      newItem.attributes.relationType = "Supplier";
    }

    // Traffic Type
    if (item?.attributes?.traffic_type === trafficType.A2P) {
      newItem.attributes.traffic_type = "A2P";
    }
    if (item?.attributes?.traffic_type === trafficType.P2P) {
      newItem.attributes.traffic_type = "P2P";
    }

    // Transmission Mode
    if (item?.attributes?.trans_mode === transmissonMode.Buffer) {
      newItem.attributes.trans_mode = "Buffer";
    }
    if (item?.attributes?.trans_mode === transmissonMode.Transit) {
      newItem.attributes.trans_mode = "Transit";
    }

    // Subscriber Status
    if (item?.attributes?.is_roaming === SubscriberStatus.Roaming) {
      newItem.attributes.is_roaming = "Roaming";
    }
    if (item?.attributes?.is_roaming === SubscriberStatus["Non-Roaming"]) {
      newItem.attributes.is_roaming = "Non-Roaming";
    }

    // Ported Subscriber
    if (item?.attributes?.is_ported === PortedSubscriber.Ported) {
      newItem.attributes.is_ported = "Ported";
    }
    if (item?.attributes?.is_ported === PortedSubscriber.Normal) {
      newItem.attributes.is_ported = "Normal";
    }

    // Ported Subscriber
    if (item?.attributes?.rule_status === ruleStatus.Active) {
      newItem.attributes.rule_status = "Active";
    }
    if (item?.attributes?.rule_status === ruleStatus.Inactive) {
      newItem.attributes.rule_status = "Inactive";
    }

    // Update isReve value to "Yes" or "No"
    if (newItem.attributes?.isReve)
      newItem.attributes.isReve = item?.attributes?.isReve === 1 ? "Yes" : "No";

    // Process schemaElements if present
    if (stepFormDetails?.forms) {
      stepFormDetails.forms.forEach((form) => {
        if (Array.isArray(form.elements)) {
          form.elements.forEach((element) => {
            if (
              (element.type === "select" ||
                element.type === "radio" ||
                element.type === "switch") &&
              Array.isArray(element.options)
            ) {
              const selectedOption = element.options.find((option) => {
                const itemValue = String(item?.attributes[element.name]).trim();
                const optionValue = String(option.value).trim();
                return optionValue === itemValue;
              });
              newItem.attributes[element.name] = selectedOption
                ? selectedOption.label
                : item.attributes[element.name];
            }
          });
        }
      });
    }
    if (Array.isArray(schemaElements) && schemaElements.length > 0) {
      schemaElements.forEach((element) => {
        if (
          (element.type === "select" ||
            element.type === "radio" ||
            element.type === "switch") &&
          Array.isArray(element.options)
        ) {
          const selectedOption = element.options.find((option) => {
            const itemValue = String(item?.attributes[element.name]).trim();
            const optionValue = String(option.value).trim();
            return optionValue === itemValue;
          });
          newItem.attributes[element.name] = selectedOption
            ? selectedOption.label
            : item.attributes[element.name];
        }
      });
    }

    //Transaction type in credit transaction
    if (item?.attributes?.transactionType) {
      if (Array.isArray(schemaElements) && schemaElements.length > 0) {
        schemaElements.forEach((element) => {
          if (
            element.name === "transactionType" &&
            Array.isArray(element.options)
          ) {
            const match = element.options.find(
              (option) => option.value === item.attributes.transactionType
            );
            if (match) {
              newItem.attributes.transactionType = match.label;
            }
          }
        });
      }
    }

    //Currency type in credit transaction
    if (item?.attributes?.transactionCurrency) {
      if (Array.isArray(schemaElements) && schemaElements.length > 0) {
        schemaElements.forEach((element) => {
          if (
            element.name === "transactionCurrency" &&
            Array.isArray(element.options)
          ) {
            const match = element.options.find(
              (option) => option.value === item.attributes.transactionCurrency
            );
            if (match) {
              newItem.attributes.transactionCurrency = match.label;
            }
          }
        });
      }
    }

    // Session Type for session management
    if (item?.attributes?.sessType) {
      if (Array.isArray(schemaElements) && schemaElements.length > 0) {
        schemaElements.forEach((element) => {
          if (element.name === "sessType" && Array.isArray(element.options)) {
            const match = element.options.find(
              (option) => option.value === item.attributes.sessType
            );
            if (match) {
              newItem.attributes.sessType = match.label;
            }
          }
        });
      }
    }

    //Session status for session management
    if (
      collectionName === "node-sessions" ||
      collectionName === "smsc-sessions"
    ) {
      if (Array.isArray(schemaElements) && schemaElements.length > 0) {
        schemaElements.forEach((element) => {
          if (element.name === "status" && Array.isArray(element.options)) {
            const match = element.options.find(
              (option) =>
                option.value.toLowerCase() ===
                item.attributes.status.toLowerCase()
            );
            if (match) {
              newItem.attributes.status = match.label;
            }
          }
        });
      }
    }

    dayjs.extend(utc);

    if (stepFormDetails?.columns) {
      stepFormDetails?.columns.forEach((column) => {
        if (column?.isDateTime) {
          const dateTimeFormat =
            column?.dateTimeFormat || "YYYY-MM-DD HH:mm:ss";
          newItem.attributes.bindTime = dayjs
            .utc(item?.attributes?.bindTime)
            .format(dateTimeFormat);

          // return dayjs.utc(item?.attributes?.bindTime).format(dateTimeFormat);
        }
      });
    }

    return newItem;
  });
};
