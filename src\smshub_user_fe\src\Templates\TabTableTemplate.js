import React, { useState, useEffect, useContext } from "react";
import {
  Box,
  Typography,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  CircularProgress,
  Button,
} from "@mui/material";
import CustomTab from "./CustomTab";
//import roleData from "../collections/role.json";
import { createRole } from "../lib/role-management-api.js";
import SuccessDialog from "../../../components/Dialog/SuccessDialog";
import ConfirmNNextButton from "../components/Buttons/Button";
import CancelButton from "../components/Buttons/OutlinedButton";
// import { getRoleRecord } from "../../../lib/list-api";
import { Checkbox } from "@mui/material";
import DashboardPanelComponent from "./DashboardPanelCell.js";
import { multiStepFormUserRoleContext } from "../context/UserRoleContext.js";

function TabTableTemplate({ roleData, paramsModule }) {
  const [currentTab, setCurrentTab] = useState(0);
  const [roleResp, setRoleResp] = useState(null);
  const [initialRoleResp, setInitialRoleResp] = useState(null); // Store the initial state
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [successDialog, setSuccessDialog] = useState(false);
  const [message, setMessage] = useState("");
  const { currentStep, stepCount } = useContext(multiStepFormUserRoleContext);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        // const response = await getRoleRecord({ paramsModule: paramsModule });
        // const response = [];
        // const responseData = response?.data?.data;
        const responseData = [
          {
            name: "reportManagement",
            permissions: {
              create: 1,
              view: 1,
              update: 1,
              delete: 1,
              download: 0,
            },
          },
          {
            name: "cardManagement",
            permissions: {
              create: 1,
              view: 1,
              update: 1,
              delete: 1,
              download: 0,
            },
          },
          {
            name: "roleManagement",
            permissions: {
              create: 1,
              view: 1,
              update: 1,
              delete: 1,
              download: 0,
            },
          },
          {
            name: "panelManagement",
            permissions: {
              create: 1,
              view: 1,
              update: 1,
              delete: 1,
              download: 0,
            },
          },
          {
            name: "dashboardManagement",
            permissions: {
              create: 1,
              view: 1,
              update: 1,
              delete: 1,
              download: 0,
            },
          },
          {
            name: "logsManagement",
            permissions: {
              create: 1,
              view: 1,
              update: 1,
              delete: 1,
              download: 0,
            },
          },
        ];
        if (responseData && typeof responseData === "object") {
          setRoleResp(responseData);
          setInitialRoleResp(responseData);
        } else {
          setRoleResp({});
          setInitialRoleResp({});
        }
      } catch (err) {
        setError(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleTabChange = (event, newTab) => {
    setCurrentTab(newTab);
  };

  const getSwitchState = (rowName, columnKey) => {
    // Find the object with the matching name
    const rowData = roleResp.find((item) => item.name === rowName);

    if (!rowData) return false; // If no matching row, return false

    const value = rowData.permissions?.[columnKey.split(".")[1]];

    if (value === undefined) return false;
    if (value === 2) return "disabled";
    return value === 1;
  };

  const [selectAll, setSelectAll] = useState(false);
  const handleSwitchChange = (elementName, columnKey, isSelectAll = false) => {
    const newRoleResp = [...roleResp]; // Ensure we work with a new reference

    if (isSelectAll) {
      // Toggle selectAll state
      const newSelectAllState = !selectAll;
      setSelectAll(newSelectAllState);

      // Iterate through newRoleResp and update permissions
      newRoleResp.forEach((item) => {
        Object.keys(item.permissions).forEach((key) => {
          if (item.permissions[key] !== 2) {
            item.permissions[key] = newSelectAllState ? 1 : 0;
          }
        });
      });
    } else {
      // Handle individual switch toggle
      setSelectAll(false);

      const fieldKey = columnKey.split(".")[1];

      // Find the existing item by name
      const existingItemIndex = newRoleResp.findIndex(
        (item) => item.name === elementName
      );

      if (existingItemIndex !== -1) {
        // Update existing item
        if (newRoleResp[existingItemIndex].permissions[fieldKey] !== 2) {
          newRoleResp[existingItemIndex].permissions[fieldKey] =
            newRoleResp[existingItemIndex].permissions[fieldKey] === 1 ? 0 : 1;
        }
      } else {
        // Add a new entry if it doesn't exist
        newRoleResp.push({
          name: elementName,
          permissions: { [fieldKey]: 1 },
        });
      }
    }

    setRoleResp(newRoleResp);
  };

  const handleSave = async () => {
    try {
      const response = await createRole({
        paramsModule: paramsModule,
        reqData: roleResp,
      });
      if (response) {
        setSuccessDialog(true);
        setMessage(`role Parameters updated successfully`);
      }
    } catch (err) {
      alert("Error saving data");
    }
  };

  const handleCancel = async () => {
    // Revert the changes by resetting to the initial state
    setRoleResp(initialRoleResp);
  };

  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="100vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Typography variant="h6">Error: {error.message}</Typography>;
  }

  if (Object.keys(roleResp).length === 0) {
    return <Typography variant="h6">No data available</Typography>;
  }
  const currentTabElements =
    roleData?.forms?.[1]?.tabs[currentTab]?.elements || [];

  const allOperations = roleData?.forms?.[currentStep]?.operations;

  const rowSelected =
    roleData?.forms?.[currentStep]?.tabs[currentTab]?.rowSelected;

  const isRowSelected = (name) => {
    const rowElement = roleData?.forms?.[currentStep]?.tabs?.[
      currentTab
    ]?.data?.find((el) => el.name === name);

    if (!rowElement) return false;

    return allOperations.every((operation) => rowElement[operation]);
  };

  const handleRowSelect = (name) => {
    setRoleResp((prevData) => {
      return {
        ...prevData,
        forms: prevData.forms.map((form, fIndex) => ({
          ...form,
          tabs: form.tabs.map((tab, tIndex) => {
            if (fIndex === currentStep && tIndex === currentTab) {
              return {
                ...tab,
                data: tab.data.map((el) =>
                  el.name === name
                    ? {
                        ...el,
                        ...Object.fromEntries(
                          allOperations.map((op) => [op, !isRowSelected(name)])
                        ),
                      }
                    : el
                ),
              };
            }
            return tab;
          }),
        })),
      };
    });
  };

  return (
    <div className="w-full">
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "8px",
        }}
      >
        <CustomTab
          defaultTab={currentTab}
          tabs={roleData?.forms?.[1]?.tabs.map((tab) => tab.tabName)}
          onChange={handleTabChange}
          selectedColor="#F04E24"
        />

        <div style={{ marginTop: "30px" }}>
          <Box
            sx={{
              border: "1px solid #dedede",
              borderRadius: "8px",
              background: "white",
              maxWidth: "full",
            }}
          >
            <Table>
              <TableHead
                sx={{
                  backgroundColor: "#E7F1FD",
                  "& .MuiTableCell-root": {
                    fontWeight: "bold",
                    textAlign: "left",
                  },
                }}
              >
                <TableRow>
                  {roleData?.forms?.[currentStep]?.tabs?.[
                    currentTab
                  ]?.columns.map((column) =>
                    column.accessorKey === "attributes.selectAll" ? (
                      <TableCell key="selectAll" sx={{ textAlign: "center" }}>
                        {column.header}
                        <Switch
                          checked={selectAll}
                          onChange={() =>
                            handleSwitchChange(
                              null,
                              null,
                              <TableCell
                                key="selectAll"
                                sx={{ textAlign: "center" }}
                              >
                                {column.header}
                                <Switch
                                  checked={selectAll}
                                  onChange={() =>
                                    handleSwitchChange(null, null, !selectAll)
                                  } // Toggle the state
                                />
                              </TableCell>
                            )
                          }
                        />
                      </TableCell>
                    ) : (
                      <TableCell
                        key={column.accessorKey}
                        sx={{
                          "&.MuiTableCell-root": {
                            textAlign: "center",
                          },
                        }}
                      >
                        {column.header}
                      </TableCell>
                    )
                  )}
                </TableRow>
              </TableHead>
              <TableBody>
                {currentTabElements.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={
                        roleData?.forms?.[currentStep]?.tabs?.[currentTab]
                          ?.columns.length
                      }
                      sx={{
                        textAlign: "center",
                        color: "#808080",
                      }}
                    >
                      No data available for this tab.
                    </TableCell>
                  </TableRow>
                ) : (
                  currentTabElements.map((element) => (
                    <TableRow key={element.name}>
                      {rowSelected && (
                        <TableCell
                          sx={{
                            textAlign: "center",
                          }}
                        >
                          <Checkbox
                            sx={{
                              transform: "scale(0.8)",
                              padding: "4px",
                            }}
                            checked={isRowSelected(element.name)}
                            onChange={() => handleRowSelect(element.name)}
                          />
                        </TableCell>
                      )}
                      <TableCell
                        sx={{
                          textAlign: "left",
                        }}
                      >
                        {element.title}
                      </TableCell>
                      {roleData?.forms?.[currentStep]?.tabs?.[
                        currentTab
                      ]?.columns
                        .slice(1)
                        .map((column) => {
                          const columnKey = column.accessorKey;
                          const switchState = getSwitchState(
                            element.name,
                            columnKey
                          );

                          const isOperationAllowed = allOperations?.some(
                            (operation) =>
                              element[operation] &&
                              columnKey === `attributes.${operation}`
                          );

                          return column.accessorKey ===
                            "attributes.selectAll" ? (
                            <TableCell>
                              {element.noOfDashboard ? (
                                <DashboardPanelComponent
                                  element={element.dashboardCount}
                                  maxLimit={element.maxLimit}
                                />
                              ) : element.noOfPanels ? (
                                <DashboardPanelComponent
                                  element={element.panelCount}
                                  maxLimit={element.maxLimit}
                                />
                              ) : (
                                ""
                              )}
                            </TableCell>
                          ) : (
                            <TableCell
                              key={columnKey}
                              sx={{ textAlign: "center" }}
                            >
                              {/* Condition to show the checkbox based on rules */}
                              {(currentTab !== 0 ||
                                element.allOperation ||
                                isOperationAllowed) && (
                                <Checkbox
                                  sx={{
                                    transform: "scale(0.8)",
                                    padding: "4px",
                                  }}
                                  checked={switchState === true}
                                  onChange={() =>
                                    handleSwitchChange(element.name, columnKey)
                                  }
                                />
                              )}
                            </TableCell>
                          );
                        })}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </Box>
        </div>
      </div>
    </div>
  );
}

export default TabTableTemplate;
