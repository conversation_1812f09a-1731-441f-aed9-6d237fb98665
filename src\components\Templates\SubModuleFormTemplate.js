import { Formik, Form } from "formik";
import Grid from "@mui/material/Grid";
import Button from "../Buttons/OutlinedButton";
import ConfirmNNextButton from "../Buttons/Button";
import * as Yup from "yup";
import { useMutation, useQuery } from "react-query";
import { createRecord, getListData } from "../../lib/list-api";
import SuccessDialog from "../Dialog/SuccessDialog";
import ErrorDialog from "../Dialog/ErrorDialog";
import { useLocation, useNavigate } from "react-router-dom";
import ManagementPayload from "../../payloadDetails/ManagementPayload";
import { useContext, useState } from "react";
import {
  generateValidationSchema,
  getDefaultInitialValues,
  renderFields,
} from "../../common/commonService";
import { handleSubmitWithFocus } from "./SubmitWithFocus";
import { DropdownContext } from "../../context/DropDownContext";

const SubModuleFormTemplate = (props) => {
  const {
    formTitle,
    elements,
    handleCancel,
    moduleName,
    action,
    id,
    categoryDetails,
    isServiceManagement,
    selectedForm,
  } = props;

  const { mutate: createRecordAPI, isLoading: createLoading } =
    useMutation(createRecord);

  const [successDialog, setSuccessDialog] = useState(false);
  const [errorDialog, setErrorDialog] = useState(false);
  const [message, setMessage] = useState("");
  const [formData, setFormData] = useState({});

  const navigate = useNavigate();
  const location = useLocation();
  const { errorDetails } = useContext(DropdownContext);

  const hasDuplicatesAcrossFields = (values) => {
    const { dummy_gt = [], a2p_gt = [], p2p_gt = [] } = values;
    const checkDuplicates = (arr1, arr2, message) => {
      if (arr1.some((value) => arr2.includes(value.trim()))) {
        setErrorDialog(true);
        setMessage(message);
        return true;
      }
      return false;
    };

    if (
      checkDuplicates(
        dummy_gt,
        a2p_gt,
        "Duplicate entries in Dummy GT and A2P GT"
      )
    ) {
      return true;
    }
    if (
      checkDuplicates(
        dummy_gt,
        p2p_gt,
        "Duplicate entries in Dummy GT and P2P GT"
      )
    ) {
      return true;
    }
    if (
      checkDuplicates(a2p_gt, p2p_gt, "Duplicate entries in A2P GT and P2P GT")
    ) {
      return true;
    }
    return false;
  };

  const handleSubmit = (values) => {
    if (hasDuplicatesAcrossFields(values)) {
      return;
    }
    // const fieldNameMapping = {
    //   A2P_cust_id: "A2P Customer ID",
    //   P2P_cust_id: "P2P Customer ID",
    //   ATI_supp_id: "Supplier ID",
    // };
    // const missingMappings = errorDetails.map(
    //   (error) => fieldNameMapping[error.fieldName] || error.fieldName
    // );

    // if (missingMappings.length > 0) {
    //   setMessage(
    //     `The following IDs are not mapped correctly: ${missingMappings.join(
    //       ", "
    //     )}. Please check and update.`
    //   );
    //   setErrorDialog(true);
    //   return;
    // }

    let reqData = ManagementPayload(values, selectedForm, formTitle);
    const missingFormTitles = [];
    let isFormCreated = true;

    categoryDetails.forEach((category) => {
      const { formTitle: categoryTitle, formElements } = category;

      const isAnyElementMissing = formElements.every(
        (element) => !(element.name in formData)
      );

      if (categoryTitle === formTitle && !isAnyElementMissing) {
        isFormCreated = false;
      }

      if (isAnyElementMissing) {
        missingFormTitles.push(categoryTitle);
      }
    });

    const missingTitles = [...missingFormTitles];
    const otherTitles = missingTitles.filter((title) => title !== formTitle);

    createRecordAPI(
      { moduleName, reqData },
      {
        onSuccess: (resp) => {
          setSuccessDialog(true);
          refetchData();
          setMessage(
            otherTitles.length > 0
              ? `${formTitle} ${
                  isFormCreated ? "created" : "updated"
                } successfully. Kindly configure the details for ${otherTitles.join(
                  ","
                )}`
              : `${formTitle} ${
                  isFormCreated ? "created" : "updated"
                } successfully.`
          );
        },
        onError: ({ response }) => {
          setErrorDialog(true);
          setMessage(response?.data?.error?.message);
        },
      }
    );
  };

  const { refetch: refetchData } = useQuery(
    ["service-management", "service-managements"],
    getListData,
    {
      enabled: isServiceManagement && !id > 0 ? true : false,
      onSuccess: ({ data }) => {
        setFormData(data?.data);
      },
    }
  );

  return (
    <div>
      <Formik
        initialValues={getDefaultInitialValues(elements, formData)}
        validationSchema={Yup.object().shape(
          generateValidationSchema(elements)
        )}
        onSubmit={(values) => handleSubmit(values)}
        enableReinitialize
      >
        {({
          values,
          submitForm,
          setFieldValue,
          validateForm,
          setTouched,
          errors,
        }) => (
          <Form>
            <fieldset className="border border-gray-300 p-1 m-0 md:p-10 md:m-8 rounded-md">
              <legend className="text-sm">{formTitle}</legend>

              <Grid container spacing={4}>
                {renderFields(
                  elements,
                  values,
                  setFieldValue,
                  navigate,
                  formData,
                  action,
                  location
                )}
              </Grid>
            </fieldset>
            <div className="flex justify-center gap-5 pt-5 pb-10">
              <Button
                label={"Cancel"}
                buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
                onClick={() => handleCancel()}
              />

              <ConfirmNNextButton
                label={"Save"}
                buttonClassName="w-full md:w-[150px] h-[40px] text-sm ml-5"
                onClick={() => {
                  handleSubmitWithFocus(validateForm, submitForm, setTouched);
                }}
                loading={createLoading}
              />
            </div>
            <SuccessDialog
              show={successDialog}
              onHide={() => {
                refetchData();
                setSuccessDialog(false);
              }}
              message={message}
            />
            <ErrorDialog
              show={errorDialog}
              onHide={() => {
                setErrorDialog(false);
              }}
              message={message}
            />
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default SubModuleFormTemplate;
