import React, { createContext, useState } from "react";

export const DropdownContext = createContext();

export const DropdownProvider = ({ children }) => {
  const [selectedData, setSelectedData] = useState(null);
  const [errorDetails, setErrorDetails] = useState([]);
  return (
    <DropdownContext.Provider
      value={{ selectedData, setSelectedData, errorDetails, setErrorDetails }}
    >
      {children}
    </DropdownContext.Provider>
  );
};
