import React from "react";
import Switch from "@mui/material/Switch";
import FormControlLabel from "@mui/material/FormControlLabel";
import theme from "../../tailwind-theme";
import { useField, useFormikContext } from "formik";

const CustomSwitchHttp = ({
  name,
  label,
  disabled,
  isDisabled,
  marginLeft,
  generalInfo,
  onChange,
  checked,
  cpassStatus,
}) => {
  const [field, meta] = useField(name);
  const { setFieldValue, setFieldTouched } = useFormikContext();
  const switchStyles = {
    thumb: {
      width: "19px",
      height: "19px",
      borderRadius: "50%",
      boxShadow: "none",
      backgroundColor: theme.backgroundColor.bgSwitch,
      border: "1px solid #7070708C",
    },
    track: {
      width: "33px",
      height: "14px",
      color:
        field.value === "Y" ||
        field.value === 1 ||
        field.value === "1" ||
        checked?.attributes?.status === 1
          ? "#3576eb"
          : "#e0dddd",
      opacity:
        field.value === "Y" ||
        field.value === 1 ||
        field.value === "1" ||
        checked?.attributes?.status === 1
          ? "1.5 !important"
          : "0.5",
      border: "1px solid black",
      cursor: isDisabled ? " " : "pointer",
    },
  };
  return (
    <FormControlLabel
      control={
        <Switch
          name={name}
          checked={
            field.value === "Y" ||
            field.value === 1 ||
            field.value === "1" ||
            checked?.attributes?.status === 1
          }
          onChange={
            !isDisabled
              ? (e) => {
                  const newValue = generalInfo
                    ? e.target.checked
                      ? "Y"
                      : "N"
                    : cpassStatus
                    ? e.target.checked
                      ? "1"
                      : "0"
                    : e.target.checked
                    ? 1
                    : 0;
                  setFieldValue(name, newValue);
                  setFieldTouched(name, true);
                  if (onChange) {
                    onChange(newValue);
                  }
                }
              : ""
          }
          disabled={disabled}
          className={marginLeft ? "ml-5" : ""}
          sx={{
            "& .MuiSwitch-thumb": switchStyles.thumb,
            "& .MuiSwitch-track": switchStyles.track,
          }}
        />
      }
      label={label}
    />
  );
};

export default CustomSwitchHttp;
