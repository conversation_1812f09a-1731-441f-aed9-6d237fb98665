{"header": "List of customers & suppliers", "columns": [{"header": "Id", "accessorKey": "id"}, {"header": "Name", "accessorKey": "attributes.name"}, {"header": "Relation type", "accessorKey": "attributes.operatorType"}, {"header": "HUB", "accessorKey": "attributes.hubFlag"}, {"header": "Billing type", "accessorKey": "attributes.billingType"}, {"header": "Billing Logic", "accessorKey": "attributes.billingLogic"}, {"header": "Protocol type", "accessorKey": "attributes.protocolType"}, {"header": "Cost sheet", "accessorKey": "attributes.costSheet"}, {"header": "Revenue sheet", "accessorKey": "attributes.revenueSheet"}, {"header": "Last Update", "accessorKey": "attributes.lastUpdate"}, {"header": "Cpass", "accessorKey": "attributes.cpass"}, {"buttons": [{"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}, {"type": "image", "title": "View", "iconUrl": ""}], "accessorKey": "actions"}], "buttonName": "+ Add customer/supplier", "moduleData": "customer-supplier-management", "moduleName": "customer/supplier", "elements": [{"name": "min", "generalInfoValues": true, "type": "select", "label": "Min", "options": [{"label": "00", "value": "00"}, {"label": "15", "value": "15"}, {"label": "30", "value": "30"}, {"label": "45", "value": "45"}]}, {"name": "hour", "generalInfoValues": true, "type": "select", "label": "Hour", "options": [{"label": "0", "value": "0"}, {"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5", "value": "5"}, {"label": "6", "value": "6"}, {"label": "7", "value": "7"}, {"label": "8", "value": "8"}, {"label": "9", "value": "9"}, {"label": "10", "value": "10"}, {"label": "11", "value": "11"}, {"label": "12", "value": "12"}, {"label": "13", "value": "13"}, {"label": "14", "value": "14"}, {"label": "15", "value": "15"}, {"label": "16", "value": "16"}, {"label": "17", "value": "17"}, {"label": "18", "value": "18"}, {"label": "19", "value": "19"}, {"label": "20", "value": "20"}, {"label": "21", "value": "21"}, {"label": "22", "value": "22"}, {"label": "23", "value": "23"}]}, {"name": "time", "generalInfoValues": true, "type": "select", "label": "Time", "options": [{"label": "(GMT +)", "value": "(GMT +)"}, {"label": "(GMT -)", "value": "(GMT -)"}]}, {"name": "customerType", "generalInfoValues": true, "type": "select", "label": "Customer type", "options": [{"label": "<PERSON><PERSON><PERSON>", "value": 0}, {"label": "HQ/Premium", "value": 1}, {"label": "Wholesale", "value": 2}, {"label": "Direct", "value": 3}, {"label": "NA/Test", "value": 4}, {"label": "Domestic", "value": 5}, {"label": "To Way", "value": 6}, {"label": "NULL", "value": 7}, {"label": "Not to be used", "value": 8}]}, {"name": "operatorType", "generalInfoValues": true, "type": "radio", "label": "Select relation type", "options": [{"label": "Customer", "value": "C"}, {"label": "Supplier", "value": "S"}, {"label": "Both", "value": "C,S"}]}, {"name": "protocolType", "generalInfoValues": true, "type": "radio", "label": "Protocol type", "options": {"default": [{"label": "A2P", "value": "1"}, {"label": "P2P", "value": "2"}], "conditions": [{"condition": {"operatorType": ["C", "C,S"], "interfaceType": ["SMPP", "SMPP_ES"]}, "options": [{"label": "A2P", "value": "1"}, {"label": "P2P", "value": "2"}, {"label": "A2P / P2P", "value": "6"}]}]}}, {"name": "interfaceType", "generalInfoValues": true, "type": "radio", "label": "Interface type", "options": {"default": [{"label": "ESME", "value": "SMPP"}, {"label": "SS7", "value": "SS7"}], "conditions": [{"condition": {"operatorType": ["S", "C,S"]}, "options": [{"label": "ESME", "value": "SMPP"}, {"label": "SS7", "value": "SS7"}, {"label": "SMPP ES", "value": "SMPP_ES"}]}]}}, {"name": "subInterface", "generalInfoValues": true, "type": "radio", "label": "Sub interface type", "options": [{"label": "SIGTRAN", "value": 1}, {"label": "SS7", "value": 2}]}, {"name": "supplierType", "generalInfoValues": true, "type": "select", "label": "Supplier type", "options": [{"label": "<PERSON><PERSON><PERSON>", "value": 0}, {"label": "HQ/Premium", "value": 1}, {"label": "Wholesale", "value": 2}, {"label": "Direct", "value": 3}, {"label": "NA/Test", "value": 4}, {"label": "Domestic", "value": 5}, {"label": "To Way", "value": 6}, {"label": "NULL", "value": 7}, {"label": "Not to be used", "value": 8}]}, {"name": "customerStatus", "generalInfoValues": true, "type": "select", "label": "Customer status", "options": [{"label": "Live", "value": 0}, {"label": "Not Live", "value": 1}, {"label": "Blocked", "value": 2}, {"label": "Dummy", "value": 3}]}, {"name": "supplierStatus", "generalInfoValues": true, "type": "select", "label": "Supplier status", "options": [{"label": "Live", "value": 0}, {"label": "Not Live", "value": 1}, {"label": "Blocked", "value": 2}, {"label": "Dummy", "value": 3}]}, {"name": "subInterfaceEsme", "generalInfoValues": true, "type": "radio", "label": "Sub interface type", "options": [{"label": "SMPP", "value": 1}, {"label": "HTTP", "value": 2}]}, {"name": "allowSpamTimeInterval", "generalInfoValues": true, "type": "radio", "label": "Alert of spam messages on configured interval", "options": [{"label": "Minute wise", "value": 1}, {"label": "Hour wise", "value": 2}, {"label": "Day wise", "value": 4}]}, {"name": "handleFraudMessage", "generalInfoValues": true, "type": "radio", "label": "<PERSON><PERSON> Fraud Messages", "options": [{"label": "Drop", "value": 1}, {"label": "Process", "value": 2}]}, {"name": "currencyIn", "billingInfo": true, "type": "select", "label": "Message fee currency (incoming)", "options": [{"label": "USD", "value": "USD"}, {"label": "EURO", "value": "EURO"}, {"label": "INR", "value": "INR"}]}, {"name": "billingType", "billingInfo": true, "type": "select", "label": "Billing type", "options": [{"label": "Commercial", "value": "C"}, {"label": "Non-commercial", "value": "N"}]}, {"name": "cbmBindType", "billingInfo": true, "type": "select", "label": "Billing model bind type", "options": [{"label": "Customer", "value": "C"}, {"label": "Supplier", "value": "S"}, {"label": "Customer and Supplier", "value": "C,S"}]}, {"name": "currencyOut", "billingInfo": true, "type": "select", "label": "Message fee currency (outgoing)", "options": [{"label": "USD", "value": "USD"}, {"label": "EURO", "value": "EURO"}, {"label": "INR", "value": "INR"}]}, {"name": "billingLogic", "billingInfo": true, "type": "select", "label": "Billing logic", "options": [{"label": "Submission", "value": "S"}, {"label": "Delivery", "value": "D"}, {"label": "Successful termination", "value": "T"}]}, {"name": "slabOperator", "billingInfo": true, "type": "select", "label": "Select operators", "options": [{"label": "All operators", "value": 1}, {"label": "Add operators", "value": 2}]}, {"name": "slabPricingType", "billingInfo": true, "type": "select", "label": "Slab pricing type", "options": [{"label": "Single price", "value": 1}, {"label": "Differential price", "value": 2}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>L<PERSON><PERSON>", "billingInfo": true, "type": "select", "label": "B number billing type", "options": [{"label": "DVOPID", "value": 0}, {"label": "DOP", "value": 1}]}, {"name": "cbmBillingPlanType", "billingInfo": true, "type": "select", "label": "Billing model type", "options": [{"label": "Per SMS", "value": 1}, {"label": "Monthly recurring", "value": 2}, {"label": "Fixed fee and per SMS", "value": 3}, {"label": "Fixed fee usage or per SMS", "value": 4}, {"label": "Slab wise pricing", "value": 5}, {"label": "Fixed fee and slab", "value": 6}]}, {"name": "fixedFeeAndPerSmsType", "billingInfo": true, "type": "select", "label": "Fixed fee and per SMS type", "options": [{"label": "Volume", "value": 1}, {"label": "Revenue", "value": 2}]}], "globalSearch": [{"value": "id", "label": "Customer/Supplier id"}, {"value": "name", "label": "Customer/Supplier name"}]}