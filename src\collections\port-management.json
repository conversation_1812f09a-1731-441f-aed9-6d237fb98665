{"header": "List of Port", "columns": [{"header": "Port ID", "accessorKey": "id", "filterVariant": "range"}, {"header": "IP Version", "accessorKey": "attributes.ipVersion"}, {"header": "Port", "accessorKey": "attributes.port"}, {"header": "Protocol", "accessorKey": "attributes.protocol"}, {"buttons": [{"type": "image", "title": "viewData", "iconUrl": ""}, {"type": "image", "title": "Edit", "iconUrl": ""}, {"type": "image", "title": "Delete", "iconUrl": ""}], "accessorKey": "actions"}], "elements": [{"name": "protocol", "size": 6, "type": "select", "title": "ESME protocol", "options": [{"label": "SMPP", "value": "S"}, {"label": "HTTP", "value": "H"}], "isMandatory": false, "defaultValue": "S", "isInfo": true, "info": "Name of the protocol"}, {"name": "ipVersion", "size": 6, "type": "select", "title": "IP version", "options": [{"label": "IPv4", "value": "4"}, {"label": "IPv6", "value": "6"}], "isMandatory": false, "defaultValue": "4", "isInfo": true, "info": "The IP version"}, {"name": "port", "size": 6, "type": "text", "title": "Listen port", "module": "port", "dynamic": {"field": "protocol", "value": ["S", "H"], "dynamicValue": "H", "dynamicType": "select"}, "options": [{"label": "8089", "value": 8089}, {"label": "1234", "value": 1234}, {"label": "4967", "value": 4967}, {"label": "4325", "value": 4325}], "isMandatory": true, "validations": [{"type": "required", "message": "Please enter the value for listen port", "selectMessage": "Please select the value for listen port"}], "validationType": "number", "isInfo": true, "info": "Enter the port number to which this protocol is to be connected"}], "formType": "simple", "buttonName": "+ Add Port", "moduleData": "port", "moduleName": "Port", "globalSearch": [{"label": "Port", "value": "port"}], "dropdownOptions": {"field": "protocol", "options": [{"label": "All Protocol", "query": "", "value": "ALL"}, {"label": "SMPP", "query": "filters[protocol][$eq]=S", "value": "S"}, {"label": "HTTP", "query": "filters[protocol][$eq]=H", "value": "H"}], "defaultQuery": "", "defaultValue": "ALL"}}