import React, { useState, useEffect } from "react";
import SubModuleFormTemplate from "./SubModuleFormTemplate";

const MultipleFormTemplate = ({
  formData,
  moduleNameValue,
  moduleName,
  handleCancel,
  action,
}) => {
  const [index, setIndex] = useState(0);
  const [subIndex, setSubIndex] = useState(null);
  const [categoryDetails, setCategoryDetails] = useState([]);

  const allFormElements =
    formData?.forms?.flatMap((form) =>
      form.hasChildren
        ? form.children.flatMap((child) => child.elements)
        : form.elements
    ) || [];

  const selectedForm = formData?.forms[index];
  const formElements = selectedForm?.hasChildren
    ? selectedForm?.children[subIndex]?.elements
    : selectedForm?.elements;
  const formTitle = selectedForm?.hasChildren
    ? selectedForm?.children[subIndex]?.formName
    : selectedForm?.formName;

  useEffect(() => {
    if (formData?.forms) {
      const details = formData.forms
        .map((form, i) => {
          if (form.hasChildren) {
            return form.children.map((child, ind) => ({
              formTitle: child.formName,
              formElements: child.elements,
            }));
          } else {
            return {
              formTitle: form.formName,
              formElements: form.elements,
            };
          }
        })
        .flat();

      setCategoryDetails(details);
    }
  }, [formData]);

  return (
    <div className="grid grid-cols-4 bg-white mt-5">
      <div className="grid-cols-1 pt-10">
        <ol>
          {formData?.forms?.map((x, i) =>
            x?.hasChildren ? (
              <div key={i} className="font-bold text-sm ml-5 py-3">
                {x.formName}
                <div className="border-t border-subNavBorder mt-3">
                  {x?.children?.map((y, ind) => (
                    <li
                      key={ind}
                      onClick={() => {
                        setIndex(i);
                        setSubIndex(ind);
                      }}
                      className={`py-3 pl-5 font-normal text-sm cursor-pointer ${
                        index === i && subIndex === ind
                          ? "bg-bgSubNavActive"
                          : ""
                      }`}
                    >
                      {y.formName}
                    </li>
                  ))}
                </div>
              </div>
            ) : (
              <li
                key={i}
                onClick={() => {
                  setIndex(i);
                  setSubIndex(null);
                }}
                className={`py-3 pl-5 text-sm font-bold cursor-pointer ${
                  index === i && subIndex === null ? "bg-bgSubNavActive" : ""
                }`}
              >
                {x.formName}
              </li>
            )
          )}
        </ol>
      </div>

      <div className="col-span-3" id="right-panel">
        <SubModuleFormTemplate
          elements={formElements}
          action={action}
          isServiceManagement={true}
          formTitle={formTitle}
          moduleNameValue={moduleNameValue}
          moduleName={moduleName}
          type={formData.formType}
          handleCancel={handleCancel}
          allFormElements={allFormElements}
          selectedForm={selectedForm}
          categoryDetails={categoryDetails}
        />
      </div>
    </div>
  );
};

export default MultipleFormTemplate;
