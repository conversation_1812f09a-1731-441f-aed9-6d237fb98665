import * as Yup from "yup";
// import { dialCodeToCountryCodeMap } from "../common/constants";
// import { isValidPhoneNumber } from "libphonenumber-js";

export const paymentInitialValue = {
  creditLine: null,
  oracleCustomer: null,
  creditLimit: null,
  oracleVendor: null,
  creditCurrency: "",
  airtelKam: "",
  billingTerm: "",
  airtelKamContact: null,
  paymentTerm: "",
  airtelKamEmail: "",
  vatNo: null,
  countryCode: null,
};
export const paymentValidationSchema = Yup.object().shape({
  vatNo: Yup.number()
    .typeError("Please enter number for VAT No")
    .integer("Please enter a valid number for VAT No")
    .min(1, "Values must be greater than or equal to 1.")
    .max(50, "Values must be less than or equal to 50.")
    .nullable(),

  airtelKam: Yup.string()
    .matches(
      /^[a-zA-Z0-9 _!@#$%^&*().-]+$/,
      "Only alphanumeric characters, underscore, space, and special characters (-!@#$%^&*().) are allowed for Airtel KAM"
    )
    .nullable(),

  creditLine: Yup.number()
    .typeError("Please enter number for credit line")
    .integer("Please enter a valid number for credit line")
    .min(1, "Values must be greater than or equal to 1.")
    .max(50, "Values must be less than or equal to 50.")
    .nullable(),

  billingTerm: Yup.string()
    .max(50, "Maximum length is 50 characters")
    .matches(
      /^[a-zA-Z][a-zA-Z0-9]*$/,
      "Billing Term must start with an alphabet and contain only alphanumeric characters"
    )
    .nullable(),

  creditLimit: Yup.number()
    .typeError("Please enter number for credit limit")
    .integer("Please enter a valid number for credit limit")
    .min(1, "Values must be greater than or equal to 1.")
    .max(50, "Values must be less than or equal to 50.")
    .nullable(),

  paymentTerm: Yup.string()
    .max(50, "Maximum length is 50 characters")
    .matches(
      /^[a-zA-Z][a-zA-Z0-9]*$/,
      "Payment Term must start with an alphabet and contain only alphanumeric characters"
    )
    .nullable(),

  oracleVendor: Yup.number()
    .typeError("Please enter number for oracle ID vendor")
    .integer("Please enter a valid number for oracle ID vendor")
    .min(1, "Values must be greater than or equal to 1.")
    .max(50, "Values must be less than or equal to 50.")
    .nullable(),

  airtelKamEmail: Yup.string()
    .email("Please enter the correct email format")
    .max(300, "Maximum character that can be entered is 300 for Email")
    .nullable(),

  creditCurrency: Yup.string()
    .max(50, "Maximum length is 50 characters")
    .matches(
      /^[a-zA-Z]+$/,
      "Only alphabetic characters are allowed for Credit Currency"
    )
    .nullable(),

  oracleCustomer: Yup.number()
    .typeError("Please enter number for oracle ID customer")
    .integer("Please enter a valid number for oracle ID customer")
    .min(1, "Values must be greater than or equal to 1.")
    .max(50, "Values must be less than or equal to 50.")
    .nullable(),

  airtelKamContact: Yup.number()
    .typeError("Please enter number for Airtel KAM contact no.")
    .integer("Please enter a valid number for Airtel KAM contact no.")
    .nullable(),

  // airtelKamContact: Yup.string()
  //   .nullable()
  //   .test(
  //     "phone-validation",
  //     "Please enter a valid number for Airtel KAM contact no.",
  //     function (value) {
  //       const { countryCode } = this.parent;
  //       const sol = handleMobileNumber({
  //         airtelKamContact: value,
  //         countryCode,
  //       });
  //       return sol;
  //     }
  //   ),
});
// function handleMobileNumber(e) {
//   try {
//     const countryCode = inferCountryCodeFromDialCode(e.countryCode);

//     if (!countryCode) {
//       return false;
//     }

//     const isValid = isValidPhoneNumber(
//       String(e.airtelKamContact),
//       String(countryCode)
//     );
//     return isValid;
//   } catch (error) {
//     console.error("Error in phone validation:", error);
//     return false;
//   }
// }

// function inferCountryCodeFromDialCode(dialCode) {
//   return dialCodeToCountryCodeMap[dialCode];
// }
