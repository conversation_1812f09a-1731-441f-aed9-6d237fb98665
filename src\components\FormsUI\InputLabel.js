import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { PlusCircleIcon } from "../../icons";
import { CssTooltip } from "../FormsUI/StyledComponent";
import { INFO_ICON } from "../../common/config";
import { HiddenEye } from "../../icons";
import ViewLCRDialog from "../Dialog/ViewLCRDialog";

const InputLabel = ({
  label,
  color,
  labelClassName,
  isMandatory,
  isMandatoryy,
  isAddButton,
  onClick,
  isInfo,
  info,
  isViewLCR,
  values,
  LCRName,
  formData,
  action,
  dynamic,
  dynamicLabel,
}) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  return (
    <div className="flex items-center justify-between w-full">
      <div className="flex gap-1 items-center">
        <div
          className={`${
            color ? color : "text-black"
          } text-xs font-normal mb-1 ${labelClassName}`}
        >
          {t(dynamic ? dynamicLabel : label)}
          {isMandatory || isMandatoryy ? (
            <span className="text-errorColor text-xs"> *</span>
          ) : null}
        </div>
        {isAddButton && action !== "viewData" && action !== "view" ? (
          <PlusCircleIcon
            className="w-5 h-5 ml-2 cursor-pointer"
            onClick={onClick}
          />
        ) : isViewLCR && values[LCRName] !== "" ? (
          <HiddenEye className="cursor-pointer" onClick={handleOpen} />
        ) : null}
      </div>

      {isInfo && INFO_ICON ? (
        <CssTooltip
          title={info}
          placement="left"
          arrow
          bgColor="#DDE9FD"
          color="#000000"
        >
          <div
            className="flex justify-center items-center text-blackColor text-xs font-normal rounded-full w-4 h-4 bg-bgSelectOption flex-shrink-0 cursor-pointer mr-2 italic"
            style={{ fontFamily: "Times New Roman, sans-serif" }}
          >
            i
          </div>
        </CssTooltip>
      ) : null}
      <ViewLCRDialog
        open={open}
        handleClose={handleClose}
        name={LCRName}
        values={values}
        formData={formData}
      />
    </div>
  );
};

export default InputLabel;
